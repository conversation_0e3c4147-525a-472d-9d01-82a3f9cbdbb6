"""Add Phase 4 models: projects, remediation, clients, compliance

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create Phase 4 tables."""
    
    # Create users table
    op.create_table('users',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('first_name', sa.String(length=100), nullable=False),
        sa.Column('last_name', sa.String(length=100), nullable=False),
        sa.Column('password_hash', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_superuser', sa.Boolean(), nullable=False),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_is_active'), 'users', ['is_active'], unique=False)

    # Create clients table
    op.create_table('clients',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('display_name', sa.String(length=255), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('primary_contact_name', sa.String(length=255), nullable=True),
        sa.Column('primary_contact_email', sa.String(length=255), nullable=True),
        sa.Column('primary_contact_phone', sa.String(length=50), nullable=True),
        sa.Column('address_line1', sa.String(length=255), nullable=True),
        sa.Column('address_line2', sa.String(length=255), nullable=True),
        sa.Column('city', sa.String(length=100), nullable=True),
        sa.Column('state_province', sa.String(length=100), nullable=True),
        sa.Column('postal_code', sa.String(length=20), nullable=True),
        sa.Column('country', sa.String(length=100), nullable=True),
        sa.Column('industry', sa.String(length=100), nullable=True),
        sa.Column('company_size', sa.String(length=50), nullable=True),
        sa.Column('website', sa.String(length=255), nullable=True),
        sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'ARCHIVED', name='clientstatus'), nullable=False),
        sa.Column('portal_enabled', sa.Boolean(), nullable=False),
        sa.Column('portal_url', sa.String(length=255), nullable=True),
        sa.Column('compliance_frameworks', sa.JSON(), nullable=True),
        sa.Column('security_requirements', sa.JSON(), nullable=True),
        sa.Column('data_retention_days', sa.Integer(), nullable=False),
        sa.Column('contract_start_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('contract_end_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('billing_contact_email', sa.String(length=255), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('custom_fields', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_clients_id'), 'clients', ['id'], unique=False)
    op.create_index(op.f('ix_clients_name'), 'clients', ['name'], unique=False)
    op.create_index(op.f('ix_clients_primary_contact_email'), 'clients', ['primary_contact_email'], unique=False)
    op.create_index(op.f('ix_clients_status'), 'clients', ['status'], unique=False)

    # Create projects table
    op.create_table('projects',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('client_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('project_manager_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('start_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('end_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('estimated_hours', sa.Integer(), nullable=False),
        sa.Column('actual_hours', sa.Integer(), nullable=False),
        sa.Column('current_phase', sa.Enum('PRE_ENGAGEMENT', 'INTELLIGENCE_GATHERING', 'THREAT_MODELING', 'VULNERABILITY_ANALYSIS', 'EXPLOITATION', 'POST_EXPLOITATION', 'REPORTING', name='ptesphase'), nullable=False),
        sa.Column('workflow_status', sa.Enum('NOT_STARTED', 'IN_PROGRESS', 'PENDING_REVIEW', 'COMPLETED', 'BLOCKED', 'CANCELLED', name='workflowstatus'), nullable=False),
        sa.Column('progress_percentage', sa.Float(), nullable=False),
        sa.Column('scope_document', sa.Text(), nullable=True),
        sa.Column('rules_of_engagement', sa.Text(), nullable=True),
        sa.Column('target_systems', sa.JSON(), nullable=True),
        sa.Column('excluded_systems', sa.JSON(), nullable=True),
        sa.Column('peer_review_required', sa.Boolean(), nullable=False),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('compliance_frameworks', sa.JSON(), nullable=True),
        sa.Column('audit_trail_enabled', sa.Boolean(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_archived', sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(['client_id'], ['clients.id'], ),
        sa.ForeignKeyConstraint(['project_manager_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_projects_client_id'), 'projects', ['client_id'], unique=False)
    op.create_index(op.f('ix_projects_current_phase'), 'projects', ['current_phase'], unique=False)
    op.create_index(op.f('ix_projects_id'), 'projects', ['id'], unique=False)
    op.create_index(op.f('ix_projects_is_active'), 'projects', ['is_active'], unique=False)
    op.create_index(op.f('ix_projects_is_archived'), 'projects', ['is_archived'], unique=False)
    op.create_index(op.f('ix_projects_name'), 'projects', ['name'], unique=False)
    op.create_index(op.f('ix_projects_project_manager_id'), 'projects', ['project_manager_id'], unique=False)
    op.create_index(op.f('ix_projects_workflow_status'), 'projects', ['workflow_status'], unique=False)

    # Create remediations table
    op.create_table('remediations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('severity', sa.Enum('CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO', name='severitylevel'), nullable=False),
        sa.Column('priority', sa.Integer(), nullable=False),
        sa.Column('cvss_score', sa.Float(), nullable=True),
        sa.Column('status', sa.Enum('IDENTIFIED', 'ASSIGNED', 'IN_PROGRESS', 'PENDING_VERIFICATION', 'VERIFIED', 'CLOSED', 'REOPENED', 'CANCELLED', name='remediationstatus'), nullable=False),
        sa.Column('assigned_to', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('assigned_team', sa.String(length=100), nullable=True),
        sa.Column('system_owner', sa.String(length=255), nullable=True),
        sa.Column('assigned_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('response_due', sa.DateTime(timezone=True), nullable=False),
        sa.Column('resolution_due', sa.DateTime(timezone=True), nullable=False),
        sa.Column('first_response_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('sla_breached', sa.Boolean(), nullable=False),
        sa.Column('breach_reason', sa.String(length=255), nullable=True),
        sa.Column('external_ticket_id', sa.String(length=100), nullable=True),
        sa.Column('external_ticket_url', sa.String(length=500), nullable=True),
        sa.Column('ticketing_system', sa.Enum('JIRA', 'SERVICENOW', 'ZENDESK', 'INTERNAL', name='ticketingsystem'), nullable=True),
        sa.Column('affected_systems', sa.JSON(), nullable=True),
        sa.Column('remediation_steps', sa.Text(), nullable=True),
        sa.Column('business_impact', sa.Text(), nullable=True),
        sa.Column('technical_details', sa.JSON(), nullable=True),
        sa.Column('verification_required', sa.Boolean(), nullable=False),
        sa.Column('verified_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('verification_notes', sa.Text(), nullable=True),
        sa.Column('closed_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('closed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('closure_reason', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['assigned_to'], ['users.id'], ),
        sa.ForeignKeyConstraint(['closed_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
        sa.ForeignKeyConstraint(['verified_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_remediations_assigned_to'), 'remediations', ['assigned_to'], unique=False)
    op.create_index(op.f('ix_remediations_external_ticket_id'), 'remediations', ['external_ticket_id'], unique=False)
    op.create_index(op.f('ix_remediations_id'), 'remediations', ['id'], unique=False)
    op.create_index(op.f('ix_remediations_project_id'), 'remediations', ['project_id'], unique=False)
    op.create_index(op.f('ix_remediations_resolution_due'), 'remediations', ['resolution_due'], unique=False)
    op.create_index(op.f('ix_remediations_response_due'), 'remediations', ['response_due'], unique=False)
    op.create_index(op.f('ix_remediations_severity'), 'remediations', ['severity'], unique=False)
    op.create_index(op.f('ix_remediations_sla_breached'), 'remediations', ['sla_breached'], unique=False)
    op.create_index(op.f('ix_remediations_status'), 'remediations', ['status'], unique=False)
    op.create_index(op.f('ix_remediations_title'), 'remediations', ['title'], unique=False)
    op.create_index(op.f('ix_remediations_vulnerability_id'), 'remediations', ['vulnerability_id'], unique=False)


def downgrade() -> None:
    """Drop Phase 4 tables."""
    op.drop_table('remediations')
    op.drop_table('projects')
    op.drop_table('clients')
    op.drop_table('users')
