"""Add Phase 3 vulnerability and risk assessment models

Revision ID: a15877d4efe7
Revises: 
Create Date: 2025-06-16 05:39:20.930797

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = 'a15877d4efe7'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create assets table
    op.create_table(
        'assets',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('name', sa.String(255), nullable=False, unique=True),
        sa.Column('asset_type', sa.String(50), nullable=False),
        sa.Column('ip_address', sa.String(45)),
        sa.Column('hostname', sa.String(255)),
        sa.Column('business_criticality', sa.String(20), nullable=False, default='medium'),
        sa.Column('business_processes', postgresql.ARRAY(sa.String)),
        sa.Column('compliance_requirements', postgresql.ARRAY(sa.String)),
        sa.Column('extra_data', postgresql.JSON),
    )

    # Create vulnerabilities table
    op.create_table(
        'vulnerabilities',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('cve_id', sa.String(20), unique=True),
        sa.Column('title', sa.String(500), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('cvss_score', sa.Numeric(3, 1)),
        sa.Column('cvss_vector', sa.String(200)),
        sa.Column('severity', sa.String(20), nullable=False, default='medium'),
        sa.Column('status', sa.String(20), nullable=False, default='discovered'),
        sa.Column('discovery_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('remediation_date', sa.DateTime(timezone=True)),
        sa.Column('verification_date', sa.DateTime(timezone=True)),
        sa.Column('business_impact_score', sa.Numeric(3, 1)),
        sa.Column('exploitability_score', sa.Numeric(3, 1)),
        sa.Column('true_risk_score', sa.Numeric(3, 1)),
        sa.Column('source', sa.String(100)),
        sa.Column('tags', postgresql.ARRAY(sa.String)),
        sa.Column('extra_data', postgresql.JSON),
        sa.CheckConstraint('cvss_score >= 0.0 AND cvss_score <= 10.0', name='cvss_score_range'),
    )

    # Create asset_vulnerabilities table
    op.create_table(
        'asset_vulnerabilities',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('assets.id'), nullable=False),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('vulnerabilities.id'), nullable=False),
        sa.Column('impact_level', sa.String(20), nullable=False),
        sa.Column('exploitability_likelihood', sa.Numeric(3, 1)),
        sa.Column('remediation_priority', sa.Integer),
        sa.Column('remediation_effort', sa.String(20)),
    )

    # Create vulnerability_metrics table
    op.create_table(
        'vulnerability_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('metric_type', sa.String(50), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('value', sa.Numeric(10, 2), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('assets.id')),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('vulnerabilities.id')),
        sa.Column('extra_data', postgresql.JSON),
    )

    # Create risk_assessments table
    op.create_table(
        'risk_assessments',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('vulnerabilities.id'), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('assets.id'), nullable=False),
        sa.Column('risk_score', sa.Numeric(3, 1), nullable=False),
        sa.Column('risk_level', sa.String(20), nullable=False),
        sa.Column('true_risk_score', sa.Numeric(3, 1)),
        sa.Column('business_impact', sa.Numeric(3, 1), nullable=False),
        sa.Column('financial_impact', sa.Numeric(12, 2)),
        sa.Column('regulatory_impact', sa.String(100)),
        sa.Column('reputation_impact', sa.Numeric(3, 1)),
        sa.Column('threat_likelihood', sa.Numeric(3, 1), nullable=False),
        sa.Column('exploit_availability', sa.Boolean, default=False),
        sa.Column('active_exploitation', sa.Boolean, default=False),
        sa.Column('threat_actors', postgresql.ARRAY(sa.String)),
        sa.Column('asset_exposure', sa.String(50)),
        sa.Column('network_segmentation', sa.String(50)),
        sa.Column('compensating_controls', postgresql.ARRAY(sa.String)),
        sa.Column('assessment_date', sa.DateTime(timezone=True), nullable=False),
        sa.Column('assessor_id', postgresql.UUID(as_uuid=True)),
        sa.Column('methodology', sa.String(50), nullable=False, default='RBVM'),
        sa.Column('confidence_level', sa.Numeric(3, 1)),
        sa.Column('notes', sa.Text),
        sa.Column('extra_data', postgresql.JSON),
        sa.CheckConstraint('risk_score >= 0.0 AND risk_score <= 10.0', name='risk_score_range'),
        sa.CheckConstraint('business_impact >= 0.0 AND business_impact <= 10.0', name='business_impact_range'),
        sa.CheckConstraint('threat_likelihood >= 0.0 AND threat_likelihood <= 10.0', name='threat_likelihood_range'),
    )

    # Create threat_intelligence table
    op.create_table(
        'threat_intelligence',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('vulnerabilities.id'), nullable=False),
        sa.Column('source', sa.String(100), nullable=False),
        sa.Column('source_confidence', sa.Numeric(3, 1)),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('actor_type', sa.String(20)),
        sa.Column('actor_name', sa.String(100)),
        sa.Column('actor_motivation', sa.String(100)),
        sa.Column('exploitation_method', sa.Text),
        sa.Column('exploit_complexity', sa.String(20)),
        sa.Column('target_sectors', postgresql.ARRAY(sa.String)),
        sa.Column('target_regions', postgresql.ARRAY(sa.String)),
        sa.Column('iocs', postgresql.ARRAY(sa.String)),
        sa.Column('ttps', postgresql.ARRAY(sa.String)),
        sa.Column('mitre_techniques', postgresql.ARRAY(sa.String)),
        sa.Column('raw_data', postgresql.JSON),
    )

    # Create remediation_plans table
    op.create_table(
        'remediation_plans',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=sa.text('uuid_generate_v4()')),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now()),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('vulnerabilities.id'), nullable=False),
        sa.Column('priority', sa.Integer, nullable=False),
        sa.Column('status', sa.String(20), nullable=False, default='planned'),
        sa.Column('remediation_type', sa.String(50), nullable=False),
        sa.Column('description', sa.Text, nullable=False),
        sa.Column('planned_start_date', sa.DateTime(timezone=True)),
        sa.Column('due_date', sa.DateTime(timezone=True)),
        sa.Column('actual_start_date', sa.DateTime(timezone=True)),
        sa.Column('completion_date', sa.DateTime(timezone=True)),
        sa.Column('assigned_to', postgresql.UUID(as_uuid=True)),
        sa.Column('estimated_effort_hours', sa.Numeric(6, 2)),
        sa.Column('actual_effort_hours', sa.Numeric(6, 2)),
        sa.Column('progress_percentage', sa.Integer, default=0),
        sa.Column('notes', sa.Text),
        sa.Column('extra_data', postgresql.JSON),
    )

    # Create indexes for performance
    op.create_index('idx_vulnerability_cve', 'vulnerabilities', ['cve_id'])
    op.create_index('idx_vulnerability_severity', 'vulnerabilities', ['severity'])
    op.create_index('idx_vulnerability_status', 'vulnerabilities', ['status'])
    op.create_index('idx_vulnerability_discovery_date', 'vulnerabilities', ['discovery_date'])
    op.create_index('idx_vulnerability_cvss_score', 'vulnerabilities', ['cvss_score'])

    op.create_index('idx_asset_name', 'assets', ['name'])
    op.create_index('idx_asset_criticality', 'assets', ['business_criticality'])
    op.create_index('idx_asset_ip_address', 'assets', ['ip_address'])

    op.create_index('idx_asset_vuln_asset', 'asset_vulnerabilities', ['asset_id'])
    op.create_index('idx_asset_vuln_vulnerability', 'asset_vulnerabilities', ['vulnerability_id'])
    op.create_index('idx_asset_vuln_impact', 'asset_vulnerabilities', ['impact_level'])

    op.create_index('idx_vuln_metric_timestamp', 'vulnerability_metrics', ['timestamp'])
    op.create_index('idx_vuln_metric_type', 'vulnerability_metrics', ['metric_type'])
    op.create_index('idx_vuln_metric_asset', 'vulnerability_metrics', ['asset_id'])

    op.create_index('idx_risk_assessment_vulnerability', 'risk_assessments', ['vulnerability_id'])
    op.create_index('idx_risk_assessment_asset', 'risk_assessments', ['asset_id'])
    op.create_index('idx_risk_assessment_level', 'risk_assessments', ['risk_level'])
    op.create_index('idx_risk_assessment_score', 'risk_assessments', ['risk_score'])

    op.create_index('idx_threat_intel_vulnerability', 'threat_intelligence', ['vulnerability_id'])
    op.create_index('idx_threat_intel_source', 'threat_intelligence', ['source'])
    op.create_index('idx_threat_intel_timestamp', 'threat_intelligence', ['timestamp'])
    op.create_index('idx_threat_intel_actor_type', 'threat_intelligence', ['actor_type'])

    op.create_index('idx_remediation_vulnerability', 'remediation_plans', ['vulnerability_id'])
    op.create_index('idx_remediation_priority', 'remediation_plans', ['priority'])
    op.create_index('idx_remediation_status', 'remediation_plans', ['status'])
    op.create_index('idx_remediation_due_date', 'remediation_plans', ['due_date'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index('idx_remediation_due_date')
    op.drop_index('idx_remediation_status')
    op.drop_index('idx_remediation_priority')
    op.drop_index('idx_remediation_vulnerability')

    op.drop_index('idx_threat_intel_actor_type')
    op.drop_index('idx_threat_intel_timestamp')
    op.drop_index('idx_threat_intel_source')
    op.drop_index('idx_threat_intel_vulnerability')

    op.drop_index('idx_risk_assessment_score')
    op.drop_index('idx_risk_assessment_level')
    op.drop_index('idx_risk_assessment_asset')
    op.drop_index('idx_risk_assessment_vulnerability')

    op.drop_index('idx_vuln_metric_asset')
    op.drop_index('idx_vuln_metric_type')
    op.drop_index('idx_vuln_metric_timestamp')

    op.drop_index('idx_asset_vuln_impact')
    op.drop_index('idx_asset_vuln_vulnerability')
    op.drop_index('idx_asset_vuln_asset')

    op.drop_index('idx_asset_ip_address')
    op.drop_index('idx_asset_criticality')
    op.drop_index('idx_asset_name')

    op.drop_index('idx_vulnerability_cvss_score')
    op.drop_index('idx_vulnerability_discovery_date')
    op.drop_index('idx_vulnerability_status')
    op.drop_index('idx_vulnerability_severity')
    op.drop_index('idx_vulnerability_cve')

    # Drop tables in reverse order
    op.drop_table('remediation_plans')
    op.drop_table('threat_intelligence')
    op.drop_table('risk_assessments')
    op.drop_table('vulnerability_metrics')
    op.drop_table('asset_vulnerabilities')
    op.drop_table('vulnerabilities')
    op.drop_table('assets')
