"""Phase 7: Integration Layer for Enterprise Systems

Revision ID: 0001
Revises: 
Create Date: 2025-01-16 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create integrations table
    op.create_table('integrations',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('integration_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('api_url', sa.String(length=500), nullable=True),
        sa.Column('api_key', sa.String(length=500), nullable=True),
        sa.Column('username', sa.String(length=255), nullable=True),
        sa.Column('password', sa.String(length=500), nullable=True),
        sa.Column('config', sa.JSON(), nullable=True),
        sa.Column('sync_interval', sa.Integer(), nullable=True),
        sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True),
        sa.Column('next_sync', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_enabled', sa.Boolean(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=True),
        sa.Column('max_retries', sa.Integer(), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_integrations_id'), 'integrations', ['id'], unique=False)
    op.create_index(op.f('ix_integrations_name'), 'integrations', ['name'], unique=False)
    op.create_index(op.f('ix_integrations_integration_type'), 'integrations', ['integration_type'], unique=False)

    # Create integration_sync_logs table
    op.create_table('integration_sync_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('records_processed', sa.Integer(), nullable=True),
        sa.Column('records_created', sa.Integer(), nullable=True),
        sa.Column('records_updated', sa.Integer(), nullable=True),
        sa.Column('records_failed', sa.Integer(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_details', sa.JSON(), nullable=True),
        sa.Column('sync_metadata', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_integration_sync_logs_id'), 'integration_sync_logs', ['id'], unique=False)
    op.create_index(op.f('ix_integration_sync_logs_integration_id'), 'integration_sync_logs', ['integration_id'], unique=False)

    # Create data_mappings table
    op.create_table('data_mappings',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('integration_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('source_field', sa.String(length=255), nullable=False),
        sa.Column('target_field', sa.String(length=255), nullable=False),
        sa.Column('field_type', sa.String(length=50), nullable=False),
        sa.Column('transformation_rules', sa.JSON(), nullable=True),
        sa.Column('is_required', sa.Boolean(), nullable=True),
        sa.Column('default_value', sa.String(length=500), nullable=True),
        sa.Column('validation_rules', sa.JSON(), nullable=True),
        sa.ForeignKeyConstraint(['integration_id'], ['integrations.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_data_mappings_id'), 'data_mappings', ['id'], unique=False)
    op.create_index(op.f('ix_data_mappings_integration_id'), 'data_mappings', ['integration_id'], unique=False)

    # Create knowledge_documents table
    op.create_table('knowledge_documents',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('document_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('file_path', sa.String(length=1000), nullable=True),
        sa.Column('file_name', sa.String(length=255), nullable=False),
        sa.Column('file_hash', sa.String(length=64), nullable=True),
        sa.Column('obsidian_path', sa.String(length=1000), nullable=True),
        sa.Column('obsidian_id', sa.String(length=255), nullable=True),
        sa.Column('last_obsidian_sync', sa.DateTime(timezone=True), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=True),
        sa.Column('parent_document_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('author_id', sa.String(length=36), nullable=True),
        sa.Column('reviewer_id', sa.String(length=36), nullable=True),
        sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['parent_document_id'], ['knowledge_documents.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_documents_id'), 'knowledge_documents', ['id'], unique=False)
    op.create_index(op.f('ix_knowledge_documents_title'), 'knowledge_documents', ['title'], unique=False)
    op.create_index(op.f('ix_knowledge_documents_document_type'), 'knowledge_documents', ['document_type'], unique=False)
    op.create_index(op.f('ix_knowledge_documents_obsidian_id'), 'knowledge_documents', ['obsidian_id'], unique=False)
    op.create_index(op.f('ix_knowledge_documents_parent_document_id'), 'knowledge_documents', ['parent_document_id'], unique=False)

    # Create document_links table
    op.create_table('document_links',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('source_document_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('target_document_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('link_type', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('link_text', sa.String(length=500), nullable=True),
        sa.Column('context', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['source_document_id'], ['knowledge_documents.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_document_id'], ['knowledge_documents.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_links_id'), 'document_links', ['id'], unique=False)
    op.create_index(op.f('ix_document_links_source_document_id'), 'document_links', ['source_document_id'], unique=False)
    op.create_index(op.f('ix_document_links_target_document_id'), 'document_links', ['target_document_id'], unique=False)

    # Create document_templates table
    op.create_table('document_templates',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('document_type', sa.String(length=50), nullable=False),
        sa.Column('template_content', sa.Text(), nullable=False),
        sa.Column('template_variables', sa.JSON(), nullable=True),
        sa.Column('generation_rules', sa.JSON(), nullable=True),
        sa.Column('auto_generate', sa.Boolean(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('category', sa.String(length=100), nullable=True),
        sa.Column('version', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_document_templates_id'), 'document_templates', ['id'], unique=False)
    op.create_index(op.f('ix_document_templates_name'), 'document_templates', ['name'], unique=False)

    # Create knowledge_graph table
    op.create_table('knowledge_graph',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('entity_type', sa.String(length=100), nullable=False),
        sa.Column('entity_id', sa.String(length=255), nullable=False),
        sa.Column('entity_name', sa.String(length=500), nullable=False),
        sa.Column('related_entity_type', sa.String(length=100), nullable=False),
        sa.Column('related_entity_id', sa.String(length=255), nullable=False),
        sa.Column('related_entity_name', sa.String(length=500), nullable=False),
        sa.Column('relationship_type', sa.String(length=100), nullable=False),
        sa.Column('relationship_strength', sa.Float(), nullable=True),
        sa.Column('context', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_graph_id'), 'knowledge_graph', ['id'], unique=False)
    op.create_index(op.f('ix_knowledge_graph_entity_type'), 'knowledge_graph', ['entity_type'], unique=False)
    op.create_index(op.f('ix_knowledge_graph_entity_id'), 'knowledge_graph', ['entity_id'], unique=False)

    # Create assets table
    op.create_table('assets',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('asset_type', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('cmdb_id', sa.String(length=255), nullable=True),
        sa.Column('cmdb_class', sa.String(length=100), nullable=True),
        sa.Column('cmdb_last_sync', sa.DateTime(timezone=True), nullable=True),
        sa.Column('hostname', sa.String(length=255), nullable=True),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('mac_address', sa.String(length=17), nullable=True),
        sa.Column('operating_system', sa.String(length=255), nullable=True),
        sa.Column('os_version', sa.String(length=100), nullable=True),
        sa.Column('business_service', sa.String(length=255), nullable=True),
        sa.Column('owner', sa.String(length=255), nullable=True),
        sa.Column('department', sa.String(length=255), nullable=True),
        sa.Column('location', sa.String(length=255), nullable=True),
        sa.Column('criticality', sa.String(length=20), nullable=True),
        sa.Column('risk_score', sa.Float(), nullable=True),
        sa.Column('compliance_status', sa.String(length=50), nullable=True),
        sa.Column('configuration', sa.JSON(), nullable=True),
        sa.Column('installed_software', sa.JSON(), nullable=True),
        sa.Column('network_interfaces', sa.JSON(), nullable=True),
        sa.Column('is_monitored', sa.Boolean(), nullable=True),
        sa.Column('last_seen', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_assets_id'), 'assets', ['id'], unique=False)
    op.create_index(op.f('ix_assets_name'), 'assets', ['name'], unique=False)
    op.create_index(op.f('ix_assets_asset_type'), 'assets', ['asset_type'], unique=False)
    op.create_index(op.f('ix_assets_cmdb_id'), 'assets', ['cmdb_id'], unique=False)
    op.create_index(op.f('ix_assets_hostname'), 'assets', ['hostname'], unique=False)
    op.create_index(op.f('ix_assets_ip_address'), 'assets', ['ip_address'], unique=False)

    # Create asset_dependencies table
    op.create_table('asset_dependencies',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('source_asset_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('target_asset_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('dependency_type', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('criticality', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('cmdb_relationship_id', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['source_asset_id'], ['assets.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['target_asset_id'], ['assets.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_asset_dependencies_id'), 'asset_dependencies', ['id'], unique=False)
    op.create_index(op.f('ix_asset_dependencies_source_asset_id'), 'asset_dependencies', ['source_asset_id'], unique=False)
    op.create_index(op.f('ix_asset_dependencies_target_asset_id'), 'asset_dependencies', ['target_asset_id'], unique=False)

    # Create vulnerabilities table
    op.create_table('vulnerabilities',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('cve_id', sa.String(length=20), nullable=True),
        sa.Column('cwe_id', sa.String(length=20), nullable=True),
        sa.Column('plugin_id', sa.String(length=50), nullable=True),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('cvss_v2_score', sa.Float(), nullable=True),
        sa.Column('cvss_v3_score', sa.Float(), nullable=True),
        sa.Column('cvss_v4_score', sa.Float(), nullable=True),
        sa.Column('cvss_vector', sa.String(length=200), nullable=True),
        sa.Column('risk_score', sa.Float(), nullable=True),
        sa.Column('exploitability_score', sa.Float(), nullable=True),
        sa.Column('business_impact_score', sa.Float(), nullable=True),
        sa.Column('source', sa.String(length=50), nullable=False),
        sa.Column('source_id', sa.String(length=255), nullable=True),
        sa.Column('scanner_name', sa.String(length=100), nullable=True),
        sa.Column('first_discovered', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_seen', sa.DateTime(timezone=True), nullable=True),
        sa.Column('affected_software', sa.String(length=500), nullable=True),
        sa.Column('affected_versions', sa.String(length=500), nullable=True),
        sa.Column('port', sa.Integer(), nullable=True),
        sa.Column('protocol', sa.String(length=20), nullable=True),
        sa.Column('service', sa.String(length=100), nullable=True),
        sa.Column('evidence', sa.Text(), nullable=True),
        sa.Column('proof_of_concept', sa.Text(), nullable=True),
        sa.Column('solution', sa.Text(), nullable=True),
        sa.Column('workaround', sa.Text(), nullable=True),
        sa.Column('remediation_effort', sa.String(length=20), nullable=True),
        sa.Column('references', sa.JSON(), nullable=True),
        sa.Column('external_references', sa.JSON(), nullable=True),
        sa.Column('mitre_techniques', sa.JSON(), nullable=True),
        sa.Column('mitre_tactics', sa.JSON(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('custom_fields', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_vulnerabilities_id'), 'vulnerabilities', ['id'], unique=False)
    op.create_index(op.f('ix_vulnerabilities_title'), 'vulnerabilities', ['title'], unique=False)
    op.create_index(op.f('ix_vulnerabilities_cve_id'), 'vulnerabilities', ['cve_id'], unique=False)
    op.create_index(op.f('ix_vulnerabilities_cwe_id'), 'vulnerabilities', ['cwe_id'], unique=False)
    op.create_index(op.f('ix_vulnerabilities_severity'), 'vulnerabilities', ['severity'], unique=False)

    # Create remaining tables
    op.create_table('asset_vulnerabilities',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('vulnerability_id', sa.String(length=36), nullable=False),
        sa.Column('cve_id', sa.String(length=20), nullable=True),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('cvss_score', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('first_detected', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_detected', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('scanner_name', sa.String(length=100), nullable=True),
        sa.Column('scan_id', sa.String(length=255), nullable=True),
        sa.Column('remediation_status', sa.String(length=50), nullable=True),
        sa.Column('remediation_notes', sa.Text(), nullable=True),
        sa.Column('assigned_to', sa.String(length=255), nullable=True),
        sa.Column('business_impact', sa.String(length=20), nullable=True),
        sa.Column('exploitability', sa.String(length=20), nullable=True),
        sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_asset_vulnerabilities_id'), 'asset_vulnerabilities', ['id'], unique=False)
    op.create_index(op.f('ix_asset_vulnerabilities_asset_id'), 'asset_vulnerabilities', ['asset_id'], unique=False)
    op.create_index(op.f('ix_asset_vulnerabilities_vulnerability_id'), 'asset_vulnerabilities', ['vulnerability_id'], unique=False)
    op.create_index(op.f('ix_asset_vulnerabilities_cve_id'), 'asset_vulnerabilities', ['cve_id'], unique=False)

    op.create_table('configuration_items',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('cmdb_id', sa.String(length=255), nullable=False),
        sa.Column('cmdb_class', sa.String(length=100), nullable=False),
        sa.Column('cmdb_subclass', sa.String(length=100), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('display_name', sa.String(length=255), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('operational_status', sa.String(length=50), nullable=True),
        sa.Column('lifecycle_status', sa.String(length=50), nullable=True),
        sa.Column('attributes', sa.JSON(), nullable=True),
        sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True),
        sa.Column('sync_hash', sa.String(length=64), nullable=True),
        sa.Column('mapped_asset_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(['mapped_asset_id'], ['assets.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_configuration_items_id'), 'configuration_items', ['id'], unique=False)
    op.create_index(op.f('ix_configuration_items_cmdb_id'), 'configuration_items', ['cmdb_id'], unique=False)
    op.create_index(op.f('ix_configuration_items_name'), 'configuration_items', ['name'], unique=False)
    op.create_index(op.f('ix_configuration_items_mapped_asset_id'), 'configuration_items', ['mapped_asset_id'], unique=False)

    op.create_table('vulnerability_findings',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('vulnerability_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('asset_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('discovered_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_verified', sa.DateTime(timezone=True), nullable=True),
        sa.Column('scan_id', sa.String(length=255), nullable=True),
        sa.Column('scanner_finding_id', sa.String(length=255), nullable=True),
        sa.Column('asset_specific_evidence', sa.Text(), nullable=True),
        sa.Column('asset_specific_impact', sa.Text(), nullable=True),
        sa.Column('assigned_to', sa.String(length=255), nullable=True),
        sa.Column('remediation_status', sa.String(length=50), nullable=True),
        sa.Column('remediation_notes', sa.Text(), nullable=True),
        sa.Column('remediation_deadline', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('verified_by', sa.String(length=255), nullable=True),
        sa.Column('verification_notes', sa.Text(), nullable=True),
        sa.Column('business_context', sa.Text(), nullable=True),
        sa.Column('environmental_score', sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['vulnerability_id'], ['vulnerabilities.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_vulnerability_findings_id'), 'vulnerability_findings', ['id'], unique=False)
    op.create_index(op.f('ix_vulnerability_findings_vulnerability_id'), 'vulnerability_findings', ['vulnerability_id'], unique=False)
    op.create_index(op.f('ix_vulnerability_findings_asset_id'), 'vulnerability_findings', ['asset_id'], unique=False)

    op.create_table('threat_intelligence',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('threat_id', sa.String(length=255), nullable=False),
        sa.Column('threat_type', sa.String(length=100), nullable=False),
        sa.Column('threat_name', sa.String(length=500), nullable=False),
        sa.Column('cve_ids', sa.JSON(), nullable=True),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('confidence', sa.Float(), nullable=False),
        sa.Column('iocs', sa.JSON(), nullable=True),
        sa.Column('mitre_techniques', sa.JSON(), nullable=True),
        sa.Column('mitre_tactics', sa.JSON(), nullable=True),
        sa.Column('first_seen', sa.DateTime(timezone=True), nullable=False),
        sa.Column('last_seen', sa.DateTime(timezone=True), nullable=True),
        sa.Column('source', sa.String(length=100), nullable=False),
        sa.Column('source_confidence', sa.Float(), nullable=True),
        sa.Column('tags', sa.JSON(), nullable=True),
        sa.Column('references', sa.JSON(), nullable=True),
        sa.Column('enrichment_data', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_threat_intelligence_id'), 'threat_intelligence', ['id'], unique=False)
    op.create_index(op.f('ix_threat_intelligence_threat_id'), 'threat_intelligence', ['threat_id'], unique=False)

    op.create_table('security_events',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('event_id', sa.String(length=255), nullable=False),
        sa.Column('source_system', sa.String(length=100), nullable=False),
        sa.Column('event_type', sa.String(length=100), nullable=False),
        sa.Column('event_name', sa.String(length=500), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('category', sa.String(length=100), nullable=False),
        sa.Column('event_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('ingested_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('source_ip', sa.String(length=45), nullable=True),
        sa.Column('source_hostname', sa.String(length=255), nullable=True),
        sa.Column('destination_ip', sa.String(length=45), nullable=True),
        sa.Column('destination_hostname', sa.String(length=255), nullable=True),
        sa.Column('raw_event', sa.JSON(), nullable=True),
        sa.Column('parsed_fields', sa.JSON(), nullable=True),
        sa.Column('correlation_id', sa.String(length=255), nullable=True),
        sa.Column('related_vulnerabilities', sa.JSON(), nullable=True),
        sa.Column('mitre_techniques', sa.JSON(), nullable=True),
        sa.Column('mitre_tactics', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_security_events_id'), 'security_events', ['id'], unique=False)
    op.create_index(op.f('ix_security_events_event_id'), 'security_events', ['event_id'], unique=False)
    op.create_index(op.f('ix_security_events_correlation_id'), 'security_events', ['correlation_id'], unique=False)


def downgrade() -> None:
    # Drop security and threat tables
    op.drop_index(op.f('ix_security_events_correlation_id'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_event_id'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_id'), table_name='security_events')
    op.drop_table('security_events')

    op.drop_index(op.f('ix_threat_intelligence_threat_id'), table_name='threat_intelligence')
    op.drop_index(op.f('ix_threat_intelligence_id'), table_name='threat_intelligence')
    op.drop_table('threat_intelligence')

    # Drop vulnerability tables
    op.drop_index(op.f('ix_vulnerability_findings_asset_id'), table_name='vulnerability_findings')
    op.drop_index(op.f('ix_vulnerability_findings_vulnerability_id'), table_name='vulnerability_findings')
    op.drop_index(op.f('ix_vulnerability_findings_id'), table_name='vulnerability_findings')
    op.drop_table('vulnerability_findings')

    op.drop_index(op.f('ix_vulnerabilities_severity'), table_name='vulnerabilities')
    op.drop_index(op.f('ix_vulnerabilities_cwe_id'), table_name='vulnerabilities')
    op.drop_index(op.f('ix_vulnerabilities_cve_id'), table_name='vulnerabilities')
    op.drop_index(op.f('ix_vulnerabilities_title'), table_name='vulnerabilities')
    op.drop_index(op.f('ix_vulnerabilities_id'), table_name='vulnerabilities')
    op.drop_table('vulnerabilities')

    # Drop asset tables
    op.drop_index(op.f('ix_configuration_items_mapped_asset_id'), table_name='configuration_items')
    op.drop_index(op.f('ix_configuration_items_name'), table_name='configuration_items')
    op.drop_index(op.f('ix_configuration_items_cmdb_id'), table_name='configuration_items')
    op.drop_index(op.f('ix_configuration_items_id'), table_name='configuration_items')
    op.drop_table('configuration_items')

    op.drop_index(op.f('ix_asset_vulnerabilities_cve_id'), table_name='asset_vulnerabilities')
    op.drop_index(op.f('ix_asset_vulnerabilities_vulnerability_id'), table_name='asset_vulnerabilities')
    op.drop_index(op.f('ix_asset_vulnerabilities_asset_id'), table_name='asset_vulnerabilities')
    op.drop_index(op.f('ix_asset_vulnerabilities_id'), table_name='asset_vulnerabilities')
    op.drop_table('asset_vulnerabilities')

    op.drop_index(op.f('ix_asset_dependencies_target_asset_id'), table_name='asset_dependencies')
    op.drop_index(op.f('ix_asset_dependencies_source_asset_id'), table_name='asset_dependencies')
    op.drop_index(op.f('ix_asset_dependencies_id'), table_name='asset_dependencies')
    op.drop_table('asset_dependencies')

    op.drop_index(op.f('ix_assets_ip_address'), table_name='assets')
    op.drop_index(op.f('ix_assets_hostname'), table_name='assets')
    op.drop_index(op.f('ix_assets_cmdb_id'), table_name='assets')
    op.drop_index(op.f('ix_assets_asset_type'), table_name='assets')
    op.drop_index(op.f('ix_assets_name'), table_name='assets')
    op.drop_index(op.f('ix_assets_id'), table_name='assets')
    op.drop_table('assets')

    # Drop knowledge tables
    op.drop_index(op.f('ix_knowledge_graph_entity_id'), table_name='knowledge_graph')
    op.drop_index(op.f('ix_knowledge_graph_entity_type'), table_name='knowledge_graph')
    op.drop_index(op.f('ix_knowledge_graph_id'), table_name='knowledge_graph')
    op.drop_table('knowledge_graph')

    op.drop_index(op.f('ix_document_templates_name'), table_name='document_templates')
    op.drop_index(op.f('ix_document_templates_id'), table_name='document_templates')
    op.drop_table('document_templates')

    op.drop_index(op.f('ix_document_links_target_document_id'), table_name='document_links')
    op.drop_index(op.f('ix_document_links_source_document_id'), table_name='document_links')
    op.drop_index(op.f('ix_document_links_id'), table_name='document_links')
    op.drop_table('document_links')

    op.drop_index(op.f('ix_knowledge_documents_parent_document_id'), table_name='knowledge_documents')
    op.drop_index(op.f('ix_knowledge_documents_obsidian_id'), table_name='knowledge_documents')
    op.drop_index(op.f('ix_knowledge_documents_document_type'), table_name='knowledge_documents')
    op.drop_index(op.f('ix_knowledge_documents_title'), table_name='knowledge_documents')
    op.drop_index(op.f('ix_knowledge_documents_id'), table_name='knowledge_documents')
    op.drop_table('knowledge_documents')

    # Drop integration tables
    op.drop_index(op.f('ix_data_mappings_integration_id'), table_name='data_mappings')
    op.drop_index(op.f('ix_data_mappings_id'), table_name='data_mappings')
    op.drop_table('data_mappings')

    op.drop_index(op.f('ix_integration_sync_logs_integration_id'), table_name='integration_sync_logs')
    op.drop_index(op.f('ix_integration_sync_logs_id'), table_name='integration_sync_logs')
    op.drop_table('integration_sync_logs')

    op.drop_index(op.f('ix_integrations_integration_type'), table_name='integrations')
    op.drop_index(op.f('ix_integrations_name'), table_name='integrations')
    op.drop_index(op.f('ix_integrations_id'), table_name='integrations')
    op.drop_table('integrations')
