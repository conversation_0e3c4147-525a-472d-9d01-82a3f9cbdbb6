"""Add Phase 5 training and competency management models

Revision ID: 83d1015e8f09
Revises: 
Create Date: 2025-06-16 05:25:43.142896

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '83d1015e8f09'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create users table
    op.create_table(
        'users',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('username', sa.String(length=100), nullable=False),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), nullable=True),
        sa.Column('is_superuser', sa.<PERSON>olean(), nullable=True),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)

    # Create competency_frameworks table
    op.create_table(
        'competency_frameworks',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('work_role_id', sa.String(length=50), nullable=False),
        sa.Column('specialty_area', sa.String(length=255), nullable=False),
        sa.Column('category', sa.String(length=255), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )

    # Create competencies table
    op.create_table(
        'competencies',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('framework_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('competency_id', sa.String(length=50), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('knowledge_statements', sa.JSON(), nullable=True),
        sa.Column('skill_statements', sa.JSON(), nullable=True),
        sa.Column('ability_statements', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['framework_id'], ['competency_frameworks.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create certifications table
    op.create_table(
        'certifications',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('abbreviation', sa.String(length=50), nullable=False),
        sa.Column('provider', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('level', sa.Enum('novice', 'advanced_beginner', 'competent', 'proficient', 'expert', name='competencylevel'), nullable=False),
        sa.Column('prerequisites', sa.JSON(), nullable=True),
        sa.Column('renewal_period_years', sa.Integer(), nullable=True),
        sa.Column('cpe_credits_required', sa.Integer(), nullable=True),
        sa.Column('exam_cost', sa.Float(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )

    # Create training_courses table
    op.create_table(
        'training_courses',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('provider', sa.String(length=255), nullable=True),
        sa.Column('course_code', sa.String(length=100), nullable=True),
        sa.Column('duration_hours', sa.Integer(), nullable=True),
        sa.Column('difficulty_level', sa.Enum('novice', 'advanced_beginner', 'competent', 'proficient', 'expert', name='competencylevel'), nullable=False),
        sa.Column('prerequisites', sa.JSON(), nullable=True),
        sa.Column('learning_objectives', sa.JSON(), nullable=True),
        sa.Column('competencies_addressed', sa.JSON(), nullable=True),
        sa.Column('is_certification_prep', sa.Boolean(), nullable=True),
        sa.Column('certification_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('cost', sa.Float(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['certification_id'], ['certifications.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create ctf_challenges table
    op.create_table(
        'ctf_challenges',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=100), nullable=False),
        sa.Column('difficulty', sa.Enum('novice', 'advanced_beginner', 'competent', 'proficient', 'expert', name='competencylevel'), nullable=False),
        sa.Column('points', sa.Integer(), nullable=False),
        sa.Column('flag', sa.String(length=255), nullable=False),
        sa.Column('hints', sa.JSON(), nullable=True),
        sa.Column('files', sa.JSON(), nullable=True),
        sa.Column('competencies_tested', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_by', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create learning_paths table
    op.create_table(
        'learning_paths',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('target_role', sa.String(length=255), nullable=True),
        sa.Column('estimated_duration_weeks', sa.Integer(), nullable=True),
        sa.Column('course_sequence', sa.JSON(), nullable=True),
        sa.Column('competency_goals', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('completion_percentage', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create mentorship_pairs table
    op.create_table(
        'mentorship_pairs',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('mentor_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('mentee_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('goals', sa.JSON(), nullable=True),
        sa.Column('meeting_frequency', sa.String(length=100), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('satisfaction_rating', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['mentee_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['mentor_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create skill_assessments table
    op.create_table(
        'skill_assessments',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('competency_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('current_level', sa.Enum('novice', 'advanced_beginner', 'competent', 'proficient', 'expert', name='competencylevel'), nullable=False),
        sa.Column('target_level', sa.Enum('novice', 'advanced_beginner', 'competent', 'proficient', 'expert', name='competencylevel'), nullable=False),
        sa.Column('assessment_date', sa.DateTime(), nullable=False),
        sa.Column('assessor_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('evidence', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['competency_id'], ['competencies.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['assessor_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create training_enrollments table
    op.create_table(
        'training_enrollments',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('course_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('learning_path_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('enrollment_date', sa.DateTime(), nullable=False),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('completion_date', sa.DateTime(), nullable=True),
        sa.Column('status', sa.Enum('not_started', 'in_progress', 'completed', 'failed', 'expired', name='trainingstatus'), nullable=False),
        sa.Column('progress_percentage', sa.Float(), nullable=True),
        sa.Column('assessment_scores', sa.JSON(), nullable=True),
        sa.Column('practical_scores', sa.JSON(), nullable=True),
        sa.Column('time_spent_hours', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['course_id'], ['training_courses.id'], ),
        sa.ForeignKeyConstraint(['learning_path_id'], ['learning_paths.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create certification_achievements table
    op.create_table(
        'certification_achievements',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('certification_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('status', sa.Enum('not_started', 'in_progress', 'achieved', 'expired', 'renewal_required', name='certificationstatus'), nullable=False),
        sa.Column('achievement_date', sa.DateTime(), nullable=True),
        sa.Column('expiration_date', sa.DateTime(), nullable=True),
        sa.Column('credential_id', sa.String(length=255), nullable=True),
        sa.Column('cpe_credits_earned', sa.Integer(), nullable=True),
        sa.Column('renewal_reminder_sent', sa.Boolean(), nullable=True),
        sa.Column('cost_reimbursed', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['certification_id'], ['certifications.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create ctf_submissions table
    op.create_table(
        'ctf_submissions',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('challenge_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('submission_time', sa.DateTime(), nullable=False),
        sa.Column('submitted_flag', sa.String(length=255), nullable=False),
        sa.Column('is_correct', sa.Boolean(), nullable=False),
        sa.Column('points_awarded', sa.Integer(), nullable=True),
        sa.Column('time_to_solve_minutes', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['challenge_id'], ['ctf_challenges.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create mentorship_sessions table
    op.create_table(
        'mentorship_sessions',
        sa.Column('id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('pair_id', sa.dialects.postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('session_date', sa.DateTime(), nullable=False),
        sa.Column('duration_minutes', sa.Integer(), nullable=True),
        sa.Column('topics_discussed', sa.JSON(), nullable=True),
        sa.Column('action_items', sa.JSON(), nullable=True),
        sa.Column('mentor_notes', sa.Text(), nullable=True),
        sa.Column('mentee_feedback', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['pair_id'], ['mentorship_pairs.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop tables in reverse order
    op.drop_table('mentorship_sessions')
    op.drop_table('ctf_submissions')
    op.drop_table('certification_achievements')
    op.drop_table('training_enrollments')
    op.drop_table('skill_assessments')
    op.drop_table('mentorship_pairs')
    op.drop_table('learning_paths')
    op.drop_table('ctf_challenges')
    op.drop_table('training_courses')
    op.drop_table('certifications')
    op.drop_table('competencies')
    op.drop_table('competency_frameworks')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
