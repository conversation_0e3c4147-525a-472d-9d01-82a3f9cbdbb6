"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
<<<<<<< HEAD
=======
from typing import Sequence, Union

>>>>>>> integrate-phase8-with-phase11
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
<<<<<<< HEAD
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
=======
revision: str = ${repr(up_revision)}
down_revision: Union[str, None] = ${repr(down_revision)}
branch_labels: Union[str, Sequence[str], None] = ${repr(branch_labels)}
depends_on: Union[str, Sequence[str], None] = ${repr(depends_on)}


def upgrade() -> None:
    """Upgrade schema."""
>>>>>>> integrate-phase8-with-phase11
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
<<<<<<< HEAD
=======
    """Downgrade schema."""
>>>>>>> integrate-phase8-with-phase11
    ${downgrades if downgrades else "pass"}
