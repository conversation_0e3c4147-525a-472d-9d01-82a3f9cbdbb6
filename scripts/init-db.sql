-- Initialize PITAS database with required extensions and test database

-- Create test database for running tests
CREATE DATABASE test_pitas;

-- Grant permissions to pitas user
GRANT ALL PRIVILEGES ON DATABASE pitas_db TO pitas;
GRANT ALL PRIVILEGES ON DATABASE test_pitas TO pitas;

-- Connect to main database to set up extensions
\c pitas_db;

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create schema for vulnerability data
CREATE SCHEMA IF NOT EXISTS vulnerability;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS security;

-- Grant schema permissions
GRANT ALL ON SCHEMA vulnerability TO pitas;
GRANT ALL ON SCHEMA analytics TO pitas;
GRANT ALL ON SCHEMA security TO pitas;

-- Connect to test database to set up extensions
\c test_pitas;

-- Enable required PostgreSQL extensions for test database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- Create schema for test data
CREATE SCHEMA IF NOT EXISTS vulnerability;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS security;

-- Grant schema permissions for test database
GRANT ALL ON SCHEMA vulnerability TO pitas;
GRANT ALL ON SCHEMA analytics TO pitas;
GRANT ALL ON SCHEMA security TO pitas;
