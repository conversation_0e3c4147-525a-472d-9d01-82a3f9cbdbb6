#!/usr/bin/env bash

# Test script to verify shell.nix dependency management
# This script tests that all required tools are available in the Nix shell environment

set -euo pipefail

# Colors for output
BLUE='\033[36m'
GREEN='\033[32m'
YELLOW='\033[33m'
RED='\033[31m'
RESET='\033[0m'

echo -e "${BLUE}🧪 Testing shell.nix Dependency Management${RESET}"
echo -e "${BLUE}===========================================${RESET}"
echo ""

# Function to test if a command exists and works
test_command() {
    local cmd="$1"
    local description="$2"
    local test_args="${3:-}"
    
    echo -n "Testing $description... "
    
    if command -v "$cmd" >/dev/null 2>&1; then
        if [ -n "$test_args" ]; then
            if $cmd $test_args >/dev/null 2>&1; then
                echo -e "${GREEN}✅ PASS${RESET}"
                return 0
            else
                echo -e "${RED}❌ FAIL (command exists but test failed)${RESET}"
                return 1
            fi
        else
            echo -e "${GREEN}✅ PASS${RESET}"
            return 0
        fi
    else
        echo -e "${RED}❌ FAIL (command not found)${RESET}"
        return 1
    fi
}

# Function to test Python packages
test_python_package() {
    local package="$1"
    local description="$2"
    
    echo -n "Testing $description... "
    
    if python -c "import $package" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${RESET}"
        return 0
    else
        echo -e "${RED}❌ FAIL (package not available)${RESET}"
        return 1
    fi
}

# Track test results
TOTAL_TESTS=0
PASSED_TESTS=0

# Test core development tools
echo -e "${YELLOW}Core Development Tools:${RESET}"
test_command "python" "Python 3.11+" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "pip" "pip package manager" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "git" "Git version control" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "gh" "GitHub CLI" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test code quality tools
echo -e "${YELLOW}Code Quality Tools:${RESET}"
test_command "ruff" "Ruff linter/formatter" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "black" "Black formatter" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "mypy" "MyPy type checker" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "pre-commit" "Pre-commit hooks" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test security tools
echo -e "${YELLOW}Security Tools:${RESET}"
test_command "bandit" "Bandit security scanner" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "semgrep" "Semgrep static analysis" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test database tools
echo -e "${YELLOW}Database Tools:${RESET}"
test_command "postgres" "PostgreSQL" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "redis-server" "Redis server" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test documentation tools
echo -e "${YELLOW}Documentation Tools:${RESET}"
test_command "sphinx-build" "Sphinx documentation" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "pandoc" "Pandoc document converter" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test container and orchestration tools
echo -e "${YELLOW}Container & Orchestration Tools:${RESET}"
test_command "docker" "Docker" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "docker-compose" "Docker Compose" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "kubectl" "Kubernetes CLI" "version --client" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "helm" "Helm package manager" "version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test monitoring tools
echo -e "${YELLOW}Monitoring Tools:${RESET}"
test_command "prometheus" "Prometheus" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "grafana-server" "Grafana" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test API and HTTP tools
echo -e "${YELLOW}API & HTTP Tools:${RESET}"
test_command "curl" "cURL" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "http" "HTTPie" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "jq" "jq JSON processor" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test cloud tools
echo -e "${YELLOW}Cloud Tools:${RESET}"
test_command "aws" "AWS CLI" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "gcloud" "Google Cloud SDK" "version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "az" "Azure CLI" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test infrastructure tools
echo -e "${YELLOW}Infrastructure Tools:${RESET}"
test_command "terraform" "Terraform" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "ansible" "Ansible" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test utility tools
echo -e "${YELLOW}Utility Tools:${RESET}"
test_command "tree" "Tree directory listing" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "fd" "fd file finder" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "rg" "ripgrep" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
test_command "bat" "bat file viewer" "--version" && ((PASSED_TESTS++)) || true; ((TOTAL_TESTS++))
echo ""

# Test Python environment
echo -e "${YELLOW}Python Environment:${RESET}"
echo -n "Testing Python version... "
PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
if [[ "$PYTHON_VERSION" =~ ^3\.11\. ]]; then
    echo -e "${GREEN}✅ PASS (Python $PYTHON_VERSION)${RESET}"
    ((PASSED_TESTS++))
else
    echo -e "${RED}❌ FAIL (Expected Python 3.11.x, got $PYTHON_VERSION)${RESET}"
fi
((TOTAL_TESTS++))

echo -n "Testing virtual environment... "
if [[ "$VIRTUAL_ENV" == *".venv"* ]]; then
    echo -e "${GREEN}✅ PASS (Virtual environment active)${RESET}"
    ((PASSED_TESTS++))
else
    echo -e "${YELLOW}⚠️  WARN (No virtual environment detected)${RESET}"
fi
((TOTAL_TESTS++))

echo -n "Testing PYTHONPATH... "
if [[ "$PYTHONPATH" == *"src"* ]]; then
    echo -e "${GREEN}✅ PASS (PYTHONPATH includes src)${RESET}"
    ((PASSED_TESTS++))
else
    echo -e "${RED}❌ FAIL (PYTHONPATH does not include src)${RESET}"
fi
((TOTAL_TESTS++))
echo ""

# Test environment variables
echo -e "${YELLOW}Environment Variables:${RESET}"
test_env_var() {
    local var="$1"
    local description="$2"
    
    echo -n "Testing $description... "
    if [[ -n "${!var:-}" ]]; then
        echo -e "${GREEN}✅ PASS${RESET}"
        ((PASSED_TESTS++))
    else
        echo -e "${RED}❌ FAIL (variable not set)${RESET}"
    fi
    ((TOTAL_TESTS++))
}

test_env_var "PITAS_ENV" "PITAS_ENV variable"
test_env_var "DATABASE_URL" "DATABASE_URL variable"
test_env_var "REDIS_URL" "REDIS_URL variable"
echo ""

# Summary
echo -e "${BLUE}Test Summary:${RESET}"
echo -e "Total tests: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$PASSED_TESTS${RESET}"
echo -e "Failed: ${RED}$((TOTAL_TESTS - PASSED_TESTS))${RESET}"

PASS_RATE=$((PASSED_TESTS * 100 / TOTAL_TESTS))
echo -e "Pass rate: $PASS_RATE%"

if [ $PASS_RATE -ge 90 ]; then
    echo -e "${GREEN}🎉 Excellent! Shell.nix environment is working well.${RESET}"
    exit 0
elif [ $PASS_RATE -ge 75 ]; then
    echo -e "${YELLOW}⚠️  Good, but some tools may be missing or misconfigured.${RESET}"
    exit 0
else
    echo -e "${RED}❌ Poor pass rate. Shell.nix environment needs attention.${RESET}"
    exit 1
fi
