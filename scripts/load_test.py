#!/usr/bin/env python3
"""Load testing script for Phase 11 performance validation."""

import asyncio
import aiohttp
import time
import json
import argparse
from typing import List, Dict, Any
from datetime import datetime
import statistics
import random


class LoadTester:
    """Advanced load testing for PITAS performance validation."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None
        self.results: Dict[str, Any] = {}
        
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(
            limit=100,  # Total connection pool size
            limit_per_host=30,  # Per-host connection limit
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(
            total=30,  # Total timeout
            connect=10,  # Connection timeout
            sock_read=10  # Socket read timeout
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def ramp_up_test(
        self, 
        endpoint: str, 
        max_users: int = 100, 
        ramp_duration: int = 300,
        test_duration: int = 600
    ) -> Dict[str, Any]:
        """Perform ramp-up load test to validate auto-scaling."""
        print(f"Starting ramp-up test: {endpoint}")
        print(f"Max users: {max_users}, Ramp duration: {ramp_duration}s, Test duration: {test_duration}s")
        
        start_time = time.time()
        ramp_end_time = start_time + ramp_duration
        test_end_time = start_time + test_duration
        
        active_tasks = []
        results = {
            'endpoint': endpoint,
            'max_users': max_users,
            'ramp_duration': ramp_duration,
            'test_duration': test_duration,
            'phases': [],
            'total_requests': 0,
            'total_errors': 0,
            'response_times': []
        }
        
        async def user_session(user_id: int, start_delay: float):
            """Simulate a user session."""
            await asyncio.sleep(start_delay)
            
            requests_made = 0
            errors = 0
            response_times = []
            
            while time.time() < test_end_time:
                try:
                    request_start = time.time()
                    async with self.session.get(f"{self.base_url}{endpoint}") as response:
                        await response.text()
                        request_end = time.time()
                        
                        response_time = request_end - request_start
                        response_times.append(response_time)
                        requests_made += 1
                        
                        if response.status != 200:
                            errors += 1
                            
                except Exception as e:
                    errors += 1
                    print(f"User {user_id} error: {e}")
                
                # Random delay between requests (0.5-2 seconds)
                await asyncio.sleep(random.uniform(0.5, 2.0))
            
            return {
                'user_id': user_id,
                'requests': requests_made,
                'errors': errors,
                'response_times': response_times
            }
        
        # Ramp up users gradually
        for i in range(max_users):
            # Calculate when this user should start
            user_start_delay = (i / max_users) * ramp_duration
            
            # Start user session
            task = asyncio.create_task(user_session(i, user_start_delay))
            active_tasks.append(task)
            
            # Log progress
            if (i + 1) % 10 == 0:
                current_time = time.time()
                elapsed = current_time - start_time
                print(f"Ramped up {i + 1}/{max_users} users (elapsed: {elapsed:.1f}s)")
        
        print(f"All {max_users} users scheduled. Waiting for test completion...")
        
        # Wait for all user sessions to complete
        user_results = await asyncio.gather(*active_tasks)
        
        # Aggregate results
        for user_result in user_results:
            results['total_requests'] += user_result['requests']
            results['total_errors'] += user_result['errors']
            results['response_times'].extend(user_result['response_times'])
        
        # Calculate final metrics
        actual_duration = time.time() - start_time
        results['actual_duration'] = actual_duration
        results['throughput_rps'] = results['total_requests'] / actual_duration
        results['error_rate'] = (results['total_errors'] / results['total_requests']) * 100 if results['total_requests'] > 0 else 0
        
        if results['response_times']:
            results['avg_response_time'] = statistics.mean(results['response_times'])
            results['p50_response_time'] = self._percentile(results['response_times'], 50)
            results['p95_response_time'] = self._percentile(results['response_times'], 95)
            results['p99_response_time'] = self._percentile(results['response_times'], 99)
            results['max_response_time'] = max(results['response_times'])
        
        return results
    
    async def stress_test(
        self, 
        endpoint: str, 
        concurrent_users: int = 200, 
        duration: int = 300
    ) -> Dict[str, Any]:
        """Perform stress test to find breaking point."""
        print(f"Starting stress test: {endpoint}")
        print(f"Concurrent users: {concurrent_users}, Duration: {duration}s")
        
        start_time = time.time()
        end_time = start_time + duration
        
        async def stress_user():
            """Aggressive user session for stress testing."""
            requests = 0
            errors = 0
            response_times = []
            
            while time.time() < end_time:
                try:
                    request_start = time.time()
                    async with self.session.get(f"{self.base_url}{endpoint}") as response:
                        await response.text()
                        request_end = time.time()
                        
                        response_time = request_end - request_start
                        response_times.append(response_time)
                        requests += 1
                        
                        if response.status != 200:
                            errors += 1
                            
                except Exception:
                    errors += 1
                
                # Minimal delay for stress testing
                await asyncio.sleep(0.01)
            
            return {
                'requests': requests,
                'errors': errors,
                'response_times': response_times
            }
        
        # Start all stress users simultaneously
        tasks = [stress_user() for _ in range(concurrent_users)]
        user_results = await asyncio.gather(*tasks)
        
        # Aggregate results
        total_requests = sum(result['requests'] for result in user_results)
        total_errors = sum(result['errors'] for result in user_results)
        all_response_times = []
        
        for result in user_results:
            all_response_times.extend(result['response_times'])
        
        actual_duration = time.time() - start_time
        
        results = {
            'endpoint': endpoint,
            'concurrent_users': concurrent_users,
            'duration': actual_duration,
            'total_requests': total_requests,
            'total_errors': total_errors,
            'throughput_rps': total_requests / actual_duration,
            'error_rate': (total_errors / total_requests) * 100 if total_requests > 0 else 0
        }
        
        if all_response_times:
            results.update({
                'avg_response_time': statistics.mean(all_response_times),
                'p95_response_time': self._percentile(all_response_times, 95),
                'p99_response_time': self._percentile(all_response_times, 99),
                'max_response_time': max(all_response_times)
            })
        
        return results
    
    async def endurance_test(
        self, 
        endpoint: str, 
        users: int = 50, 
        duration: int = 3600
    ) -> Dict[str, Any]:
        """Perform endurance test to check for memory leaks and degradation."""
        print(f"Starting endurance test: {endpoint}")
        print(f"Users: {users}, Duration: {duration}s ({duration/3600:.1f} hours)")
        
        start_time = time.time()
        end_time = start_time + duration
        
        # Collect metrics every 5 minutes
        metrics_interval = 300
        metrics_collection = []
        
        async def endurance_user():
            """Long-running user session."""
            requests = 0
            errors = 0
            response_times = []
            
            while time.time() < end_time:
                try:
                    request_start = time.time()
                    async with self.session.get(f"{self.base_url}{endpoint}") as response:
                        await response.text()
                        request_end = time.time()
                        
                        response_time = request_end - request_start
                        response_times.append(response_time)
                        requests += 1
                        
                        if response.status != 200:
                            errors += 1
                            
                except Exception:
                    errors += 1
                
                # Regular user behavior
                await asyncio.sleep(random.uniform(1.0, 5.0))
            
            return {
                'requests': requests,
                'errors': errors,
                'response_times': response_times
            }
        
        async def metrics_collector():
            """Collect performance metrics during the test."""
            while time.time() < end_time:
                try:
                    # Get current performance metrics
                    async with self.session.get(f"{self.base_url}/api/v1/performance/summary") as response:
                        if response.status == 200:
                            metrics = await response.json()
                            metrics['timestamp'] = time.time() - start_time
                            metrics_collection.append(metrics)
                except Exception as e:
                    print(f"Metrics collection error: {e}")
                
                await asyncio.sleep(metrics_interval)
        
        # Start endurance users and metrics collection
        user_tasks = [endurance_user() for _ in range(users)]
        metrics_task = asyncio.create_task(metrics_collector())
        
        # Wait for completion
        user_results = await asyncio.gather(*user_tasks)
        metrics_task.cancel()
        
        # Aggregate results
        total_requests = sum(result['requests'] for result in user_results)
        total_errors = sum(result['errors'] for result in user_results)
        all_response_times = []
        
        for result in user_results:
            all_response_times.extend(result['response_times'])
        
        actual_duration = time.time() - start_time
        
        results = {
            'endpoint': endpoint,
            'users': users,
            'duration': actual_duration,
            'total_requests': total_requests,
            'total_errors': total_errors,
            'throughput_rps': total_requests / actual_duration,
            'error_rate': (total_errors / total_requests) * 100 if total_requests > 0 else 0,
            'metrics_timeline': metrics_collection
        }
        
        if all_response_times:
            results.update({
                'avg_response_time': statistics.mean(all_response_times),
                'p95_response_time': self._percentile(all_response_times, 95),
                'degradation_analysis': self._analyze_degradation(all_response_times, actual_duration)
            })
        
        return results
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset."""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def _analyze_degradation(self, response_times: List[float], duration: float) -> Dict[str, Any]:
        """Analyze performance degradation over time."""
        if len(response_times) < 100:
            return {'error': 'Insufficient data for degradation analysis'}
        
        # Split into time buckets
        bucket_size = len(response_times) // 10
        buckets = [response_times[i:i+bucket_size] for i in range(0, len(response_times), bucket_size)]
        
        bucket_averages = [statistics.mean(bucket) for bucket in buckets if bucket]
        
        if len(bucket_averages) < 2:
            return {'error': 'Insufficient buckets for analysis'}
        
        # Calculate trend
        first_half_avg = statistics.mean(bucket_averages[:len(bucket_averages)//2])
        second_half_avg = statistics.mean(bucket_averages[len(bucket_averages)//2:])
        
        degradation_percent = ((second_half_avg - first_half_avg) / first_half_avg) * 100
        
        return {
            'first_half_avg_response_time': first_half_avg,
            'second_half_avg_response_time': second_half_avg,
            'degradation_percent': degradation_percent,
            'bucket_averages': bucket_averages,
            'trend': 'degrading' if degradation_percent > 10 else 'stable'
        }


async def main():
    parser = argparse.ArgumentParser(description="PITAS Load Testing Suite")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL for testing")
    parser.add_argument("--test-type", choices=["ramp", "stress", "endurance", "all"], default="all", help="Type of load test")
    parser.add_argument("--endpoint", default="/health", help="Endpoint to test")
    parser.add_argument("--max-users", type=int, default=100, help="Maximum concurrent users")
    parser.add_argument("--duration", type=int, default=300, help="Test duration in seconds")
    parser.add_argument("--output", help="Output file for results")
    
    args = parser.parse_args()
    
    async with LoadTester(args.url) as tester:
        results = {
            'timestamp': datetime.utcnow().isoformat(),
            'configuration': vars(args),
            'tests': {}
        }
        
        if args.test_type in ["ramp", "all"]:
            print("\n" + "="*60)
            print("RAMP-UP LOAD TEST")
            print("="*60)
            ramp_result = await tester.ramp_up_test(
                args.endpoint, 
                args.max_users, 
                ramp_duration=args.duration//2,
                test_duration=args.duration
            )
            results['tests']['ramp_up'] = ramp_result
            print(f"Ramp-up test completed: {ramp_result['throughput_rps']:.1f} RPS, {ramp_result['error_rate']:.1f}% errors")
        
        if args.test_type in ["stress", "all"]:
            print("\n" + "="*60)
            print("STRESS TEST")
            print("="*60)
            stress_result = await tester.stress_test(
                args.endpoint, 
                args.max_users * 2,  # Double users for stress test
                args.duration
            )
            results['tests']['stress'] = stress_result
            print(f"Stress test completed: {stress_result['throughput_rps']:.1f} RPS, {stress_result['error_rate']:.1f}% errors")
        
        if args.test_type in ["endurance", "all"]:
            print("\n" + "="*60)
            print("ENDURANCE TEST")
            print("="*60)
            endurance_result = await tester.endurance_test(
                args.endpoint, 
                args.max_users // 2,  # Fewer users for endurance
                args.duration * 2  # Longer duration
            )
            results['tests']['endurance'] = endurance_result
            print(f"Endurance test completed: {endurance_result['throughput_rps']:.1f} RPS, {endurance_result['error_rate']:.1f}% errors")
        
        # Save results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\nResults saved to: {args.output}")
        
        # Print summary
        print("\n" + "="*60)
        print("LOAD TEST SUMMARY")
        print("="*60)
        for test_name, test_result in results['tests'].items():
            print(f"{test_name.upper()}:")
            print(f"  Throughput: {test_result.get('throughput_rps', 0):.1f} RPS")
            print(f"  Error Rate: {test_result.get('error_rate', 0):.1f}%")
            print(f"  P95 Response Time: {test_result.get('p95_response_time', 0):.3f}s")


if __name__ == "__main__":
    asyncio.run(main())
