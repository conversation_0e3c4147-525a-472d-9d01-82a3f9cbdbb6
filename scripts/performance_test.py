#!/usr/bin/env python3
"""Performance testing script for Phase 11 implementation."""

import asyncio
import aiohttp
import time
import statistics
from typing import List, Dict, Any
from datetime import datetime
import json
import argparse


class PerformanceTester:
    """Performance testing suite for PITAS Phase 11."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None
        self.results: Dict[str, List[float]] = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_api_response_time(self, endpoint: str, num_requests: int = 100) -> Dict[str, Any]:
        """Test API response time for a specific endpoint."""
        print(f"Testing API response time for {endpoint} ({num_requests} requests)...")
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        for i in range(num_requests):
            start_time = time.time()
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    await response.text()
                    end_time = time.time()
                    response_time = end_time - start_time
                    response_times.append(response_time)
                    
                    if response.status == 200:
                        successful_requests += 1
                    else:
                        failed_requests += 1
                        
            except Exception as e:
                failed_requests += 1
                print(f"Request {i+1} failed: {e}")
            
            if (i + 1) % 10 == 0:
                print(f"Completed {i+1}/{num_requests} requests")
        
        if response_times:
            results = {
                'endpoint': endpoint,
                'total_requests': num_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'avg_response_time': statistics.mean(response_times),
                'median_response_time': statistics.median(response_times),
                'p95_response_time': self._percentile(response_times, 95),
                'p99_response_time': self._percentile(response_times, 99),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'success_rate': (successful_requests / num_requests) * 100
            }
        else:
            results = {
                'endpoint': endpoint,
                'error': 'No successful requests'
            }
        
        self.results[endpoint] = results
        return results
    
    async def test_concurrent_load(self, endpoint: str, concurrent_users: int = 10, duration_seconds: int = 60) -> Dict[str, Any]:
        """Test concurrent load on an endpoint."""
        print(f"Testing concurrent load for {endpoint} ({concurrent_users} users, {duration_seconds}s)...")
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        async def user_session():
            request_count = 0
            response_times = []
            errors = 0
            
            while time.time() < end_time:
                try:
                    request_start = time.time()
                    async with self.session.get(f"{self.base_url}{endpoint}") as response:
                        await response.text()
                        request_end = time.time()
                        response_times.append(request_end - request_start)
                        request_count += 1
                        
                        if response.status != 200:
                            errors += 1
                            
                except Exception:
                    errors += 1
                
                await asyncio.sleep(0.1)  # Small delay between requests
            
            return {
                'requests': request_count,
                'response_times': response_times,
                'errors': errors
            }
        
        # Run concurrent user sessions
        tasks = [user_session() for _ in range(concurrent_users)]
        user_results = await asyncio.gather(*tasks)
        
        # Aggregate results
        total_requests = sum(result['requests'] for result in user_results)
        all_response_times = []
        total_errors = sum(result['errors'] for result in user_results)
        
        for result in user_results:
            all_response_times.extend(result['response_times'])
        
        actual_duration = time.time() - start_time
        throughput = total_requests / actual_duration if actual_duration > 0 else 0
        
        results = {
            'endpoint': endpoint,
            'concurrent_users': concurrent_users,
            'duration_seconds': actual_duration,
            'total_requests': total_requests,
            'total_errors': total_errors,
            'throughput_rps': throughput,
            'avg_response_time': statistics.mean(all_response_times) if all_response_times else 0,
            'p95_response_time': self._percentile(all_response_times, 95) if all_response_times else 0,
            'error_rate': (total_errors / total_requests) * 100 if total_requests > 0 else 0
        }
        
        return results
    
    async def test_cache_performance(self) -> Dict[str, Any]:
        """Test cache performance and hit rates."""
        print("Testing cache performance...")
        
        cache_endpoint = "/api/v1/performance/cache/stats"
        
        # Get initial cache stats
        async with self.session.get(f"{self.base_url}{cache_endpoint}") as response:
            initial_stats = await response.json()
        
        # Make requests to cacheable endpoints
        cacheable_endpoints = [
            "/api/v1/vulnerabilities",
            "/api/v1/assets",
            "/api/v1/projects"
        ]
        
        for endpoint in cacheable_endpoints:
            # First request (cache miss)
            await self.session.get(f"{self.base_url}{endpoint}")
            # Second request (should be cache hit)
            await self.session.get(f"{self.base_url}{endpoint}")
        
        # Get final cache stats
        async with self.session.get(f"{self.base_url}{cache_endpoint}") as response:
            final_stats = await response.json()
        
        return {
            'initial_stats': initial_stats,
            'final_stats': final_stats,
            'cache_improvement': self._calculate_cache_improvement(initial_stats, final_stats)
        }
    
    async def test_database_performance(self) -> Dict[str, Any]:
        """Test database query performance."""
        print("Testing database performance...")
        
        db_endpoint = "/api/v1/performance/database/slow-queries"
        
        try:
            async with self.session.get(f"{self.base_url}{db_endpoint}") as response:
                db_stats = await response.json()
                return db_stats
        except Exception as e:
            return {'error': f"Failed to get database stats: {e}"}
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive performance test suite."""
        print("Starting comprehensive performance test suite...")
        
        test_results = {
            'timestamp': datetime.utcnow().isoformat(),
            'test_configuration': {
                'base_url': self.base_url,
                'test_duration': '5 minutes'
            },
            'results': {}
        }
        
        # Test individual endpoint response times
        endpoints_to_test = [
            "/health",
            "/api/v1/performance/health",
            "/api/v1/performance/summary",
            "/api/v1/vulnerabilities",
            "/api/v1/assets"
        ]
        
        for endpoint in endpoints_to_test:
            try:
                result = await self.test_api_response_time(endpoint, num_requests=50)
                test_results['results'][f'response_time_{endpoint.replace("/", "_")}'] = result
            except Exception as e:
                test_results['results'][f'response_time_{endpoint.replace("/", "_")}'] = {'error': str(e)}
        
        # Test concurrent load
        try:
            load_result = await self.test_concurrent_load("/health", concurrent_users=20, duration_seconds=30)
            test_results['results']['concurrent_load'] = load_result
        except Exception as e:
            test_results['results']['concurrent_load'] = {'error': str(e)}
        
        # Test cache performance
        try:
            cache_result = await self.test_cache_performance()
            test_results['results']['cache_performance'] = cache_result
        except Exception as e:
            test_results['results']['cache_performance'] = {'error': str(e)}
        
        # Test database performance
        try:
            db_result = await self.test_database_performance()
            test_results['results']['database_performance'] = db_result
        except Exception as e:
            test_results['results']['database_performance'] = {'error': str(e)}
        
        return test_results
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset."""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def _calculate_cache_improvement(self, initial: Dict, final: Dict) -> Dict[str, Any]:
        """Calculate cache performance improvement."""
        try:
            initial_hit_rate = initial.get('cache_stats', {}).get('hit_rate', 0)
            final_hit_rate = final.get('cache_stats', {}).get('hit_rate', 0)
            
            return {
                'hit_rate_improvement': final_hit_rate - initial_hit_rate,
                'initial_hit_rate': initial_hit_rate,
                'final_hit_rate': final_hit_rate
            }
        except Exception:
            return {'error': 'Could not calculate cache improvement'}
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate a formatted performance test report."""
        report = []
        report.append("=" * 80)
        report.append("PITAS PHASE 11 PERFORMANCE TEST REPORT")
        report.append("=" * 80)
        report.append(f"Test Date: {results['timestamp']}")
        report.append(f"Base URL: {results['test_configuration']['base_url']}")
        report.append("")
        
        for test_name, test_result in results['results'].items():
            report.append(f"Test: {test_name.replace('_', ' ').title()}")
            report.append("-" * 40)
            
            if 'error' in test_result:
                report.append(f"ERROR: {test_result['error']}")
            else:
                if 'avg_response_time' in test_result:
                    report.append(f"Average Response Time: {test_result['avg_response_time']:.3f}s")
                    report.append(f"95th Percentile: {test_result['p95_response_time']:.3f}s")
                    report.append(f"Success Rate: {test_result['success_rate']:.1f}%")
                
                if 'throughput_rps' in test_result:
                    report.append(f"Throughput: {test_result['throughput_rps']:.1f} requests/second")
                    report.append(f"Error Rate: {test_result['error_rate']:.1f}%")
            
            report.append("")
        
        return "\n".join(report)


async def main():
    parser = argparse.ArgumentParser(description="PITAS Performance Testing Suite")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL for testing")
    parser.add_argument("--output", help="Output file for results")
    args = parser.parse_args()
    
    async with PerformanceTester(args.url) as tester:
        results = await tester.run_comprehensive_test()
        
        # Generate report
        report = tester.generate_report(results)
        print(report)
        
        # Save results
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\nDetailed results saved to: {args.output}")
        
        # Save report
        report_file = args.output.replace('.json', '_report.txt') if args.output else 'performance_report.txt'
        with open(report_file, 'w') as f:
            f.write(report)
        print(f"Report saved to: {report_file}")


if __name__ == "__main__":
    asyncio.run(main())
