#!/bin/bash
# Build script for PITAS documentation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="docs"
BUILD_DIR="docs/_build"
VENV_DIR=".venv-docs"
PYTHON_VERSION="3.11"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ] || [ ! -d "$DOCS_DIR" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

# Parse command line arguments
BUILD_TYPE="html"
CLEAN=false
SERVE=false
CHECK_LINKS=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -s|--serve)
            SERVE=true
            shift
            ;;
        -l|--check-links)
            CHECK_LINKS=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -t, --type TYPE      Build type (html, pdf, epub) [default: html]"
            echo "  -c, --clean          Clean build directory before building"
            echo "  -s, --serve          Serve documentation after building"
            echo "  -l, --check-links    Check for broken links"
            echo "  -v, --verbose        Verbose output"
            echo "  -h, --help           Show this help message"
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Setup virtual environment for documentation
setup_venv() {
    log_info "Setting up documentation virtual environment..."
    
    if [ ! -d "$VENV_DIR" ]; then
        python$PYTHON_VERSION -m venv "$VENV_DIR"
        log_success "Created virtual environment: $VENV_DIR"
    fi
    
    source "$VENV_DIR/bin/activate"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install documentation requirements
    if [ -f "$DOCS_DIR/requirements-docs.txt" ]; then
        pip install -r "$DOCS_DIR/requirements-docs.txt"
        log_success "Installed documentation dependencies"
    else
        log_warning "No requirements-docs.txt found, installing basic Sphinx"
        pip install sphinx sphinx-rtd-theme
    fi
    
    # Install project in development mode
    pip install -e .
    log_success "Installed project in development mode"
}

# Clean build directory
clean_build() {
    if [ "$CLEAN" = true ] || [ "$1" = "force" ]; then
        log_info "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
        mkdir -p "$BUILD_DIR"
        log_success "Build directory cleaned"
    fi
}

# Generate API documentation
generate_api_docs() {
    log_info "Generating API documentation..."
    
    cd "$DOCS_DIR"
    
    # Generate API documentation stubs
    if command -v sphinx-apidoc &> /dev/null; then
        sphinx-apidoc -o api ../src/pitas --force --separate --module-first
        log_success "API documentation stubs generated"
    else
        log_warning "sphinx-apidoc not found, skipping API doc generation"
    fi
    
    cd ..
}

# Build documentation
build_docs() {
    log_info "Building documentation (type: $BUILD_TYPE)..."
    
    cd "$DOCS_DIR"
    
    # Set Sphinx options
    SPHINX_OPTS=""
    if [ "$VERBOSE" = true ]; then
        SPHINX_OPTS="-v"
    fi
    
    # Build based on type
    case $BUILD_TYPE in
        html)
            make html SPHINXOPTS="$SPHINX_OPTS"
            log_success "HTML documentation built successfully"
            ;;
        pdf)
            make pdf SPHINXOPTS="$SPHINX_OPTS"
            log_success "PDF documentation built successfully"
            ;;
        epub)
            make epub SPHINXOPTS="$SPHINX_OPTS"
            log_success "EPUB documentation built successfully"
            ;;
        *)
            log_error "Unknown build type: $BUILD_TYPE"
            exit 1
            ;;
    esac
    
    cd ..
}

# Check for broken links
check_links() {
    if [ "$CHECK_LINKS" = true ]; then
        log_info "Checking for broken links..."
        
        cd "$DOCS_DIR"
        make linkcheck
        
        if [ $? -eq 0 ]; then
            log_success "No broken links found"
        else
            log_warning "Some links may be broken, check the output above"
        fi
        
        cd ..
    fi
}

# Serve documentation
serve_docs() {
    if [ "$SERVE" = true ] && [ "$BUILD_TYPE" = "html" ]; then
        log_info "Starting documentation server..."
        
        # Check if Python http.server is available
        if command -v python3 &> /dev/null; then
            cd "$BUILD_DIR/html"
            log_success "Documentation server started at http://localhost:8000"
            log_info "Press Ctrl+C to stop the server"
            python3 -m http.server 8000
        else
            log_error "Python 3 not found, cannot serve documentation"
            exit 1
        fi
    fi
}

# Generate coverage report
generate_coverage() {
    log_info "Generating documentation coverage report..."
    
    cd "$DOCS_DIR"
    make coverage
    
    if [ -f "_build/coverage/python.txt" ]; then
        log_info "Coverage report:"
        cat "_build/coverage/python.txt"
    fi
    
    cd ..
}

# Main execution
main() {
    log_info "Starting PITAS documentation build process..."
    
    # Setup environment
    setup_venv
    
    # Clean if requested
    clean_build
    
    # Generate API docs
    generate_api_docs
    
    # Build documentation
    build_docs
    
    # Check links if requested
    check_links
    
    # Generate coverage report
    generate_coverage
    
    # Serve if requested
    serve_docs
    
    log_success "Documentation build process completed!"
    
    # Show build location
    case $BUILD_TYPE in
        html)
            log_info "HTML documentation available at: $BUILD_DIR/html/index.html"
            ;;
        pdf)
            log_info "PDF documentation available at: $BUILD_DIR/latex/"
            ;;
        epub)
            log_info "EPUB documentation available at: $BUILD_DIR/epub/"
            ;;
    esac
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    deactivate 2>/dev/null || true
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function
main "$@"
