#!/usr/bin/env python3
"""Migration status checker for PITAS project."""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Set test environment
os.environ["PITAS_ENV"] = "test"

# Load test environment variables
test_env_file = Path(__file__).parent / ".env.test"
if test_env_file.exists():
    with open(test_env_file) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                os.environ[key] = value

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def get_migration_files() -> List[Dict[str, Any]]:
    """Get list of migration files with metadata."""
    migrations_dir = Path("migrations/versions")
    migration_files = []
    
    if not migrations_dir.exists():
        print("❌ Migrations directory not found")
        return []
    
    for file_path in migrations_dir.glob("*.py"):
        if file_path.name == "__init__.py":
            continue
            
        # Parse migration file for metadata
        try:
            with open(file_path) as f:
                content = f.read()
                
            # Extract revision and down_revision
            revision = None
            down_revision = None
            description = ""
            
            for line in content.split('\n'):
                line = line.strip()
                if line.startswith('revision = '):
                    revision = line.split('=')[1].strip().strip('"\'')
                elif line.startswith('down_revision = '):
                    down_revision = line.split('=')[1].strip().strip('"\'')
                elif line.startswith('"""') and not description:
                    # Extract description from docstring
                    description = line.strip('"""').strip()
            
            migration_files.append({
                'file': file_path.name,
                'revision': revision,
                'down_revision': down_revision,
                'description': description,
                'path': file_path
            })
            
        except Exception as e:
            print(f"⚠️  Error parsing {file_path.name}: {e}")
    
    return sorted(migration_files, key=lambda x: x['file'])


def analyze_migration_chain(migrations: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze migration chain for consistency."""
    analysis = {
        'total_migrations': len(migrations),
        'phases_covered': set(),
        'chain_valid': True,
        'issues': []
    }
    
    # Check for phase coverage
    for migration in migrations:
        file_name = migration['file'].lower()
        if 'phase' in file_name:
            # Extract phase number
            for i in range(1, 13):
                if f'phase_{i}' in file_name or f'phase{i}' in file_name:
                    analysis['phases_covered'].add(i)
                    break
    
    # Check chain consistency
    revisions = {m['revision']: m for m in migrations if m['revision']}
    down_revisions = {m['down_revision'] for m in migrations if m['down_revision'] and m['down_revision'] != 'None'}
    
    # Find orphaned migrations
    for migration in migrations:
        if migration['down_revision'] and migration['down_revision'] != 'None':
            if migration['down_revision'] not in revisions:
                analysis['issues'].append(f"Migration {migration['file']} references unknown down_revision: {migration['down_revision']}")
                analysis['chain_valid'] = False
    
    return analysis


def check_database_models():
    """Check if database models can be imported."""
    print("\n🔍 Checking database models...")
    
    try:
        from pitas.db.models import Base
        print("✅ Base model imported successfully")
        
        # Get all model classes
        model_classes = []
        for attr_name in dir(Base.registry._class_registry):
            if not attr_name.startswith('_'):
                model_classes.append(attr_name)
        
        print(f"✅ Found {len(model_classes)} model classes registered")
        
        # Check for table conflicts
        table_names = set()
        conflicts = []
        
        for cls in Base.registry._class_registry.data.values():
            if hasattr(cls, '__tablename__'):
                table_name = cls.__tablename__
                if table_name in table_names:
                    conflicts.append(table_name)
                table_names.add(table_name)
        
        if conflicts:
            print(f"⚠️  Table name conflicts detected: {conflicts}")
        else:
            print("✅ No table name conflicts detected")
            
        return True
        
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        return False


def main():
    """Main migration checker."""
    print("🔍 PITAS Migration Status Checker")
    print("=" * 50)
    
    # Check migration files
    print("\n📁 Checking migration files...")
    migrations = get_migration_files()
    
    if not migrations:
        print("❌ No migration files found")
        return 1
    
    print(f"✅ Found {len(migrations)} migration files:")
    for migration in migrations:
        phase_info = ""
        if 'phase' in migration['file'].lower():
            phase_info = " 🎯"
        print(f"  • {migration['file']}{phase_info}")
        if migration['description']:
            print(f"    Description: {migration['description']}")
    
    # Analyze migration chain
    print("\n🔗 Analyzing migration chain...")
    analysis = analyze_migration_chain(migrations)
    
    print(f"✅ Total migrations: {analysis['total_migrations']}")
    print(f"✅ Phases covered: {sorted(analysis['phases_covered'])}")
    
    if analysis['chain_valid']:
        print("✅ Migration chain appears valid")
    else:
        print("⚠️  Migration chain issues detected:")
        for issue in analysis['issues']:
            print(f"  • {issue}")
    
    # Check database models
    models_ok = check_database_models()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 MIGRATION STATUS SUMMARY")
    print("=" * 50)
    
    print(f"Migration Files: ✅ {len(migrations)} found")
    print(f"Phase Coverage: ✅ Phases {sorted(analysis['phases_covered'])}")
    print(f"Chain Validity: {'✅ Valid' if analysis['chain_valid'] else '⚠️  Issues detected'}")
    print(f"Model Import: {'✅ Success' if models_ok else '❌ Failed'}")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    print(f"1. ✅ Migration files are present for integrated phases")
    print(f"2. ⚠️  Database setup required for actual migration execution")
    print(f"3. ✅ Models are importable and ready for schema generation")
    print(f"4. 🔄 Run 'alembic upgrade head' when database is available")
    
    if not analysis['chain_valid'] or not models_ok:
        print(f"\n⚠️  Issues detected - review before applying migrations")
        return 1
    else:
        print(f"\n🎉 Migration system is ready for database setup!")
        return 0


if __name__ == "__main__":
    sys.exit(main())
