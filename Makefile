.PHONY: help setup clean test lint format security docs docker deploy
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
RESET := \033[0m

help: ## Show this help message
	@echo "$(BLUE)PITAS - Pentesting Team Management System$(RESET)"
	@echo "$(BLUE)===========================================$(RESET)"
	@echo ""
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, $$2}'
	@echo ""

setup: ## Set up development environment using shell.nix
	@echo "$(BLUE)Setting up development environment with shell.nix...$(RESET)"
	@if ! command -v nix >/dev/null 2>&1; then \
		echo "$(RED)❌ Nix is not installed. Please install Nix first:$(RESET)"; \
		echo "$(YELLOW)  curl -L https://nixos.org/nix/install | sh$(RESET)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)Entering Nix shell environment...$(RESET)"
	@nix-shell --run "echo 'Nix shell environment ready'"
	@echo "$(YELLOW)Setting up pre-commit hooks...$(RESET)"
	@nix-shell --run "pre-commit install"
	@echo "$(YELLOW)Creating .env file from template...$(RESET)"
	@if [ ! -f ".env" ]; then cp .env.example .env; fi
	@echo "$(GREEN)✅ Development environment ready with shell.nix!$(RESET)"
	@echo "$(YELLOW)To enter the development environment, run: nix-shell$(RESET)"

setup-fallback: ## Set up development environment with traditional venv (fallback)
	@echo "$(BLUE)Setting up development environment with venv (fallback)...$(RESET)"
	@if [ ! -d ".venv" ]; then \
		echo "$(YELLOW)Creating virtual environment...$(RESET)"; \
		python -m venv .venv; \
	fi
	@echo "$(YELLOW)Installing dependencies...$(RESET)"
	@.venv/bin/pip install --upgrade pip
	@.venv/bin/pip install -e ".[dev,docs,security]"
	@echo "$(YELLOW)Setting up pre-commit hooks...$(RESET)"
	@.venv/bin/pre-commit install
	@echo "$(YELLOW)Creating .env file from template...$(RESET)"
	@if [ ! -f ".env" ]; then cp .env.example .env; fi
	@echo "$(GREEN)✅ Development environment ready!$(RESET)"

clean: ## Clean up generated files and caches
	@echo "$(BLUE)Cleaning up...$(RESET)"
	@rm -rf .venv/
	@rm -rf __pycache__/
	@rm -rf .pytest_cache/
	@rm -rf .mypy_cache/
	@rm -rf .ruff_cache/
	@rm -rf htmlcov/
	@rm -rf dist/
	@rm -rf build/
	@rm -rf *.egg-info/
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -delete
	@echo "$(GREEN)✅ Cleanup complete!$(RESET)"

test: ## Run tests using shell.nix
	@echo "$(BLUE)Running tests with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/pytest -v --cov=src --cov-report=html --cov-report=term-missing"

test-fast: ## Run tests without coverage using shell.nix
	@echo "$(BLUE)Running fast tests with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/pytest -v -x"

test-integration: ## Run integration tests using shell.nix
	@echo "$(BLUE)Running integration tests with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/pytest -v -m integration"

test-security: ## Run security tests using shell.nix
	@echo "$(BLUE)Running security tests with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/pytest -v -m security"

test-fallback: ## Run tests with venv (fallback)
	@echo "$(BLUE)Running tests with venv...$(RESET)"
	@.venv/bin/pytest -v --cov=src --cov-report=html --cov-report=term-missing

lint: ## Run linting using shell.nix
	@echo "$(BLUE)Running linting with shell.nix...$(RESET)"
	@nix-shell --run "ruff check src/ tests/ || true"
	@nix-shell --run "mypy src/ || true"

format: ## Format code using shell.nix
	@echo "$(BLUE)Formatting code with shell.nix...$(RESET)"
	@nix-shell --run "ruff format src/ tests/"
	@nix-shell --run "ruff check --fix src/ tests/"

lint-fallback: ## Run linting with venv (fallback)
	@echo "$(BLUE)Running linting with venv...$(RESET)"
	@.venv/bin/ruff check src/ tests/
	@.venv/bin/mypy src/

security: ## Run security checks using shell.nix
	@echo "$(BLUE)Running security checks with shell.nix...$(RESET)"
	@nix-shell --run "bandit -r src/ -f json -o bandit-report.json || true"
	@nix-shell --run "bandit -r src/ || true"
	@echo "$(YELLOW)Running semgrep...$(RESET)"
	@nix-shell --run "semgrep --config=auto src/ || true"

docs: ## Build documentation using shell.nix
	@echo "$(BLUE)Building documentation with shell.nix...$(RESET)"
	@nix-shell --run "cd docs && sphinx-build -b html source _build/html"
	@echo "$(GREEN)✅ Documentation built in docs/_build/html/$(RESET)"

docs-serve: ## Serve documentation locally using shell.nix
	@echo "$(BLUE)Serving documentation with shell.nix...$(RESET)"
	@nix-shell --run "cd docs/_build/html && python -m http.server 8080"

security-fallback: ## Run security checks with venv (fallback)
	@echo "$(BLUE)Running security checks with venv...$(RESET)"
	@.venv/bin/bandit -r src/ -f json -o bandit-report.json || true
	@.venv/bin/bandit -r src/
	@echo "$(YELLOW)Running semgrep...$(RESET)"
	@semgrep --config=auto src/ || true

docker-build: ## Build Docker images
	@echo "$(BLUE)Building Docker images...$(RESET)"
	@docker build -t pitas:latest .
	@docker build -t pitas:dev -f Dockerfile.dev .

docker-up: ## Start development services with Docker Compose
	@echo "$(BLUE)Starting development services...$(RESET)"
	@docker-compose up -d

docker-down: ## Stop development services
	@echo "$(BLUE)Stopping development services...$(RESET)"
	@docker-compose down

docker-logs: ## Show Docker Compose logs
	@docker-compose logs -f

db-init: ## Initialize database using shell.nix
	@echo "$(BLUE)Initializing database with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/alembic upgrade head"

db-migrate: ## Create new database migration using shell.nix
	@echo "$(BLUE)Creating database migration with shell.nix...$(RESET)"
	@read -p "Migration message: " msg; \
	nix-shell --run ".venv/bin/alembic revision --autogenerate -m \"$$msg\""

db-upgrade: ## Upgrade database to latest migration using shell.nix
	@echo "$(BLUE)Upgrading database with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/alembic upgrade head"

db-downgrade: ## Downgrade database by one migration using shell.nix
	@echo "$(BLUE)Downgrading database with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/alembic downgrade -1"

db-reset: ## Reset database (WARNING: destroys all data) using shell.nix
	@echo "$(RED)⚠️  This will destroy all database data!$(RESET)"
	@read -p "Are you sure? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		nix-shell --run ".venv/bin/alembic downgrade base"; \
		nix-shell --run ".venv/bin/alembic upgrade head"; \
		echo "$(GREEN)✅ Database reset complete!$(RESET)"; \
	else \
		echo "$(YELLOW)Database reset cancelled.$(RESET)"; \
	fi

run: ## Run the application using shell.nix
	@echo "$(BLUE)Starting PITAS application with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/uvicorn pitas.main:app --reload --host 0.0.0.0 --port 8000"

run-prod: ## Run the application in production mode using shell.nix
	@echo "$(BLUE)Starting PITAS application (production mode) with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/uvicorn pitas.main:app --host 0.0.0.0 --port 8000 --workers 4"

run-fallback: ## Run the application with venv (fallback)
	@echo "$(BLUE)Starting PITAS application with venv...$(RESET)"
	@.venv/bin/uvicorn pitas.main:app --reload --host 0.0.0.0 --port 8000

install: ## Install the package using shell.nix
	@echo "$(BLUE)Installing PITAS package with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/pip install -e ."

install-dev: ## Install development dependencies using shell.nix
	@echo "$(BLUE)Installing development dependencies with shell.nix...$(RESET)"
	@nix-shell --run ".venv/bin/pip install -e \".[dev,docs,security]\""

install-fallback: ## Install with venv (fallback)
	@echo "$(BLUE)Installing PITAS package with venv...$(RESET)"
	@.venv/bin/pip install -e .

check: ## Run all checks (lint, test, security)
	@echo "$(BLUE)Running all checks...$(RESET)"
	@$(MAKE) lint
	@$(MAKE) test
	@$(MAKE) security
	@echo "$(GREEN)✅ All checks passed!$(RESET)"

pre-commit: ## Run pre-commit hooks on all files using shell.nix
	@echo "$(BLUE)Running pre-commit hooks with shell.nix...$(RESET)"
	@nix-shell --run "pre-commit run --all-files"

pre-commit-fallback: ## Run pre-commit hooks with venv (fallback)
	@echo "$(BLUE)Running pre-commit hooks with venv...$(RESET)"
	@.venv/bin/pre-commit run --all-files

release: ## Create a new release
	@echo "$(BLUE)Creating release...$(RESET)"
	@echo "$(YELLOW)This would typically bump version, create git tag, and build artifacts$(RESET)"
	@echo "$(YELLOW)Implementation depends on your release process$(RESET)"

deploy-dev: ## Deploy to development environment
	@echo "$(BLUE)Deploying to development...$(RESET)"
	@echo "$(YELLOW)Implementation depends on your deployment strategy$(RESET)"

deploy-staging: ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(RESET)"
	@echo "$(YELLOW)Implementation depends on your deployment strategy$(RESET)"

deploy-prod: ## Deploy to production environment
	@echo "$(BLUE)Deploying to production...$(RESET)"
	@echo "$(RED)⚠️  Production deployment requires additional safety checks$(RESET)"

monitor: ## Show application monitoring dashboard
	@echo "$(BLUE)Opening monitoring dashboard...$(RESET)"
	@echo "$(YELLOW)Prometheus: http://localhost:9090$(RESET)"
	@echo "$(YELLOW)Grafana: http://localhost:3000$(RESET)"

logs: ## Show application logs
	@echo "$(BLUE)Showing application logs...$(RESET)"
	@tail -f logs/pitas.log 2>/dev/null || echo "$(YELLOW)No log file found. Run the application first.$(RESET)"

# Performance Testing Targets
perf-test: ## Run comprehensive performance tests
	@echo "$(BLUE)Running performance tests...$(RESET)"
	@python scripts/performance_test.py --output performance_results.json

load-test: ## Run load testing suite
	@echo "$(BLUE)Running load tests...$(RESET)"
	@python scripts/load_test.py --test-type all --max-users 50 --duration 180 --output load_test_results.json

stress-test: ## Run stress test only
	@echo "$(BLUE)Running stress test...$(RESET)"
	@python scripts/load_test.py --test-type stress --max-users 200 --duration 300 --output stress_test_results.json

perf-monitor: ## Start performance monitoring stack
	@echo "$(BLUE)Starting performance monitoring stack...$(RESET)"
	@docker-compose -f docker-compose.performance.yml up -d
	@echo "$(GREEN)Performance monitoring started:$(RESET)"
	@echo "$(YELLOW)Grafana: http://localhost:3001 (admin/pitas_grafana)$(RESET)"
	@echo "$(YELLOW)Prometheus: http://localhost:9091$(RESET)"
	@echo "$(YELLOW)Jaeger: http://localhost:16686$(RESET)"

perf-stop: ## Stop performance monitoring stack
	@echo "$(BLUE)Stopping performance monitoring stack...$(RESET)"
	@docker-compose -f docker-compose.performance.yml down

backup: ## Create database backup
	@echo "$(BLUE)Creating database backup...$(RESET)"
	@mkdir -p backups
	@pg_dump $(DATABASE_URL) > backups/pitas_backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)✅ Database backup created!$(RESET)"

restore: ## Restore database from backup
	@echo "$(BLUE)Available backups:$(RESET)"
	@ls -la backups/*.sql 2>/dev/null || echo "$(YELLOW)No backups found$(RESET)"
	@read -p "Enter backup filename: " backup; \
	if [ -f "backups/$$backup" ]; then \
		psql $(DATABASE_URL) < "backups/$$backup"; \
		echo "$(GREEN)✅ Database restored!$(RESET)"; \
	else \
		echo "$(RED)❌ Backup file not found$(RESET)"; \
	fi
