# Docker Compose for Phase 11: Performance Optimization and Scalability
version: '3.8'

services:
  # PITAS Application with Performance Monitoring
  pitas-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pitas-app-performance
    ports:
      - "8000:8000"
      - "9090:9090"  # Prometheus metrics
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/pitas_db
      - REDIS_URL=redis://:pitas_redis@redis-cluster:6379/0
      - REDIS_CLUSTER_NODES=redis-node-1:6379,redis-node-2:6379,redis-node-3:6379
      - NEO4J_URL=bolt://neo4j:7687
      - NEO4J_PASSWORD=pitas_neo4j
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=pitas_influxdb_token
      - PROMETHEUS_METRICS_PORT=9090
      - <PERSON><PERSON><PERSON><PERSON>_AGENT_HOST=jaeger
      - JAEGER_AGENT_PORT=6831
    volumes:
      - ./src:/app/src
      - ./logs:/app/logs
    networks:
      - pitas_performance_net
    depends_on:
      - postgres
      - redis-cluster
      - neo4j
      - influxdb
      - jaeger
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis Cluster for High-Performance Caching
  redis-node-1:
    image: redis:7-alpine
    container_name: redis-node-1
    ports:
      - "7001:6379"
      - "17001:16379"
    command: >
      redis-server
      --port 6379
      --cluster-enabled yes
      --cluster-config-file nodes.conf
      --cluster-node-timeout 5000
      --appendonly yes
      --requirepass pitas_redis
      --masterauth pitas_redis
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_node_1_data:/data
    networks:
      - pitas_performance_net
    restart: unless-stopped

  redis-node-2:
    image: redis:7-alpine
    container_name: redis-node-2
    ports:
      - "7002:6379"
      - "17002:16379"
    command: >
      redis-server
      --port 6379
      --cluster-enabled yes
      --cluster-config-file nodes.conf
      --cluster-node-timeout 5000
      --appendonly yes
      --requirepass pitas_redis
      --masterauth pitas_redis
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_node_2_data:/data
    networks:
      - pitas_performance_net
    restart: unless-stopped

  redis-node-3:
    image: redis:7-alpine
    container_name: redis-node-3
    ports:
      - "7003:6379"
      - "17003:16379"
    command: >
      redis-server
      --port 6379
      --cluster-enabled yes
      --cluster-config-file nodes.conf
      --cluster-node-timeout 5000
      --appendonly yes
      --requirepass pitas_redis
      --masterauth pitas_redis
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_node_3_data:/data
    networks:
      - pitas_performance_net
    restart: unless-stopped

  # Redis Cluster Setup
  redis-cluster:
    image: redis:7-alpine
    container_name: redis-cluster-setup
    command: >
      sh -c "
        sleep 10 &&
        redis-cli --cluster create
        redis-node-1:6379
        redis-node-2:6379
        redis-node-3:6379
        --cluster-replicas 0
        --cluster-yes
        -a pitas_redis
      "
    networks:
      - pitas_performance_net
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-performance
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - pitas_performance_net
    restart: unless-stopped

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-performance
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=pitas_grafana
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - pitas_performance_net
    depends_on:
      - prometheus
    restart: unless-stopped

  # Jaeger for Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger-performance
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "6831:6831/udp"  # UDP agent
    environment:
      - COLLECTOR_ZIPKIN_HTTP_PORT=9411
    networks:
      - pitas_performance_net
    restart: unless-stopped

  # Load Balancer (HAProxy)
  load-balancer:
    image: haproxy:latest
    container_name: haproxy-performance
    ports:
      - "8080:80"
      - "8443:443"
      - "8404:8404"  # Stats page
    volumes:
      - ./monitoring/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg
    networks:
      - pitas_performance_net
    depends_on:
      - pitas-app
    restart: unless-stopped

  # PostgreSQL with Performance Tuning
  postgres:
    image: postgres:15-alpine
    container_name: postgres-performance
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: pitas_db
      POSTGRES_USER: pitas_user
      POSTGRES_PASSWORD: pitas_password
    volumes:
      - postgres_performance_data:/var/lib/postgresql/data
      - ./monitoring/postgresql.conf:/etc/postgresql/postgresql.conf
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
    networks:
      - pitas_performance_net
    restart: unless-stopped

  # Neo4j with Performance Optimization
  neo4j:
    image: neo4j:5-community
    container_name: neo4j-performance
    ports:
      - "7475:7474"
      - "7688:7687"
    environment:
      NEO4J_AUTH: neo4j/pitas_neo4j
      NEO4J_dbms_memory_heap_initial__size: 512m
      NEO4J_dbms_memory_heap_max__size: 2G
      NEO4J_dbms_memory_pagecache_size: 1G
      NEO4J_dbms_query__cache__size: 256
      NEO4J_dbms_jvm_additional: "-XX:+UseG1GC -XX:+UnlockExperimentalVMOptions"
    volumes:
      - neo4j_performance_data:/data
      - neo4j_performance_logs:/logs
    networks:
      - pitas_performance_net
    restart: unless-stopped

  # InfluxDB for Time-Series Metrics
  influxdb:
    image: influxdb:2.7-alpine
    container_name: influxdb-performance
    ports:
      - "8087:8086"
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: pitas_admin
      DOCKER_INFLUXDB_INIT_PASSWORD: pitas_influxdb
      DOCKER_INFLUXDB_INIT_ORG: pitas
      DOCKER_INFLUXDB_INIT_BUCKET: performance-metrics
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: pitas_influxdb_token
    volumes:
      - influxdb_performance_data:/var/lib/influxdb2
      - influxdb_performance_config:/etc/influxdb2
    networks:
      - pitas_performance_net
    restart: unless-stopped

  # Node Exporter for System Metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter-performance
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - pitas_performance_net
    restart: unless-stopped

volumes:
  redis_node_1_data:
    driver: local
  redis_node_2_data:
    driver: local
  redis_node_3_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  postgres_performance_data:
    driver: local
  neo4j_performance_data:
    driver: local
  neo4j_performance_logs:
    driver: local
  influxdb_performance_data:
    driver: local
  influxdb_performance_config:
    driver: local

networks:
  pitas_performance_net:
    driver: bridge
    name: pitas_performance_net
