"""MITRE ATT&CK framework integration with STIX/TAXII protocol support."""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import httpx
import structlog
from pydantic import BaseModel, Field

from pitas.core.config import settings
from pitas.core.exceptions import IntegrationError

logger = structlog.get_logger(__name__)


class MITRETechnique(BaseModel):
    """MITRE ATT&CK technique representation."""
    
    technique_id: str = Field(..., description="MITRE technique ID (e.g., T1055)")
    name: str = Field(..., description="Technique name")
    description: str = Field(..., description="Technique description")
    tactic: str = Field(..., description="Associated tactic")
    platform: List[str] = Field(default_factory=list, description="Supported platforms")
    data_sources: List[str] = Field(default_factory=list, description="Data sources")
    mitigations: List[str] = Field(default_factory=list, description="Mitigation IDs")
    detection: Optional[str] = Field(None, description="Detection guidance")
    url: str = Field(..., description="MITRE ATT&CK URL")


class MITREMapping(BaseModel):
    """Vulnerability to MITRE ATT&CK technique mapping."""
    
    vulnerability_id: str = Field(..., description="Vulnerability identifier")
    techniques: List[MITRETechnique] = Field(..., description="Mapped techniques")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Mapping confidence")
    mapped_at: datetime = Field(default_factory=datetime.utcnow, description="Mapping timestamp")


class MITREAttackClient:
    """Client for MITRE ATT&CK framework integration."""
    
    def __init__(self) -> None:
        """Initialize MITRE ATT&CK client."""
        self.base_url = settings.mitre_attack_api_url
        self.cache_ttl = timedelta(hours=6)  # 4-6 hour polling cycles
        self._techniques_cache: Optional[Dict[str, MITRETechnique]] = None
        self._cache_timestamp: Optional[datetime] = None
        
    async def _fetch_techniques(self) -> Dict[str, MITRETechnique]:
        """Fetch MITRE ATT&CK techniques from API.
        
        Returns:
            Dict[str, MITRETechnique]: Techniques indexed by ID
            
        Raises:
            IntegrationError: If API request fails
        """
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # MITRE ATT&CK Enterprise techniques endpoint
                url = urljoin(self.base_url, "techniques/enterprise")
                
                response = await client.get(url)
                response.raise_for_status()
                
                data = response.json()
                techniques = {}
                
                for technique_data in data.get("objects", []):
                    if technique_data.get("type") == "attack-pattern":
                        technique = self._parse_technique(technique_data)
                        techniques[technique.technique_id] = technique
                
                logger.info(
                    "Fetched MITRE ATT&CK techniques",
                    count=len(techniques),
                    url=url
                )
                
                return techniques
                
        except httpx.HTTPError as e:
            logger.error(
                "Failed to fetch MITRE ATT&CK techniques",
                error=str(e),
                url=self.base_url
            )
            raise IntegrationError(f"MITRE ATT&CK API error: {e}")
        except Exception as e:
            logger.error(
                "Unexpected error fetching MITRE techniques",
                error=str(e)
            )
            raise IntegrationError(f"MITRE integration error: {e}")
    
    def _parse_technique(self, technique_data: Dict[str, Any]) -> MITRETechnique:
        """Parse MITRE technique data from API response.
        
        Args:
            technique_data: Raw technique data from API
            
        Returns:
            MITRETechnique: Parsed technique object
        """
        external_refs = technique_data.get("external_references", [])
        mitre_ref = next(
            (ref for ref in external_refs if ref.get("source_name") == "mitre-attack"),
            {}
        )
        
        technique_id = mitre_ref.get("external_id", "")
        url = mitre_ref.get("url", "")
        
        # Extract kill chain phases (tactics)
        kill_chain_phases = technique_data.get("kill_chain_phases", [])
        tactic = kill_chain_phases[0].get("phase_name", "") if kill_chain_phases else ""
        
        # Extract platforms
        platforms = technique_data.get("x_mitre_platforms", [])
        
        # Extract data sources
        data_sources = []
        for data_component in technique_data.get("x_mitre_data_sources", []):
            data_sources.extend(data_component.get("data_components", []))
        
        return MITRETechnique(
            technique_id=technique_id,
            name=technique_data.get("name", ""),
            description=technique_data.get("description", ""),
            tactic=tactic,
            platform=platforms,
            data_sources=data_sources,
            mitigations=[],  # Will be populated separately
            detection=technique_data.get("x_mitre_detection", ""),
            url=url
        )
    
    async def get_techniques(self, force_refresh: bool = False) -> Dict[str, MITRETechnique]:
        """Get MITRE ATT&CK techniques with caching.
        
        Args:
            force_refresh: Force refresh of cached data
            
        Returns:
            Dict[str, MITRETechnique]: Techniques indexed by ID
        """
        now = datetime.utcnow()
        
        # Check cache validity
        if (
            not force_refresh
            and self._techniques_cache is not None
            and self._cache_timestamp is not None
            and (now - self._cache_timestamp) < self.cache_ttl
        ):
            logger.debug("Using cached MITRE techniques")
            return self._techniques_cache
        
        # Fetch fresh data
        logger.info("Refreshing MITRE ATT&CK techniques cache")
        self._techniques_cache = await self._fetch_techniques()
        self._cache_timestamp = now
        
        return self._techniques_cache
    
    async def map_techniques(self, vulnerability_data: Dict[str, Any]) -> List[MITRETechnique]:
        """Map vulnerability to MITRE ATT&CK techniques.
        
        Args:
            vulnerability_data: Vulnerability information
            
        Returns:
            List[MITRETechnique]: Mapped techniques
        """
        techniques = await self.get_techniques()
        mapped_techniques = []
        
        # Simple keyword-based mapping (can be enhanced with ML)
        vulnerability_text = (
            f"{vulnerability_data.get('title', '')} "
            f"{vulnerability_data.get('description', '')}"
        ).lower()
        
        for technique in techniques.values():
            # Check if technique keywords match vulnerability
            technique_keywords = [
                technique.name.lower(),
                technique.description.lower()
            ]
            
            for keyword in technique_keywords:
                if any(word in vulnerability_text for word in keyword.split()[:3]):
                    mapped_techniques.append(technique)
                    break
        
        logger.info(
            "Mapped vulnerability to MITRE techniques",
            vulnerability_id=vulnerability_data.get("id"),
            technique_count=len(mapped_techniques)
        )
        
        return mapped_techniques[:10]  # Limit to top 10 matches
    
    async def get_technique_by_id(self, technique_id: str) -> Optional[MITRETechnique]:
        """Get specific MITRE technique by ID.
        
        Args:
            technique_id: MITRE technique ID (e.g., T1055)
            
        Returns:
            Optional[MITRETechnique]: Technique if found
        """
        techniques = await self.get_techniques()
        return techniques.get(technique_id)
    
    async def search_techniques(self, query: str, limit: int = 10) -> List[MITRETechnique]:
        """Search MITRE techniques by query.
        
        Args:
            query: Search query
            limit: Maximum results to return
            
        Returns:
            List[MITRETechnique]: Matching techniques
        """
        techniques = await self.get_techniques()
        query_lower = query.lower()
        
        matches = []
        for technique in techniques.values():
            if (
                query_lower in technique.name.lower()
                or query_lower in technique.description.lower()
                or query_lower in technique.tactic.lower()
            ):
                matches.append(technique)
                
                if len(matches) >= limit:
                    break
        
        return matches


# Global client instance
mitre_client = MITREAttackClient()
