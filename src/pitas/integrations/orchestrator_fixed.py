"""Security Framework Orchestrator for multi-framework integration."""

from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from pydantic import BaseModel, Field

from pitas.integrations.cvss import CVSSScore, cvss_calculator
from pitas.integrations.mitre import MITReTechnique, mitre_client
from pitas.integrations.nist import NISTControlMapping, nist_assessor

logger = structlog.get_logger(__name__)


class EnrichedVulnerability(BaseModel):
    """Vulnerability enriched with multi-framework security data."""
    
    # Original vulnerability data
    vulnerability_id: str = Field(..., description="Vulnerability identifier")
    title: str = Field(..., description="Vulnerability title")
    description: str = Field(..., description="Vulnerability description")
    severity: str = Field(..., description="Original severity rating")
    
    # MITRE ATT&CK mapping
    mitre_techniques: List[MITReTechnique] = Field(default_factory=list, description="Mapped MITRE techniques")
    mitre_confidence: float = Field(0.0, ge=0.0, le=1.0, description="MITRE mapping confidence")
    
    # CVSS 4.0 scoring
    cvss_scores: Optional[CVSSScore] = Field(None, description="CVSS 4.0 scores")
    
    # NIST CSF 2.0 controls
    nist_controls: List[str] = Field(default_factory=list, description="Relevant NIST controls")
    nist_mapping: Optional[NISTControlMapping] = Field(None, description="NIST control mapping")
    
    # Enrichment metadata
    enriched_at: datetime = Field(default_factory=datetime.utcnow, description="Enrichment timestamp")
    enrichment_version: str = Field("1.0", description="Enrichment process version")
    
    # Risk assessment
    risk_score: float = Field(0.0, ge=0.0, le=10.0, description="Composite risk score")
    business_impact: str = Field("Unknown", description="Business impact assessment")
    remediation_priority: str = Field("Medium", description="Remediation priority")


class FrameworkCorrelation(BaseModel):
    """Correlation between different security frameworks."""
    
    mitre_technique_id: str = Field(..., description="MITRE technique ID")
    cvss_vector: str = Field(..., description="CVSS vector string")
    nist_controls: List[str] = Field(..., description="Related NIST controls")
    correlation_strength: float = Field(..., ge=0.0, le=1.0, description="Correlation strength")
    correlation_type: str = Field(..., description="Type of correlation")


class SecurityFrameworkOrchestrator:
    """Central orchestrator for multi-framework security integration."""
    
    def __init__(self) -> None:
        """Initialize the security framework orchestrator."""
        self.mitre_client = mitre_client
        self.cvss_calculator = cvss_calculator
        self.nist_assessor = nist_assessor
        
        # Framework correlation cache
        self._correlation_cache: Dict[str, FrameworkCorrelation] = {}
    
    async def correlate_security_data(self, vulnerability_data: Dict[str, Any]) -> EnrichedVulnerability:
        """Correlate vulnerability across all security frameworks.
        
        Args:
            vulnerability_data: Raw vulnerability data
            
        Returns:
            EnrichedVulnerability: Enriched vulnerability with framework data
        """
        logger.info(
            "Starting multi-framework vulnerability correlation",
            vulnerability_id=vulnerability_data.get("id")
        )
        
        # Extract basic vulnerability information
        vuln_id = vulnerability_data.get("id", "unknown")
        title = vulnerability_data.get("title", "")
        description = vulnerability_data.get("description", "")
        severity = vulnerability_data.get("severity", "Unknown")
        
        # MITRE ATT&CK mapping
        mitre_techniques = await self._map_mitre_techniques(vulnerability_data)
        mitre_confidence = self._calculate_mitre_confidence(mitre_techniques, vulnerability_data)
        
        # CVSS 4.0 scoring
        cvss_scores = self._calculate_cvss_scores(vulnerability_data)
        
        # NIST CSF 2.0 control mapping
        nist_controls = self._map_nist_controls(vulnerability_data)
        nist_mapping = self._create_nist_mapping(vuln_id, nist_controls)
        
        # Calculate composite risk score
        risk_score = self._calculate_composite_risk_score(
            cvss_scores, mitre_techniques, nist_controls
        )
        
        # Assess business impact and priority
        business_impact = self._assess_business_impact(vulnerability_data, risk_score)
        remediation_priority = self._determine_remediation_priority(
            risk_score, business_impact, mitre_techniques
        )
        
        enriched_vulnerability = EnrichedVulnerability(
            vulnerability_id=vuln_id,
            title=title,
            description=description,
            severity=severity,
            mitre_techniques=mitre_techniques,
            mitre_confidence=mitre_confidence,
            cvss_scores=cvss_scores,
            nist_controls=nist_controls,
            nist_mapping=nist_mapping,
            risk_score=risk_score,
            business_impact=business_impact,
            remediation_priority=remediation_priority
        )
        
        logger.info(
            "Completed multi-framework vulnerability correlation",
            vulnerability_id=vuln_id,
            risk_score=risk_score,
            mitre_technique_count=len(mitre_techniques),
            nist_control_count=len(nist_controls),
            remediation_priority=remediation_priority
        )
        
        return enriched_vulnerability
    
    async def _map_mitre_techniques(self, vulnerability_data: Dict[str, Any]) -> List[MITReTechnique]:
        """Map vulnerability to MITRE ATT&CK techniques."""
        try:
            techniques = await self.mitre_client.map_techniques(vulnerability_data)
            return techniques
        except Exception as e:
            logger.error(
                "Failed to map MITRE techniques",
                error=str(e),
                vulnerability_id=vulnerability_data.get("id")
            )
            return []
    
    def _calculate_mitre_confidence(
        self,
        techniques: List[MITReTechnique],
        vulnerability_data: Dict[str, Any]
    ) -> float:
        """Calculate confidence score for MITRE technique mapping."""
        if not techniques:
            return 0.0
        
        # Simple confidence calculation based on technique count and description match
        base_confidence = min(0.8, len(techniques) * 0.1)
        
        # Boost confidence if vulnerability description contains technique keywords
        description = vulnerability_data.get("description", "").lower()
        keyword_matches = sum(
            1 for technique in techniques
            if any(word in description for word in technique.name.lower().split()[:3])
        )
        
        keyword_boost = min(0.2, keyword_matches * 0.05)
        
        return min(1.0, base_confidence + keyword_boost)
    
    def _calculate_cvss_scores(self, vulnerability_data: Dict[str, Any]) -> Optional[CVSSScore]:
        """Calculate CVSS 4.0 scores for vulnerability."""
        try:
            cvss_scores = self.cvss_calculator.calculate_score_with_context(vulnerability_data)
            return cvss_scores
        except Exception as e:
            logger.error(
                "Failed to calculate CVSS scores",
                error=str(e),
                vulnerability_id=vulnerability_data.get("id")
            )
            return None
    
    def _map_nist_controls(self, vulnerability_data: Dict[str, Any]) -> List[str]:
        """Map vulnerability to NIST CSF controls."""
        try:
            controls = self.nist_assessor.identify_relevant_controls(vulnerability_data)
            return controls
        except Exception as e:
            logger.error(
                "Failed to map NIST controls",
                error=str(e),
                vulnerability_id=vulnerability_data.get("id")
            )
            return []
    
    def _create_nist_mapping(self, vulnerability_id: str, nist_controls: List[str]) -> NISTControlMapping:
        """Create NIST control mapping for vulnerability."""
        from pitas.integrations.nist import NISTImplementationTier
        
        return NISTControlMapping(
            vulnerability_id=vulnerability_id,
            relevant_subcategories=nist_controls,
            implementation_tier=NISTImplementationTier.RISK_INFORMED,
            gaps=["Control implementation assessment needed"],
            recommendations=["Conduct detailed control assessment", "Implement missing controls"]
        )
    
    def _calculate_composite_risk_score(
        self,
        cvss_scores: Optional[CVSSScore],
        mitre_techniques: List[MITReTechnique],
        nist_controls: List[str]
    ) -> float:
        """Calculate composite risk score from all frameworks."""
        # Base score from CVSS
        base_score = cvss_scores.base_score if cvss_scores else 5.0
        
        # MITRE technique multiplier (more techniques = higher risk)
        mitre_multiplier = 1.0 + (len(mitre_techniques) * 0.1)
        
        # NIST control coverage (more controls = better coverage, lower risk)
        nist_coverage = max(0.5, 1.0 - (len(nist_controls) * 0.05))
        
        # Calculate composite score
        composite_score = (base_score * mitre_multiplier) / nist_coverage
        
        return min(10.0, composite_score)
    
    def _assess_business_impact(self, vulnerability_data: Dict[str, Any], risk_score: float) -> str:
        """Assess business impact of vulnerability."""
        # Simple business impact assessment
        if risk_score >= 9.0:
            return "Critical"
        elif risk_score >= 7.0:
            return "High"
        elif risk_score >= 4.0:
            return "Medium"
        else:
            return "Low"
    
    def _determine_remediation_priority(
        self,
        risk_score: float,
        business_impact: str,
        mitre_techniques: List[MITReTechnique]
    ) -> str:
        """Determine remediation priority based on multiple factors."""
        # Check for high-priority MITRE techniques
        high_priority_tactics = {"initial-access", "execution", "persistence", "privilege-escalation"}
        has_critical_techniques = any(
            technique.tactic in high_priority_tactics for technique in mitre_techniques
        )
        
        if risk_score >= 9.0 or (risk_score >= 7.0 and has_critical_techniques):
            return "Critical"
        elif risk_score >= 7.0 or business_impact == "High":
            return "High"
        elif risk_score >= 4.0 or business_impact == "Medium":
            return "Medium"
        else:
            return "Low"
    
    async def correlate_frameworks(
        self,
        mitre_technique_id: str,
        cvss_vector: str,
        nist_controls: List[str]
    ) -> FrameworkCorrelation:
        """Correlate data between different security frameworks."""
        correlation_key = f"{mitre_technique_id}:{cvss_vector}:{':'.join(sorted(nist_controls))}"
        
        if correlation_key in self._correlation_cache:
            return self._correlation_cache[correlation_key]
        
        # Calculate correlation strength based on framework alignment
        correlation_strength = self._calculate_correlation_strength(
            mitre_technique_id, cvss_vector, nist_controls
        )
        
        correlation = FrameworkCorrelation(
            mitre_technique_id=mitre_technique_id,
            cvss_vector=cvss_vector,
            nist_controls=nist_controls,
            correlation_strength=correlation_strength,
            correlation_type="multi-framework"
        )
        
        self._correlation_cache[correlation_key] = correlation
        return correlation
    
    def _calculate_correlation_strength(
        self,
        mitre_technique_id: str,
        cvss_vector: str,
        nist_controls: List[str]
    ) -> float:
        """Calculate strength of correlation between frameworks."""
        # Simplified correlation calculation
        # In production, this would use ML models and historical data
        
        base_strength = 0.5
        
        # Boost for known high-correlation patterns
        if mitre_technique_id.startswith("T1") and len(nist_controls) > 2:
            base_strength += 0.2
        
        # Boost for comprehensive CVSS vectors
        if len(cvss_vector.split("/")) > 8:
            base_strength += 0.1
        
        # Boost for multiple NIST controls
        if len(nist_controls) > 3:
            base_strength += 0.1
        
        return min(1.0, base_strength)
    
    async def get_framework_statistics(self) -> Dict[str, Any]:
        """Get statistics about framework integration."""
        mitre_techniques = await self.mitre_client.get_techniques()
        
        return {
            "mitre_techniques_count": len(mitre_techniques),
            "cvss_version": self.cvss_calculator.version,
            "nist_categories_count": len(self.nist_assessor._categories),
            "nist_subcategories_count": len(self.nist_assessor._subcategories),
            "correlation_cache_size": len(self._correlation_cache),
            "last_updated": datetime.utcnow().isoformat()
        }


# Global orchestrator instance
security_orchestrator = SecurityFrameworkOrchestrator()
