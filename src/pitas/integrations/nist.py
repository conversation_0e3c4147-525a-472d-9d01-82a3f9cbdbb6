"""NIST Cybersecurity Framework 2.0 integration and compliance assessment."""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

import structlog
from pydantic import BaseModel, Field

logger = structlog.get_logger(__name__)


class NISTFunction(str, Enum):
    """NIST CSF 2.0 core functions."""
    
    GOVERN = "GV"
    IDENTIFY = "ID"
    PROTECT = "PR"
    DETECT = "DE"
    RESPOND = "RS"
    RECOVER = "RC"


class NISTCategory(BaseModel):
    """NIST CSF 2.0 category."""
    
    function: NISTFunction = Field(..., description="Core function")
    category_id: str = Field(..., description="Category identifier")
    name: str = Field(..., description="Category name")
    description: str = Field(..., description="Category description")
    subcategories: List[str] = Field(default_factory=list, description="Subcategory IDs")


class NISTSubcategory(BaseModel):
    """NIST CSF 2.0 subcategory."""
    
    subcategory_id: str = Field(..., description="Subcategory identifier")
    category_id: str = Field(..., description="Parent category ID")
    name: str = Field(..., description="Subcategory name")
    description: str = Field(..., description="Subcategory description")
    implementation_examples: List[str] = Field(default_factory=list, description="Implementation examples")
    informative_references: List[str] = Field(default_factory=list, description="Informative references")


class NISTImplementationTier(str, Enum):
    """NIST CSF implementation tiers."""
    
    PARTIAL = "Partial"
    RISK_INFORMED = "Risk Informed"
    REPEATABLE = "Repeatable"
    ADAPTIVE = "Adaptive"


class NISTControlMapping(BaseModel):
    """Mapping between vulnerability and NIST controls."""
    
    vulnerability_id: str = Field(..., description="Vulnerability identifier")
    relevant_subcategories: List[str] = Field(..., description="Relevant subcategory IDs")
    implementation_tier: NISTImplementationTier = Field(..., description="Current implementation tier")
    gaps: List[str] = Field(default_factory=list, description="Identified gaps")
    recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")
    mapped_at: datetime = Field(default_factory=datetime.utcnow, description="Mapping timestamp")


class NISTAssessmentResult(BaseModel):
    """NIST CSF assessment result."""
    
    function: NISTFunction = Field(..., description="Assessed function")
    current_tier: NISTImplementationTier = Field(..., description="Current implementation tier")
    target_tier: NISTImplementationTier = Field(..., description="Target implementation tier")
    maturity_score: float = Field(..., ge=0.0, le=100.0, description="Maturity percentage")
    gaps: List[str] = Field(default_factory=list, description="Identified gaps")
    recommendations: List[str] = Field(default_factory=list, description="Recommendations")
    assessed_at: datetime = Field(default_factory=datetime.utcnow, description="Assessment timestamp")


class NISTCSFAssessor:
    """NIST Cybersecurity Framework 2.0 assessor and compliance manager."""
    
    def __init__(self) -> None:
        """Initialize NIST CSF assessor."""
        self._categories = self._load_categories()
        self._subcategories = self._load_subcategories()
    
    def _load_categories(self) -> Dict[str, NISTCategory]:
        """Load NIST CSF 2.0 categories."""
        categories = {
            # GOVERN (GV)
            "GV.OC": NISTCategory(
                function=NISTFunction.GOVERN,
                category_id="GV.OC",
                name="Organizational Context",
                description="The organization's mission, stakeholder expectations, and legal and regulatory requirements are understood and inform cybersecurity risk management decisions.",
                subcategories=["GV.OC-01", "GV.OC-02", "GV.OC-03", "GV.OC-04", "GV.OC-05"]
            ),
            "GV.RM": NISTCategory(
                function=NISTFunction.GOVERN,
                category_id="GV.RM",
                name="Risk Management Strategy",
                description="Priorities, constraints, risk tolerance and assumptions are established and used to support risk decisions.",
                subcategories=["GV.RM-01", "GV.RM-02", "GV.RM-03", "GV.RM-04", "GV.RM-05", "GV.RM-06", "GV.RM-07"]
            ),
            
            # IDENTIFY (ID)
            "ID.AM": NISTCategory(
                function=NISTFunction.IDENTIFY,
                category_id="ID.AM",
                name="Asset Management",
                description="Assets are identified and managed consistent with their relative importance to organizational objectives and the organization's risk strategy.",
                subcategories=["ID.AM-01", "ID.AM-02", "ID.AM-03", "ID.AM-04", "ID.AM-05", "ID.AM-06", "ID.AM-07", "ID.AM-08"]
            ),
            "ID.RA": NISTCategory(
                function=NISTFunction.IDENTIFY,
                category_id="ID.RA",
                name="Risk Assessment",
                description="The organization understands the cybersecurity risk to organizational operations, assets, and individuals.",
                subcategories=["ID.RA-01", "ID.RA-02", "ID.RA-03", "ID.RA-04", "ID.RA-05", "ID.RA-06", "ID.RA-07", "ID.RA-08", "ID.RA-09", "ID.RA-10"]
            ),
            
            # PROTECT (PR)
            "PR.AC": NISTCategory(
                function=NISTFunction.PROTECT,
                category_id="PR.AC",
                name="Identity Management, Authentication and Access Control",
                description="Access to physical and logical assets and associated facilities is limited to authorized users, processes, and devices.",
                subcategories=["PR.AC-01", "PR.AC-02", "PR.AC-03", "PR.AC-04", "PR.AC-05", "PR.AC-06", "PR.AC-07", "PR.AC-08", "PR.AC-09", "PR.AC-10", "PR.AC-11", "PR.AC-12"]
            ),
            "PR.DS": NISTCategory(
                function=NISTFunction.PROTECT,
                category_id="PR.DS",
                name="Data Security",
                description="Information and records are managed consistent with the organization's risk strategy to protect the confidentiality, integrity, and availability of information.",
                subcategories=["PR.DS-01", "PR.DS-02", "PR.DS-03", "PR.DS-04", "PR.DS-05", "PR.DS-06", "PR.DS-07", "PR.DS-08", "PR.DS-09", "PR.DS-10", "PR.DS-11"]
            ),
            
            # DETECT (DE)
            "DE.AE": NISTCategory(
                function=NISTFunction.DETECT,
                category_id="DE.AE",
                name="Anomalies and Events",
                description="Anomalous activity is detected and the potential impact of events is understood.",
                subcategories=["DE.AE-01", "DE.AE-02", "DE.AE-03", "DE.AE-04", "DE.AE-05", "DE.AE-06", "DE.AE-07", "DE.AE-08"]
            ),
            "DE.CM": NISTCategory(
                function=NISTFunction.DETECT,
                category_id="DE.CM",
                name="Continuous Monitoring",
                description="The organization's assets and network are monitored to identify cybersecurity events and verify the effectiveness of protective measures.",
                subcategories=["DE.CM-01", "DE.CM-02", "DE.CM-03", "DE.CM-04", "DE.CM-05", "DE.CM-06", "DE.CM-07", "DE.CM-08", "DE.CM-09"]
            ),
            
            # RESPOND (RS)
            "RS.RP": NISTCategory(
                function=NISTFunction.RESPOND,
                category_id="RS.RP",
                name="Response Planning",
                description="Response processes and procedures are executed and maintained to ensure response to detected cybersecurity incidents.",
                subcategories=["RS.RP-01", "RS.RP-02", "RS.RP-03", "RS.RP-04", "RS.RP-05"]
            ),
            "RS.CO": NISTCategory(
                function=NISTFunction.RESPOND,
                category_id="RS.CO",
                name="Communications",
                description="Response activities are coordinated with internal and external stakeholders.",
                subcategories=["RS.CO-01", "RS.CO-02", "RS.CO-03", "RS.CO-04", "RS.CO-05"]
            ),
            
            # RECOVER (RC)
            "RC.RP": NISTCategory(
                function=NISTFunction.RECOVER,
                category_id="RC.RP",
                name="Recovery Planning",
                description="Recovery processes and procedures are executed and maintained to ensure restoration of systems or assets affected by cybersecurity incidents.",
                subcategories=["RC.RP-01", "RC.RP-02", "RC.RP-03", "RC.RP-04", "RC.RP-05"]
            ),
            "RC.IM": NISTCategory(
                function=NISTFunction.RECOVER,
                category_id="RC.IM",
                name="Improvements",
                description="Recovery planning and processes are improved by incorporating lessons learned into future activities.",
                subcategories=["RC.IM-01", "RC.IM-02", "RC.IM-03"]
            )
        }
        
        return categories
    
    def _load_subcategories(self) -> Dict[str, NISTSubcategory]:
        """Load NIST CSF 2.0 subcategories."""
        # Sample subcategories - in production, this would be a complete mapping
        subcategories = {
            "ID.AM-01": NISTSubcategory(
                subcategory_id="ID.AM-01",
                category_id="ID.AM",
                name="Inventories",
                description="Inventories of hardware, software, services, and systems are maintained.",
                implementation_examples=[
                    "Asset management systems",
                    "Configuration management databases (CMDB)",
                    "Network discovery tools"
                ],
                informative_references=["CIS Controls v8 1", "ISO/IEC 27001:2013 A.8.1.1"]
            ),
            "PR.AC-01": NISTSubcategory(
                subcategory_id="PR.AC-01",
                category_id="PR.AC",
                name="Identity Management",
                description="Identities and credentials are issued, managed, verified, revoked, and audited for authorized devices, users and processes.",
                implementation_examples=[
                    "Identity and access management (IAM) systems",
                    "Multi-factor authentication",
                    "Privileged access management"
                ],
                informative_references=["CIS Controls v8 5", "ISO/IEC 27001:2013 A.9.2.1"]
            ),
            "DE.AE-01": NISTSubcategory(
                subcategory_id="DE.AE-01",
                category_id="DE.AE",
                name="Baseline Establishment",
                description="A baseline of network operations and expected data flows for users and systems is established and managed.",
                implementation_examples=[
                    "Network monitoring tools",
                    "Security information and event management (SIEM)",
                    "User and entity behavior analytics (UEBA)"
                ],
                informative_references=["CIS Controls v8 12", "ISO/IEC 27001:2013 A.12.4.1"]
            )
        }
        
        return subcategories
    
    def identify_relevant_controls(self, vulnerability_data: Dict) -> List[str]:
        """Identify relevant NIST CSF controls for a vulnerability.
        
        Args:
            vulnerability_data: Vulnerability information
            
        Returns:
            List[str]: Relevant subcategory IDs
        """
        relevant_controls = []
        
        # Simple keyword-based mapping (can be enhanced with ML)
        vulnerability_text = (
            f"{vulnerability_data.get('title', '')} "
            f"{vulnerability_data.get('description', '')}"
        ).lower()
        
        # Map based on vulnerability characteristics
        if any(keyword in vulnerability_text for keyword in ["authentication", "access", "credential"]):
            relevant_controls.extend(["PR.AC-01", "PR.AC-02", "PR.AC-03"])
        
        if any(keyword in vulnerability_text for keyword in ["data", "encryption", "confidentiality"]):
            relevant_controls.extend(["PR.DS-01", "PR.DS-02", "PR.DS-05"])
        
        if any(keyword in vulnerability_text for keyword in ["monitoring", "detection", "logging"]):
            relevant_controls.extend(["DE.AE-01", "DE.CM-01", "DE.CM-03"])
        
        if any(keyword in vulnerability_text for keyword in ["asset", "inventory", "configuration"]):
            relevant_controls.extend(["ID.AM-01", "ID.AM-02", "ID.AM-03"])
        
        if any(keyword in vulnerability_text for keyword in ["incident", "response", "recovery"]):
            relevant_controls.extend(["RS.RP-01", "RS.CO-01", "RC.RP-01"])
        
        logger.info(
            "Identified relevant NIST controls",
            vulnerability_id=vulnerability_data.get("id"),
            control_count=len(relevant_controls)
        )
        
        return list(set(relevant_controls))  # Remove duplicates
    
    def assess_function_maturity(self, function: NISTFunction, assessment_data: Dict) -> NISTAssessmentResult:
        """Assess maturity of a NIST CSF function.
        
        Args:
            function: NIST function to assess
            assessment_data: Assessment input data
            
        Returns:
            NISTAssessmentResult: Assessment result
        """
        # Simplified maturity assessment
        # In production, this would involve detailed questionnaires and evidence collection
        
        current_tier = NISTImplementationTier.RISK_INFORMED
        target_tier = NISTImplementationTier.REPEATABLE
        maturity_score = 65.0  # Placeholder
        
        gaps = [
            f"Incomplete implementation of {function.value} controls",
            f"Limited automation in {function.value} processes",
            f"Insufficient metrics for {function.value} effectiveness"
        ]
        
        recommendations = [
            f"Implement comprehensive {function.value} program",
            f"Automate {function.value} processes where possible",
            f"Establish metrics and KPIs for {function.value}",
            f"Conduct regular {function.value} assessments"
        ]
        
        return NISTAssessmentResult(
            function=function,
            current_tier=current_tier,
            target_tier=target_tier,
            maturity_score=maturity_score,
            gaps=gaps,
            recommendations=recommendations
        )
    
    def generate_compliance_report(self, organization_data: Dict) -> Dict:
        """Generate comprehensive NIST CSF compliance report.
        
        Args:
            organization_data: Organization assessment data
            
        Returns:
            Dict: Compliance report
        """
        report = {
            "assessment_date": datetime.utcnow().isoformat(),
            "organization": organization_data.get("name", "Unknown"),
            "framework_version": "NIST CSF 2.0",
            "functions": {},
            "overall_maturity": 0.0,
            "priority_gaps": [],
            "recommendations": []
        }
        
        total_maturity = 0.0
        function_count = 0
        
        for function in NISTFunction:
            assessment = self.assess_function_maturity(function, organization_data)
            report["functions"][function.value] = {
                "name": function.value,
                "current_tier": assessment.current_tier.value,
                "target_tier": assessment.target_tier.value,
                "maturity_score": assessment.maturity_score,
                "gaps": assessment.gaps,
                "recommendations": assessment.recommendations
            }
            
            total_maturity += assessment.maturity_score
            function_count += 1
        
        report["overall_maturity"] = total_maturity / function_count if function_count > 0 else 0.0
        
        # Aggregate priority gaps and recommendations
        for function_data in report["functions"].values():
            report["priority_gaps"].extend(function_data["gaps"][:2])  # Top 2 gaps per function
            report["recommendations"].extend(function_data["recommendations"][:2])  # Top 2 recommendations per function
        
        return report
    
    def get_subcategory(self, subcategory_id: str) -> Optional[NISTSubcategory]:
        """Get specific NIST subcategory by ID.
        
        Args:
            subcategory_id: Subcategory identifier
            
        Returns:
            Optional[NISTSubcategory]: Subcategory if found
        """
        return self._subcategories.get(subcategory_id)
    
    def get_category(self, category_id: str) -> Optional[NISTCategory]:
        """Get specific NIST category by ID.
        
        Args:
            category_id: Category identifier
            
        Returns:
            Optional[NISTCategory]: Category if found
        """
        return self._categories.get(category_id)


# Global assessor instance
nist_assessor = NISTCSFAssessor()
