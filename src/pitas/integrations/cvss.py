"""CVSS 4.0 vulnerability scoring implementation with environmental and supplemental metrics."""

from enum import Enum
from typing import Dict, Optional

import structlog
from pydantic import BaseModel, Field, validator

logger = structlog.get_logger(__name__)


class CVSSMetric(str, Enum):
    """CVSS 4.0 metric values."""
    
    # Attack Vector (AV)
    NETWORK = "N"
    ADJACENT = "A"
    LOCAL = "L"
    PHYSICAL = "P"
    
    # Attack Complexity (AC)
    LOW = "L"
    HIGH = "H"
    
    # Attack Requirements (AT)
    NONE = "N"
    PRESENT = "P"
    
    # Privileges Required (PR)
    NONE_PR = "N"
    LOW_PR = "L"
    HIGH_PR = "H"
    
    # User Interaction (UI)
    NONE_UI = "N"
    PASSIVE = "P"
    ACTIVE = "A"
    
    # Vulnerable System Impact
    HIGH_IMPACT = "H"
    LOW_IMPACT = "L"
    NONE_IMPACT = "N"
    
    # Subsequent System Impact
    HIGH_SUB = "H"
    LOW_SUB = "L"
    NONE_SUB = "N"
    
    # Exploitability (E)
    NOT_DEFINED = "X"
    ATTACKED = "A"
    POC = "P"
    UNREPORTED = "U"
    
    # Remediation Level (RL)
    OFFICIAL_FIX = "O"
    TEMPORARY_FIX = "T"
    WORKAROUND = "W"
    UNAVAILABLE = "U"
    
    # Report Confidence (RC)
    CONFIRMED = "C"
    REASONABLE = "R"
    UNKNOWN = "U"


class CVSSBaseMetrics(BaseModel):
    """CVSS 4.0 Base metrics."""
    
    attack_vector: CVSSMetric = Field(..., description="Attack Vector (AV)")
    attack_complexity: CVSSMetric = Field(..., description="Attack Complexity (AC)")
    attack_requirements: CVSSMetric = Field(..., description="Attack Requirements (AT)")
    privileges_required: CVSSMetric = Field(..., description="Privileges Required (PR)")
    user_interaction: CVSSMetric = Field(..., description="User Interaction (UI)")
    
    # Vulnerable System Impact
    vuln_confidentiality: CVSSMetric = Field(..., description="Vulnerable System Confidentiality (VC)")
    vuln_integrity: CVSSMetric = Field(..., description="Vulnerable System Integrity (VI)")
    vuln_availability: CVSSMetric = Field(..., description="Vulnerable System Availability (VA)")
    
    # Subsequent System Impact
    subseq_confidentiality: CVSSMetric = Field(..., description="Subsequent System Confidentiality (SC)")
    subseq_integrity: CVSSMetric = Field(..., description="Subsequent System Integrity (SI)")
    subseq_availability: CVSSMetric = Field(..., description="Subsequent System Availability (SA)")


class CVSSThreatMetrics(BaseModel):
    """CVSS 4.0 Threat metrics (formerly Temporal)."""
    
    exploitability: CVSSMetric = Field(CVSSMetric.NOT_DEFINED, description="Exploitability (E)")
    remediation_level: CVSSMetric = Field(CVSSMetric.NOT_DEFINED, description="Remediation Level (RL)")
    report_confidence: CVSSMetric = Field(CVSSMetric.NOT_DEFINED, description="Report Confidence (RC)")


class CVSSEnvironmentalMetrics(BaseModel):
    """CVSS 4.0 Environmental metrics."""
    
    # Modified Base Metrics
    modified_attack_vector: Optional[CVSSMetric] = Field(None, description="Modified Attack Vector (MAV)")
    modified_attack_complexity: Optional[CVSSMetric] = Field(None, description="Modified Attack Complexity (MAC)")
    modified_attack_requirements: Optional[CVSSMetric] = Field(None, description="Modified Attack Requirements (MAT)")
    modified_privileges_required: Optional[CVSSMetric] = Field(None, description="Modified Privileges Required (MPR)")
    modified_user_interaction: Optional[CVSSMetric] = Field(None, description="Modified User Interaction (MUI)")
    
    # Modified Vulnerable System Impact
    modified_vuln_confidentiality: Optional[CVSSMetric] = Field(None, description="Modified Vulnerable System Confidentiality (MVC)")
    modified_vuln_integrity: Optional[CVSSMetric] = Field(None, description="Modified Vulnerable System Integrity (MVI)")
    modified_vuln_availability: Optional[CVSSMetric] = Field(None, description="Modified Vulnerable System Availability (MVA)")
    
    # Modified Subsequent System Impact
    modified_subseq_confidentiality: Optional[CVSSMetric] = Field(None, description="Modified Subsequent System Confidentiality (MSC)")
    modified_subseq_integrity: Optional[CVSSMetric] = Field(None, description="Modified Subsequent System Integrity (MSI)")
    modified_subseq_availability: Optional[CVSSMetric] = Field(None, description="Modified Subsequent System Availability (MSA)")
    
    # Confidentiality, Integrity, Availability Requirements
    confidentiality_requirement: CVSSMetric = Field(CVSSMetric.HIGH_IMPACT, description="Confidentiality Requirement (CR)")
    integrity_requirement: CVSSMetric = Field(CVSSMetric.HIGH_IMPACT, description="Integrity Requirement (IR)")
    availability_requirement: CVSSMetric = Field(CVSSMetric.HIGH_IMPACT, description="Availability Requirement (AR)")


class CVSSSupplementalMetrics(BaseModel):
    """CVSS 4.0 Supplemental metrics."""
    
    safety: Optional[CVSSMetric] = Field(None, description="Safety (S)")
    automatable: Optional[CVSSMetric] = Field(None, description="Automatable (AU)")
    recovery: Optional[CVSSMetric] = Field(None, description="Recovery (R)")
    value_density: Optional[CVSSMetric] = Field(None, description="Value Density (V)")
    vulnerability_response_effort: Optional[CVSSMetric] = Field(None, description="Vulnerability Response Effort (RE)")
    provider_urgency: Optional[CVSSMetric] = Field(None, description="Provider Urgency (U)")


class CVSSScore(BaseModel):
    """CVSS 4.0 complete score calculation."""
    
    base_score: float = Field(..., ge=0.0, le=10.0, description="Base score")
    threat_score: Optional[float] = Field(None, ge=0.0, le=10.0, description="Threat score")
    environmental_score: Optional[float] = Field(None, ge=0.0, le=10.0, description="Environmental score")
    
    base_severity: str = Field(..., description="Base severity rating")
    threat_severity: Optional[str] = Field(None, description="Threat severity rating")
    environmental_severity: Optional[str] = Field(None, description="Environmental severity rating")
    
    vector_string: str = Field(..., description="CVSS vector string")


class CVSSCalculator:
    """CVSS 4.0 calculator with environmental and supplemental metrics."""
    
    # CVSS 4.0 scoring lookup tables
    SEVERITY_RATINGS = {
        (0.0, 0.0): "None",
        (0.1, 3.9): "Low",
        (4.0, 6.9): "Medium",
        (7.0, 8.9): "High",
        (9.0, 10.0): "Critical"
    }
    
    # Base metric scoring values
    METRIC_VALUES = {
        # Attack Vector
        "AV:N": 0.0, "AV:A": 0.1, "AV:L": 0.2, "AV:P": 0.3,
        
        # Attack Complexity
        "AC:L": 0.0, "AC:H": 0.1,
        
        # Attack Requirements
        "AT:N": 0.0, "AT:P": 0.1,
        
        # Privileges Required
        "PR:N": 0.0, "PR:L": 0.1, "PR:H": 0.2,
        
        # User Interaction
        "UI:N": 0.0, "UI:P": 0.1, "UI:A": 0.2,
        
        # Impact metrics
        "H": 0.0, "L": 0.1, "N": 0.2,
    }
    
    def __init__(self, version: str = "4.0") -> None:
        """Initialize CVSS calculator.
        
        Args:
            version: CVSS version (currently only 4.0 supported)
        """
        if version != "4.0":
            raise ValueError("Only CVSS 4.0 is currently supported")
        
        self.version = version
    
    def calculate_base_score(self, metrics: CVSSBaseMetrics) -> float:
        """Calculate CVSS 4.0 base score.
        
        Args:
            metrics: Base metrics
            
        Returns:
            float: Base score (0.0-10.0)
        """
        # Simplified CVSS 4.0 calculation
        # In production, this would implement the full CVSS 4.0 algorithm
        
        exploitability = self._calculate_exploitability(metrics)
        impact = self._calculate_impact(metrics)
        
        if impact <= 0:
            return 0.0
        
        # CVSS 4.0 base score formula (simplified)
        if exploitability <= 0:
            base_score = 0.0
        else:
            base_score = min(10.0, (exploitability + impact) * 1.08)
        
        return round(base_score, 1)
    
    def _calculate_exploitability(self, metrics: CVSSBaseMetrics) -> float:
        """Calculate exploitability sub-score."""
        av_score = self.METRIC_VALUES.get(f"AV:{metrics.attack_vector.value}", 0.0)
        ac_score = self.METRIC_VALUES.get(f"AC:{metrics.attack_complexity.value}", 0.0)
        at_score = self.METRIC_VALUES.get(f"AT:{metrics.attack_requirements.value}", 0.0)
        pr_score = self.METRIC_VALUES.get(f"PR:{metrics.privileges_required.value}", 0.0)
        ui_score = self.METRIC_VALUES.get(f"UI:{metrics.user_interaction.value}", 0.0)
        
        return 8.22 * (1 - (1 - av_score) * (1 - ac_score) * (1 - at_score) * (1 - pr_score) * (1 - ui_score))
    
    def _calculate_impact(self, metrics: CVSSBaseMetrics) -> float:
        """Calculate impact sub-score."""
        vc_score = self.METRIC_VALUES.get(metrics.vuln_confidentiality.value, 0.0)
        vi_score = self.METRIC_VALUES.get(metrics.vuln_integrity.value, 0.0)
        va_score = self.METRIC_VALUES.get(metrics.vuln_availability.value, 0.0)
        
        sc_score = self.METRIC_VALUES.get(metrics.subseq_confidentiality.value, 0.0)
        si_score = self.METRIC_VALUES.get(metrics.subseq_integrity.value, 0.0)
        sa_score = self.METRIC_VALUES.get(metrics.subseq_availability.value, 0.0)
        
        vuln_impact = 1 - ((1 - vc_score) * (1 - vi_score) * (1 - va_score))
        subseq_impact = 1 - ((1 - sc_score) * (1 - si_score) * (1 - sa_score))
        
        return 6.42 * max(vuln_impact, subseq_impact)
    
    def get_severity_rating(self, score: float) -> str:
        """Get severity rating for a CVSS score.
        
        Args:
            score: CVSS score
            
        Returns:
            str: Severity rating
        """
        for (min_score, max_score), rating in self.SEVERITY_RATINGS.items():
            if min_score <= score <= max_score:
                return rating
        
        return "Unknown"
    
    def calculate_score_with_context(
        self,
        vulnerability_data: Dict,
        environmental_metrics: Optional[CVSSEnvironmentalMetrics] = None,
        threat_metrics: Optional[CVSSThreatMetrics] = None
    ) -> CVSSScore:
        """Calculate CVSS score with environmental context.
        
        Args:
            vulnerability_data: Vulnerability information
            environmental_metrics: Environmental metrics
            threat_metrics: Threat metrics
            
        Returns:
            CVSSScore: Complete CVSS score
        """
        # Extract or infer base metrics from vulnerability data
        base_metrics = self._infer_base_metrics(vulnerability_data)
        
        base_score = self.calculate_base_score(base_metrics)
        base_severity = self.get_severity_rating(base_score)
        
        # Calculate threat score if metrics provided
        threat_score = None
        threat_severity = None
        if threat_metrics:
            threat_score = self._calculate_threat_score(base_score, threat_metrics)
            threat_severity = self.get_severity_rating(threat_score)
        
        # Calculate environmental score if metrics provided
        environmental_score = None
        environmental_severity = None
        if environmental_metrics:
            environmental_score = self._calculate_environmental_score(
                base_metrics, environmental_metrics
            )
            environmental_severity = self.get_severity_rating(environmental_score)
        
        # Generate vector string
        vector_string = self._generate_vector_string(
            base_metrics, threat_metrics, environmental_metrics
        )
        
        return CVSSScore(
            base_score=base_score,
            threat_score=threat_score,
            environmental_score=environmental_score,
            base_severity=base_severity,
            threat_severity=threat_severity,
            environmental_severity=environmental_severity,
            vector_string=vector_string
        )
    
    def _infer_base_metrics(self, vulnerability_data: Dict) -> CVSSBaseMetrics:
        """Infer base metrics from vulnerability data."""
        # This is a simplified inference - in production, this would be more sophisticated
        return CVSSBaseMetrics(
            attack_vector=CVSSMetric.NETWORK,
            attack_complexity=CVSSMetric.LOW,
            attack_requirements=CVSSMetric.NONE,
            privileges_required=CVSSMetric.NONE_PR,
            user_interaction=CVSSMetric.NONE_UI,
            vuln_confidentiality=CVSSMetric.HIGH_IMPACT,
            vuln_integrity=CVSSMetric.HIGH_IMPACT,
            vuln_availability=CVSSMetric.HIGH_IMPACT,
            subseq_confidentiality=CVSSMetric.NONE_SUB,
            subseq_integrity=CVSSMetric.NONE_SUB,
            subseq_availability=CVSSMetric.NONE_SUB
        )
    
    def _calculate_threat_score(self, base_score: float, threat_metrics: CVSSThreatMetrics) -> float:
        """Calculate threat score from base score and threat metrics."""
        # Simplified threat score calculation
        return base_score * 0.9  # Placeholder
    
    def _calculate_environmental_score(
        self,
        base_metrics: CVSSBaseMetrics,
        environmental_metrics: CVSSEnvironmentalMetrics
    ) -> float:
        """Calculate environmental score."""
        # Simplified environmental score calculation
        base_score = self.calculate_base_score(base_metrics)
        return base_score * 1.1  # Placeholder
    
    def _generate_vector_string(
        self,
        base_metrics: CVSSBaseMetrics,
        threat_metrics: Optional[CVSSThreatMetrics] = None,
        environmental_metrics: Optional[CVSSEnvironmentalMetrics] = None
    ) -> str:
        """Generate CVSS vector string."""
        vector_parts = [
            f"CVSS:{self.version}",
            f"AV:{base_metrics.attack_vector.value}",
            f"AC:{base_metrics.attack_complexity.value}",
            f"AT:{base_metrics.attack_requirements.value}",
            f"PR:{base_metrics.privileges_required.value}",
            f"UI:{base_metrics.user_interaction.value}",
            f"VC:{base_metrics.vuln_confidentiality.value}",
            f"VI:{base_metrics.vuln_integrity.value}",
            f"VA:{base_metrics.vuln_availability.value}",
            f"SC:{base_metrics.subseq_confidentiality.value}",
            f"SI:{base_metrics.subseq_integrity.value}",
            f"SA:{base_metrics.subseq_availability.value}"
        ]
        
        return "/".join(vector_parts)


# Global calculator instance
cvss_calculator = CVSSCalculator()
