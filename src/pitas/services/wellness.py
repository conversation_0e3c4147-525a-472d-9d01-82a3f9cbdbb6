"""Work-life balance and wellness monitoring services."""

from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.wellness import (
    WellnessCheck, WellnessAlert, WorkSchedule, WellnessResource,
    AlertSeverity, AlertStatus, WorkScheduleType
)
from pitas.db.models.user import User
from pitas.schemas.wellness import (
    WellnessCheckCreate, WellnessCheckUpdate, WellnessAlertCreate, WellnessAlertUpdate,
    WorkScheduleCreate, WorkScheduleUpdate, WellnessResourceCreate, WellnessResourceUpdate,
    WellnessMetrics
)
from pitas.services.base import BaseService


class WellnessService(BaseService[WellnessCheck, WellnessCheckCreate, WellnessCheckUpdate]):
    """Service for managing wellness checks and monitoring."""

    def __init__(self):
        super().__init__(WellnessCheck)

    async def create_wellness_check(
        self,
        db: AsyncSession,
        *,
        check_data: WellnessCheckCreate
    ) -> WellnessCheck:
        """Create a new wellness check."""
        # Verify user exists
        user_result = await db.execute(select(User).where(User.id == check_data.user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {check_data.user_id} not found")

        # Calculate burnout risk score
        burnout_risk = self._calculate_burnout_risk(
            stress_level=check_data.stress_level,
            emotional_exhaustion=check_data.emotional_exhaustion,
            cynicism_level=check_data.cynicism_level,
            personal_accomplishment=check_data.personal_accomplishment,
            workload_rating=check_data.workload_rating,
            work_life_balance=check_data.work_life_balance
        )

        wellness_check = WellnessCheck(
            user_id=check_data.user_id,
            check_date=check_data.check_date,
            overall_wellness_score=check_data.overall_wellness_score,
            stress_level=check_data.stress_level,
            energy_level=check_data.energy_level,
            job_satisfaction=check_data.job_satisfaction,
            work_life_balance=check_data.work_life_balance,
            workload_rating=check_data.workload_rating,
            hours_worked_last_week=check_data.hours_worked_last_week,
            overtime_hours=check_data.overtime_hours,
            weekend_work_hours=check_data.weekend_work_hours,
            engagement_level=check_data.engagement_level,
            motivation_level=check_data.motivation_level,
            career_satisfaction=check_data.career_satisfaction,
            learning_opportunities=check_data.learning_opportunities,
            team_satisfaction=check_data.team_satisfaction,
            manager_support=check_data.manager_support,
            communication_effectiveness=check_data.communication_effectiveness,
            recognition_satisfaction=check_data.recognition_satisfaction,
            burnout_risk_score=burnout_risk,
            emotional_exhaustion=check_data.emotional_exhaustion,
            cynicism_level=check_data.cynicism_level,
            personal_accomplishment=check_data.personal_accomplishment,
            positive_highlights=check_data.positive_highlights,
            concerns_challenges=check_data.concerns_challenges,
            improvement_suggestions=check_data.improvement_suggestions,
            support_needed=check_data.support_needed,
            preferred_work_schedule=check_data.preferred_work_schedule,
            workspace_preferences=check_data.workspace_preferences,
            development_interests=check_data.development_interests,
            check_type=check_data.check_type,
            completion_time_minutes=check_data.completion_time_minutes,
            metadata=check_data.metadata
        )
        
        db.add(wellness_check)
        await db.flush()
        await db.refresh(wellness_check)
        
        # Update user's wellness score and last check date
        user.wellness_score = check_data.overall_wellness_score * 10  # Convert to 1-100 scale
        user.last_wellness_check = datetime.utcnow()
        
        # Check for alerts
        await self._check_and_create_alerts(db, wellness_check)
        
        await db.flush()
        return wellness_check

    async def get_user_wellness_history(
        self,
        db: AsyncSession,
        user_id: UUID,
        *,
        days: int = 90,
        limit: int = 50
    ) -> List[WellnessCheck]:
        """Get wellness check history for a user."""
        start_date = date.today() - timedelta(days=days)
        
        query = select(WellnessCheck).where(
            and_(
                WellnessCheck.user_id == user_id,
                WellnessCheck.check_date >= start_date
            )
        ).order_by(desc(WellnessCheck.check_date)).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_wellness_trends(
        self,
        db: AsyncSession,
        user_id: UUID,
        *,
        days: int = 90
    ) -> Dict[str, Any]:
        """Get wellness trends for a user."""
        start_date = date.today() - timedelta(days=days)
        
        checks_result = await db.execute(
            select(WellnessCheck).where(
                and_(
                    WellnessCheck.user_id == user_id,
                    WellnessCheck.check_date >= start_date
                )
            ).order_by(WellnessCheck.check_date)
        )
        checks = list(checks_result.scalars().all())
        
        if not checks:
            return {}
        
        # Calculate trends
        trends = {
            "wellness_score": [c.overall_wellness_score for c in checks],
            "stress_level": [c.stress_level for c in checks],
            "energy_level": [c.energy_level for c in checks],
            "job_satisfaction": [c.job_satisfaction for c in checks],
            "work_life_balance": [c.work_life_balance for c in checks],
            "burnout_risk": [c.burnout_risk_score for c in checks],
            "dates": [c.check_date.isoformat() for c in checks]
        }
        
        # Calculate averages and changes
        latest = checks[-1]
        previous = checks[-2] if len(checks) > 1 else checks[0]
        
        changes = {
            "wellness_score_change": latest.overall_wellness_score - previous.overall_wellness_score,
            "stress_level_change": latest.stress_level - previous.stress_level,
            "burnout_risk_change": latest.burnout_risk_score - previous.burnout_risk_score
        }
        
        return {
            "trends": trends,
            "changes": changes,
            "latest_check": latest,
            "check_count": len(checks)
        }

    def _calculate_burnout_risk(
        self,
        stress_level: int,
        emotional_exhaustion: int,
        cynicism_level: int,
        personal_accomplishment: int,
        workload_rating: int,
        work_life_balance: int
    ) -> float:
        """Calculate burnout risk score (0.0 to 1.0)."""
        # Maslach Burnout Inventory inspired calculation
        # Higher stress, exhaustion, cynicism = higher risk
        # Higher accomplishment, work-life balance = lower risk
        
        risk_factors = (
            (stress_level / 10.0) * 0.25 +
            (emotional_exhaustion / 10.0) * 0.3 +
            (cynicism_level / 10.0) * 0.2 +
            (workload_rating / 10.0) * 0.15 +
            ((10 - personal_accomplishment) / 10.0) * 0.05 +
            ((10 - work_life_balance) / 10.0) * 0.05
        )
        
        return min(1.0, max(0.0, risk_factors))

    async def _check_and_create_alerts(
        self,
        db: AsyncSession,
        wellness_check: WellnessCheck
    ) -> List[WellnessAlert]:
        """Check wellness metrics and create alerts if needed."""
        alerts = []
        
        # High burnout risk alert
        if wellness_check.burnout_risk_score >= 0.7:
            alert = await self._create_alert(
                db,
                user_id=wellness_check.user_id,
                wellness_check_id=wellness_check.id,
                alert_type="burnout_risk",
                severity=AlertSeverity.HIGH if wellness_check.burnout_risk_score >= 0.8 else AlertSeverity.MEDIUM,
                title="High Burnout Risk Detected",
                description=f"Burnout risk score is {wellness_check.burnout_risk_score:.2f}",
                trigger_metrics={"burnout_risk_score": wellness_check.burnout_risk_score}
            )
            alerts.append(alert)
        
        # High stress alert
        if wellness_check.stress_level >= 8:
            alert = await self._create_alert(
                db,
                user_id=wellness_check.user_id,
                wellness_check_id=wellness_check.id,
                alert_type="high_stress",
                severity=AlertSeverity.HIGH if wellness_check.stress_level >= 9 else AlertSeverity.MEDIUM,
                title="High Stress Level Detected",
                description=f"Stress level is {wellness_check.stress_level}/10",
                trigger_metrics={"stress_level": wellness_check.stress_level}
            )
            alerts.append(alert)
        
        # Low wellness score alert
        if wellness_check.overall_wellness_score <= 4:
            alert = await self._create_alert(
                db,
                user_id=wellness_check.user_id,
                wellness_check_id=wellness_check.id,
                alert_type="low_wellness",
                severity=AlertSeverity.MEDIUM,
                title="Low Overall Wellness Score",
                description=f"Overall wellness score is {wellness_check.overall_wellness_score}/10",
                trigger_metrics={"overall_wellness_score": wellness_check.overall_wellness_score}
            )
            alerts.append(alert)
        
        # Excessive hours alert
        if wellness_check.hours_worked_last_week and wellness_check.hours_worked_last_week > 50:
            alert = await self._create_alert(
                db,
                user_id=wellness_check.user_id,
                wellness_check_id=wellness_check.id,
                alert_type="excessive_hours",
                severity=AlertSeverity.MEDIUM,
                title="Excessive Work Hours",
                description=f"Worked {wellness_check.hours_worked_last_week} hours last week",
                trigger_metrics={"hours_worked": wellness_check.hours_worked_last_week}
            )
            alerts.append(alert)
        
        return alerts

    async def _create_alert(
        self,
        db: AsyncSession,
        user_id: UUID,
        wellness_check_id: UUID,
        alert_type: str,
        severity: AlertSeverity,
        title: str,
        description: str,
        trigger_metrics: Dict[str, Any]
    ) -> WellnessAlert:
        """Create a wellness alert."""
        alert = WellnessAlert(
            user_id=user_id,
            wellness_check_id=wellness_check_id,
            alert_type=alert_type,
            severity=severity,
            status=AlertStatus.ACTIVE,
            title=title,
            description=description,
            triggered_date=datetime.utcnow(),
            manager_notified=False,
            hr_notified=severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL],
            follow_up_required=True,
            trigger_metrics=trigger_metrics,
            recommended_actions=self._get_recommended_actions(alert_type, severity)
        )
        
        db.add(alert)
        await db.flush()
        return alert

    def _get_recommended_actions(self, alert_type: str, severity: AlertSeverity) -> Dict[str, Any]:
        """Get recommended actions for an alert type."""
        actions = {
            "burnout_risk": [
                "Schedule a one-on-one with manager",
                "Consider workload adjustment",
                "Access mental health resources",
                "Take time off if needed"
            ],
            "high_stress": [
                "Practice stress management techniques",
                "Consider flexible work arrangements",
                "Speak with manager about workload",
                "Access employee assistance program"
            ],
            "low_wellness": [
                "Schedule wellness check-in",
                "Review work-life balance",
                "Consider professional development opportunities",
                "Access wellness resources"
            ],
            "excessive_hours": [
                "Review workload distribution",
                "Discuss time management strategies",
                "Consider delegation opportunities",
                "Enforce work-life boundaries"
            ]
        }
        
        return {"actions": actions.get(alert_type, [])}


class WellnessAlertService(BaseService[WellnessAlert, WellnessAlertCreate, WellnessAlertUpdate]):
    """Service for managing wellness alerts."""

    def __init__(self):
        super().__init__(WellnessAlert)

    async def get_active_alerts(
        self,
        db: AsyncSession,
        *,
        user_id: Optional[UUID] = None,
        severity: Optional[AlertSeverity] = None,
        limit: int = 50
    ) -> List[WellnessAlert]:
        """Get active wellness alerts."""
        query = select(WellnessAlert).where(WellnessAlert.status == AlertStatus.ACTIVE)
        
        if user_id:
            query = query.where(WellnessAlert.user_id == user_id)
        if severity:
            query = query.where(WellnessAlert.severity == severity)
        
        query = query.order_by(
            WellnessAlert.severity.desc(),
            desc(WellnessAlert.triggered_date)
        ).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def acknowledge_alert(
        self,
        db: AsyncSession,
        alert_id: UUID,
        acknowledged_by_id: UUID,
        *,
        follow_up_date: Optional[datetime] = None
    ) -> WellnessAlert:
        """Acknowledge a wellness alert."""
        alert = await self.get(db, alert_id)
        if not alert:
            raise ValueError(f"Alert with ID {alert_id} not found")
        
        alert.status = AlertStatus.ACKNOWLEDGED
        alert.acknowledged_by_id = acknowledged_by_id
        alert.acknowledged_date = datetime.utcnow()
        
        if follow_up_date:
            alert.follow_up_date = follow_up_date
        
        await db.flush()
        await db.refresh(alert)
        return alert

    async def resolve_alert(
        self,
        db: AsyncSession,
        alert_id: UUID,
        *,
        resolution_notes: Optional[str] = None,
        actions_taken: Optional[Dict[str, Any]] = None
    ) -> WellnessAlert:
        """Resolve a wellness alert."""
        alert = await self.get(db, alert_id)
        if not alert:
            raise ValueError(f"Alert with ID {alert_id} not found")
        
        alert.status = AlertStatus.RESOLVED
        alert.resolved_date = datetime.utcnow()
        
        if resolution_notes:
            alert.resolution_notes = resolution_notes
        if actions_taken:
            alert.actions_taken = actions_taken
        
        await db.flush()
        await db.refresh(alert)
        return alert


class WorkScheduleService(BaseService[WorkSchedule, WorkScheduleCreate, WorkScheduleUpdate]):
    """Service for managing work schedules."""

    def __init__(self):
        super().__init__(WorkSchedule)

    async def create_work_schedule(
        self,
        db: AsyncSession,
        *,
        schedule_data: WorkScheduleCreate
    ) -> WorkSchedule:
        """Create a new work schedule."""
        # Verify user exists
        user_result = await db.execute(select(User).where(User.id == schedule_data.user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {schedule_data.user_id} not found")

        # Deactivate existing active schedules
        await db.execute(
            select(WorkSchedule).where(
                and_(
                    WorkSchedule.user_id == schedule_data.user_id,
                    WorkSchedule.is_active == True
                )
            )
        )
        existing_schedules = await db.execute(
            select(WorkSchedule).where(
                and_(
                    WorkSchedule.user_id == schedule_data.user_id,
                    WorkSchedule.is_active == True
                )
            )
        )
        for schedule in existing_schedules.scalars().all():
            schedule.is_active = False

        work_schedule = WorkSchedule(
            user_id=schedule_data.user_id,
            schedule_name=schedule_data.schedule_name,
            schedule_type=schedule_data.schedule_type,
            is_active=schedule_data.is_active,
            effective_start_date=schedule_data.effective_start_date,
            effective_end_date=schedule_data.effective_end_date,
            standard_hours_per_week=schedule_data.standard_hours_per_week,
            core_hours_start=schedule_data.core_hours_start,
            core_hours_end=schedule_data.core_hours_end,
            timezone=schedule_data.timezone,
            weekly_schedule=schedule_data.weekly_schedule,
            allows_flexible_start=schedule_data.allows_flexible_start,
            allows_flexible_end=schedule_data.allows_flexible_end,
            allows_remote_work=schedule_data.allows_remote_work,
            remote_work_days=schedule_data.remote_work_days,
            lunch_break_duration=schedule_data.lunch_break_duration,
            short_break_frequency=schedule_data.short_break_frequency,
            approved_by_id=schedule_data.approved_by_id,
            notes=schedule_data.notes,
            employee_comments=schedule_data.employee_comments,
            manager_comments=schedule_data.manager_comments
        )
        
        db.add(work_schedule)
        await db.flush()
        await db.refresh(work_schedule)
        return work_schedule

    async def get_user_active_schedule(
        self,
        db: AsyncSession,
        user_id: UUID
    ) -> Optional[WorkSchedule]:
        """Get the active work schedule for a user."""
        query = select(WorkSchedule).where(
            and_(
                WorkSchedule.user_id == user_id,
                WorkSchedule.is_active == True
            )
        )
        
        result = await db.execute(query)
        return result.scalar_one_or_none()


class WellnessAnalyticsService:
    """Service for wellness analytics and reporting."""

    async def get_wellness_metrics(
        self,
        db: AsyncSession,
        user_id: Optional[UUID] = None,
        *,
        period_days: int = 90
    ) -> WellnessMetrics:
        """Get wellness metrics for a user or organization."""
        end_date = date.today()
        start_date = end_date - timedelta(days=period_days)
        
        query = select(WellnessCheck).where(
            and_(
                WellnessCheck.check_date >= start_date,
                WellnessCheck.check_date <= end_date
            )
        )
        
        if user_id:
            query = query.where(WellnessCheck.user_id == user_id)
        
        checks_result = await db.execute(query)
        checks = list(checks_result.scalars().all())
        
        if not checks:
            return WellnessMetrics(
                user_id=user_id,
                period_start=start_date,
                period_end=end_date,
                average_wellness_score=0.0,
                average_stress_level=0.0,
                average_energy_level=0.0,
                average_job_satisfaction=0.0,
                average_work_life_balance=0.0,
                average_burnout_risk=0.0,
                total_checks=0,
                active_alerts=0,
                resolved_alerts=0,
                wellness_trend={},
                risk_factors=[],
                improvement_areas=[]
            )
        
        # Calculate averages
        avg_wellness = sum(c.overall_wellness_score for c in checks) / len(checks)
        avg_stress = sum(c.stress_level for c in checks) / len(checks)
        avg_energy = sum(c.energy_level for c in checks) / len(checks)
        avg_job_satisfaction = sum(c.job_satisfaction for c in checks) / len(checks)
        avg_work_life_balance = sum(c.work_life_balance for c in checks) / len(checks)
        avg_burnout_risk = sum(c.burnout_risk_score for c in checks) / len(checks)
        
        # Get alert counts
        alert_query = select(WellnessAlert).where(
            WellnessAlert.triggered_date >= datetime.combine(start_date, datetime.min.time())
        )
        if user_id:
            alert_query = alert_query.where(WellnessAlert.user_id == user_id)
        
        alerts_result = await db.execute(alert_query)
        alerts = list(alerts_result.scalars().all())
        
        active_alerts = len([a for a in alerts if a.status == AlertStatus.ACTIVE])
        resolved_alerts = len([a for a in alerts if a.status == AlertStatus.RESOLVED])
        
        # Identify risk factors and improvement areas
        risk_factors = []
        improvement_areas = []
        
        if avg_stress >= 7:
            risk_factors.append("High stress levels")
        if avg_burnout_risk >= 0.6:
            risk_factors.append("Elevated burnout risk")
        if avg_work_life_balance <= 5:
            improvement_areas.append("Work-life balance")
        if avg_job_satisfaction <= 6:
            improvement_areas.append("Job satisfaction")
        
        # Create trend data (simplified)
        wellness_trend = {}
        for i, check in enumerate(checks[-10:]):  # Last 10 checks
            wellness_trend[f"check_{i}"] = check.overall_wellness_score
        
        return WellnessMetrics(
            user_id=user_id,
            period_start=start_date,
            period_end=end_date,
            average_wellness_score=avg_wellness,
            average_stress_level=avg_stress,
            average_energy_level=avg_energy,
            average_job_satisfaction=avg_job_satisfaction,
            average_work_life_balance=avg_work_life_balance,
            average_burnout_risk=avg_burnout_risk,
            total_checks=len(checks),
            active_alerts=active_alerts,
            resolved_alerts=resolved_alerts,
            wellness_trend=wellness_trend,
            risk_factors=risk_factors,
            improvement_areas=improvement_areas
        )
