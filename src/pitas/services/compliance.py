"""Compliance and audit trail service for Phase 8."""

import hashlib
import json
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from .base import BaseService
from ..db.models.compliance import (
    ComplianceMapping, AuditTrail, ComplianceReport, ComplianceEvidence,
    ComplianceFramework, AuditEventType, ControlStatus
)
from ..schemas.compliance import (
    ComplianceMappingCreate, ComplianceMappingUpdate,
    AuditTrailCreate, ComplianceReportCreate,
    ComplianceEvidenceCreate
)
from ..core.exceptions import AppException

logger = logging.getLogger(__name__)


class ComplianceService(BaseService[ComplianceMapping, ComplianceMappingCreate, ComplianceMappingUpdate]):
    """Service for managing compliance mappings and controls."""
    
    def __init__(self):
        super().__init__(ComplianceMapping)
    
    async def get_by_framework(
        self,
        db: AsyncSession,
        framework: ComplianceFramework,
        status: Optional[ControlStatus] = None
    ) -> List[ComplianceMapping]:
        """Get compliance mappings by framework."""
        stmt = select(ComplianceMapping).where(ComplianceMapping.framework == framework)
        
        if status:
            stmt = stmt.where(ComplianceMapping.implementation_status == status)
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_control_by_id(
        self,
        db: AsyncSession,
        framework: ComplianceFramework,
        control_id: str
    ) -> Optional[ComplianceMapping]:
        """Get specific control by framework and control ID."""
        stmt = select(ComplianceMapping).where(
            and_(
                ComplianceMapping.framework == framework,
                ComplianceMapping.control_id == control_id
            )
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_overdue_controls(
        self,
        db: AsyncSession,
        framework: Optional[ComplianceFramework] = None
    ) -> List[ComplianceMapping]:
        """Get controls that are overdue for testing."""
        now = datetime.utcnow()
        stmt = select(ComplianceMapping).where(
            and_(
                ComplianceMapping.next_test_due < now,
                ComplianceMapping.implementation_status.in_([
                    ControlStatus.IMPLEMENTED,
                    ControlStatus.EFFECTIVE
                ])
            )
        )
        
        if framework:
            stmt = stmt.where(ComplianceMapping.framework == framework)
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def update_control_test(
        self,
        db: AsyncSession,
        control_id: UUID,
        tested_by: UUID,
        test_results: Dict[str, Any],
        next_test_days: Optional[int] = None
    ) -> ComplianceMapping:
        """Update control testing information."""
        control = await self.get(db, control_id)
        if not control:
            raise AppException(f"Control not found: {control_id}")
        
        now = datetime.utcnow()
        control.last_tested_at = now
        control.last_tested_by = tested_by
        
        # Calculate next test due date
        test_frequency = next_test_days or control.testing_frequency_days
        control.next_test_due = now + timedelta(days=test_frequency)
        
        # Update status based on test results
        if test_results.get("passed", False):
            control.implementation_status = ControlStatus.EFFECTIVE
        else:
            control.implementation_status = ControlStatus.NEEDS_IMPROVEMENT
        
        # Update implementation notes with test results
        test_summary = f"Test conducted on {now.isoformat()}: "
        test_summary += "PASSED" if test_results.get("passed") else "FAILED"
        if test_results.get("notes"):
            test_summary += f" - {test_results['notes']}"
        
        if control.implementation_notes:
            control.implementation_notes += f"\n{test_summary}"
        else:
            control.implementation_notes = test_summary
        
        await db.flush()
        await db.refresh(control)
        return control
    
    async def get_compliance_dashboard(
        self,
        db: AsyncSession,
        framework: Optional[ComplianceFramework] = None
    ) -> Dict[str, Any]:
        """Get compliance dashboard summary."""
        stmt = select(ComplianceMapping)
        if framework:
            stmt = stmt.where(ComplianceMapping.framework == framework)
        
        result = await db.execute(stmt)
        controls = result.scalars().all()
        
        # Calculate statistics
        total_controls = len(controls)
        status_counts = {}
        overdue_count = 0
        now = datetime.utcnow()
        
        for control in controls:
            status = control.implementation_status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            
            if (control.next_test_due and control.next_test_due < now and
                control.implementation_status in [ControlStatus.IMPLEMENTED, ControlStatus.EFFECTIVE]):
                overdue_count += 1
        
        # Calculate compliance percentage
        effective_controls = status_counts.get(ControlStatus.EFFECTIVE.value, 0)
        implemented_controls = status_counts.get(ControlStatus.IMPLEMENTED.value, 0)
        compliance_percentage = ((effective_controls + implemented_controls) / total_controls * 100) if total_controls > 0 else 0
        
        return {
            "total_controls": total_controls,
            "compliance_percentage": round(compliance_percentage, 2),
            "status_breakdown": status_counts,
            "overdue_tests": overdue_count,
            "framework": framework.value if framework else "all",
            "last_updated": datetime.utcnow().isoformat()
        }


class AuditTrailService:
    """Service for managing audit trail entries."""
    
    async def create_audit_entry(
        self,
        db: AsyncSession,
        event_type: AuditEventType,
        event_name: str,
        event_description: str,
        user_id: Optional[UUID] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[UUID] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        project_id: Optional[UUID] = None,
        client_id: Optional[UUID] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AuditTrail:
        """Create a new audit trail entry."""
        
        # Create audit entry
        audit_entry = AuditTrail(
            event_type=event_type,
            event_name=event_name,
            event_description=event_description,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            old_values=old_values,
            new_values=new_values,
            project_id=project_id,
            client_id=client_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            event_metadata=metadata,
            event_timestamp=datetime.utcnow()
        )
        
        # Generate integrity checksum
        audit_entry.checksum = self._generate_checksum(audit_entry)
        
        db.add(audit_entry)
        await db.flush()
        await db.refresh(audit_entry)
        
        logger.info(f"Audit entry created: {event_type.value} - {event_name}")
        return audit_entry
    
    def _generate_checksum(self, audit_entry: AuditTrail) -> str:
        """Generate SHA-256 checksum for audit entry integrity."""
        data = {
            "event_type": audit_entry.event_type.value,
            "event_name": audit_entry.event_name,
            "event_description": audit_entry.event_description,
            "user_id": str(audit_entry.user_id) if audit_entry.user_id else None,
            "resource_type": audit_entry.resource_type,
            "resource_id": str(audit_entry.resource_id) if audit_entry.resource_id else None,
            "old_values": audit_entry.old_values,
            "new_values": audit_entry.new_values,
            "event_timestamp": audit_entry.event_timestamp.isoformat()
        }
        
        json_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(json_str.encode()).hexdigest()
    
    async def get_audit_trail(
        self,
        db: AsyncSession,
        user_id: Optional[UUID] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[UUID] = None,
        event_type: Optional[AuditEventType] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[AuditTrail]:
        """Get audit trail entries with filtering."""
        stmt = select(AuditTrail).order_by(desc(AuditTrail.event_timestamp))
        
        # Apply filters
        filters = []
        if user_id:
            filters.append(AuditTrail.user_id == user_id)
        if resource_type:
            filters.append(AuditTrail.resource_type == resource_type)
        if resource_id:
            filters.append(AuditTrail.resource_id == resource_id)
        if event_type:
            filters.append(AuditTrail.event_type == event_type)
        if start_date:
            filters.append(AuditTrail.event_timestamp >= start_date)
        if end_date:
            filters.append(AuditTrail.event_timestamp <= end_date)
        
        if filters:
            stmt = stmt.where(and_(*filters))
        
        stmt = stmt.limit(limit).offset(offset)
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def verify_audit_integrity(
        self,
        db: AsyncSession,
        audit_id: UUID
    ) -> bool:
        """Verify audit entry integrity using checksum."""
        stmt = select(AuditTrail).where(AuditTrail.id == audit_id)
        result = await db.execute(stmt)
        audit_entry = result.scalar_one_or_none()
        
        if not audit_entry:
            return False
        
        expected_checksum = self._generate_checksum(audit_entry)
        return audit_entry.checksum == expected_checksum
