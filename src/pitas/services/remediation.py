"""Remediation workflow and automation service."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.core.escalation import EscalationEngine, SeverityLevel, EscalationLevel, SLATracker
from pitas.core.config import settings
from pitas.db.models.remediation import (
    Remediation, 
    RemediationEscalation, 
    RemediationComment,
    RemediationStatus
)
from pitas.schemas.remediation import (
    RemediationCreate,
    RemediationUpdate,
    RemediationAssignRequest,
    RemediationVerifyRequest,
    RemediationCloseRequest,
    RemediationCommentCreate,
    TicketingIntegrationRequest,
    TicketingIntegrationResponse
)
from pitas.services.base import BaseService


class RemediationService(BaseService[Remediation, RemediationCreate, RemediationUpdate]):
    """Service for managing remediation workflows and automation."""
    
    def __init__(self):
        """Initialize the remediation service."""
        super().__init__(Remediation)
        self.escalation_engine = EscalationEngine()
    
    async def create_remediation(
        self,
        db: AsyncSession,
        remediation_data: RemediationCreate,
        created_by: UUID
    ) -> Remediation:
        """Create a new remediation with SLA tracking.
        
        Args:
            db: Database session
            remediation_data: Remediation creation data
            created_by: User creating the remediation
            
        Returns:
            Created remediation
        """
        # Calculate SLA dates
        now = datetime.utcnow()
        sla_config = self.escalation_engine.sla_configs[remediation_data.severity]
        
        response_due = now + timedelta(hours=sla_config.response_time_hours)
        resolution_due = now + timedelta(hours=sla_config.resolution_time_hours)
        
        # Create remediation
        remediation = Remediation(
            id=uuid4(),
            title=remediation_data.title,
            description=remediation_data.description,
            project_id=remediation_data.project_id,
            vulnerability_id=remediation_data.vulnerability_id,
            severity=remediation_data.severity,
            priority=remediation_data.priority,
            cvss_score=remediation_data.cvss_score,
            assigned_to=remediation_data.assigned_to,
            assigned_team=remediation_data.assigned_team,
            system_owner=remediation_data.system_owner,
            response_due=response_due,
            resolution_due=resolution_due,
            affected_systems=remediation_data.affected_systems,
            remediation_steps=remediation_data.remediation_steps,
            business_impact=remediation_data.business_impact,
            technical_details=remediation_data.technical_details,
            verification_required=remediation_data.verification_required,
            status=RemediationStatus.IDENTIFIED
        )
        
        # Set assignment timestamp if assigned
        if remediation_data.assigned_to:
            remediation.assigned_at = now
            remediation.status = RemediationStatus.ASSIGNED
        
        db.add(remediation)
        await db.flush()
        await db.refresh(remediation)
        
        # Create initial comment
        initial_comment = RemediationComment(
            id=uuid4(),
            remediation_id=remediation.id,
            author_id=created_by,
            content=f"Remediation created for {remediation_data.severity} severity issue",
            comment_type="status_update"
        )
        db.add(initial_comment)
        
        await db.flush()
        return remediation
    
    async def assign_remediation(
        self,
        db: AsyncSession,
        remediation_id: UUID,
        assign_request: RemediationAssignRequest,
        assigned_by: UUID
    ) -> Optional[Remediation]:
        """Assign a remediation to a user or team.
        
        Args:
            db: Database session
            remediation_id: Remediation ID
            assign_request: Assignment request data
            assigned_by: User performing the assignment
            
        Returns:
            Updated remediation or None if not found
        """
        remediation = await self.get(db, remediation_id)
        if not remediation:
            return None
        
        # Update assignment
        remediation.assigned_to = assign_request.assigned_to
        remediation.assigned_team = assign_request.assigned_team
        remediation.system_owner = assign_request.system_owner
        remediation.assigned_at = datetime.utcnow()
        
        # Update status
        if remediation.status == RemediationStatus.IDENTIFIED:
            remediation.status = RemediationStatus.ASSIGNED
        
        # Add assignment comment
        if assign_request.notes:
            comment = RemediationComment(
                id=uuid4(),
                remediation_id=remediation_id,
                author_id=assigned_by,
                content=assign_request.notes,
                comment_type="assignment"
            )
            db.add(comment)
        
        await db.flush()
        await db.refresh(remediation)
        return remediation
    
    async def update_remediation_status(
        self,
        db: AsyncSession,
        remediation_id: UUID,
        new_status: RemediationStatus,
        user_id: UUID,
        notes: Optional[str] = None
    ) -> Optional[Remediation]:
        """Update remediation status with tracking.
        
        Args:
            db: Database session
            remediation_id: Remediation ID
            new_status: New status
            user_id: User updating the status
            notes: Optional status update notes
            
        Returns:
            Updated remediation or None if not found
        """
        remediation = await self.get(db, remediation_id)
        if not remediation:
            return None
        
        old_status = remediation.status
        remediation.status = new_status
        
        # Handle status-specific updates
        if new_status == RemediationStatus.IN_PROGRESS and not remediation.first_response_at:
            remediation.first_response_at = datetime.utcnow()
        elif new_status == RemediationStatus.VERIFIED:
            remediation.resolved_at = datetime.utcnow()
        
        # Add status update comment
        comment_content = f"Status changed from {old_status} to {new_status}"
        if notes:
            comment_content += f"\n\nNotes: {notes}"
        
        comment = RemediationComment(
            id=uuid4(),
            remediation_id=remediation_id,
            author_id=user_id,
            content=comment_content,
            comment_type="status_update"
        )
        db.add(comment)
        
        await db.flush()
        await db.refresh(remediation)
        return remediation
    
    async def verify_remediation(
        self,
        db: AsyncSession,
        remediation_id: UUID,
        verify_request: RemediationVerifyRequest,
        verified_by: UUID
    ) -> Optional[Remediation]:
        """Verify a remediation fix.
        
        Args:
            db: Database session
            remediation_id: Remediation ID
            verify_request: Verification request data
            verified_by: User performing verification
            
        Returns:
            Updated remediation or None if not found
        """
        remediation = await self.get(db, remediation_id)
        if not remediation:
            return None
        
        remediation.verified_by = verified_by
        remediation.verified_at = datetime.utcnow()
        remediation.verification_notes = verify_request.verification_notes
        
        if verify_request.is_verified:
            remediation.status = RemediationStatus.VERIFIED
            remediation.resolved_at = datetime.utcnow()
        else:
            remediation.status = RemediationStatus.REOPENED
        
        # Add verification comment
        comment = RemediationComment(
            id=uuid4(),
            remediation_id=remediation_id,
            author_id=verified_by,
            content=f"Verification {'passed' if verify_request.is_verified else 'failed'}: {verify_request.verification_notes}",
            comment_type="verification"
        )
        db.add(comment)
        
        await db.flush()
        await db.refresh(remediation)
        return remediation
    
    async def close_remediation(
        self,
        db: AsyncSession,
        remediation_id: UUID,
        close_request: RemediationCloseRequest,
        closed_by: UUID
    ) -> Optional[Remediation]:
        """Close a remediation.
        
        Args:
            db: Database session
            remediation_id: Remediation ID
            close_request: Close request data
            closed_by: User closing the remediation
            
        Returns:
            Updated remediation or None if not found
        """
        remediation = await self.get(db, remediation_id)
        if not remediation:
            return None
        
        remediation.status = RemediationStatus.CLOSED
        remediation.closed_by = closed_by
        remediation.closed_at = datetime.utcnow()
        remediation.closure_reason = close_request.closure_reason
        
        if not remediation.resolved_at:
            remediation.resolved_at = datetime.utcnow()
        
        # Add closure comment
        comment_content = f"Remediation closed: {close_request.closure_reason}"
        if close_request.notes:
            comment_content += f"\n\nNotes: {close_request.notes}"
        
        comment = RemediationComment(
            id=uuid4(),
            remediation_id=remediation_id,
            author_id=closed_by,
            content=comment_content,
            comment_type="closure"
        )
        db.add(comment)
        
        await db.flush()
        await db.refresh(remediation)
        return remediation
    
    async def check_sla_breaches(
        self,
        db: AsyncSession,
        project_id: Optional[UUID] = None
    ) -> List[Remediation]:
        """Check for SLA breaches and trigger escalations.
        
        Args:
            db: Database session
            project_id: Optional project ID to filter by
            
        Returns:
            List of remediations with SLA breaches
        """
        # Build query for active remediations
        query = select(Remediation).where(
            and_(
                Remediation.status.in_([
                    RemediationStatus.IDENTIFIED,
                    RemediationStatus.ASSIGNED,
                    RemediationStatus.IN_PROGRESS,
                    RemediationStatus.PENDING_VERIFICATION
                ]),
                or_(
                    Remediation.sla_breached == False,
                    Remediation.sla_breached.is_(None)
                )
            )
        )
        
        if project_id:
            query = query.where(Remediation.project_id == project_id)
        
        result = await db.execute(query)
        remediations = result.scalars().all()
        
        breached_remediations = []
        now = datetime.utcnow()
        
        for remediation in remediations:
            # Create SLA tracker
            sla_tracker = self.escalation_engine.create_sla_tracker(
                remediation_id=remediation.id,
                severity=remediation.severity,
                created_at=remediation.created_at
            )
            
            # Set response time if available
            if remediation.first_response_at:
                sla_tracker.first_response_at = remediation.first_response_at
            
            # Set resolution time if available
            if remediation.resolved_at:
                sla_tracker.resolved_at = remediation.resolved_at
            
            # Check for breach
            if self.escalation_engine.check_sla_breach(sla_tracker, now):
                remediation.sla_breached = True
                remediation.breach_reason = sla_tracker.breach_reason
                breached_remediations.append(remediation)
                
                # Check for required escalations
                required_escalations = self.escalation_engine.get_required_escalations(sla_tracker, now)
                
                for escalation_level in required_escalations:
                    await self._create_escalation(db, remediation, escalation_level, sla_tracker)
        
        await db.flush()
        return breached_remediations
    
    async def _create_escalation(
        self,
        db: AsyncSession,
        remediation: Remediation,
        level: EscalationLevel,
        sla_tracker: SLATracker
    ) -> RemediationEscalation:
        """Create an escalation record.
        
        Args:
            db: Database session
            remediation: Remediation instance
            level: Escalation level
            sla_tracker: SLA tracker
            
        Returns:
            Created escalation
        """
        escalation_event = self.escalation_engine.trigger_escalation(
            tracker=sla_tracker,
            level=level,
            message=f"SLA breach escalation for {remediation.severity} severity remediation: {remediation.title}"
        )
        
        escalation = RemediationEscalation(
            id=escalation_event.id,
            remediation_id=remediation.id,
            level=level,
            triggered_at=escalation_event.triggered_at,
            assignee_id=escalation_event.assignee_id,
            message=escalation_event.message,
            escalation_metadata=escalation_event.metadata
        )
        
        db.add(escalation)
        await db.flush()
        return escalation
    
    async def get_remediation_metrics(
        self,
        db: AsyncSession,
        project_id: Optional[UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get remediation metrics and statistics.
        
        Args:
            db: Database session
            project_id: Optional project ID filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            Metrics dictionary
        """
        # Build base query
        query = select(Remediation)
        
        filters = []
        if project_id:
            filters.append(Remediation.project_id == project_id)
        if start_date:
            filters.append(Remediation.created_at >= start_date)
        if end_date:
            filters.append(Remediation.created_at <= end_date)
        
        if filters:
            query = query.where(and_(*filters))
        
        result = await db.execute(query)
        remediations = result.scalars().all()
        
        # Calculate metrics
        total_count = len(remediations)
        if total_count == 0:
            return {
                "total_remediations": 0,
                "by_severity": {},
                "by_status": {},
                "sla_compliance_rate": 0.0,
                "average_resolution_time_hours": 0.0,
                "escalation_rate": 0.0,
                "overdue_count": 0
            }
        
        # Count by severity
        by_severity = {}
        for severity in SeverityLevel:
            by_severity[severity] = len([r for r in remediations if r.severity == severity])
        
        # Count by status
        by_status = {}
        for status in RemediationStatus:
            by_status[status] = len([r for r in remediations if r.status == status])
        
        # Calculate SLA compliance
        sla_compliant = len([r for r in remediations if not r.sla_breached])
        sla_compliance_rate = sla_compliant / total_count
        
        # Calculate average resolution time
        resolved_remediations = [r for r in remediations if r.resolved_at]
        if resolved_remediations:
            total_resolution_time = sum([
                (r.resolved_at - r.created_at).total_seconds() / 3600
                for r in resolved_remediations
            ])
            average_resolution_time = total_resolution_time / len(resolved_remediations)
        else:
            average_resolution_time = 0.0
        
        # Calculate escalation rate
        escalated_count = len([r for r in remediations if r.escalations])
        escalation_rate = escalated_count / total_count
        
        # Count overdue
        now = datetime.utcnow()
        overdue_count = len([
            r for r in remediations 
            if r.status not in [RemediationStatus.CLOSED, RemediationStatus.VERIFIED]
            and r.resolution_due < now
        ])
        
        return {
            "total_remediations": total_count,
            "by_severity": by_severity,
            "by_status": by_status,
            "sla_compliance_rate": sla_compliance_rate,
            "average_resolution_time_hours": average_resolution_time,
            "escalation_rate": escalation_rate,
            "overdue_count": overdue_count
        }
