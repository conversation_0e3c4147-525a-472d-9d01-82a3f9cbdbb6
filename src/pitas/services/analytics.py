"""Analytics service for Phase 9: Advanced Analytics and Reporting Engine."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from uuid import UUID
import structlog

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.analytics import (
    AnalyticsModel, ModelTrainingJob, AnalyticsPrediction,
    AnalyticsReport, AnalyticsAlert, AnalyticsMetric, AnalyticsDashboard
)
from pitas.schemas.analytics import (
    AnalyticsModelCreate, AnalyticsModelUpdate, AnalyticsModelResponse,
    ModelTrainingJobCreate, ModelTrainingJobResponse,
    AnalyticsPredictionCreate, AnalyticsPredictionResponse,
    AnalyticsReportCreate, AnalyticsReportResponse,
    AnalyticsAlertCreate, AnalyticsAlertUpdate, AnalyticsAlertResponse,
    AnalyticsDashboardCreate, AnalyticsDashboardUpdate, AnalyticsDashboardResponse,
    PredictiveInsights, AnalyticsSummary, ModelType, TrainingStatus, AlertStatus
)
from pitas.core.analytics import security_analytics_engine

logger = structlog.get_logger(__name__)


class AnalyticsService:
    """Service for managing analytics and ML operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.analytics_engine = security_analytics_engine
    
    # Model Management
    async def create_model(self, model_data: AnalyticsModelCreate) -> AnalyticsModelResponse:
        """Create a new analytics model."""
        logger.info("Creating analytics model", name=model_data.name, type=model_data.model_type)
        
        model = AnalyticsModel(
            name=model_data.name,
            model_type=model_data.model_type,
            version=model_data.version,
            description=model_data.description,
            hyperparameters=model_data.hyperparameters,
            feature_columns=model_data.feature_columns,
            target_column=model_data.target_column,
            is_active=model_data.is_active
        )
        
        self.db.add(model)
        await self.db.commit()
        await self.db.refresh(model)
        
        return AnalyticsModelResponse.model_validate(model)
    
    async def get_model(self, model_id: UUID) -> Optional[AnalyticsModelResponse]:
        """Get analytics model by ID."""
        result = await self.db.execute(
            select(AnalyticsModel).where(AnalyticsModel.id == model_id)
        )
        model = result.scalar_one_or_none()
        
        if model:
            return AnalyticsModelResponse.model_validate(model)
        return None
    
    async def list_models(
        self, 
        model_type: Optional[ModelType] = None,
        is_active: Optional[bool] = None,
        page: int = 1,
        page_size: int = 50
    ) -> List[AnalyticsModelResponse]:
        """List analytics models with filtering."""
        query = select(AnalyticsModel)
        
        if model_type:
            query = query.where(AnalyticsModel.model_type == model_type)
        if is_active is not None:
            query = query.where(AnalyticsModel.is_active == is_active)
        
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        result = await self.db.execute(query)
        models = result.scalars().all()
        
        return [AnalyticsModelResponse.model_validate(model) for model in models]
    
    async def update_model(self, model_id: UUID, model_data: AnalyticsModelUpdate) -> Optional[AnalyticsModelResponse]:
        """Update analytics model."""
        result = await self.db.execute(
            select(AnalyticsModel).where(AnalyticsModel.id == model_id)
        )
        model = result.scalar_one_or_none()
        
        if not model:
            return None
        
        update_data = model_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(model, field, value)
        
        await self.db.commit()
        await self.db.refresh(model)
        
        return AnalyticsModelResponse.model_validate(model)
    
    async def delete_model(self, model_id: UUID) -> bool:
        """Delete analytics model."""
        result = await self.db.execute(
            select(AnalyticsModel).where(AnalyticsModel.id == model_id)
        )
        model = result.scalar_one_or_none()
        
        if not model:
            return False
        
        await self.db.delete(model)
        await self.db.commit()
        return True
    
    # Training Job Management
    async def create_training_job(self, job_data: ModelTrainingJobCreate) -> ModelTrainingJobResponse:
        """Create a new model training job."""
        logger.info("Creating training job", model_id=job_data.model_id, name=job_data.job_name)
        
        job = ModelTrainingJob(
            model_id=job_data.model_id,
            job_name=job_data.job_name,
            status=TrainingStatus.PENDING,
            training_config=job_data.training_config,
            dataset_size=job_data.dataset_size,
            validation_split=job_data.validation_split
        )
        
        self.db.add(job)
        await self.db.commit()
        await self.db.refresh(job)
        
        # Start training asynchronously
        asyncio.create_task(self._execute_training_job(job.id))
        
        return ModelTrainingJobResponse.model_validate(job)
    
    async def _execute_training_job(self, job_id: UUID):
        """Execute model training job asynchronously."""
        try:
            # Get job details
            result = await self.db.execute(
                select(ModelTrainingJob).where(ModelTrainingJob.id == job_id)
            )
            job = result.scalar_one_or_none()
            
            if not job:
                return
            
            # Update job status
            job.status = TrainingStatus.RUNNING
            job.started_at = datetime.utcnow()
            await self.db.commit()
            
            # Simulate training process
            await asyncio.sleep(5)  # Simulate training time
            
            # Mock training results
            job.status = TrainingStatus.COMPLETED
            job.completed_at = datetime.utcnow()
            job.final_accuracy = 0.85
            job.final_loss = 0.15
            job.training_metrics = {"epochs": 100, "learning_rate": 0.001}
            job.validation_metrics = {"val_accuracy": 0.83, "val_loss": 0.17}
            job.cpu_hours = 2.5
            job.memory_peak_gb = 4.2
            
            await self.db.commit()
            
            logger.info("Training job completed", job_id=job_id, accuracy=job.final_accuracy)
            
        except Exception as e:
            logger.error("Training job failed", job_id=job_id, error=str(e))
            
            # Update job with error
            job.status = TrainingStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            await self.db.commit()
    
    # Prediction Management
    async def create_prediction(self, prediction_data: AnalyticsPredictionCreate) -> AnalyticsPredictionResponse:
        """Create a new prediction."""
        logger.info("Creating prediction", model_id=prediction_data.model_id, type=prediction_data.prediction_type)
        
        # Mock prediction result
        prediction_result = {
            "prediction": "high_risk",
            "probability": 0.78,
            "risk_score": 8.5
        }
        
        prediction = AnalyticsPrediction(
            model_id=prediction_data.model_id,
            prediction_type=prediction_data.prediction_type,
            input_data=prediction_data.input_data,
            prediction_result=prediction_result,
            confidence_score=0.78,
            entity_type=prediction_data.entity_type,
            entity_id=prediction_data.entity_id,
            feature_importance={"severity": 0.4, "age": 0.3, "complexity": 0.3}
        )
        
        self.db.add(prediction)
        await self.db.commit()
        await self.db.refresh(prediction)
        
        return AnalyticsPredictionResponse.model_validate(prediction)
    
    # Report Generation
    async def generate_report(self, report_data: AnalyticsReportCreate, user_id: UUID) -> AnalyticsReportResponse:
        """Generate analytics report."""
        logger.info("Generating analytics report", name=report_data.report_name, type=report_data.report_type)
        
        start_time = datetime.utcnow()
        
        # Mock report generation
        report_content = {
            "executive_summary": "Security posture has improved by 15% this quarter",
            "key_metrics": {
                "vulnerabilities_resolved": 245,
                "mean_time_to_resolution": "5.2 days",
                "security_score": 8.7
            },
            "trends": {
                "vulnerability_discovery": "decreasing",
                "remediation_velocity": "increasing"
            }
        }
        
        generation_time = (datetime.utcnow() - start_time).total_seconds()
        
        report = AnalyticsReport(
            report_name=report_data.report_name,
            report_type=report_data.report_type,
            report_format=report_data.report_format,
            generated_by=user_id,
            generation_time_seconds=generation_time,
            data_period_start=report_data.data_period_start,
            data_period_end=report_data.data_period_end,
            report_data=report_content,
            report_summary="Comprehensive security analytics report showing positive trends",
            key_findings=["Vulnerability resolution improved", "Team efficiency increased"],
            recommendations=["Continue current practices", "Invest in automation"],
            recipients=report_data.recipients,
            report_config=report_data.report_config
        )
        
        self.db.add(report)
        await self.db.commit()
        await self.db.refresh(report)
        
        return AnalyticsReportResponse.model_validate(report)
    
    # Alert Management
    async def create_alert(self, alert_data: AnalyticsAlertCreate) -> AnalyticsAlertResponse:
        """Create analytics alert."""
        logger.info("Creating analytics alert", type=alert_data.alert_type, severity=alert_data.severity)
        
        alert = AnalyticsAlert(
            alert_type=alert_data.alert_type,
            severity=alert_data.severity,
            title=alert_data.title,
            description=alert_data.description,
            trigger_condition=alert_data.trigger_condition,
            threshold_value=alert_data.threshold_value,
            actual_value=alert_data.actual_value,
            entity_type=alert_data.entity_type,
            entity_id=alert_data.entity_id,
            recommended_actions=["Investigate immediately", "Escalate to security team"]
        )
        
        self.db.add(alert)
        await self.db.commit()
        await self.db.refresh(alert)
        
        return AnalyticsAlertResponse.model_validate(alert)
    
    async def update_alert(self, alert_id: UUID, alert_data: AnalyticsAlertUpdate) -> Optional[AnalyticsAlertResponse]:
        """Update analytics alert."""
        result = await self.db.execute(
            select(AnalyticsAlert).where(AnalyticsAlert.id == alert_id)
        )
        alert = result.scalar_one_or_none()
        
        if not alert:
            return None
        
        update_data = alert_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(alert, field, value)
        
        if alert_data.status == AlertStatus.ACKNOWLEDGED:
            alert.acknowledged_at = datetime.utcnow()
        elif alert_data.status == AlertStatus.RESOLVED:
            alert.resolved_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(alert)
        
        return AnalyticsAlertResponse.model_validate(alert)
    
    # Predictive Insights
    async def generate_predictive_insights(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> PredictiveInsights:
        """Generate ML-powered predictive insights."""
        logger.info("Generating predictive insights", start=start_date, end=end_date)
        
        return await self.analytics_engine.generate_predictive_insights(
            (start_date, end_date), self.db
        )
    
    # Analytics Summary
    async def get_analytics_summary(self) -> AnalyticsSummary:
        """Get analytics summary for dashboards."""
        # Get model counts
        total_models_result = await self.db.execute(select(func.count(AnalyticsModel.id)))
        total_models = total_models_result.scalar()
        
        active_models_result = await self.db.execute(
            select(func.count(AnalyticsModel.id)).where(AnalyticsModel.is_active == True)
        )
        active_models = active_models_result.scalar()
        
        # Get prediction count
        total_predictions_result = await self.db.execute(select(func.count(AnalyticsPrediction.id)))
        total_predictions = total_predictions_result.scalar()
        
        # Get average accuracy
        avg_accuracy_result = await self.db.execute(
            select(func.avg(AnalyticsModel.accuracy_score)).where(AnalyticsModel.accuracy_score.isnot(None))
        )
        avg_accuracy = avg_accuracy_result.scalar() or 0.0
        
        # Get recent alerts
        recent_alerts_result = await self.db.execute(
            select(AnalyticsAlert)
            .where(AnalyticsAlert.created_at >= datetime.utcnow() - timedelta(days=7))
            .order_by(AnalyticsAlert.created_at.desc())
            .limit(5)
        )
        recent_alerts = [
            AnalyticsAlertResponse.model_validate(alert) 
            for alert in recent_alerts_result.scalars().all()
        ]
        
        return AnalyticsSummary(
            total_models=total_models,
            active_models=active_models,
            total_predictions=total_predictions,
            average_accuracy=float(avg_accuracy),
            recent_alerts=recent_alerts,
            top_insights=[
                "Vulnerability discovery rate decreasing",
                "Team efficiency improving",
                "Cloud security posture strengthening"
            ],
            performance_metrics={
                "model_accuracy": float(avg_accuracy),
                "prediction_latency": 0.15,
                "alert_response_time": 2.3
            }
        )
