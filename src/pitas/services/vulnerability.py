"""Vulnerability management service."""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Tuple, Dict, Any
from uuid import UUID

from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.vulnerability import (
    Vulnerability, VulnerabilityMetric,
    VulnerabilitySeverity, VulnerabilityStatus
)
from pitas.db.models.asset import (
    Asset, AssetVulnerability
)
from pitas.schemas.vulnerability import (
    VulnerabilityCreate, VulnerabilityUpdate, VulnerabilitySearchFilters
)
from pitas.services.base import BaseService


class VulnerabilityService(BaseService[Vulnerability, VulnerabilityCreate, VulnerabilityUpdate]):
    """Service for vulnerability management operations."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Vulnerability, db)
    
    async def create_vulnerability(self, vulnerability_data: VulnerabilityCreate) -> Vulnerability:
        """Create a new vulnerability.
        
        Args:
            vulnerability_data: Vulnerability creation data
            
        Returns:
            Vulnerability: Created vulnerability
        """
        vulnerability = Vulnerability(**vulnerability_data.model_dump())
        self.db.add(vulnerability)
        await self.db.commit()
        await self.db.refresh(vulnerability)
        return vulnerability
    
    async def get_vulnerability_by_id(self, vulnerability_id: UUID) -> Optional[Vulnerability]:
        """Get vulnerability by ID.
        
        Args:
            vulnerability_id: Vulnerability ID
            
        Returns:
            Optional[Vulnerability]: Vulnerability if found
        """
        stmt = select(Vulnerability).where(Vulnerability.id == vulnerability_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_vulnerability_by_cve(self, cve_id: str) -> Optional[Vulnerability]:
        """Get vulnerability by CVE ID.
        
        Args:
            cve_id: CVE identifier
            
        Returns:
            Optional[Vulnerability]: Vulnerability if found
        """
        stmt = select(Vulnerability).where(Vulnerability.cve_id == cve_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_vulnerability(
        self, 
        vulnerability_id: UUID, 
        vulnerability_data: VulnerabilityUpdate
    ) -> Optional[Vulnerability]:
        """Update vulnerability.
        
        Args:
            vulnerability_id: Vulnerability ID
            vulnerability_data: Update data
            
        Returns:
            Optional[Vulnerability]: Updated vulnerability if found
        """
        vulnerability = await self.get_vulnerability_by_id(vulnerability_id)
        if not vulnerability:
            return None
        
        update_data = vulnerability_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(vulnerability, field, value)
        
        await self.db.commit()
        await self.db.refresh(vulnerability)
        return vulnerability
    
    async def delete_vulnerability(self, vulnerability_id: UUID) -> bool:
        """Delete vulnerability.
        
        Args:
            vulnerability_id: Vulnerability ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        vulnerability = await self.get_vulnerability_by_id(vulnerability_id)
        if not vulnerability:
            return False
        
        await self.db.delete(vulnerability)
        await self.db.commit()
        return True
    
    async def search_vulnerabilities(
        self,
        filters: VulnerabilitySearchFilters,
        page: int = 1,
        page_size: int = 50,
    ) -> Tuple[List[Vulnerability], int]:
        """Search vulnerabilities with filters and pagination.
        
        Args:
            filters: Search filters
            page: Page number
            page_size: Items per page
            
        Returns:
            Tuple[List[Vulnerability], int]: Vulnerabilities and total count
        """
        stmt = select(Vulnerability)
        count_stmt = select(func.count(Vulnerability.id))
        
        # Apply filters
        conditions = []
        
        if filters.severity:
            conditions.append(Vulnerability.severity.in_(filters.severity))
        
        if filters.status:
            conditions.append(Vulnerability.status.in_(filters.status))
        
        if filters.cvss_score_min is not None:
            conditions.append(Vulnerability.cvss_score >= filters.cvss_score_min)
        
        if filters.cvss_score_max is not None:
            conditions.append(Vulnerability.cvss_score <= filters.cvss_score_max)
        
        if filters.discovery_date_from:
            conditions.append(Vulnerability.discovery_date >= filters.discovery_date_from)
        
        if filters.discovery_date_to:
            conditions.append(Vulnerability.discovery_date <= filters.discovery_date_to)
        
        if filters.source:
            conditions.append(Vulnerability.source == filters.source)
        
        if filters.tags:
            # Check if any of the filter tags are in the vulnerability tags
            tag_conditions = [
                Vulnerability.tags.contains([tag]) for tag in filters.tags
            ]
            conditions.append(or_(*tag_conditions))
        
        if filters.asset_ids:
            # Join with AssetVulnerability to filter by assets
            stmt = stmt.join(AssetVulnerability).where(
                AssetVulnerability.asset_id.in_(filters.asset_ids)
            )
            count_stmt = count_stmt.join(AssetVulnerability).where(
                AssetVulnerability.asset_id.in_(filters.asset_ids)
            )
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
            count_stmt = count_stmt.where(and_(*conditions))
        
        # Apply pagination
        offset = (page - 1) * page_size
        stmt = stmt.order_by(desc(Vulnerability.discovery_date)).offset(offset).limit(page_size)
        
        # Execute queries
        result = await self.db.execute(stmt)
        vulnerabilities = result.scalars().all()
        
        count_result = await self.db.execute(count_stmt)
        total_count = count_result.scalar()
        
        return list(vulnerabilities), total_count
    
    async def get_vulnerability_assets(self, vulnerability_id: UUID) -> List[Asset]:
        """Get assets associated with a vulnerability.
        
        Args:
            vulnerability_id: Vulnerability ID
            
        Returns:
            List[Asset]: Associated assets
        """
        stmt = (
            select(Asset)
            .join(AssetVulnerability)
            .where(AssetVulnerability.vulnerability_id == vulnerability_id)
        )
        
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_dashboard_summary(self) -> Dict[str, Any]:
        """Get vulnerability dashboard summary data.
        
        Returns:
            Dict[str, Any]: Dashboard summary
        """
        # Total vulnerabilities
        total_stmt = select(func.count(Vulnerability.id))
        total_result = await self.db.execute(total_stmt)
        total_vulnerabilities = total_result.scalar()
        
        # By severity
        severity_stmt = (
            select(Vulnerability.severity, func.count(Vulnerability.id))
            .group_by(Vulnerability.severity)
        )
        severity_result = await self.db.execute(severity_stmt)
        by_severity = {row[0].value: row[1] for row in severity_result.all()}
        
        # By status
        status_stmt = (
            select(Vulnerability.status, func.count(Vulnerability.id))
            .group_by(Vulnerability.status)
        )
        status_result = await self.db.execute(status_stmt)
        by_status = {row[0].value: row[1] for row in status_result.all()}
        
        # Critical assets affected
        critical_assets_stmt = (
            select(func.count(func.distinct(Asset.id)))
            .join(AssetVulnerability)
            .join(Vulnerability)
            .where(
                and_(
                    Asset.business_criticality == "critical",
                    Vulnerability.severity.in_(["critical", "high"])
                )
            )
        )
        critical_assets_result = await self.db.execute(critical_assets_stmt)
        critical_assets_affected = critical_assets_result.scalar()
        
        # Average remediation time
        remediation_time_stmt = (
            select(
                func.avg(
                    func.extract('epoch', Vulnerability.remediation_date - Vulnerability.discovery_date) / 86400
                )
            )
            .where(
                and_(
                    Vulnerability.status == VulnerabilityStatus.REMEDIATED,
                    Vulnerability.remediation_date.is_not(None)
                )
            )
        )
        remediation_time_result = await self.db.execute(remediation_time_stmt)
        avg_remediation_time = remediation_time_result.scalar()
        
        # Vulnerability density (vulnerabilities per asset)
        try:
            density_stmt = (
                select(func.count(Vulnerability.id) / func.count(func.distinct(Asset.id)))
                .join(AssetVulnerability)
                .join(Asset)
            )
            density_result = await self.db.execute(density_stmt)
            vulnerability_density = density_result.scalar()
        except Exception:
            vulnerability_density = None
        
        # Trend data (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        trend_stmt = (
            select(
                func.date(Vulnerability.discovery_date).label('date'),
                func.count(Vulnerability.id).label('count')
            )
            .where(Vulnerability.discovery_date >= thirty_days_ago)
            .group_by(func.date(Vulnerability.discovery_date))
            .order_by(func.date(Vulnerability.discovery_date))
        )
        trend_result = await self.db.execute(trend_stmt)
        trend_data = [
            {"date": row.date.isoformat(), "count": row.count}
            for row in trend_result.all()
        ]
        
        return {
            "total_vulnerabilities": total_vulnerabilities,
            "by_severity": by_severity,
            "by_status": by_status,
            "critical_assets_affected": critical_assets_affected,
            "average_remediation_time_days": float(avg_remediation_time) if avg_remediation_time else None,
            "vulnerability_density": float(vulnerability_density) if vulnerability_density else None,
            "trend_data": trend_data,
        }
    
    async def get_vulnerabilities_by_severity(
        self, 
        severity: VulnerabilitySeverity
    ) -> List[Vulnerability]:
        """Get vulnerabilities by severity level.
        
        Args:
            severity: Severity level
            
        Returns:
            List[Vulnerability]: Vulnerabilities with specified severity
        """
        stmt = select(Vulnerability).where(Vulnerability.severity == severity)
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_overdue_vulnerabilities(self, days: int = 30) -> List[Vulnerability]:
        """Get vulnerabilities that are overdue for remediation.
        
        Args:
            days: Number of days to consider overdue
            
        Returns:
            List[Vulnerability]: Overdue vulnerabilities
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        stmt = (
            select(Vulnerability)
            .where(
                and_(
                    Vulnerability.discovery_date <= cutoff_date,
                    Vulnerability.status.in_([
                        VulnerabilityStatus.DISCOVERED,
                        VulnerabilityStatus.CONFIRMED,
                        VulnerabilityStatus.TRIAGED,
                        VulnerabilityStatus.IN_PROGRESS
                    ])
                )
            )
            .order_by(Vulnerability.discovery_date)
        )
        
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
