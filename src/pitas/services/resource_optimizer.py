"""Resource optimization service for Phase 2 AI-driven allocation."""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession

from ..db.models import (
    PentesterProfile, Project, ResourceAllocation, SkillMatrix, CapacityPlan,
    SecurityDomain, CertificationTier
)
from ..core.workflow import WorkflowStatus
from ..schemas.resource_allocation import AllocationConflict
from ..core.exceptions import AppException

logger = logging.getLogger(__name__)


class ConstraintViolation(Exception):
    """Exception raised when allocation constraints are violated."""
    pass


class SkillMatchingConstraint:
    """Constraint for matching pentester skills to project requirements."""
    
    def __init__(self, minimum_match_score: float = 70.0):
        self.minimum_match_score = minimum_match_score
    
    def validate(self, pentester: PentesterProfile, project: Project) -> <PERSON><PERSON>[bool, float]:
        """Validate skill matching constraint."""
        if not project.required_skills:
            return True, 100.0
        
        # Calculate skill match score based on pentester's skill matrices
        match_score = self._calculate_skill_match(pentester, project.required_skills)
        return match_score >= self.minimum_match_score, match_score
    
    def _calculate_skill_match(self, pentester: PentesterProfile, required_skills: List[str]) -> float:
        """Calculate skill match score between pentester and required skills."""
        if not required_skills:
            return 100.0
        
        pentester_skills = {}
        for skill_matrix in pentester.skill_matrices:
            pentester_skills[skill_matrix.security_domain.value] = skill_matrix.skill_level
        
        matched_skills = 0
        total_skill_level = 0
        
        for skill in required_skills:
            if skill in pentester_skills:
                matched_skills += 1
                total_skill_level += pentester_skills[skill]
        
        if matched_skills == 0:
            return 0.0
        
        # Calculate match percentage and skill quality
        match_percentage = (matched_skills / len(required_skills)) * 100
        average_skill_level = total_skill_level / matched_skills
        skill_quality = (average_skill_level / 10) * 100
        
        # Weighted score: 70% match percentage, 30% skill quality
        return (match_percentage * 0.7) + (skill_quality * 0.3)


class CapacityConstraint:
    """Constraint for pentester capacity and availability."""
    
    def __init__(self, max_utilization: float = 90.0):
        self.max_utilization = max_utilization
    
    def validate(self, pentester: PentesterProfile, allocation_hours: float) -> Tuple[bool, str]:
        """Validate capacity constraint."""
        available_hours = pentester.available_hours_remaining
        
        if allocation_hours > available_hours:
            return False, f"Insufficient capacity: needs {allocation_hours}h, has {available_hours}h"
        
        new_utilization = ((pentester.availability_hours - available_hours + allocation_hours) / 
                          pentester.availability_hours) * 100
        
        if new_utilization > self.max_utilization:
            return False, f"Would exceed max utilization: {new_utilization:.1f}% > {self.max_utilization}%"
        
        return True, "Capacity available"


class ProjectPriorityConstraint:
    """Constraint for project priority-based allocation."""
    
    def __init__(self):
        self.priority_weights = {
            "emergency": 100,
            "critical": 80,
            "high": 60,
            "medium": 40,
            "low": 20
        }
    
    def get_priority_score(self, project: Project) -> int:
        """Get priority score for project."""
        return self.priority_weights.get(project.priority.value, 40)


class GeographicDistributionConstraint:
    """Constraint for geographic and timezone distribution."""
    
    def validate(self, pentester: PentesterProfile, project: Project) -> Tuple[bool, str]:
        """Validate geographic distribution constraint."""
        # Check timezone compatibility
        if project.client_timezone and pentester.working_timezone:
            # Simple timezone compatibility check (can be enhanced)
            if not self._timezones_compatible(pentester.working_timezone.value, project.client_timezone):
                return False, f"Timezone incompatible: {pentester.working_timezone.value} vs {project.client_timezone}"
        
        # Check location constraints
        if project.location_constraints and pentester.location:
            if not any(constraint.lower() in pentester.location.lower() 
                      for constraint in project.location_constraints):
                return False, f"Location constraint not met: {pentester.location}"
        
        # Check onsite requirements
        if project.requires_onsite and not pentester.location:
            return False, "Onsite required but pentester location unknown"
        
        return True, "Geographic constraints satisfied"
    
    def _timezones_compatible(self, pentester_tz: str, client_tz: str) -> bool:
        """Check if timezones are compatible for collaboration."""
        # Simplified timezone compatibility logic
        # In production, this would use proper timezone calculations
        compatible_zones = {
            "UTC": ["Europe/London", "Europe/Berlin"],
            "America/New_York": ["America/Los_Angeles"],
            "Europe/London": ["UTC", "Europe/Berlin"],
            "Europe/Berlin": ["UTC", "Europe/London"],
            "Asia/Tokyo": ["Asia/Kolkata"],
            "Australia/Sydney": ["Asia/Tokyo"]
        }
        
        return client_tz in compatible_zones.get(pentester_tz, [client_tz])


class AllocationPlan:
    """Represents an optimized allocation plan."""
    
    def __init__(self):
        self.allocations: List[Dict[str, Any]] = []
        self.unallocated_projects: List[UUID] = []
        self.conflicts: List[AllocationConflict] = []
        self.optimization_score: float = 0.0
        self.total_utilization: float = 0.0
        self.skill_coverage: Dict[str, float] = {}
    
    def add_allocation(self, pentester_id: UUID, project_id: UUID, 
                      allocation_data: Dict[str, Any]) -> None:
        """Add an allocation to the plan."""
        self.allocations.append({
            "pentester_id": pentester_id,
            "project_id": project_id,
            **allocation_data
        })
    
    def add_conflict(self, conflict: AllocationConflict) -> None:
        """Add a conflict to the plan."""
        self.conflicts.append(conflict)


class ResourceOptimizer:
    """AI-driven resource optimization service for Phase 2."""
    
    def __init__(self):
        self.constraints = [
            SkillMatchingConstraint(),
            CapacityConstraint(),
            ProjectPriorityConstraint(),
            GeographicDistributionConstraint()
        ]
    
    async def optimize_team_allocation(
        self,
        db: AsyncSession,
        projects: List[Project],
        team_members: List[PentesterProfile]
    ) -> AllocationPlan:
        """Optimize resource allocation using constraint satisfaction."""
        logger.info(f"Starting optimization for {len(projects)} projects and {len(team_members)} team members")
        
        plan = AllocationPlan()
        
        # Sort projects by priority and deadline
        sorted_projects = self._sort_projects_by_priority(projects)
        
        # Create allocation matrix
        allocation_matrix = await self._create_allocation_matrix(db, sorted_projects, team_members)
        
        # Solve allocation using constraint satisfaction
        for project in sorted_projects:
            best_allocation = await self._find_best_allocation(
                db, project, team_members, allocation_matrix
            )
            
            if best_allocation:
                plan.add_allocation(
                    best_allocation["pentester_id"],
                    project.id,
                    best_allocation
                )
                # Update team member availability
                self._update_availability(team_members, best_allocation)
            else:
                plan.unallocated_projects.append(project.id)
                logger.warning(f"Could not allocate project {project.project_code}")
        
        # Calculate optimization metrics
        plan.optimization_score = self._calculate_optimization_score(plan)
        plan.total_utilization = self._calculate_total_utilization(team_members)
        plan.skill_coverage = self._calculate_skill_coverage(plan, projects)
        
        # Detect conflicts
        conflicts = await self._detect_allocation_conflicts(db, plan)
        plan.conflicts = conflicts
        
        logger.info(f"Optimization complete. Score: {plan.optimization_score:.2f}, "
                   f"Utilization: {plan.total_utilization:.1f}%, "
                   f"Conflicts: {len(conflicts)}")
        
        return plan
    
    def _sort_projects_by_priority(self, projects: List[Project]) -> List[Project]:
        """Sort projects by priority and deadline."""
        priority_constraint = ProjectPriorityConstraint()
        
        def sort_key(project):
            priority_score = priority_constraint.get_priority_score(project)
            # Add deadline urgency
            deadline_score = 0
            if project.deadline:
                days_to_deadline = (project.deadline - datetime.utcnow()).days
                deadline_score = max(0, 100 - days_to_deadline)  # More urgent = higher score
            
            return -(priority_score + deadline_score)  # Negative for descending sort
        
        return sorted(projects, key=sort_key)
    
    async def _create_allocation_matrix(
        self,
        db: AsyncSession,
        projects: List[Project],
        team_members: List[PentesterProfile]
    ) -> Dict[Tuple[UUID, UUID], float]:
        """Create allocation compatibility matrix."""
        matrix = {}
        
        for project in projects:
            for pentester in team_members:
                score = await self._calculate_allocation_score(db, pentester, project)
                matrix[(pentester.id, project.id)] = score
        
        return matrix
    
    async def _calculate_allocation_score(
        self,
        db: AsyncSession,
        pentester: PentesterProfile,
        project: Project
    ) -> float:
        """Calculate allocation compatibility score."""
        total_score = 0.0
        weight_sum = 0.0
        
        # Skill matching (40% weight)
        skill_constraint = SkillMatchingConstraint()
        skill_valid, skill_score = skill_constraint.validate(pentester, project)
        if skill_valid:
            total_score += skill_score * 0.4
            weight_sum += 0.4
        
        # Capacity availability (30% weight)
        capacity_constraint = CapacityConstraint()
        estimated_hours = project.estimated_hours or 40.0  # Default estimate
        capacity_valid, _ = capacity_constraint.validate(pentester, estimated_hours)
        if capacity_valid:
            # Score based on available capacity
            capacity_score = min(100, (pentester.available_hours_remaining / estimated_hours) * 100)
            total_score += capacity_score * 0.3
            weight_sum += 0.3
        
        # Geographic compatibility (20% weight)
        geo_constraint = GeographicDistributionConstraint()
        geo_valid, _ = geo_constraint.validate(pentester, project)
        if geo_valid:
            total_score += 100 * 0.2
            weight_sum += 0.2
        
        # Performance history (10% weight)
        if pentester.performance_rating:
            performance_score = (pentester.performance_rating / 5.0) * 100
            total_score += performance_score * 0.1
            weight_sum += 0.1
        
        return total_score / weight_sum if weight_sum > 0 else 0.0
    
    async def _find_best_allocation(
        self,
        db: AsyncSession,
        project: Project,
        team_members: List[PentesterProfile],
        allocation_matrix: Dict[Tuple[UUID, UUID], float]
    ) -> Optional[Dict[str, Any]]:
        """Find the best allocation for a project."""
        best_score = 0.0
        best_pentester = None
        
        for pentester in team_members:
            if not pentester.is_active:
                continue
            
            score = allocation_matrix.get((pentester.id, project.id), 0.0)
            
            # Check if pentester has enough capacity
            estimated_hours = project.estimated_hours or 40.0
            if pentester.available_hours_remaining < estimated_hours:
                continue
            
            if score > best_score:
                best_score = score
                best_pentester = pentester
        
        if not best_pentester:
            return None
        
        # Create allocation data
        estimated_hours = project.estimated_hours or 40.0
        duration_days = (project.end_date - project.start_date).days if project.start_date and project.end_date else 30
        hours_per_week = estimated_hours / (duration_days / 7) if duration_days > 0 else 40
        
        return {
            "pentester_id": best_pentester.id,
            "allocated_hours": estimated_hours,
            "hours_per_week": min(hours_per_week, best_pentester.availability_hours),
            "allocation_start_date": project.start_date or datetime.utcnow(),
            "allocation_end_date": project.end_date or datetime.utcnow() + timedelta(days=30),
            "utilization_percentage": (estimated_hours / best_pentester.availability_hours) * 100,
            "skill_match_score": best_score,
            "status": WorkflowStatus.NOT_STARTED,
            "role": "team_lead" if project.requires_team_lead and best_pentester.is_team_lead else "pentester"
        }
    
    def _update_availability(self, team_members: List[PentesterProfile], allocation: Dict[str, Any]) -> None:
        """Update team member availability after allocation."""
        pentester_id = allocation["pentester_id"]
        allocated_hours = allocation["allocated_hours"]
        
        for pentester in team_members:
            if pentester.id == pentester_id:
                # Update current utilization
                new_utilization = pentester.current_utilization + allocation["utilization_percentage"]
                pentester.current_utilization = min(new_utilization, 100.0)
                break
    
    def _calculate_optimization_score(self, plan: AllocationPlan) -> float:
        """Calculate overall optimization score."""
        if not plan.allocations:
            return 0.0
        
        total_score = sum(alloc.get("skill_match_score", 0) for alloc in plan.allocations)
        average_score = total_score / len(plan.allocations)
        
        # Penalty for unallocated projects
        penalty = len(plan.unallocated_projects) * 10
        
        # Penalty for conflicts
        conflict_penalty = len(plan.conflicts) * 5
        
        return max(0, average_score - penalty - conflict_penalty)
    
    def _calculate_total_utilization(self, team_members: List[PentesterProfile]) -> float:
        """Calculate total team utilization."""
        if not team_members:
            return 0.0
        
        total_utilization = sum(member.current_utilization for member in team_members)
        return total_utilization / len(team_members)
    
    def _calculate_skill_coverage(self, plan: AllocationPlan, projects: List[Project]) -> Dict[str, float]:
        """Calculate skill coverage across projects."""
        skill_coverage = {}
        
        # Count required skills across all projects
        required_skills = {}
        for project in projects:
            if project.required_skills:
                for skill in project.required_skills:
                    required_skills[skill] = required_skills.get(skill, 0) + 1
        
        # Count covered skills in allocations
        covered_skills = {}
        for allocation in plan.allocations:
            # This would need to be enhanced to track actual skills used
            pass
        
        # Calculate coverage percentage
        for skill, required_count in required_skills.items():
            covered_count = covered_skills.get(skill, 0)
            skill_coverage[skill] = (covered_count / required_count) * 100 if required_count > 0 else 0
        
        return skill_coverage
    
    async def _detect_allocation_conflicts(self, db: AsyncSession, plan: AllocationPlan) -> List[AllocationConflict]:
        """Detect allocation conflicts in the plan."""
        conflicts = []
        
        # Group allocations by pentester
        pentester_allocations = {}
        for allocation in plan.allocations:
            pentester_id = allocation["pentester_id"]
            if pentester_id not in pentester_allocations:
                pentester_allocations[pentester_id] = []
            pentester_allocations[pentester_id].append(allocation)
        
        # Check for over-allocation
        for pentester_id, allocations in pentester_allocations.items():
            total_utilization = sum(alloc.get("utilization_percentage", 0) for alloc in allocations)
            
            if total_utilization > 100:
                # Get pentester name
                stmt = select(PentesterProfile).where(PentesterProfile.id == pentester_id)
                result = await db.execute(stmt)
                pentester = result.scalar_one_or_none()
                
                if pentester:
                    conflict = AllocationConflict(
                        pentester_id=pentester_id,
                        pentester_name=pentester.full_name,
                        conflict_type="over_allocation",
                        conflicting_allocations=[UUID(str(alloc["project_id"])) for alloc in allocations],
                        total_utilization=total_utilization,
                        recommended_action="Reduce allocation hours or reassign projects",
                        severity="high" if total_utilization > 120 else "medium"
                    )
                    conflicts.append(conflict)
        
        return conflicts
