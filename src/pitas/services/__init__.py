"""Services package for business logic."""

# Import all services for easy access
from .base import BaseService
from .user import UserService

# Phase 5: Training services
from .training import (
    CompetencyService,
    TrainingService,
    CertificationService,
    CTFService,
    MentorshipService as TrainingMentorshipService,
)

# Phase 6: Career development services
from .career import (
    CareerDevelopmentService,
    DevelopmentGoalService,
    DevelopmentActivityService,
    CareerAnalyticsService,
)

from .recognition import (
    RecognitionService,
    PeerNominationService,
    RewardService,
    RecognitionAnalyticsService,
)

from .wellness import (
    WellnessService,
    WellnessAlertService,
    WorkScheduleService,
    WellnessAnalyticsService,
)

from .mentorship import (
    MentorshipService,
    MentorshipSessionService,
    MentorProfileService,
    MentorshipRequestService,
    MentorshipAnalyticsService,
)

# Phase 8: Compliance and Audit Trail services
from .compliance import (
    ComplianceService,
    AuditTrailService,
)

__all__ = [
    # Base service
    "BaseService",
    # User service
    "UserService",
    # Phase 5: Training services
    "CompetencyService",
    "TrainingService",
    "CertificationService",
    "CTFService",
    "TrainingMentorshipService",
    # Phase 6: Career development services
    "CareerDevelopmentService",
    "DevelopmentGoalService",
    "DevelopmentActivityService",
    "CareerAnalyticsService",
    # Recognition services
    "RecognitionService",
    "PeerNominationService",
    "RewardService",
    "RecognitionAnalyticsService",
    # Wellness services
    "WellnessService",
    "WellnessAlertService",
    "WorkScheduleService",
    "WellnessAnalyticsService",
    # Mentorship services
    "MentorshipService",
    "MentorshipSessionService",
    "MentorProfileService",
    "MentorshipRequestService",
    "MentorshipAnalyticsService",
    # Phase 8: Compliance services
    "ComplianceService",
    "AuditTrailService",
]