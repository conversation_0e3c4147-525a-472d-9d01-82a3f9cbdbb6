"""Project service for Phase 2."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from .base import BaseService
from ..db.models import Project, ResourceAllocation, PentesterProfile
from ..schemas.project import (
    ProjectCreate, ProjectUpdate
)
from ..core.exceptions import AppException

logger = logging.getLogger(__name__)


class ProjectService(BaseService[Project, ProjectCreate, ProjectUpdate]):
    """Service for managing projects."""
    
    def __init__(self):
        super().__init__(Project)
    
    async def get_by_project_code(
        self,
        db: AsyncSession,
        project_code: str
    ) -> Optional[Project]:
        """Get project by project code."""
        stmt = select(Project).where(Project.project_code == project_code)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_with_allocations(
        self,
        db: AsyncSession,
        project_id: UUID
    ) -> Optional[Project]:
        """Get project with resource allocations loaded."""
        stmt = (
            select(Project)
            .options(selectinload(Project.resource_allocations))
            .where(Project.id == project_id)
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_active_projects(
        self,
        db: AsyncSession,
        include_scheduled: bool = True
    ) -> List[Project]:
        """Get active projects."""
        statuses = ["in_progress"]
        if include_scheduled:
            statuses.append("scheduled")
        
        stmt = (
            select(Project)
            .where(
                and_(
                    Project.is_active == True,
                    Project.status.in_(statuses)
                )
            )
            .order_by(desc(Project.priority), Project.start_date)
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_projects_by_status(
        self,
        db: AsyncSession,
        status: str
    ) -> List[Project]:
        """Get projects by status."""
        stmt = (
            select(Project)
            .where(Project.status == status)
            .order_by(desc(Project.created_at))
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_projects_by_priority(
        self,
        db: AsyncSession,
        priority: str,
        active_only: bool = True
    ) -> List[Project]:
        """Get projects by priority."""
        stmt = select(Project).where(Project.priority == priority)
        
        if active_only:
            stmt = stmt.where(Project.is_active == True)
        
        stmt = stmt.order_by(Project.start_date)
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_overdue_projects(
        self,
        db: AsyncSession
    ) -> List[Project]:
        """Get overdue projects."""
        now = datetime.utcnow()
        stmt = (
            select(Project)
            .where(
                and_(
                    Project.deadline < now,
                    Project.status.in_(["planning", "scheduled", "in_progress"]),
                    Project.is_active == True
                )
            )
            .order_by(Project.deadline)
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_projects_by_client(
        self,
        db: AsyncSession,
        client_name: str
    ) -> List[Project]:
        """Get projects by client."""
        stmt = (
            select(Project)
            .where(Project.client_name.ilike(f"%{client_name}%"))
            .order_by(desc(Project.created_at))
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_projects_requiring_skills(
        self,
        db: AsyncSession,
        required_skills: List[str]
    ) -> List[Project]:
        """Get projects requiring specific skills."""
        stmt = (
            select(Project)
            .where(
                and_(
                    Project.required_skills.overlap(required_skills),
                    Project.is_active == True,
                    Project.status.in_(["planning", "scheduled"])
                )
            )
            .order_by(desc(Project.priority), Project.start_date)
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_unallocated_projects(
        self,
        db: AsyncSession
    ) -> List[Project]:
        """Get projects without resource allocations."""
        stmt = (
            select(Project)
            .outerjoin(ResourceAllocation)
            .where(
                and_(
                    ResourceAllocation.id.is_(None),
                    Project.is_active == True,
                    Project.status.in_(["planning", "scheduled"])
                )
            )
            .order_by(desc(Project.priority), Project.start_date)
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_resource_requirements(
        self,
        db: AsyncSession,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get project resource requirements."""
        project = await self.get(db, project_id)
        if not project:
            raise AppException(f"Project not found: {project_id}")
        
        return {
            "project_id": project.id,
            "project_name": project.name,
            "estimated_hours": project.estimated_hours,
            "start_date": project.start_date,
            "end_date": project.end_date
        }
    
    async def get_project_progress(
        self,
        db: AsyncSession,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get project progress information."""
        project = await self.get_with_allocations(db, project_id)
        if not project:
            raise AppException(f"Project not found: {project_id}")
        
        # Calculate team size from active allocations
        team_size = len([
            alloc for alloc in project.resource_allocations
            if alloc.status in ["approved", "active"]
        ])
        
        # Calculate hours remaining
        hours_remaining = None
        if project.estimated_hours and project.actual_hours:
            hours_remaining = max(0, project.estimated_hours - project.actual_hours)
        
        return {
            "project_id": project.id,
            "project_name": project.name,
            "workflow_status": project.workflow_status,
            "progress_percentage": project.progress_percentage,
            "estimated_hours": project.estimated_hours,
            "actual_hours": project.actual_hours,
            "hours_remaining": hours_remaining,
            "start_date": project.start_date,
            "end_date": project.end_date
        }
    
    async def update_progress(
        self,
        db: AsyncSession,
        project_id: UUID,
        completion_percentage: float,
        actual_hours: Optional[float] = None,
        quality_score: Optional[float] = None
    ) -> Project:
        """Update project progress."""
        project = await self.get(db, project_id)
        if not project:
            raise AppException(f"Project not found: {project_id}")
        
        project.completion_percentage = max(0.0, min(100.0, completion_percentage))
        
        if actual_hours is not None:
            project.actual_hours = actual_hours
        
        if quality_score is not None:
            project.quality_score = quality_score
        
        # Auto-update status based on completion
        if completion_percentage >= 100 and project.status != "completed":
            project.status = "completed"
            project.actual_end_date = datetime.utcnow()
        elif completion_percentage > 0 and project.status == "scheduled":
            project.status = "in_progress"
            if not project.actual_start_date:
                project.actual_start_date = datetime.utcnow()
        
        await db.flush()
        await db.refresh(project)
        return project
    
    async def get_project_statistics(
        self,
        db: AsyncSession,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get project statistics."""
        stmt = select(Project).where(Project.is_active == True)
        
        if start_date:
            stmt = stmt.where(Project.created_at >= start_date)
        if end_date:
            stmt = stmt.where(Project.created_at <= end_date)
        
        result = await db.execute(stmt)
        projects = result.scalars().all()
        
        if not projects:
            return {
                "total_projects": 0,
                "by_status": {},
                "by_priority": {},
                "by_type": {},
                "average_completion": 0,
                "overdue_count": 0
            }
        
        # Count by status
        status_counts = {}
        for project in projects:
            status = project.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # Count by priority
        priority_counts = {}
        for project in projects:
            priority = project.priority.value
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
        
        # Count by type
        type_counts = {}
        for project in projects:
            proj_type = project.project_type.value
            type_counts[proj_type] = type_counts.get(proj_type, 0) + 1
        
        # Calculate average completion
        total_completion = sum(p.completion_percentage for p in projects)
        average_completion = total_completion / len(projects)
        
        # Count overdue projects
        overdue_count = sum(1 for p in projects if p.is_overdue)
        
        return {
            "total_projects": len(projects),
            "by_status": status_counts,
            "by_priority": priority_counts,
            "by_type": type_counts,
            "average_completion": average_completion,
            "overdue_count": overdue_count
        }
    
    async def get_capacity_demand(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get capacity demand for a time period."""
        stmt = (
            select(Project)
            .where(
                and_(
                    Project.is_active == True,
                    Project.status.in_(["planning", "scheduled", "in_progress"]),
                    or_(
                        and_(Project.start_date >= start_date, Project.start_date <= end_date),
                        and_(Project.end_date >= start_date, Project.end_date <= end_date),
                        and_(Project.start_date <= start_date, Project.end_date >= end_date)
                    )
                )
            )
        )
        
        result = await db.execute(stmt)
        projects = result.scalars().all()
        
        total_estimated_hours = sum(p.estimated_hours or 0 for p in projects)
        total_team_members_needed = sum(p.required_team_size for p in projects)
        
        # Skill demand analysis
        skill_demand = {}
        for project in projects:
            if project.required_skills:
                for skill in project.required_skills:
                    skill_demand[skill] = skill_demand.get(skill, 0) + 1
        
        return {
            "period_start": start_date,
            "period_end": end_date,
            "total_projects": len(projects),
            "total_estimated_hours": total_estimated_hours,
            "total_team_members_needed": total_team_members_needed,
            "skill_demand": skill_demand,
            "projects_by_priority": {
                priority: len([p for p in projects if p.priority.value == priority])
                for priority in ["emergency", "critical", "high", "medium", "low"]
            }
        }
