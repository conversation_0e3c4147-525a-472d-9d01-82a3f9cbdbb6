"""Career development and progression services."""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.career import (
    IndividualDevelopmentPlan, DevelopmentGoal, DevelopmentActivity,
    IDPStatus, GoalStatus, ActivityStatus
)
from pitas.db.models.user import User, CareerTier, CareerTrack
from pitas.schemas.career import (
    IDPCreate, IDPUpdate, DevelopmentGoalCreate, DevelopmentGoalUpdate,
    DevelopmentActivityCreate, DevelopmentActivityUpdate, CareerProgressSummary
)
from pitas.services.base import BaseService


class CareerDevelopmentService(BaseService[IndividualDevelopmentPlan, IDPCreate, IDPUpdate]):
    """Service for managing Individual Development Plans."""

    def __init__(self):
        super().__init__(IndividualDevelopmentPlan)

    async def create_idp(
        self,
        db: AsyncSession,
        *,
        idp_data: IDPCreate,
        user_id: UUID
    ) -> IndividualDevelopmentPlan:
        """Create a new Individual Development Plan."""
        # Verify user exists
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        # Create IDP
        idp = IndividualDevelopmentPlan(
            title=idp_data.title,
            description=idp_data.description,
            status=idp_data.status,
            user_id=user_id,
            manager_id=idp_data.manager_id,
            start_date=idp_data.start_date,
            end_date=idp_data.end_date,
            current_tier=idp_data.current_tier,
            target_tier=idp_data.target_tier,
            career_track=idp_data.career_track,
            overall_progress=idp_data.overall_progress,
            last_review_notes=idp_data.last_review_notes,
            next_review_date=idp_data.next_review_date,
            metadata=idp_data.metadata
        )
        
        db.add(idp)
        await db.flush()
        await db.refresh(idp)
        return idp

    async def get_user_idps(
        self,
        db: AsyncSession,
        user_id: UUID,
        *,
        status: Optional[IDPStatus] = None,
        include_goals: bool = False
    ) -> List[IndividualDevelopmentPlan]:
        """Get all IDPs for a user."""
        query = select(IndividualDevelopmentPlan).where(
            IndividualDevelopmentPlan.user_id == user_id
        )
        
        if status:
            query = query.where(IndividualDevelopmentPlan.status == status)
        
        if include_goals:
            query = query.options(selectinload(IndividualDevelopmentPlan.goals))
        
        query = query.order_by(IndividualDevelopmentPlan.created_at.desc())
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_active_idp(
        self,
        db: AsyncSession,
        user_id: UUID
    ) -> Optional[IndividualDevelopmentPlan]:
        """Get the active IDP for a user."""
        query = select(IndividualDevelopmentPlan).where(
            and_(
                IndividualDevelopmentPlan.user_id == user_id,
                IndividualDevelopmentPlan.status == IDPStatus.ACTIVE
            )
        ).options(selectinload(IndividualDevelopmentPlan.goals))
        
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def update_progress(
        self,
        db: AsyncSession,
        idp_id: UUID,
        progress: float
    ) -> IndividualDevelopmentPlan:
        """Update IDP progress."""
        idp = await self.get(db, idp_id)
        if not idp:
            raise ValueError(f"IDP with ID {idp_id} not found")
        
        idp.overall_progress = max(0.0, min(1.0, progress))
        idp.updated_at = datetime.utcnow()
        
        await db.flush()
        await db.refresh(idp)
        return idp

    async def complete_idp(
        self,
        db: AsyncSession,
        idp_id: UUID,
        completion_notes: Optional[str] = None
    ) -> IndividualDevelopmentPlan:
        """Mark an IDP as completed."""
        idp = await self.get(db, idp_id)
        if not idp:
            raise ValueError(f"IDP with ID {idp_id} not found")
        
        idp.status = IDPStatus.COMPLETED
        idp.overall_progress = 1.0
        if completion_notes:
            idp.last_review_notes = completion_notes
        
        await db.flush()
        await db.refresh(idp)
        return idp


class DevelopmentGoalService(BaseService[DevelopmentGoal, DevelopmentGoalCreate, DevelopmentGoalUpdate]):
    """Service for managing Development Goals."""

    def __init__(self):
        super().__init__(DevelopmentGoal)

    async def create_goal(
        self,
        db: AsyncSession,
        *,
        goal_data: DevelopmentGoalCreate
    ) -> DevelopmentGoal:
        """Create a new development goal."""
        # Verify IDP exists
        idp_result = await db.execute(
            select(IndividualDevelopmentPlan).where(
                IndividualDevelopmentPlan.id == goal_data.idp_id
            )
        )
        idp = idp_result.scalar_one_or_none()
        if not idp:
            raise ValueError(f"IDP with ID {goal_data.idp_id} not found")

        goal = DevelopmentGoal(
            title=goal_data.title,
            description=goal_data.description,
            status=goal_data.status,
            priority=goal_data.priority,
            idp_id=goal_data.idp_id,
            target_date=goal_data.target_date,
            progress=goal_data.progress,
            success_criteria=goal_data.success_criteria,
            measurement_method=goal_data.measurement_method,
            required_resources=goal_data.required_resources,
            estimated_cost=goal_data.estimated_cost,
            assigned_mentor=goal_data.assigned_mentor,
            target_skills=goal_data.target_skills,
            current_skill_level=goal_data.current_skill_level,
            target_skill_level=goal_data.target_skill_level,
            notes=goal_data.notes,
            last_update_notes=goal_data.last_update_notes
        )
        
        db.add(goal)
        await db.flush()
        await db.refresh(goal)
        
        # Update IDP progress
        await self._update_idp_progress(db, goal_data.idp_id)
        
        return goal

    async def get_idp_goals(
        self,
        db: AsyncSession,
        idp_id: UUID,
        *,
        status: Optional[GoalStatus] = None,
        include_activities: bool = False
    ) -> List[DevelopmentGoal]:
        """Get all goals for an IDP."""
        query = select(DevelopmentGoal).where(DevelopmentGoal.idp_id == idp_id)
        
        if status:
            query = query.where(DevelopmentGoal.status == status)
        
        if include_activities:
            query = query.options(selectinload(DevelopmentGoal.activities))
        
        query = query.order_by(DevelopmentGoal.priority.desc(), DevelopmentGoal.created_at)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def update_goal_progress(
        self,
        db: AsyncSession,
        goal_id: UUID,
        progress: float,
        notes: Optional[str] = None
    ) -> DevelopmentGoal:
        """Update goal progress."""
        goal = await self.get(db, goal_id)
        if not goal:
            raise ValueError(f"Goal with ID {goal_id} not found")
        
        goal.progress = max(0.0, min(1.0, progress))
        if notes:
            goal.last_update_notes = notes
        
        # Auto-complete if progress is 100%
        if progress >= 1.0 and goal.status != GoalStatus.COMPLETED:
            goal.status = GoalStatus.COMPLETED
            goal.completed_date = datetime.utcnow()
        
        await db.flush()
        await db.refresh(goal)
        
        # Update IDP progress
        await self._update_idp_progress(db, goal.idp_id)
        
        return goal

    async def _update_idp_progress(self, db: AsyncSession, idp_id: UUID) -> None:
        """Update IDP progress based on goal completion."""
        # Get all goals for the IDP
        goals_result = await db.execute(
            select(DevelopmentGoal).where(DevelopmentGoal.idp_id == idp_id)
        )
        goals = list(goals_result.scalars().all())
        
        if not goals:
            return
        
        # Calculate overall progress
        total_progress = sum(goal.progress for goal in goals)
        overall_progress = total_progress / len(goals)
        
        # Update IDP
        idp_result = await db.execute(
            select(IndividualDevelopmentPlan).where(
                IndividualDevelopmentPlan.id == idp_id
            )
        )
        idp = idp_result.scalar_one_or_none()
        if idp:
            idp.overall_progress = overall_progress
            await db.flush()


class DevelopmentActivityService(BaseService[DevelopmentActivity, DevelopmentActivityCreate, DevelopmentActivityUpdate]):
    """Service for managing Development Activities."""

    def __init__(self):
        super().__init__(DevelopmentActivity)

    async def create_activity(
        self,
        db: AsyncSession,
        *,
        activity_data: DevelopmentActivityCreate
    ) -> DevelopmentActivity:
        """Create a new development activity."""
        # Verify goal exists
        goal_result = await db.execute(
            select(DevelopmentGoal).where(DevelopmentGoal.id == activity_data.goal_id)
        )
        goal = goal_result.scalar_one_or_none()
        if not goal:
            raise ValueError(f"Goal with ID {activity_data.goal_id} not found")

        activity = DevelopmentActivity(
            title=activity_data.title,
            description=activity_data.description,
            activity_type=activity_data.activity_type,
            status=activity_data.status,
            goal_id=activity_data.goal_id,
            planned_start_date=activity_data.planned_start_date,
            planned_end_date=activity_data.planned_end_date,
            estimated_hours=activity_data.estimated_hours,
            estimated_cost=activity_data.estimated_cost,
            provider=activity_data.provider,
            url=activity_data.url,
            location=activity_data.location,
            completion_percentage=activity_data.completion_percentage,
            completion_notes=activity_data.completion_notes,
            outcome=activity_data.outcome,
            certificate_url=activity_data.certificate_url,
            metadata=activity_data.metadata
        )
        
        db.add(activity)
        await db.flush()
        await db.refresh(activity)
        return activity

    async def get_goal_activities(
        self,
        db: AsyncSession,
        goal_id: UUID,
        *,
        status: Optional[ActivityStatus] = None
    ) -> List[DevelopmentActivity]:
        """Get all activities for a goal."""
        query = select(DevelopmentActivity).where(DevelopmentActivity.goal_id == goal_id)
        
        if status:
            query = query.where(DevelopmentActivity.status == status)
        
        query = query.order_by(DevelopmentActivity.planned_start_date)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def complete_activity(
        self,
        db: AsyncSession,
        activity_id: UUID,
        *,
        completion_notes: Optional[str] = None,
        outcome: Optional[str] = None,
        certificate_url: Optional[str] = None,
        actual_hours: Optional[float] = None,
        actual_cost: Optional[float] = None
    ) -> DevelopmentActivity:
        """Mark an activity as completed."""
        activity = await self.get(db, activity_id)
        if not activity:
            raise ValueError(f"Activity with ID {activity_id} not found")
        
        activity.status = ActivityStatus.COMPLETED
        activity.completion_percentage = 100.0
        activity.actual_end_date = datetime.utcnow()
        
        if completion_notes:
            activity.completion_notes = completion_notes
        if outcome:
            activity.outcome = outcome
        if certificate_url:
            activity.certificate_url = certificate_url
        if actual_hours is not None:
            activity.actual_hours = actual_hours
        if actual_cost is not None:
            activity.actual_cost = actual_cost
        
        await db.flush()
        await db.refresh(activity)
        return activity


class CareerAnalyticsService:
    """Service for career development analytics and reporting."""

    async def get_career_progress_summary(
        self,
        db: AsyncSession,
        user_id: UUID
    ) -> CareerProgressSummary:
        """Get comprehensive career progress summary for a user."""
        # Get user information
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        # Get IDP statistics
        idp_stats = await db.execute(
            select(
                func.count(IndividualDevelopmentPlan.id).label('total_idps'),
                func.count(
                    func.case(
                        (IndividualDevelopmentPlan.status == IDPStatus.ACTIVE, 1)
                    )
                ).label('active_idps'),
                func.count(
                    func.case(
                        (IndividualDevelopmentPlan.status == IDPStatus.COMPLETED, 1)
                    )
                ).label('completed_idps')
            ).where(IndividualDevelopmentPlan.user_id == user_id)
        )
        idp_counts = idp_stats.first()

        # Get goal statistics
        goal_stats = await db.execute(
            select(
                func.count(DevelopmentGoal.id).label('total_goals'),
                func.count(
                    func.case(
                        (DevelopmentGoal.status == GoalStatus.COMPLETED, 1)
                    )
                ).label('completed_goals'),
                func.count(
                    func.case(
                        (DevelopmentGoal.status == GoalStatus.IN_PROGRESS, 1)
                    )
                ).label('in_progress_goals'),
                func.avg(DevelopmentGoal.progress).label('avg_progress')
            ).select_from(
                DevelopmentGoal.join(IndividualDevelopmentPlan)
            ).where(IndividualDevelopmentPlan.user_id == user_id)
        )
        goal_counts = goal_stats.first()

        # Get latest review dates
        latest_idp = await db.execute(
            select(IndividualDevelopmentPlan).where(
                IndividualDevelopmentPlan.user_id == user_id
            ).order_by(IndividualDevelopmentPlan.updated_at.desc()).limit(1)
        )
        latest = latest_idp.scalar_one_or_none()

        return CareerProgressSummary(
            user_id=user_id,
            current_tier=user.career_tier.value,
            target_tier=latest.target_tier if latest else user.career_tier.value,
            career_track=user.career_track.value if user.career_track else None,
            active_idps=idp_counts.active_idps or 0,
            completed_idps=idp_counts.completed_idps or 0,
            total_goals=goal_counts.total_goals or 0,
            completed_goals=goal_counts.completed_goals or 0,
            in_progress_goals=goal_counts.in_progress_goals or 0,
            overall_progress=float(goal_counts.avg_progress or 0.0),
            last_review_date=latest.review_date if latest else None,
            next_review_date=latest.next_review_date if latest else None,
            development_budget_used=0.0,  # TODO: Calculate from activities
            development_budget_remaining=float(user.professional_dev_budget)
        )
