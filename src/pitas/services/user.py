"""User service for managing user accounts and authentication."""

from typing import Optional, List
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from .base import BaseService
from ..db.models.user import User, UserRole, CareerTier, CareerTrack
from ..schemas.user import UserCreate, UserUpdate
from ..core.exceptions import AppException


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """Service for managing user accounts."""

    def __init__(self):
        super().__init__(User)

    async def get_by_email(
        self,
        db: AsyncSession,
        email: str
    ) -> Optional[User]:
        """Get user by email address."""
        stmt = select(User).where(User.email == email)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_by_username(
        self,
        db: AsyncSession,
        username: str
    ) -> Optional[User]:
        """Get user by username."""
        stmt = select(User).where(User.username == username)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_active_users(
        self,
        db: AsyncSession,
        role: Optional[UserRole] = None
    ) -> List[User]:
        """Get all active users, optionally filtered by role."""
        stmt = select(User).where(User.is_active == True)

        if role:
            stmt = stmt.where(User.role == role)

        result = await db.execute(stmt)
        return list(result.scalars().all())

    async def authenticate(
        self,
        db: AsyncSession,
        email: str,
        password: str
    ) -> Optional[User]:
        """Authenticate user with email and password."""
        user = await self.get_by_email(db, email)
        if not user or not user.is_active:
            return None

        # TODO: Implement password verification
        # For now, this is a placeholder
        return user

    async def create_user(
        self,
        db: AsyncSession,
        user_data: UserCreate
    ) -> User:
        """Create a new user account."""
        # Check if user already exists
        existing_user = await self.get_by_email(db, user_data.email)
        if existing_user:
            raise AppException(f"User with email {user_data.email} already exists")

        # Create new user
        user = User(**user_data.dict())
        db.add(user)
        await db.flush()
        await db.refresh(user)
        return user

    async def update_user_role(
        self,
        db: AsyncSession,
        user_id: UUID,
        new_role: UserRole
    ) -> User:
        """Update user role."""
        user = await self.get(db, user_id)
        if not user:
            raise AppException(f"User not found: {user_id}")

        user.role = new_role
        await db.flush()
        await db.refresh(user)
        return user

    async def deactivate_user(
        self,
        db: AsyncSession,
        user_id: UUID
    ) -> User:
        """Deactivate a user account."""
        user = await self.get(db, user_id)
        if not user:
            raise AppException(f"User not found: {user_id}")

        user.is_active = False
        await db.flush()
        await db.refresh(user)
        return user