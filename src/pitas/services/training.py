"""Training and competency management services."""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.training import (
    CompetencyFramework,
    Competency,
    SkillAssessment,
    TrainingCourse,
    LearningPath,
    TrainingEnrollment,
    Certification,
    CertificationAchievement,
    CTFChallenge,
    CTFSubmission,
    CompetencyLevel,
    TrainingStatus,
    CertificationStatus,
)
from pitas.db.models.mentorship import (
    Mentorship as MentorshipPair,  # Alias for compatibility
    MentorshipSession,
)
from pitas.schemas.mentorship import (
    MentorshipCreate as MentorshipPairCreate,  # Alias for compatibility
    MentorshipUpdate as MentorshipPairUpdate,
    MentorshipSessionCreate,
    MentorshipSessionUpdate,
)
from pitas.schemas.training import (
    CompetencyFrameworkCreate,
    CompetencyFrameworkUpdate,
    CompetencyCreate,
    Competency<PERSON>pdate,
    <PERSON>ll<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Skill<PERSON><PERSON>smentUpdate,
    TrainingCourseCreate,
    TrainingCourseUpdate,
    LearningPathCreate,
    LearningPathUpdate,
    TrainingEnrollmentCreate,
    TrainingEnrollmentUpdate,
    CertificationCreate,
    CertificationUpdate,
    CertificationAchievementCreate,
    CertificationAchievementUpdate,
    CTFChallengeCreate,
    CTFChallengeUpdate,
    CTFSubmissionCreate,
    SkillGapAnalysis,
    LearningProgress,
    CTFLeaderboard,
    TrainingROIAnalysis,
    CompetencyMatrix,
    CertificationPathway,
)
from pitas.services.base import BaseService


class CompetencyService(BaseService[CompetencyFramework, CompetencyFrameworkCreate, CompetencyFrameworkUpdate]):
    """Service for managing competency frameworks and assessments."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(CompetencyFramework, db)
    
    async def create_framework(self, framework_data: CompetencyFrameworkCreate) -> CompetencyFramework:
        """Create a new competency framework."""
        framework = CompetencyFramework(**framework_data.model_dump())
        self.db.add(framework)
        await self.db.commit()
        await self.db.refresh(framework)
        return framework
    
    async def get_frameworks_by_category(self, category: str) -> List[CompetencyFramework]:
        """Get competency frameworks by category."""
        result = await self.db.execute(
            select(CompetencyFramework)
            .where(CompetencyFramework.category == category)
            .options(selectinload(CompetencyFramework.competencies))
        )
        return result.scalars().all()
    
    async def create_competency(self, competency_data: CompetencyCreate) -> Competency:
        """Create a new competency."""
        competency = Competency(**competency_data.model_dump())
        self.db.add(competency)
        await self.db.commit()
        await self.db.refresh(competency)
        return competency
    
    async def assess_skill_gaps(self, user_id: UUID) -> SkillGapAnalysis:
        """Perform skill gap analysis for a user."""
        # Get current assessments
        current_assessments = await self.db.execute(
            select(SkillAssessment)
            .where(SkillAssessment.user_id == user_id)
            .options(selectinload(SkillAssessment.competency))
        )
        assessments = current_assessments.scalars().all()
        
        # Build competency maps
        current_competencies = {}
        target_competencies = {}
        skill_gaps = {}
        
        for assessment in assessments:
            comp_id = str(assessment.competency.competency_id)
            current_competencies[comp_id] = assessment.current_level
            target_competencies[comp_id] = assessment.target_level
            
            # Calculate gap (difference in enum values)
            current_level_value = list(CompetencyLevel).index(assessment.current_level)
            target_level_value = list(CompetencyLevel).index(assessment.target_level)
            
            if target_level_value > current_level_value:
                gap_level = list(CompetencyLevel)[target_level_value - current_level_value]
                skill_gaps[comp_id] = gap_level
        
        # Get recommended training courses
        recommended_training = await self._get_recommended_training(skill_gaps)
        
        # Estimate completion time
        estimated_weeks = len(recommended_training) * 2  # Rough estimate
        
        return SkillGapAnalysis(
            user_id=user_id,
            current_competencies=current_competencies,
            target_competencies=target_competencies,
            skill_gaps=skill_gaps,
            recommended_training=recommended_training,
            estimated_completion_weeks=estimated_weeks
        )
    
    async def _get_recommended_training(self, skill_gaps: Dict[str, CompetencyLevel]) -> List[UUID]:
        """Get recommended training courses based on skill gaps."""
        # This would implement logic to match skill gaps to appropriate courses
        # For now, return empty list - would need more complex matching logic
        return []
    
    async def create_skill_assessment(self, assessment_data: SkillAssessmentCreate) -> SkillAssessment:
        """Create a new skill assessment."""
        assessment = SkillAssessment(**assessment_data.model_dump())
        self.db.add(assessment)
        await self.db.commit()
        await self.db.refresh(assessment)
        return assessment
    
    async def get_user_assessments(self, user_id: UUID) -> List[SkillAssessment]:
        """Get all skill assessments for a user."""
        result = await self.db.execute(
            select(SkillAssessment)
            .where(SkillAssessment.user_id == user_id)
            .options(selectinload(SkillAssessment.competency))
            .order_by(SkillAssessment.assessment_date.desc())
        )
        return result.scalars().all()


class TrainingService(BaseService[TrainingCourse, TrainingCourseCreate, TrainingCourseUpdate]):
    """Service for managing training courses and enrollments."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(TrainingCourse, db)
    
    async def create_course(self, course_data: TrainingCourseCreate) -> TrainingCourse:
        """Create a new training course."""
        course = TrainingCourse(**course_data.model_dump())
        self.db.add(course)
        await self.db.commit()
        await self.db.refresh(course)
        return course
    
    async def get_courses_by_provider(self, provider: str) -> List[TrainingCourse]:
        """Get courses by training provider."""
        result = await self.db.execute(
            select(TrainingCourse)
            .where(and_(
                TrainingCourse.provider == provider,
                TrainingCourse.is_active == True
            ))
        )
        return result.scalars().all()
    
    async def enroll_user(self, enrollment_data: TrainingEnrollmentCreate) -> TrainingEnrollment:
        """Enroll a user in a training course."""
        enrollment = TrainingEnrollment(**enrollment_data.model_dump())
        self.db.add(enrollment)
        await self.db.commit()
        await self.db.refresh(enrollment)
        return enrollment
    
    async def update_progress(self, enrollment_id: UUID, progress_data: TrainingEnrollmentUpdate) -> TrainingEnrollment:
        """Update training progress for an enrollment."""
        enrollment = await self.get(enrollment_id)
        if not enrollment:
            raise ValueError("Enrollment not found")
        
        for field, value in progress_data.model_dump(exclude_unset=True).items():
            setattr(enrollment, field, value)
        
        # Auto-complete if progress reaches 100%
        if progress_data.progress_percentage == 100.0:
            enrollment.status = TrainingStatus.COMPLETED
            enrollment.completion_date = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(enrollment)
        return enrollment
    
    async def get_learning_progress(self, user_id: UUID, course_id: UUID) -> LearningProgress:
        """Get detailed learning progress for a user and course."""
        enrollment = await self.db.execute(
            select(TrainingEnrollment)
            .where(and_(
                TrainingEnrollment.user_id == user_id,
                TrainingEnrollment.course_id == course_id
            ))
        )
        enrollment = enrollment.scalar_one_or_none()
        
        if not enrollment:
            raise ValueError("Enrollment not found")
        
        # Calculate projected completion date
        projected_completion = None
        if enrollment.progress_percentage > 0 and enrollment.start_date:
            days_elapsed = (datetime.utcnow() - enrollment.start_date).days
            if days_elapsed > 0:
                progress_rate = enrollment.progress_percentage / days_elapsed
                remaining_days = (100 - enrollment.progress_percentage) / progress_rate
                projected_completion = datetime.utcnow() + timedelta(days=remaining_days)
        
        return LearningProgress(
            user_id=user_id,
            course_id=course_id,
            completion_percentage=enrollment.progress_percentage,
            assessment_scores=enrollment.assessment_scores or {},
            practical_demonstrations=enrollment.practical_scores or {},
            time_spent_hours=enrollment.time_spent_hours,
            projected_completion_date=projected_completion
        )
    
    async def create_learning_path(self, path_data: LearningPathCreate) -> LearningPath:
        """Create a personalized learning path."""
        learning_path = LearningPath(**path_data.model_dump())
        self.db.add(learning_path)
        await self.db.commit()
        await self.db.refresh(learning_path)
        return learning_path
    
    async def get_user_learning_paths(self, user_id: UUID) -> List[LearningPath]:
        """Get all learning paths for a user."""
        result = await self.db.execute(
            select(LearningPath)
            .where(and_(
                LearningPath.user_id == user_id,
                LearningPath.is_active == True
            ))
        )
        return result.scalars().all()


class CertificationService(BaseService[Certification, CertificationCreate, CertificationUpdate]):
    """Service for managing certifications and achievements."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Certification, db)
    
    async def create_certification(self, cert_data: CertificationCreate) -> Certification:
        """Create a new certification."""
        certification = Certification(**cert_data.model_dump())
        self.db.add(certification)
        await self.db.commit()
        await self.db.refresh(certification)
        return certification
    
    async def record_achievement(self, achievement_data: CertificationAchievementCreate) -> CertificationAchievement:
        """Record a certification achievement."""
        achievement = CertificationAchievement(**achievement_data.model_dump())
        self.db.add(achievement)
        await self.db.commit()
        await self.db.refresh(achievement)
        return achievement
    
    async def get_user_certifications(self, user_id: UUID) -> List[CertificationAchievement]:
        """Get all certifications for a user."""
        result = await self.db.execute(
            select(CertificationAchievement)
            .where(CertificationAchievement.user_id == user_id)
            .options(selectinload(CertificationAchievement.certification))
        )
        return result.scalars().all()
    
    async def get_expiring_certifications(self, days_ahead: int = 90) -> List[CertificationAchievement]:
        """Get certifications expiring within specified days."""
        expiry_date = datetime.utcnow() + timedelta(days=days_ahead)
        result = await self.db.execute(
            select(CertificationAchievement)
            .where(and_(
                CertificationAchievement.expiration_date <= expiry_date,
                CertificationAchievement.status == CertificationStatus.ACHIEVED
            ))
            .options(selectinload(CertificationAchievement.certification))
        )
        return result.scalars().all()
    
    async def generate_certification_pathway(self, user_id: UUID, target_level: CompetencyLevel) -> CertificationPathway:
        """Generate a certification pathway for a user."""
        # Get current certifications
        current_certs = await self.get_user_certifications(user_id)
        current_cert_ids = [cert.certification_id for cert in current_certs if cert.status == CertificationStatus.ACHIEVED]
        
        # Get available certifications at target level
        target_certs = await self.db.execute(
            select(Certification)
            .where(and_(
                Certification.level == target_level,
                Certification.is_active == True
            ))
        )
        target_cert_ids = [cert.id for cert in target_certs.scalars().all()]
        
        # Simple pathway logic - would need more sophisticated algorithm
        recommended_sequence = target_cert_ids[:3]  # Limit to 3 for example
        estimated_timeline = len(recommended_sequence) * 6  # 6 months per cert
        total_cost = 0.0  # Would calculate based on exam costs
        
        return CertificationPathway(
            user_id=user_id,
            current_certifications=current_cert_ids,
            target_certifications=target_cert_ids,
            recommended_sequence=recommended_sequence,
            estimated_timeline_months=estimated_timeline,
            total_cost=total_cost,
            prerequisites_met={}  # Would check prerequisites
        )


class CTFService(BaseService[CTFChallenge, CTFChallengeCreate, CTFChallengeUpdate]):
    """Service for managing CTF challenges and competitions."""

    def __init__(self, db: AsyncSession):
        super().__init__(CTFChallenge, db)

    async def create_challenge(self, challenge_data: CTFChallengeCreate) -> CTFChallenge:
        """Create a new CTF challenge."""
        challenge = CTFChallenge(**challenge_data.model_dump())
        self.db.add(challenge)
        await self.db.commit()
        await self.db.refresh(challenge)
        return challenge

    async def submit_flag(self, submission_data: CTFSubmissionCreate) -> CTFSubmission:
        """Submit a flag for a CTF challenge."""
        # Get the challenge to check the correct flag
        challenge = await self.get(submission_data.challenge_id)
        if not challenge:
            raise ValueError("Challenge not found")

        # Check if flag is correct
        is_correct = submission_data.submitted_flag == challenge.flag
        points_awarded = challenge.points if is_correct else 0

        # Calculate time to solve if this is the first correct submission
        time_to_solve = None
        if is_correct:
            # Get the first submission time for this user/challenge
            first_submission = await self.db.execute(
                select(CTFSubmission)
                .where(and_(
                    CTFSubmission.user_id == submission_data.user_id,
                    CTFSubmission.challenge_id == submission_data.challenge_id
                ))
                .order_by(CTFSubmission.submission_time.asc())
                .limit(1)
            )
            first_sub = first_submission.scalar_one_or_none()

            if first_sub:
                time_diff = datetime.utcnow() - first_sub.submission_time
                time_to_solve = int(time_diff.total_seconds() / 60)  # minutes

        submission = CTFSubmission(
            user_id=submission_data.user_id,
            challenge_id=submission_data.challenge_id,
            submitted_flag=submission_data.submitted_flag,
            is_correct=is_correct,
            points_awarded=points_awarded,
            time_to_solve_minutes=time_to_solve
        )

        self.db.add(submission)
        await self.db.commit()
        await self.db.refresh(submission)
        return submission

    async def get_leaderboard(self, limit: int = 10) -> List[CTFLeaderboard]:
        """Get CTF leaderboard."""
        # Aggregate points by user
        result = await self.db.execute(
            select(
                CTFSubmission.user_id,
                func.sum(CTFSubmission.points_awarded).label('total_points'),
                func.count(CTFSubmission.id).label('challenges_solved'),
                func.max(CTFSubmission.submission_time).label('last_submission')
            )
            .where(CTFSubmission.is_correct == True)
            .group_by(CTFSubmission.user_id)
            .order_by(func.sum(CTFSubmission.points_awarded).desc())
            .limit(limit)
        )

        leaderboard = []
        for rank, row in enumerate(result.all(), 1):
            leaderboard.append(CTFLeaderboard(
                user_id=row.user_id,
                username=f"User_{str(row.user_id)[:8]}",  # Would get actual username
                total_points=row.total_points or 0,
                challenges_solved=row.challenges_solved or 0,
                rank=rank,
                last_submission=row.last_submission
            ))

        return leaderboard

    async def get_challenges_by_category(self, category: str) -> List[CTFChallenge]:
        """Get challenges by category."""
        result = await self.db.execute(
            select(CTFChallenge)
            .where(and_(
                CTFChallenge.category == category,
                CTFChallenge.is_active == True
            ))
            .order_by(CTFChallenge.difficulty, CTFChallenge.points)
        )
        return result.scalars().all()

    async def get_user_submissions(self, user_id: UUID) -> List[CTFSubmission]:
        """Get all submissions for a user."""
        result = await self.db.execute(
            select(CTFSubmission)
            .where(CTFSubmission.user_id == user_id)
            .options(selectinload(CTFSubmission.challenge))
            .order_by(CTFSubmission.submission_time.desc())
        )
        return result.scalars().all()


class MentorshipService(BaseService[MentorshipPair, MentorshipPairCreate, MentorshipPairUpdate]):
    """Service for managing mentorship programs."""

    def __init__(self, db: AsyncSession):
        super().__init__(MentorshipPair, db)

    async def create_mentorship_pair(self, pair_data: MentorshipPairCreate) -> MentorshipPair:
        """Create a new mentorship pair."""
        # Check if either user is already in an active mentorship
        existing_mentor = await self.db.execute(
            select(MentorshipPair)
            .where(and_(
                MentorshipPair.mentor_id == pair_data.mentor_id,
                MentorshipPair.is_active == True
            ))
        )

        existing_mentee = await self.db.execute(
            select(MentorshipPair)
            .where(and_(
                MentorshipPair.mentee_id == pair_data.mentee_id,
                MentorshipPair.is_active == True
            ))
        )

        if existing_mentor.scalar_one_or_none():
            raise ValueError("Mentor is already in an active mentorship")

        if existing_mentee.scalar_one_or_none():
            raise ValueError("Mentee is already in an active mentorship")

        pair = MentorshipPair(**pair_data.model_dump())
        self.db.add(pair)
        await self.db.commit()
        await self.db.refresh(pair)
        return pair

    async def create_session(self, session_data: MentorshipSessionCreate) -> MentorshipSession:
        """Create a new mentorship session."""
        session = MentorshipSession(**session_data.model_dump())
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        return session

    async def get_user_mentorships(self, user_id: UUID) -> List[MentorshipPair]:
        """Get all mentorships for a user (as mentor or mentee)."""
        result = await self.db.execute(
            select(MentorshipPair)
            .where(and_(
                (MentorshipPair.mentor_id == user_id) | (MentorshipPair.mentee_id == user_id),
                MentorshipPair.is_active == True
            ))
            .options(selectinload(MentorshipPair.sessions))
        )
        return result.scalars().all()

    async def end_mentorship(self, pair_id: UUID, satisfaction_rating: Optional[float] = None) -> MentorshipPair:
        """End a mentorship relationship."""
        pair = await self.get(pair_id)
        if not pair:
            raise ValueError("Mentorship pair not found")

        pair.is_active = False
        pair.end_date = datetime.utcnow()
        if satisfaction_rating:
            pair.satisfaction_rating = satisfaction_rating

        await self.db.commit()
        await self.db.refresh(pair)
        return pair

    async def get_mentorship_analytics(self, pair_id: UUID) -> Dict[str, Any]:
        """Get analytics for a mentorship pair."""
        pair = await self.get(pair_id)
        if not pair:
            raise ValueError("Mentorship pair not found")

        # Get session statistics
        sessions = await self.db.execute(
            select(MentorshipSession)
            .where(MentorshipSession.pair_id == pair_id)
        )
        session_list = sessions.scalars().all()

        total_sessions = len(session_list)
        total_duration = sum(s.duration_minutes or 0 for s in session_list)
        avg_duration = total_duration / total_sessions if total_sessions > 0 else 0

        return {
            "total_sessions": total_sessions,
            "total_duration_minutes": total_duration,
            "average_session_duration": avg_duration,
            "mentorship_duration_days": (datetime.utcnow() - pair.start_date).days,
            "satisfaction_rating": pair.satisfaction_rating,
            "goals_count": len(pair.goals or [])
        }
