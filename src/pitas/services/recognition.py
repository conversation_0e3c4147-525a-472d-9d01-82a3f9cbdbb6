"""Recognition and rewards system services."""

from datetime import datetime, timed<PERSON>ta
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.recognition import (
    Recognition, PeerNomination, NominationVote, Reward,
    RecognitionType, RecognitionStatus, AchievementCategory, RewardStatus
)
from pitas.db.models.user import User
from pitas.schemas.recognition import (
    RecognitionCreate, RecognitionUpdate, PeerNominationCreate, PeerNominationUpdate,
    NominationVoteCreate, RewardCreate, RewardUpdate, RecognitionStats, RewardStats
)
from pitas.services.base import BaseService


class RecognitionService(BaseService[Recognition, RecognitionCreate, RecognitionUpdate]):
    """Service for managing employee recognition."""

    def __init__(self):
        super().__init__(Recognition)

    async def create_recognition(
        self,
        db: AsyncSession,
        *,
        recognition_data: RecognitionCreate,
        auto_approve: bool = False
    ) -> Recognition:
        """Create a new recognition."""
        # Verify recipient exists
        recipient_result = await db.execute(
            select(User).where(User.id == recognition_data.recipient_id)
        )
        recipient = recipient_result.scalar_one_or_none()
        if not recipient:
            raise ValueError(f"Recipient with ID {recognition_data.recipient_id} not found")

        recognition = Recognition(
            title=recognition_data.title,
            description=recognition_data.description,
            recognition_type=recognition_data.recognition_type,
            status=RecognitionStatus.APPROVED if auto_approve else recognition_data.status,
            recipient_id=recognition_data.recipient_id,
            nominator_id=recognition_data.nominator_id,
            points_awarded=recognition_data.points_awarded,
            monetary_value=recognition_data.monetary_value,
            achievement_category=recognition_data.achievement_category,
            nomination_date=datetime.utcnow(),
            approval_date=datetime.utcnow() if auto_approve else None,
            is_public=recognition_data.is_public,
            is_featured=recognition_data.is_featured,
            share_externally=recognition_data.share_externally,
            evidence_urls=recognition_data.evidence_urls,
            impact_metrics=recognition_data.impact_metrics,
            testimonials=recognition_data.testimonials,
            tags=recognition_data.tags,
            metadata=recognition_data.metadata
        )
        
        db.add(recognition)
        await db.flush()
        await db.refresh(recognition)
        
        # Update recipient's recognition points if approved
        if auto_approve and recognition.points_awarded > 0:
            await self._update_user_points(db, recognition_data.recipient_id, recognition.points_awarded)
        
        return recognition

    async def approve_recognition(
        self,
        db: AsyncSession,
        recognition_id: UUID,
        approver_id: UUID,
        *,
        points_override: Optional[int] = None,
        monetary_override: Optional[float] = None
    ) -> Recognition:
        """Approve a recognition."""
        recognition = await self.get(db, recognition_id)
        if not recognition:
            raise ValueError(f"Recognition with ID {recognition_id} not found")
        
        if recognition.status != RecognitionStatus.PENDING:
            raise ValueError("Recognition is not in pending status")
        
        recognition.status = RecognitionStatus.APPROVED
        recognition.approver_id = approver_id
        recognition.approval_date = datetime.utcnow()
        
        if points_override is not None:
            recognition.points_awarded = points_override
        if monetary_override is not None:
            recognition.monetary_value = monetary_override
        
        await db.flush()
        await db.refresh(recognition)
        
        # Update recipient's recognition points
        if recognition.points_awarded > 0:
            await self._update_user_points(db, recognition.recipient_id, recognition.points_awarded)
        
        return recognition

    async def get_user_recognitions(
        self,
        db: AsyncSession,
        user_id: UUID,
        *,
        limit: int = 50,
        status: Optional[RecognitionStatus] = None,
        recognition_type: Optional[RecognitionType] = None
    ) -> List[Recognition]:
        """Get recognitions for a user."""
        query = select(Recognition).where(Recognition.recipient_id == user_id)
        
        if status:
            query = query.where(Recognition.status == status)
        if recognition_type:
            query = query.where(Recognition.recognition_type == recognition_type)
        
        query = query.order_by(desc(Recognition.nomination_date)).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_public_recognitions(
        self,
        db: AsyncSession,
        *,
        limit: int = 20,
        featured_only: bool = False
    ) -> List[Recognition]:
        """Get public recognitions for display."""
        query = select(Recognition).where(
            and_(
                Recognition.is_public == True,
                Recognition.status == RecognitionStatus.APPROVED
            )
        )
        
        if featured_only:
            query = query.where(Recognition.is_featured == True)
        
        query = query.order_by(desc(Recognition.approval_date)).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def _update_user_points(self, db: AsyncSession, user_id: UUID, points: int) -> None:
        """Update user's total recognition points."""
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if user:
            user.total_recognition_points += points
            await db.flush()


class PeerNominationService(BaseService[PeerNomination, PeerNominationCreate, PeerNominationUpdate]):
    """Service for managing peer nominations."""

    def __init__(self):
        super().__init__(PeerNomination)

    async def create_nomination(
        self,
        db: AsyncSession,
        *,
        nomination_data: PeerNominationCreate
    ) -> PeerNomination:
        """Create a new peer nomination."""
        # Verify nominee and nominator exist
        users_result = await db.execute(
            select(User).where(
                User.id.in_([nomination_data.nominee_id, nomination_data.nominator_id])
            )
        )
        users = {user.id: user for user in users_result.scalars().all()}
        
        if nomination_data.nominee_id not in users:
            raise ValueError(f"Nominee with ID {nomination_data.nominee_id} not found")
        if nomination_data.nominator_id not in users:
            raise ValueError(f"Nominator with ID {nomination_data.nominator_id} not found")
        
        # Check if nominator is trying to nominate themselves
        if nomination_data.nominee_id == nomination_data.nominator_id:
            raise ValueError("Users cannot nominate themselves")

        nomination = PeerNomination(
            title=nomination_data.title,
            description=nomination_data.description,
            nominee_id=nomination_data.nominee_id,
            nominator_id=nomination_data.nominator_id,
            achievement_category=nomination_data.achievement_category,
            impact_description=nomination_data.impact_description,
            specific_examples=nomination_data.specific_examples,
            status=RecognitionStatus.PENDING,
            votes_received=0,
            votes_required=nomination_data.votes_required,
            submission_date=datetime.utcnow(),
            voting_deadline=nomination_data.voting_deadline,
            metadata=nomination_data.metadata
        )
        
        db.add(nomination)
        await db.flush()
        await db.refresh(nomination)
        return nomination

    async def vote_on_nomination(
        self,
        db: AsyncSession,
        nomination_id: UUID,
        voter_id: UUID,
        vote: bool,
        comment: Optional[str] = None
    ) -> NominationVote:
        """Vote on a peer nomination."""
        # Check if nomination exists and is still open for voting
        nomination = await self.get(db, nomination_id)
        if not nomination:
            raise ValueError(f"Nomination with ID {nomination_id} not found")
        
        if nomination.status != RecognitionStatus.PENDING:
            raise ValueError("Nomination is not open for voting")
        
        if nomination.voting_deadline and datetime.utcnow() > nomination.voting_deadline:
            raise ValueError("Voting deadline has passed")
        
        # Check if user already voted
        existing_vote = await db.execute(
            select(NominationVote).where(
                and_(
                    NominationVote.nomination_id == nomination_id,
                    NominationVote.voter_id == voter_id
                )
            )
        )
        if existing_vote.scalar_one_or_none():
            raise ValueError("User has already voted on this nomination")
        
        # Create vote
        vote_record = NominationVote(
            nomination_id=nomination_id,
            voter_id=voter_id,
            vote=vote,
            comment=comment,
            vote_date=datetime.utcnow()
        )
        
        db.add(vote_record)
        
        # Update nomination vote count
        if vote:  # Only count positive votes
            nomination.votes_received += 1
        
        # Check if nomination should be approved
        if nomination.votes_received >= nomination.votes_required:
            nomination.status = RecognitionStatus.APPROVED
            nomination.decision_date = datetime.utcnow()
            
            # Create recognition record
            await self._create_recognition_from_nomination(db, nomination)
        
        await db.flush()
        await db.refresh(vote_record)
        return vote_record

    async def get_pending_nominations(
        self,
        db: AsyncSession,
        *,
        voter_id: Optional[UUID] = None,
        limit: int = 50
    ) -> List[PeerNomination]:
        """Get pending nominations for voting."""
        query = select(PeerNomination).where(
            PeerNomination.status == RecognitionStatus.PENDING
        )
        
        # Exclude nominations where user already voted
        if voter_id:
            voted_nominations = select(NominationVote.nomination_id).where(
                NominationVote.voter_id == voter_id
            )
            query = query.where(
                PeerNomination.id.not_in(voted_nominations)
            )
        
        query = query.order_by(desc(PeerNomination.submission_date)).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def _create_recognition_from_nomination(
        self,
        db: AsyncSession,
        nomination: PeerNomination
    ) -> Recognition:
        """Create a recognition record from an approved nomination."""
        recognition = Recognition(
            title=f"Peer Recognition: {nomination.title}",
            description=nomination.description,
            recognition_type=RecognitionType.PEER_NOMINATION,
            status=RecognitionStatus.APPROVED,
            recipient_id=nomination.nominee_id,
            nominator_id=nomination.nominator_id,
            points_awarded=self._calculate_nomination_points(nomination.achievement_category),
            achievement_category=nomination.achievement_category,
            nomination_date=nomination.submission_date,
            approval_date=datetime.utcnow(),
            is_public=True,
            is_featured=False,
            share_externally=False,
            impact_metrics={"peer_votes": nomination.votes_received},
            metadata={"source_nomination_id": str(nomination.id)}
        )
        
        db.add(recognition)
        await db.flush()
        
        # Update recipient's points
        await self._update_user_points(db, nomination.nominee_id, recognition.points_awarded)
        
        return recognition

    def _calculate_nomination_points(self, category: AchievementCategory) -> int:
        """Calculate points based on achievement category."""
        point_values = {
            AchievementCategory.TECHNICAL_EXCELLENCE: 100,
            AchievementCategory.INNOVATION: 150,
            AchievementCategory.LEADERSHIP: 120,
            AchievementCategory.COLLABORATION: 80,
            AchievementCategory.CLIENT_SUCCESS: 110,
            AchievementCategory.MENTORING: 90,
            AchievementCategory.CONTINUOUS_LEARNING: 70,
            AchievementCategory.PROCESS_IMPROVEMENT: 100,
        }
        return point_values.get(category, 50)

    async def _update_user_points(self, db: AsyncSession, user_id: UUID, points: int) -> None:
        """Update user's total recognition points."""
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        if user:
            user.total_recognition_points += points
            await db.flush()


class RewardService(BaseService[Reward, RewardCreate, RewardUpdate]):
    """Service for managing rewards and benefits."""

    def __init__(self):
        super().__init__(Reward)

    async def create_reward(
        self,
        db: AsyncSession,
        *,
        reward_data: RewardCreate
    ) -> Reward:
        """Create a new reward."""
        # Verify recipient exists
        recipient_result = await db.execute(
            select(User).where(User.id == reward_data.recipient_id)
        )
        recipient = recipient_result.scalar_one_or_none()
        if not recipient:
            raise ValueError(f"Recipient with ID {reward_data.recipient_id} not found")

        reward = Reward(
            title=reward_data.title,
            description=reward_data.description,
            reward_type=reward_data.reward_type,
            status=reward_data.status,
            recipient_id=reward_data.recipient_id,
            authorized_by_id=reward_data.authorized_by_id,
            recognition_id=reward_data.recognition_id,
            monetary_value=reward_data.monetary_value,
            points_cost=reward_data.points_cost,
            quantity=reward_data.quantity,
            awarded_date=datetime.utcnow(),
            expiration_date=reward_data.expiration_date,
            fulfillment_notes=reward_data.fulfillment_notes,
            vendor_information=reward_data.vendor_information,
            tracking_information=reward_data.tracking_information,
            metadata=reward_data.metadata
        )
        
        db.add(reward)
        await db.flush()
        await db.refresh(reward)
        return reward

    async def fulfill_reward(
        self,
        db: AsyncSession,
        reward_id: UUID,
        *,
        fulfillment_notes: Optional[str] = None,
        tracking_info: Optional[Dict[str, Any]] = None
    ) -> Reward:
        """Mark a reward as fulfilled."""
        reward = await self.get(db, reward_id)
        if not reward:
            raise ValueError(f"Reward with ID {reward_id} not found")
        
        reward.status = RewardStatus.FULFILLED
        reward.fulfilled_date = datetime.utcnow()
        
        if fulfillment_notes:
            reward.fulfillment_notes = fulfillment_notes
        if tracking_info:
            reward.tracking_information = tracking_info
        
        await db.flush()
        await db.refresh(reward)
        return reward

    async def get_user_rewards(
        self,
        db: AsyncSession,
        user_id: UUID,
        *,
        status: Optional[RewardStatus] = None,
        limit: int = 50
    ) -> List[Reward]:
        """Get rewards for a user."""
        query = select(Reward).where(Reward.recipient_id == user_id)
        
        if status:
            query = query.where(Reward.status == status)
        
        query = query.order_by(desc(Reward.awarded_date)).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())


class RecognitionAnalyticsService:
    """Service for recognition and rewards analytics."""

    async def get_recognition_stats(
        self,
        db: AsyncSession,
        user_id: Optional[UUID] = None,
        *,
        period_days: int = 365
    ) -> RecognitionStats:
        """Get recognition statistics."""
        start_date = datetime.utcnow() - timedelta(days=period_days)
        
        query = select(Recognition).where(
            and_(
                Recognition.status == RecognitionStatus.APPROVED,
                Recognition.approval_date >= start_date
            )
        )
        
        if user_id:
            query = query.where(Recognition.recipient_id == user_id)
        
        recognitions = await db.execute(query)
        recognition_list = list(recognitions.scalars().all())
        
        # Calculate statistics
        total_recognitions = len(recognition_list)
        total_points = sum(r.points_awarded for r in recognition_list)
        total_monetary_value = sum(r.monetary_value or 0 for r in recognition_list)
        
        # Group by type and category
        by_type = {}
        by_category = {}
        for r in recognition_list:
            by_type[r.recognition_type.value] = by_type.get(r.recognition_type.value, 0) + 1
            if r.achievement_category:
                by_category[r.achievement_category.value] = by_category.get(r.achievement_category.value, 0) + 1
        
        # Get peer nominations
        peer_noms_received = len([r for r in recognition_list if r.recognition_type == RecognitionType.PEER_NOMINATION])
        
        # Get peer nominations given (if user_id provided)
        peer_noms_given = 0
        if user_id:
            given_result = await db.execute(
                select(func.count(PeerNomination.id)).where(
                    and_(
                        PeerNomination.nominator_id == user_id,
                        PeerNomination.submission_date >= start_date
                    )
                )
            )
            peer_noms_given = given_result.scalar() or 0
        
        # Recent recognitions
        recent = recognition_list[:10]  # Last 10
        
        # Trend (simplified - monthly counts)
        trend = {}  # TODO: Implement monthly trend calculation
        
        return RecognitionStats(
            user_id=user_id,
            total_recognitions=total_recognitions,
            total_points=total_points,
            total_monetary_value=total_monetary_value,
            recognitions_by_type=by_type,
            recognitions_by_category=by_category,
            peer_nominations_received=peer_noms_received,
            peer_nominations_given=peer_noms_given,
            recent_recognitions=recent,
            recognition_trend=trend
        )
