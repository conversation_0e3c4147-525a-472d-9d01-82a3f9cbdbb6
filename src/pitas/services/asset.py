"""Asset management service."""

from typing import List, Optional
from uuid import UUID

from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.asset import Asset, AssetVulnerability
from pitas.db.models.vulnerability import Vulnerability
from pitas.schemas.vulnerability import (
    AssetCreate, AssetUpdate, AssetVulnerabilityCreate, AssetVulnerabilityUpdate
)
from pitas.services.base import BaseService


class AssetService(BaseService[Asset, AssetCreate, AssetUpdate]):
    """Service for asset management operations."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Asset, db)
    
    async def create_asset(self, asset_data: AssetCreate) -> Asset:
        """Create a new asset.
        
        Args:
            asset_data: Asset creation data
            
        Returns:
            Asset: Created asset
        """
        asset = Asset(**asset_data.model_dump())
        self.db.add(asset)
        await self.db.commit()
        await self.db.refresh(asset)
        return asset
    
    async def get_asset_by_id(self, asset_id: UUID) -> Optional[Asset]:
        """Get asset by ID.
        
        Args:
            asset_id: Asset ID
            
        Returns:
            Optional[Asset]: Asset if found
        """
        stmt = select(Asset).where(Asset.id == asset_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_asset_by_name(self, name: str) -> Optional[Asset]:
        """Get asset by name.
        
        Args:
            name: Asset name
            
        Returns:
            Optional[Asset]: Asset if found
        """
        stmt = select(Asset).where(Asset.name == name)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_asset(self, asset_id: UUID, asset_data: AssetUpdate) -> Optional[Asset]:
        """Update asset.
        
        Args:
            asset_id: Asset ID
            asset_data: Update data
            
        Returns:
            Optional[Asset]: Updated asset if found
        """
        asset = await self.get_asset_by_id(asset_id)
        if not asset:
            return None
        
        update_data = asset_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(asset, field, value)
        
        await self.db.commit()
        await self.db.refresh(asset)
        return asset
    
    async def delete_asset(self, asset_id: UUID) -> bool:
        """Delete asset.
        
        Args:
            asset_id: Asset ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        asset = await self.get_asset_by_id(asset_id)
        if not asset:
            return False
        
        await self.db.delete(asset)
        await self.db.commit()
        return True
    
    async def list_assets(
        self,
        page: int = 1,
        page_size: int = 50,
        asset_type: Optional[str] = None,
        business_criticality: Optional[str] = None,
    ) -> List[Asset]:
        """List assets with filtering and pagination.
        
        Args:
            page: Page number
            page_size: Items per page
            asset_type: Filter by asset type
            business_criticality: Filter by business criticality
            
        Returns:
            List[Asset]: List of assets
        """
        stmt = select(Asset)
        
        # Apply filters
        conditions = []
        if asset_type:
            conditions.append(Asset.asset_type == asset_type)
        if business_criticality:
            conditions.append(Asset.business_criticality == business_criticality)
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        # Apply pagination
        offset = (page - 1) * page_size
        stmt = stmt.offset(offset).limit(page_size)
        
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_asset_vulnerabilities(self, asset_id: UUID) -> List[Vulnerability]:
        """Get vulnerabilities associated with an asset.
        
        Args:
            asset_id: Asset ID
            
        Returns:
            List[Vulnerability]: Associated vulnerabilities
        """
        stmt = (
            select(Vulnerability)
            .join(AssetVulnerability)
            .where(AssetVulnerability.asset_id == asset_id)
        )
        
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def create_asset_vulnerability_association(
        self, 
        association_data: AssetVulnerabilityCreate
    ) -> AssetVulnerability:
        """Create asset-vulnerability association.
        
        Args:
            association_data: Association data
            
        Returns:
            AssetVulnerability: Created association
        """
        association = AssetVulnerability(**association_data.model_dump())
        self.db.add(association)
        await self.db.commit()
        await self.db.refresh(association)
        return association
    
    async def get_asset_vulnerability_associations(
        self, 
        asset_id: UUID
    ) -> List[AssetVulnerability]:
        """Get asset-vulnerability associations for an asset.
        
        Args:
            asset_id: Asset ID
            
        Returns:
            List[AssetVulnerability]: Associations
        """
        stmt = select(AssetVulnerability).where(AssetVulnerability.asset_id == asset_id)
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_asset_vulnerability_association(
        self, 
        asset_id: UUID, 
        vulnerability_id: UUID
    ) -> Optional[AssetVulnerability]:
        """Get specific asset-vulnerability association.
        
        Args:
            asset_id: Asset ID
            vulnerability_id: Vulnerability ID
            
        Returns:
            Optional[AssetVulnerability]: Association if found
        """
        stmt = select(AssetVulnerability).where(
            and_(
                AssetVulnerability.asset_id == asset_id,
                AssetVulnerability.vulnerability_id == vulnerability_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def update_asset_vulnerability_association(
        self,
        asset_id: UUID,
        vulnerability_id: UUID,
        association_data: AssetVulnerabilityUpdate
    ) -> Optional[AssetVulnerability]:
        """Update asset-vulnerability association.
        
        Args:
            asset_id: Asset ID
            vulnerability_id: Vulnerability ID
            association_data: Update data
            
        Returns:
            Optional[AssetVulnerability]: Updated association if found
        """
        association = await self.get_asset_vulnerability_association(asset_id, vulnerability_id)
        if not association:
            return None
        
        update_data = association_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(association, field, value)
        
        await self.db.commit()
        await self.db.refresh(association)
        return association
    
    async def delete_asset_vulnerability_association(
        self, 
        asset_id: UUID, 
        vulnerability_id: UUID
    ) -> bool:
        """Delete asset-vulnerability association.
        
        Args:
            asset_id: Asset ID
            vulnerability_id: Vulnerability ID
            
        Returns:
            bool: True if deleted, False if not found
        """
        association = await self.get_asset_vulnerability_association(asset_id, vulnerability_id)
        if not association:
            return False
        
        await self.db.delete(association)
        await self.db.commit()
        return True
    
    async def get_critical_assets(self) -> List[Asset]:
        """Get assets with critical business criticality.
        
        Returns:
            List[Asset]: Critical assets
        """
        stmt = select(Asset).where(Asset.business_criticality == "critical")
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_assets_by_type(self, asset_type: str) -> List[Asset]:
        """Get assets by type.
        
        Args:
            asset_type: Asset type
            
        Returns:
            List[Asset]: Assets of specified type
        """
        stmt = select(Asset).where(Asset.asset_type == asset_type)
        result = await self.db.execute(stmt)
        return list(result.scalars().all())
