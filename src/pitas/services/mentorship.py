"""Mentorship and peer support services."""

from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any, Tu<PERSON>
from uuid import UUID

from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.db.models.mentorship import (
    Mentorship, MentorshipSession, MentorProfile, MentorshipRequest,
    MentorshipType, MentorshipStatus, SessionStatus
)
from pitas.db.models.user import User, CareerTier
from pitas.schemas.mentorship import (
    MentorshipCreate, MentorshipUpdate, MentorshipSessionCreate, MentorshipSessionUpdate,
    MentorProfileCreate, MentorProfileUpdate, MentorshipRequestCreate, MentorshipRequestUpdate,
    MentorshipStats
)
from pitas.services.base import BaseService


class MentorshipService(BaseService[Mentorship, MentorshipCreate, MentorshipUpdate]):
    """Service for managing mentorship relationships."""

    def __init__(self):
        super().__init__(Mentorship)

    async def create_mentorship(
        self,
        db: AsyncSession,
        *,
        mentorship_data: MentorshipCreate
    ) -> Mentorship:
        """Create a new mentorship relationship."""
        # Verify mentor and mentee exist
        users_result = await db.execute(
            select(User).where(
                User.id.in_([mentorship_data.mentor_id, mentorship_data.mentee_id])
            )
        )
        users = {user.id: user for user in users_result.scalars().all()}
        
        if mentorship_data.mentor_id not in users:
            raise ValueError(f"Mentor with ID {mentorship_data.mentor_id} not found")
        if mentorship_data.mentee_id not in users:
            raise ValueError(f"Mentee with ID {mentorship_data.mentee_id} not found")
        
        # Check if mentor and mentee are the same person
        if mentorship_data.mentor_id == mentorship_data.mentee_id:
            raise ValueError("Mentor and mentee cannot be the same person")

        # Check mentor availability
        mentor_profile = await self._get_mentor_profile(db, mentorship_data.mentor_id)
        if mentor_profile and mentor_profile.current_mentees >= mentor_profile.max_mentees:
            raise ValueError("Mentor has reached maximum mentee capacity")

        mentorship = Mentorship(
            mentor_id=mentorship_data.mentor_id,
            mentee_id=mentorship_data.mentee_id,
            mentorship_type=mentorship_data.mentorship_type,
            status=mentorship_data.status,
            start_date=mentorship_data.start_date,
            planned_end_date=mentorship_data.planned_end_date,
            primary_goals=mentorship_data.primary_goals,
            focus_areas=mentorship_data.focus_areas,
            success_criteria=mentorship_data.success_criteria,
            meeting_frequency=mentorship_data.meeting_frequency,
            preferred_meeting_duration=mentorship_data.preferred_meeting_duration,
            preferred_meeting_format=mentorship_data.preferred_meeting_format,
            overall_progress=mentorship_data.overall_progress,
            mentor_satisfaction=mentorship_data.mentor_satisfaction,
            mentee_satisfaction=mentorship_data.mentee_satisfaction,
            assigned_by_id=mentorship_data.assigned_by_id,
            program_id=mentorship_data.program_id,
            initial_notes=mentorship_data.initial_notes,
            mentor_notes=mentorship_data.mentor_notes,
            mentee_notes=mentorship_data.mentee_notes,
            completion_notes=mentorship_data.completion_notes,
            metadata=mentorship_data.metadata
        )
        
        db.add(mentorship)
        await db.flush()
        await db.refresh(mentorship)
        
        # Update mentor profile mentee count
        if mentor_profile:
            mentor_profile.current_mentees += 1
            await db.flush()
        
        return mentorship

    async def get_user_mentorships(
        self,
        db: AsyncSession,
        user_id: UUID,
        *,
        as_mentor: bool = True,
        status: Optional[MentorshipStatus] = None,
        include_sessions: bool = False
    ) -> List[Mentorship]:
        """Get mentorships for a user as mentor or mentee."""
        if as_mentor:
            query = select(Mentorship).where(Mentorship.mentor_id == user_id)
        else:
            query = select(Mentorship).where(Mentorship.mentee_id == user_id)
        
        if status:
            query = query.where(Mentorship.status == status)
        
        if include_sessions:
            query = query.options(selectinload(Mentorship.sessions))
        
        query = query.order_by(desc(Mentorship.start_date))
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def complete_mentorship(
        self,
        db: AsyncSession,
        mentorship_id: UUID,
        *,
        completion_notes: Optional[str] = None,
        mentor_satisfaction: Optional[int] = None,
        mentee_satisfaction: Optional[int] = None
    ) -> Mentorship:
        """Complete a mentorship relationship."""
        mentorship = await self.get(db, mentorship_id)
        if not mentorship:
            raise ValueError(f"Mentorship with ID {mentorship_id} not found")
        
        mentorship.status = MentorshipStatus.COMPLETED
        mentorship.actual_end_date = date.today()
        mentorship.overall_progress = 1.0
        
        if completion_notes:
            mentorship.completion_notes = completion_notes
        if mentor_satisfaction is not None:
            mentorship.mentor_satisfaction = mentor_satisfaction
        if mentee_satisfaction is not None:
            mentorship.mentee_satisfaction = mentee_satisfaction
        
        await db.flush()
        await db.refresh(mentorship)
        
        # Update mentor profile statistics
        mentor_profile = await self._get_mentor_profile(db, mentorship.mentor_id)
        if mentor_profile:
            mentor_profile.current_mentees = max(0, mentor_profile.current_mentees - 1)
            mentor_profile.completed_mentorships += 1
            
            # Update average rating if satisfaction provided
            if mentor_satisfaction is not None:
                total_rating = (mentor_profile.average_rating or 0) * mentor_profile.total_ratings
                mentor_profile.total_ratings += 1
                mentor_profile.average_rating = (total_rating + mentor_satisfaction) / mentor_profile.total_ratings
            
            await db.flush()
        
        return mentorship

    async def _get_mentor_profile(self, db: AsyncSession, user_id: UUID) -> Optional[MentorProfile]:
        """Get mentor profile for a user."""
        result = await db.execute(
            select(MentorProfile).where(MentorProfile.user_id == user_id)
        )
        return result.scalar_one_or_none()


class MentorshipSessionService(BaseService[MentorshipSession, MentorshipSessionCreate, MentorshipSessionUpdate]):
    """Service for managing mentorship sessions."""

    def __init__(self):
        super().__init__(MentorshipSession)

    async def create_session(
        self,
        db: AsyncSession,
        *,
        session_data: MentorshipSessionCreate
    ) -> MentorshipSession:
        """Create a new mentorship session."""
        # Verify mentorship exists
        mentorship_result = await db.execute(
            select(Mentorship).where(Mentorship.id == session_data.mentorship_id)
        )
        mentorship = mentorship_result.scalar_one_or_none()
        if not mentorship:
            raise ValueError(f"Mentorship with ID {session_data.mentorship_id} not found")

        session = MentorshipSession(
            mentorship_id=session_data.mentorship_id,
            session_type=session_data.session_type,
            status=session_data.status,
            scheduled_date=session_data.scheduled_date,
            scheduled_duration=session_data.scheduled_duration,
            agenda=session_data.agenda,
            topics_covered=session_data.topics_covered,
            goals_for_session=session_data.goals_for_session,
            meeting_format=session_data.meeting_format,
            location=session_data.location,
            meeting_link=session_data.meeting_link,
            session_notes=session_data.session_notes,
            mentor_feedback=session_data.mentor_feedback,
            mentee_feedback=session_data.mentee_feedback,
            action_items=session_data.action_items,
            next_session_goals=session_data.next_session_goals,
            mentor_rating=session_data.mentor_rating,
            mentee_rating=session_data.mentee_rating,
            session_effectiveness=session_data.session_effectiveness,
            resources_shared=session_data.resources_shared,
            homework_assigned=session_data.homework_assigned,
            metadata=session_data.metadata
        )
        
        db.add(session)
        await db.flush()
        await db.refresh(session)
        return session

    async def complete_session(
        self,
        db: AsyncSession,
        session_id: UUID,
        *,
        session_notes: Optional[str] = None,
        mentor_feedback: Optional[str] = None,
        mentee_feedback: Optional[str] = None,
        action_items: Optional[Dict[str, Any]] = None,
        mentor_rating: Optional[int] = None,
        mentee_rating: Optional[int] = None,
        session_effectiveness: Optional[int] = None
    ) -> MentorshipSession:
        """Complete a mentorship session."""
        session = await self.get(db, session_id)
        if not session:
            raise ValueError(f"Session with ID {session_id} not found")
        
        session.status = SessionStatus.COMPLETED
        session.actual_end_time = datetime.utcnow()
        
        if session_notes:
            session.session_notes = session_notes
        if mentor_feedback:
            session.mentor_feedback = mentor_feedback
        if mentee_feedback:
            session.mentee_feedback = mentee_feedback
        if action_items:
            session.action_items = action_items
        if mentor_rating is not None:
            session.mentor_rating = mentor_rating
        if mentee_rating is not None:
            session.mentee_rating = mentee_rating
        if session_effectiveness is not None:
            session.session_effectiveness = session_effectiveness
        
        await db.flush()
        await db.refresh(session)
        return session

    async def get_mentorship_sessions(
        self,
        db: AsyncSession,
        mentorship_id: UUID,
        *,
        status: Optional[SessionStatus] = None,
        limit: int = 50
    ) -> List[MentorshipSession]:
        """Get sessions for a mentorship."""
        query = select(MentorshipSession).where(
            MentorshipSession.mentorship_id == mentorship_id
        )
        
        if status:
            query = query.where(MentorshipSession.status == status)
        
        query = query.order_by(desc(MentorshipSession.scheduled_date)).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())


class MentorProfileService(BaseService[MentorProfile, MentorProfileCreate, MentorProfileUpdate]):
    """Service for managing mentor profiles."""

    def __init__(self):
        super().__init__(MentorProfile)

    async def create_mentor_profile(
        self,
        db: AsyncSession,
        *,
        profile_data: MentorProfileCreate
    ) -> MentorProfile:
        """Create a new mentor profile."""
        # Verify user exists
        user_result = await db.execute(select(User).where(User.id == profile_data.user_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {profile_data.user_id} not found")

        # Check if profile already exists
        existing_result = await db.execute(
            select(MentorProfile).where(MentorProfile.user_id == profile_data.user_id)
        )
        if existing_result.scalar_one_or_none():
            raise ValueError(f"Mentor profile already exists for user {profile_data.user_id}")

        profile = MentorProfile(
            user_id=profile_data.user_id,
            is_active=profile_data.is_active,
            is_available=profile_data.is_available,
            expertise_areas=profile_data.expertise_areas,
            mentoring_skills=profile_data.mentoring_skills,
            industry_experience=profile_data.industry_experience,
            max_mentees=profile_data.max_mentees,
            current_mentees=0,  # Start with 0
            preferred_mentee_level=profile_data.preferred_mentee_level,
            preferred_mentorship_type=profile_data.preferred_mentorship_type,
            hours_per_week=profile_data.hours_per_week,
            available_time_slots=profile_data.available_time_slots,
            timezone_preferences=profile_data.timezone_preferences,
            total_mentorships=0,
            completed_mentorships=0,
            average_rating=None,
            total_ratings=0,
            mentoring_philosophy=profile_data.mentoring_philosophy,
            success_stories=profile_data.success_stories,
            approach_description=profile_data.approach_description,
            mentoring_certifications=profile_data.mentoring_certifications,
            training_completed=profile_data.training_completed,
            last_training_date=profile_data.last_training_date,
            metadata=profile_data.metadata
        )
        
        db.add(profile)
        await db.flush()
        await db.refresh(profile)
        return profile

    async def find_available_mentors(
        self,
        db: AsyncSession,
        *,
        expertise_areas: Optional[List[str]] = None,
        mentee_level: Optional[str] = None,
        timezone: Optional[str] = None,
        limit: int = 20
    ) -> List[MentorProfile]:
        """Find available mentors based on criteria."""
        query = select(MentorProfile).where(
            and_(
                MentorProfile.is_active == True,
                MentorProfile.is_available == True,
                MentorProfile.current_mentees < MentorProfile.max_mentees
            )
        )
        
        # Add filters based on criteria
        # Note: This is a simplified implementation
        # In practice, you'd want more sophisticated matching logic
        
        query = query.order_by(
            desc(MentorProfile.average_rating),
            desc(MentorProfile.completed_mentorships)
        ).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())


class MentorshipRequestService(BaseService[MentorshipRequest, MentorshipRequestCreate, MentorshipRequestUpdate]):
    """Service for managing mentorship requests."""

    def __init__(self):
        super().__init__(MentorshipRequest)

    async def create_request(
        self,
        db: AsyncSession,
        *,
        request_data: MentorshipRequestCreate
    ) -> MentorshipRequest:
        """Create a new mentorship request."""
        # Verify requester exists
        user_result = await db.execute(select(User).where(User.id == request_data.requester_id))
        user = user_result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User with ID {request_data.requester_id} not found")

        request = MentorshipRequest(
            requester_id=request_data.requester_id,
            request_type=request_data.request_type,
            status=request_data.status,
            priority=request_data.priority,
            desired_expertise=request_data.desired_expertise,
            goals_and_objectives=request_data.goals_and_objectives,
            preferred_mentor_level=request_data.preferred_mentor_level,
            preferred_meeting_frequency=request_data.preferred_meeting_frequency,
            requested_start_date=request_data.requested_start_date,
            preferred_duration=request_data.preferred_duration,
            location_preference=request_data.location_preference,
            timezone_preference=request_data.timezone_preference,
            language_preference=request_data.language_preference,
            background_info=request_data.background_info,
            special_requirements=request_data.special_requirements,
            previous_mentoring_experience=request_data.previous_mentoring_experience,
            request_date=datetime.utcnow()
        )
        
        db.add(request)
        await db.flush()
        await db.refresh(request)
        return request

    async def match_request(
        self,
        db: AsyncSession,
        request_id: UUID,
        mentor_id: UUID,
        *,
        matching_score: Optional[float] = None,
        matching_notes: Optional[str] = None
    ) -> MentorshipRequest:
        """Match a mentorship request with a mentor."""
        request = await self.get(db, request_id)
        if not request:
            raise ValueError(f"Request with ID {request_id} not found")
        
        # Verify mentor exists
        mentor_result = await db.execute(select(User).where(User.id == mentor_id))
        mentor = mentor_result.scalar_one_or_none()
        if not mentor:
            raise ValueError(f"Mentor with ID {mentor_id} not found")
        
        request.status = "matched"
        request.matched_with_id = mentor_id
        request.matching_score = matching_score
        request.matching_notes = matching_notes
        request.matched_date = datetime.utcnow()
        
        await db.flush()
        await db.refresh(request)
        return request

    async def get_pending_requests(
        self,
        db: AsyncSession,
        *,
        request_type: Optional[str] = None,
        priority: Optional[str] = None,
        limit: int = 50
    ) -> List[MentorshipRequest]:
        """Get pending mentorship requests."""
        query = select(MentorshipRequest).where(
            MentorshipRequest.status == "pending"
        )
        
        if request_type:
            query = query.where(MentorshipRequest.request_type == request_type)
        if priority:
            query = query.where(MentorshipRequest.priority == priority)
        
        query = query.order_by(
            MentorshipRequest.priority.desc(),
            desc(MentorshipRequest.request_date)
        ).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())


class MentorshipAnalyticsService:
    """Service for mentorship analytics and reporting."""

    async def get_mentorship_stats(
        self,
        db: AsyncSession,
        user_id: Optional[UUID] = None,
        *,
        period_days: int = 365
    ) -> MentorshipStats:
        """Get mentorship statistics."""
        start_date = date.today() - timedelta(days=period_days)
        
        # Base query for mentorships
        mentorship_query = select(Mentorship).where(
            Mentorship.start_date >= start_date
        )
        
        if user_id:
            mentorship_query = mentorship_query.where(
                or_(
                    Mentorship.mentor_id == user_id,
                    Mentorship.mentee_id == user_id
                )
            )
        
        mentorships_result = await db.execute(mentorship_query)
        mentorships = list(mentorships_result.scalars().all())
        
        # Calculate statistics
        total_mentorships = len(mentorships)
        active_mentorships = len([m for m in mentorships if m.status == MentorshipStatus.ACTIVE])
        completed_mentorships = len([m for m in mentorships if m.status == MentorshipStatus.COMPLETED])
        
        if user_id:
            mentorships_as_mentor = len([m for m in mentorships if m.mentor_id == user_id])
            mentorships_as_mentee = len([m for m in mentorships if m.mentee_id == user_id])
        else:
            mentorships_as_mentor = 0
            mentorships_as_mentee = 0
        
        # Calculate average duration for completed mentorships
        completed = [m for m in mentorships if m.status == MentorshipStatus.COMPLETED and m.actual_end_date]
        avg_duration = None
        if completed:
            durations = [(m.actual_end_date - m.start_date).days / 30 for m in completed]  # Convert to months
            avg_duration = sum(durations) / len(durations)
        
        # Calculate average satisfaction
        satisfaction_ratings = []
        for m in mentorships:
            if m.mentor_satisfaction:
                satisfaction_ratings.append(m.mentor_satisfaction)
            if m.mentee_satisfaction:
                satisfaction_ratings.append(m.mentee_satisfaction)
        
        avg_satisfaction = sum(satisfaction_ratings) / len(satisfaction_ratings) if satisfaction_ratings else None
        
        # Get session statistics
        session_query = select(MentorshipSession).join(Mentorship).where(
            Mentorship.start_date >= start_date
        )
        
        if user_id:
            session_query = session_query.where(
                or_(
                    Mentorship.mentor_id == user_id,
                    Mentorship.mentee_id == user_id
                )
            )
        
        sessions_result = await db.execute(session_query)
        sessions = list(sessions_result.scalars().all())
        
        total_sessions = len(sessions)
        avg_sessions_per_mentorship = total_sessions / total_mentorships if total_mentorships > 0 else 0
        
        # Calculate success rate
        success_rate = completed_mentorships / total_mentorships if total_mentorships > 0 else 0
        
        # Mentorship outcomes
        outcomes = {}
        for status in MentorshipStatus:
            count = len([m for m in mentorships if m.status == status])
            if count > 0:
                outcomes[status.value] = count
        
        return MentorshipStats(
            user_id=user_id,
            total_mentorships=total_mentorships,
            active_mentorships=active_mentorships,
            completed_mentorships=completed_mentorships,
            mentorships_as_mentor=mentorships_as_mentor,
            mentorships_as_mentee=mentorships_as_mentee,
            average_mentorship_duration=avg_duration,
            average_satisfaction_rating=avg_satisfaction,
            total_sessions=total_sessions,
            average_sessions_per_mentorship=avg_sessions_per_mentorship,
            success_rate=success_rate,
            mentorship_outcomes=outcomes
        )
