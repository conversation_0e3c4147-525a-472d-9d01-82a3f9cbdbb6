"""PTES workflow management service."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from pitas.core.workflow import PT<PERSON>WorkflowEngine, PTESPhase, WorkflowStatus, ValidationResult, WorkflowTransition
from pitas.db.models.project import Project, PhaseTransition, ProjectDeliverable
from pitas.schemas.project import (
    WorkflowAdvanceRequest, 
    WorkflowAdvanceResponse,
    PhaseTransitionCreate,
    ProjectDeliverableCreate,
    ProjectDeliverableUpdate
)
from pitas.services.base import BaseService


class WorkflowService(BaseService[Project, None, None]):
    """Service for managing PTES workflow operations."""
    
    def __init__(self):
        """Initialize the workflow service."""
        super().__init__(Project)
        self.workflow_engine = PTESWorkflowEngine()
    
    async def get_project_with_workflow_data(
        self,
        db: AsyncSession,
        project_id: UUID
    ) -> Optional[Project]:
        """Get project with workflow-related data loaded.
        
        Args:
            db: Database session
            project_id: Project ID
            
        Returns:
            Project with workflow data or None if not found
        """
        result = await db.execute(
            select(Project)
            .options(
                selectinload(Project.phase_transitions),
                selectinload(Project.deliverables),
                selectinload(Project.team_assignments)
            )
            .where(Project.id == project_id)
        )
        return result.scalar_one_or_none()
    
    async def advance_project_phase(
        self,
        db: AsyncSession,
        project_id: UUID,
        advance_request: WorkflowAdvanceRequest,
        user_id: UUID
    ) -> WorkflowAdvanceResponse:
        """Advance project to the next PTES phase.
        
        Args:
            db: Database session
            project_id: Project ID
            advance_request: Phase advance request data
            user_id: User performing the advance
            
        Returns:
            Workflow advance response
        """
        # Get project
        project = await self.get_project_with_workflow_data(db, project_id)
        if not project:
            return WorkflowAdvanceResponse(
                success=False,
                from_phase=PTESPhase.PRE_ENGAGEMENT,
                message="Project not found"
            )
        
        # Validate phase completion
        validation_result = self.workflow_engine.validate_phase_completion(
            phase=project.current_phase,
            deliverables=advance_request.deliverables,
            quality_checks=advance_request.quality_checks,
            reviewer_id=advance_request.reviewer_id
        )
        
        # Attempt to advance phase
        transition_result = self.workflow_engine.advance_phase(
            current_phase=project.current_phase,
            validation_result=validation_result
        )
        
        if not transition_result.success:
            return WorkflowAdvanceResponse(
                success=False,
                from_phase=project.current_phase,
                message=transition_result.message,
                validation_issues=validation_result.issues
            )
        
        # Create phase transition record
        transition_data = PhaseTransitionCreate(
            project_id=project_id,
            from_phase=project.current_phase,
            to_phase=transition_result.to_phase,
            transition_reason=advance_request.notes,
            validation_data={
                "deliverables": advance_request.deliverables,
                "quality_checks": advance_request.quality_checks,
                "validation_result": validation_result.dict()
            }
        )
        
        transition_record = PhaseTransition(
            id=uuid4(),
            project_id=transition_data.project_id,
            from_phase=transition_data.from_phase,
            to_phase=transition_data.to_phase,
            transitioned_by=user_id,
            transition_reason=transition_data.transition_reason,
            validation_data=transition_data.validation_data,
            approved_by=advance_request.reviewer_id,
            approved_at=datetime.utcnow() if advance_request.reviewer_id else None
        )
        
        db.add(transition_record)
        
        # Update project phase and status
        if transition_result.to_phase:
            project.current_phase = transition_result.to_phase
            project.progress_percentage = self.workflow_engine.get_phase_progress(transition_result.to_phase)
            
            # Update workflow status
            if transition_result.to_phase == PTESPhase.REPORTING:
                project.workflow_status = WorkflowStatus.PENDING_REVIEW
            else:
                project.workflow_status = WorkflowStatus.IN_PROGRESS
        else:
            # Project completed
            project.workflow_status = WorkflowStatus.COMPLETED
            project.progress_percentage = 1.0
        
        await db.flush()
        await db.refresh(project)
        await db.refresh(transition_record)
        
        return WorkflowAdvanceResponse(
            success=True,
            from_phase=transition_result.from_phase,
            to_phase=transition_result.to_phase,
            message=transition_result.message,
            transition_id=transition_record.id
        )
    
    async def get_phase_requirements(
        self,
        phase: PTESPhase
    ) -> Dict[str, Any]:
        """Get requirements for a specific PTES phase.
        
        Args:
            phase: PTES phase
            
        Returns:
            Phase requirements dictionary
        """
        requirements = self.workflow_engine.phase_requirements.get(phase)
        if not requirements:
            return {}
        
        return {
            "phase": requirements.phase,
            "required_deliverables": requirements.required_deliverables,
            "quality_checks": requirements.quality_checks,
            "approval_required": requirements.approval_required,
            "estimated_hours": requirements.estimated_hours,
            "dependencies": requirements.dependencies
        }
    
    async def get_project_progress_metrics(
        self,
        db: AsyncSession,
        project_id: UUID
    ) -> Dict[str, Any]:
        """Get detailed progress metrics for a project.
        
        Args:
            db: Database session
            project_id: Project ID
            
        Returns:
            Progress metrics dictionary
        """
        project = await self.get_project_with_workflow_data(db, project_id)
        if not project:
            return {}
        
        # Calculate deliverable completion
        total_deliverables = len(project.deliverables)
        completed_deliverables = len([
            d for d in project.deliverables 
            if d.status == WorkflowStatus.COMPLETED
        ])
        
        # Calculate time metrics
        remaining_hours = self.workflow_engine.estimate_remaining_time(project.current_phase)
        time_elapsed_hours = project.actual_hours
        
        # Calculate phase-specific metrics
        current_phase_requirements = await self.get_phase_requirements(project.current_phase)
        
        return {
            "overall_progress": project.progress_percentage,
            "current_phase": project.current_phase,
            "workflow_status": project.workflow_status,
            "phase_progress": {
                "current": project.current_phase,
                "requirements": current_phase_requirements,
                "next_phase": self.workflow_engine.get_next_phase(project.current_phase)
            },
            "deliverables": {
                "total": total_deliverables,
                "completed": completed_deliverables,
                "completion_rate": completed_deliverables / total_deliverables if total_deliverables > 0 else 0
            },
            "time_tracking": {
                "estimated_hours": project.estimated_hours,
                "actual_hours": project.actual_hours,
                "remaining_hours": remaining_hours,
                "utilization_rate": project.actual_hours / project.estimated_hours if project.estimated_hours > 0 else 0
            },
            "quality_metrics": {
                "quality_score": project.quality_score,
                "peer_review_required": project.peer_review_required,
                "quality_checked_deliverables": len([
                    d for d in project.deliverables 
                    if d.quality_checked
                ])
            }
        }
    
    async def create_phase_deliverables(
        self,
        db: AsyncSession,
        project_id: UUID,
        phase: PTESPhase
    ) -> List[ProjectDeliverable]:
        """Create default deliverables for a project phase.
        
        Args:
            db: Database session
            project_id: Project ID
            phase: PTES phase
            
        Returns:
            List of created deliverables
        """
        requirements = self.workflow_engine.phase_requirements.get(phase)
        if not requirements:
            return []
        
        deliverables = []
        for deliverable_name in requirements.required_deliverables:
            deliverable = ProjectDeliverable(
                id=uuid4(),
                project_id=project_id,
                phase=phase,
                name=deliverable_name,
                description=f"Required deliverable for {phase} phase",
                status=WorkflowStatus.NOT_STARTED
            )
            db.add(deliverable)
            deliverables.append(deliverable)
        
        await db.flush()
        return deliverables
    
    async def update_deliverable_status(
        self,
        db: AsyncSession,
        deliverable_id: UUID,
        update_data: ProjectDeliverableUpdate,
        user_id: UUID
    ) -> Optional[ProjectDeliverable]:
        """Update deliverable status and metadata.
        
        Args:
            db: Database session
            deliverable_id: Deliverable ID
            update_data: Update data
            user_id: User performing the update
            
        Returns:
            Updated deliverable or None if not found
        """
        result = await db.execute(
            select(ProjectDeliverable)
            .where(ProjectDeliverable.id == deliverable_id)
        )
        deliverable = result.scalar_one_or_none()
        
        if not deliverable:
            return None
        
        # Update fields
        for field, value in update_data.dict(exclude_unset=True).items():
            setattr(deliverable, field, value)
        
        # Handle status changes
        if update_data.status == WorkflowStatus.COMPLETED and not deliverable.completed_at:
            deliverable.completed_at = datetime.utcnow()
        
        # Handle quality check
        if update_data.quality_checked and not deliverable.quality_checked_at:
            deliverable.quality_checked_by = user_id
            deliverable.quality_checked_at = datetime.utcnow()
        
        await db.flush()
        await db.refresh(deliverable)
        return deliverable
    
    async def get_project_timeline(
        self,
        db: AsyncSession,
        project_id: UUID
    ) -> List[Dict[str, Any]]:
        """Get project timeline with phase transitions and milestones.
        
        Args:
            db: Database session
            project_id: Project ID
            
        Returns:
            List of timeline events
        """
        # Get phase transitions
        result = await db.execute(
            select(PhaseTransition)
            .where(PhaseTransition.project_id == project_id)
            .order_by(PhaseTransition.created_at)
        )
        transitions = result.scalars().all()
        
        timeline = []
        for transition in transitions:
            timeline.append({
                "type": "phase_transition",
                "timestamp": transition.created_at,
                "from_phase": transition.from_phase,
                "to_phase": transition.to_phase,
                "user_id": transition.transitioned_by,
                "reason": transition.transition_reason,
                "approved_by": transition.approved_by,
                "approved_at": transition.approved_at
            })
        
        return timeline
