"""Pentester profile service for Phase 2."""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from .base import BaseService
from ..db.models import PentesterProfile, SkillMatrix, ResourceAllocation, User
from ..schemas.pentester import (
    PentesterProfileCreate, PentesterProfileUpdate, 
    PentesterAvailability, PentesterPerformance
)
from ..core.exceptions import AppException

logger = logging.getLogger(__name__)


class PentesterService(BaseService[PentesterProfile, PentesterProfileCreate, PentesterProfileUpdate]):
    """Service for managing pentester profiles."""
    
    def __init__(self):
        super().__init__(PentesterProfile)
    
    async def get_by_employee_id(
        self,
        db: AsyncSession,
        employee_id: str
    ) -> Optional[PentesterProfile]:
        """Get pentester by employee ID."""
        stmt = select(PentesterProfile).where(PentesterProfile.employee_id == employee_id)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_by_email(
        self,
        db: AsyncSession,
        email: str
    ) -> Optional[PentesterProfile]:
        """Get pentester by email."""
        stmt = select(PentesterProfile).where(PentesterProfile.email == email)
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_with_skills(
        self,
        db: AsyncSession,
        pentester_id: UUID
    ) -> Optional[PentesterProfile]:
        """Get pentester with skill matrices loaded."""
        stmt = (
            select(PentesterProfile)
            .options(selectinload(PentesterProfile.skill_matrices))
            .where(PentesterProfile.id == pentester_id)
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_with_allocations(
        self,
        db: AsyncSession,
        pentester_id: UUID
    ) -> Optional[PentesterProfile]:
        """Get pentester with resource allocations loaded."""
        stmt = (
            select(PentesterProfile)
            .options(selectinload(PentesterProfile.resource_allocations))
            .where(PentesterProfile.id == pentester_id)
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_available_pentesters(
        self,
        db: AsyncSession,
        max_utilization: float = 85.0,
        required_skills: Optional[List[str]] = None,
        minimum_seniority: Optional[str] = None
    ) -> List[PentesterProfile]:
        """Get available pentesters based on criteria."""
        stmt = select(PentesterProfile).where(
            and_(
                PentesterProfile.is_active == True,
                PentesterProfile.current_utilization <= max_utilization,
                PentesterProfile.availability_status.in_(["available", "partially_available"])
            )
        )
        
        # Add seniority filter
        if minimum_seniority:
            seniority_order = ["junior", "mid", "senior", "lead", "principal"]
            if minimum_seniority in seniority_order:
                min_index = seniority_order.index(minimum_seniority)
                valid_levels = seniority_order[min_index:]
                stmt = stmt.where(PentesterProfile.seniority_level.in_(valid_levels))
        
        # Load skill matrices for skill filtering
        stmt = stmt.options(selectinload(PentesterProfile.skill_matrices))
        
        result = await db.execute(stmt)
        pentesters = result.scalars().all()
        
        # Filter by required skills if specified
        if required_skills:
            filtered_pentesters = []
            for pentester in pentesters:
                pentester_skills = {sm.security_domain.value for sm in pentester.skill_matrices}
                if any(skill in pentester_skills for skill in required_skills):
                    filtered_pentesters.append(pentester)
            return filtered_pentesters
        
        return list(pentesters)
    
    async def get_team_leads(
        self,
        db: AsyncSession,
        available_only: bool = True
    ) -> List[PentesterProfile]:
        """Get team leads."""
        stmt = select(PentesterProfile).where(PentesterProfile.is_team_lead == True)
        
        if available_only:
            stmt = stmt.where(
                and_(
                    PentesterProfile.is_active == True,
                    PentesterProfile.current_utilization <= 80.0,  # Team leads need more buffer
                    PentesterProfile.availability_status.in_(["available", "partially_available"])
                )
            )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_by_specialization(
        self,
        db: AsyncSession,
        security_domain: str,
        minimum_skill_level: int = 5
    ) -> List[PentesterProfile]:
        """Get pentesters by security domain specialization."""
        stmt = (
            select(PentesterProfile)
            .join(SkillMatrix)
            .where(
                and_(
                    SkillMatrix.security_domain == security_domain,
                    SkillMatrix.skill_level >= minimum_skill_level,
                    PentesterProfile.is_active == True
                )
            )
            .options(selectinload(PentesterProfile.skill_matrices))
        )
        
        result = await db.execute(stmt)
        return list(result.scalars().all())
    
    async def get_availability_overview(
        self,
        db: AsyncSession,
        team_ids: Optional[List[UUID]] = None
    ) -> List[PentesterAvailability]:
        """Get availability overview for pentesters."""
        stmt = select(PentesterProfile).where(PentesterProfile.is_active == True)
        
        if team_ids:
            stmt = stmt.where(PentesterProfile.id.in_(team_ids))
        
        # Load resource allocations to calculate active projects
        stmt = stmt.options(selectinload(PentesterProfile.resource_allocations))
        
        result = await db.execute(stmt)
        pentesters = result.scalars().all()
        
        availability_list = []
        for pentester in pentesters:
            # Count active projects
            active_projects = sum(
                1 for alloc in pentester.resource_allocations
                if alloc.status == "active"
            )
            
            availability = PentesterAvailability(
                pentester_id=pentester.id,
                full_name=pentester.full_name,
                availability_status=pentester.availability_status,
                current_utilization=pentester.current_utilization,
                available_hours_remaining=pentester.available_hours_remaining,
                working_timezone=pentester.working_timezone,
                max_concurrent_projects=pentester.max_concurrent_projects,
                active_projects=active_projects
            )
            availability_list.append(availability)
        
        return availability_list
    
    async def get_performance_metrics(
        self,
        db: AsyncSession,
        pentester_id: Optional[UUID] = None
    ) -> List[PentesterPerformance]:
        """Get performance metrics for pentesters."""
        stmt = select(PentesterProfile).where(PentesterProfile.is_active == True)
        
        if pentester_id:
            stmt = stmt.where(PentesterProfile.id == pentester_id)
        
        # Load resource allocations for performance calculations
        stmt = stmt.options(selectinload(PentesterProfile.resource_allocations))
        
        result = await db.execute(stmt)
        pentesters = result.scalars().all()
        
        performance_list = []
        for pentester in pentesters:
            # Calculate efficiency score from allocations
            efficiency_scores = [
                alloc.efficiency_score for alloc in pentester.resource_allocations
                if alloc.efficiency_score is not None
            ]
            avg_efficiency = sum(efficiency_scores) / len(efficiency_scores) if efficiency_scores else None
            
            # Calculate average skill match score
            skill_match_scores = [
                alloc.skill_match_score for alloc in pentester.resource_allocations
                if alloc.skill_match_score is not None
            ]
            avg_skill_match = sum(skill_match_scores) / len(skill_match_scores) if skill_match_scores else None
            
            performance = PentesterPerformance(
                pentester_id=pentester.id,
                full_name=pentester.full_name,
                performance_rating=pentester.performance_rating,
                projects_completed=pentester.projects_completed,
                average_project_rating=pentester.average_project_rating,
                current_utilization=pentester.current_utilization,
                efficiency_score=avg_efficiency,
                skill_match_average=avg_skill_match
            )
            performance_list.append(performance)
        
        return performance_list
    
    async def update_utilization(
        self,
        db: AsyncSession,
        pentester_id: UUID,
        new_utilization: float
    ) -> PentesterProfile:
        """Update pentester utilization."""
        pentester = await self.get(db, pentester_id)
        if not pentester:
            raise AppException(f"Pentester not found: {pentester_id}")
        
        pentester.current_utilization = max(0.0, min(100.0, new_utilization))
        
        # Update availability status based on utilization
        if new_utilization >= 95:
            pentester.availability_status = "unavailable"
        elif new_utilization >= 85:
            pentester.availability_status = "busy"
        elif new_utilization >= 50:
            pentester.availability_status = "partially_available"
        else:
            pentester.availability_status = "available"
        
        await db.flush()
        await db.refresh(pentester)
        return pentester
    
    async def calculate_team_capacity(
        self,
        db: AsyncSession,
        team_ids: Optional[List[UUID]] = None
    ) -> Dict[str, Any]:
        """Calculate total team capacity metrics."""
        stmt = select(PentesterProfile).where(PentesterProfile.is_active == True)
        
        if team_ids:
            stmt = stmt.where(PentesterProfile.id.in_(team_ids))
        
        result = await db.execute(stmt)
        pentesters = result.scalars().all()
        
        if not pentesters:
            return {
                "total_capacity": 0,
                "allocated_capacity": 0,
                "available_capacity": 0,
                "average_utilization": 0,
                "team_size": 0
            }
        
        total_capacity = sum(p.availability_hours for p in pentesters)
        allocated_capacity = sum(
            (p.availability_hours * p.current_utilization / 100) for p in pentesters
        )
        available_capacity = total_capacity - allocated_capacity
        average_utilization = sum(p.current_utilization for p in pentesters) / len(pentesters)
        
        return {
            "total_capacity": total_capacity,
            "allocated_capacity": allocated_capacity,
            "available_capacity": available_capacity,
            "average_utilization": average_utilization,
            "team_size": len(pentesters)
        }
    
    async def get_skill_distribution(
        self,
        db: AsyncSession,
        team_ids: Optional[List[UUID]] = None
    ) -> Dict[str, Dict[str, int]]:
        """Get skill distribution across the team."""
        stmt = (
            select(PentesterProfile)
            .options(selectinload(PentesterProfile.skill_matrices))
            .where(PentesterProfile.is_active == True)
        )
        
        if team_ids:
            stmt = stmt.where(PentesterProfile.id.in_(team_ids))
        
        result = await db.execute(stmt)
        pentesters = result.scalars().all()
        
        skill_distribution = {}
        
        for pentester in pentesters:
            for skill_matrix in pentester.skill_matrices:
                domain = skill_matrix.security_domain.value
                level = skill_matrix.skill_level
                
                if domain not in skill_distribution:
                    skill_distribution[domain] = {
                        "beginner": 0,    # 1-3
                        "intermediate": 0, # 4-6
                        "advanced": 0,    # 7-8
                        "expert": 0       # 9-10
                    }
                
                if level <= 3:
                    skill_distribution[domain]["beginner"] += 1
                elif level <= 6:
                    skill_distribution[domain]["intermediate"] += 1
                elif level <= 8:
                    skill_distribution[domain]["advanced"] += 1
                else:
                    skill_distribution[domain]["expert"] += 1
        
        return skill_distribution
