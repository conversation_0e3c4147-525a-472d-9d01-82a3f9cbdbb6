"""Sync service for managing integration synchronization operations."""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

import structlog
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.models.integration import Integration, IntegrationSyncLog, IntegrationStatus, SyncStatus
from pitas.schemas.integration import (
    IntegrationSyncLogCreate, SyncOperationRequest, SyncOperationStatus
)
from pitas.services.base import BaseService

logger = structlog.get_logger(__name__)


class SyncService(BaseService[IntegrationSyncLog, IntegrationSyncLogCreate, dict]):
    """Service for managing synchronization operations."""

    def __init__(self):
        super().__init__(IntegrationSyncLog)
        self._active_syncs: Dict[str, Dict[str, Any]] = {}

    async def start_sync_operation(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
        sync_request: SyncOperationRequest,
    ) -> str:
        """Start a synchronization operation.
        
        Args:
            db: Database session
            integration_id: Integration ID
            sync_request: Sync request parameters
            
        Returns:
            Operation ID
        """
        # Check if integration exists and is enabled
        integration_query = select(Integration).where(Integration.id == integration_id)
        result = await db.execute(integration_query)
        integration = result.scalar_one_or_none()
        
        if not integration:
            raise ValueError("Integration not found")
        
        if not integration.is_enabled:
            raise ValueError("Integration is disabled")
        
        # Check if sync is already in progress
        if integration.status == IntegrationStatus.SYNCING:
            if not sync_request.force_sync:
                raise ValueError("Sync already in progress")
        
        # Generate operation ID
        operation_id = str(uuid.uuid4())
        
        # Create sync log entry
        sync_log_data = IntegrationSyncLogCreate(
            integration_id=integration_id,
            status=SyncStatus.IN_PROGRESS,
            started_at=datetime.utcnow(),
            sync_metadata={
                "operation_id": operation_id,
                "sync_type": sync_request.sync_type,
                "force_sync": sync_request.force_sync,
                "sync_options": sync_request.sync_options or {}
            }
        )
        
        sync_log = await self.create(db, obj_in=sync_log_data)
        
        # Update integration status
        await self._update_integration_status(
            db, integration_id, IntegrationStatus.SYNCING
        )
        
        # Store operation status
        self._active_syncs[operation_id] = {
            "integration_id": integration_id,
            "sync_log_id": sync_log.id,
            "status": SyncStatus.IN_PROGRESS,
            "progress": 0.0,
            "message": "Sync operation started",
            "started_at": sync_log.started_at,
            "records_processed": 0,
            "total_records": None,
        }
        
        # Start async sync task
        asyncio.create_task(
            self._execute_sync_operation(db, operation_id, integration, sync_request)
        )
        
        logger.info(
            "Sync operation started",
            operation_id=operation_id,
            integration_id=integration_id,
            sync_type=sync_request.sync_type
        )
        
        return operation_id

    async def get_sync_status(self, operation_id: str) -> Optional[SyncOperationStatus]:
        """Get sync operation status.
        
        Args:
            operation_id: Operation ID
            
        Returns:
            Sync operation status or None if not found
        """
        if operation_id not in self._active_syncs:
            return None
        
        sync_data = self._active_syncs[operation_id]
        
        # Estimate completion time
        estimated_completion = None
        if sync_data["progress"] > 0 and sync_data["total_records"]:
            elapsed = (datetime.utcnow() - sync_data["started_at"]).total_seconds()
            estimated_total = elapsed / sync_data["progress"]
            estimated_completion = sync_data["started_at"] + timedelta(seconds=estimated_total)
        
        return SyncOperationStatus(
            operation_id=operation_id,
            status=sync_data["status"],
            progress=sync_data["progress"],
            message=sync_data["message"],
            started_at=sync_data["started_at"],
            estimated_completion=estimated_completion,
            records_processed=sync_data["records_processed"],
            total_records=sync_data["total_records"]
        )

    async def cancel_sync_operation(
        self,
        db: AsyncSession,
        *,
        operation_id: str,
    ) -> bool:
        """Cancel a sync operation.
        
        Args:
            db: Database session
            operation_id: Operation ID
            
        Returns:
            True if cancelled, False if not found or already completed
        """
        if operation_id not in self._active_syncs:
            return False
        
        sync_data = self._active_syncs[operation_id]
        
        if sync_data["status"] not in [SyncStatus.IN_PROGRESS]:
            return False
        
        # Update status
        sync_data["status"] = SyncStatus.FAILED
        sync_data["message"] = "Sync operation cancelled"
        
        # Update sync log
        await self._complete_sync_log(
            db,
            sync_data["sync_log_id"],
            SyncStatus.FAILED,
            error_message="Operation cancelled by user"
        )
        
        # Update integration status
        await self._update_integration_status(
            db, sync_data["integration_id"], IntegrationStatus.ACTIVE
        )
        
        # Remove from active syncs
        del self._active_syncs[operation_id]
        
        logger.info("Sync operation cancelled", operation_id=operation_id)
        
        return True

    async def get_sync_logs(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
        limit: int = 50,
        offset: int = 0,
    ) -> List[IntegrationSyncLog]:
        """Get sync logs for an integration.
        
        Args:
            db: Database session
            integration_id: Integration ID
            limit: Maximum number of logs to return
            offset: Number of logs to skip
            
        Returns:
            List of sync logs
        """
        query = (
            select(IntegrationSyncLog)
            .where(IntegrationSyncLog.integration_id == integration_id)
            .order_by(IntegrationSyncLog.started_at.desc())
            .offset(offset)
            .limit(limit)
        )
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def _execute_sync_operation(
        self,
        db: AsyncSession,
        operation_id: str,
        integration: Integration,
        sync_request: SyncOperationRequest,
    ):
        """Execute the actual sync operation.
        
        Args:
            db: Database session
            operation_id: Operation ID
            integration: Integration instance
            sync_request: Sync request parameters
        """
        sync_data = self._active_syncs[operation_id]
        
        try:
            # Get the appropriate connector
            connector = await self._get_connector(integration)
            
            # Update progress
            sync_data["message"] = "Initializing sync"
            sync_data["progress"] = 0.1
            
            # Execute sync based on integration type
            sync_result = await connector.sync_data(
                sync_type=sync_request.sync_type,
                options=sync_request.sync_options or {},
                progress_callback=lambda progress, message, records_processed, total_records: 
                    self._update_sync_progress(
                        operation_id, progress, message, records_processed, total_records
                    )
            )
            
            # Complete sync log
            await self._complete_sync_log(
                db,
                sync_data["sync_log_id"],
                SyncStatus.SUCCESS,
                records_processed=sync_result.get("records_processed", 0),
                records_created=sync_result.get("records_created", 0),
                records_updated=sync_result.get("records_updated", 0),
                records_failed=sync_result.get("records_failed", 0),
                sync_metadata=sync_result.get("metadata", {})
            )
            
            # Update integration status
            await self._update_integration_status(
                db, integration.id, IntegrationStatus.ACTIVE
            )
            
            # Update operation status
            sync_data["status"] = SyncStatus.SUCCESS
            sync_data["progress"] = 1.0
            sync_data["message"] = "Sync completed successfully"
            
            logger.info(
                "Sync operation completed successfully",
                operation_id=operation_id,
                integration_id=integration.id,
                records_processed=sync_result.get("records_processed", 0)
            )
            
        except Exception as e:
            logger.error(
                "Sync operation failed",
                operation_id=operation_id,
                integration_id=integration.id,
                error=str(e)
            )
            
            # Complete sync log with error
            await self._complete_sync_log(
                db,
                sync_data["sync_log_id"],
                SyncStatus.FAILED,
                error_message=str(e),
                error_details={"exception_type": type(e).__name__}
            )
            
            # Update integration status
            await self._update_integration_status(
                db, integration.id, IntegrationStatus.ERROR, error_message=str(e)
            )
            
            # Update operation status
            sync_data["status"] = SyncStatus.FAILED
            sync_data["message"] = f"Sync failed: {str(e)}"
        
        finally:
            # Remove from active syncs after a delay to allow status queries
            await asyncio.sleep(60)  # Keep status available for 1 minute
            if operation_id in self._active_syncs:
                del self._active_syncs[operation_id]

    def _update_sync_progress(
        self,
        operation_id: str,
        progress: float,
        message: str,
        records_processed: int,
        total_records: Optional[int] = None,
    ):
        """Update sync progress.
        
        Args:
            operation_id: Operation ID
            progress: Progress (0.0 to 1.0)
            message: Progress message
            records_processed: Number of records processed
            total_records: Total number of records
        """
        if operation_id in self._active_syncs:
            sync_data = self._active_syncs[operation_id]
            sync_data["progress"] = max(0.0, min(1.0, progress))
            sync_data["message"] = message
            sync_data["records_processed"] = records_processed
            if total_records is not None:
                sync_data["total_records"] = total_records

    async def _complete_sync_log(
        self,
        db: AsyncSession,
        sync_log_id: UUID,
        status: SyncStatus,
        *,
        records_processed: int = 0,
        records_created: int = 0,
        records_updated: int = 0,
        records_failed: int = 0,
        error_message: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None,
        sync_metadata: Optional[Dict[str, Any]] = None,
    ):
        """Complete a sync log entry.
        
        Args:
            db: Database session
            sync_log_id: Sync log ID
            status: Final status
            records_processed: Number of records processed
            records_created: Number of records created
            records_updated: Number of records updated
            records_failed: Number of records failed
            error_message: Error message if failed
            error_details: Error details if failed
            sync_metadata: Sync metadata
        """
        sync_log = await self.get(db, id=sync_log_id)
        if sync_log:
            update_data = {
                "status": status,
                "completed_at": datetime.utcnow(),
                "records_processed": records_processed,
                "records_created": records_created,
                "records_updated": records_updated,
                "records_failed": records_failed,
            }
            
            if error_message:
                update_data["error_message"] = error_message
            if error_details:
                update_data["error_details"] = error_details
            if sync_metadata:
                update_data["sync_metadata"] = sync_metadata
            
            await self.update(db, db_obj=sync_log, obj_in=update_data)

    async def _update_integration_status(
        self,
        db: AsyncSession,
        integration_id: UUID,
        status: IntegrationStatus,
        *,
        error_message: Optional[str] = None,
    ):
        """Update integration status.
        
        Args:
            db: Database session
            integration_id: Integration ID
            status: New status
            error_message: Error message if failed
        """
        from pitas.services.integration.integration_service import IntegrationService
        
        integration_service = IntegrationService()
        await integration_service.update_sync_status(
            db,
            integration_id=integration_id,
            status=status,
            last_sync=datetime.utcnow() if status == IntegrationStatus.ACTIVE else None,
            error_message=error_message
        )

    async def _get_connector(self, integration: Integration):
        """Get the appropriate connector for an integration.
        
        Args:
            integration: Integration instance
            
        Returns:
            Connector instance
        """
        # This would be the same logic as in IntegrationService
        # Import here to avoid circular imports
        from pitas.services.integration.integration_service import IntegrationService
        
        integration_service = IntegrationService()
        return await integration_service._get_connector(integration)
