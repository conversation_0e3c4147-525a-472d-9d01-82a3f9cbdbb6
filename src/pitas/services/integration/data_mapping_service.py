"""Data mapping service for integration data transformation."""

import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from uuid import UUID

import structlog
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.models.integration import DataMapping, Integration
from pitas.schemas.integration import DataMappingCreate, DataMappingUpdate
from pitas.services.base import BaseService

logger = structlog.get_logger(__name__)


class DataMappingService(BaseService[DataMapping, DataMappingCreate, DataMappingUpdate]):
    """Service for managing data mappings between systems."""

    def __init__(self):
        super().__init__(DataMapping)

    async def get_mappings_for_integration(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
    ) -> List[DataMapping]:
        """Get all data mappings for an integration.
        
        Args:
            db: Database session
            integration_id: Integration ID
            
        Returns:
            List of data mappings
        """
        query = select(DataMapping).where(DataMapping.integration_id == integration_id)
        result = await db.execute(query)
        return list(result.scalars().all())

    async def create_mapping(
        self,
        db: AsyncSession,
        *,
        mapping_data: DataMappingCreate,
    ) -> DataMapping:
        """Create a new data mapping.
        
        Args:
            db: Database session
            mapping_data: Mapping creation data
            
        Returns:
            Created data mapping
        """
        # Validate the mapping
        await self._validate_mapping(db, mapping_data)
        
        mapping = await self.create(db, obj_in=mapping_data)
        
        logger.info(
            "Data mapping created",
            mapping_id=mapping.id,
            integration_id=mapping.integration_id,
            source_field=mapping.source_field,
            target_field=mapping.target_field
        )
        
        return mapping

    async def update_mapping(
        self,
        db: AsyncSession,
        *,
        mapping_id: UUID,
        mapping_data: DataMappingUpdate,
    ) -> Optional[DataMapping]:
        """Update an existing data mapping.
        
        Args:
            db: Database session
            mapping_id: Mapping ID
            mapping_data: Update data
            
        Returns:
            Updated mapping or None if not found
        """
        mapping = await self.get(db, id=mapping_id)
        if not mapping:
            return None
        
        # Validate the updated mapping
        if mapping_data.model_dump(exclude_unset=True):
            # Create a temporary mapping object for validation
            temp_data = DataMappingCreate(
                integration_id=mapping.integration_id,
                source_field=mapping_data.source_field or mapping.source_field,
                target_field=mapping_data.target_field or mapping.target_field,
                field_type=mapping_data.field_type or mapping.field_type,
                transformation_rules=mapping_data.transformation_rules or mapping.transformation_rules,
                is_required=mapping_data.is_required if mapping_data.is_required is not None else mapping.is_required,
                default_value=mapping_data.default_value or mapping.default_value,
                validation_rules=mapping_data.validation_rules or mapping.validation_rules
            )
            await self._validate_mapping(db, temp_data)
        
        updated_mapping = await self.update(db, db_obj=mapping, obj_in=mapping_data)
        
        logger.info(
            "Data mapping updated",
            mapping_id=mapping.id,
            changes=list(mapping_data.model_dump(exclude_unset=True).keys())
        )
        
        return updated_mapping

    async def transform_data(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
        source_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Transform source data using mappings.
        
        Args:
            db: Database session
            integration_id: Integration ID
            source_data: Source data to transform
            
        Returns:
            Transformed data
        """
        mappings = await self.get_mappings_for_integration(db, integration_id=integration_id)
        
        transformed_data = {}
        errors = []
        
        for mapping in mappings:
            try:
                value = self._extract_source_value(source_data, mapping.source_field)
                
                # Apply transformations
                if mapping.transformation_rules:
                    value = self._apply_transformations(value, mapping.transformation_rules)
                
                # Apply validation
                if mapping.validation_rules:
                    is_valid, validation_error = self._validate_value(value, mapping.validation_rules)
                    if not is_valid:
                        if mapping.is_required:
                            errors.append(f"Validation failed for {mapping.target_field}: {validation_error}")
                            continue
                        else:
                            logger.warning(
                                "Validation warning for optional field",
                                field=mapping.target_field,
                                error=validation_error
                            )
                
                # Handle missing required fields
                if value is None and mapping.is_required:
                    if mapping.default_value:
                        value = mapping.default_value
                    else:
                        errors.append(f"Required field {mapping.target_field} is missing")
                        continue
                
                # Set the transformed value
                if value is not None:
                    self._set_target_value(transformed_data, mapping.target_field, value)
                
            except Exception as e:
                error_msg = f"Error transforming {mapping.source_field} to {mapping.target_field}: {str(e)}"
                errors.append(error_msg)
                logger.error("Data transformation error", error=error_msg)
        
        if errors:
            transformed_data["_transformation_errors"] = errors
        
        return transformed_data

    async def validate_mapping_set(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
    ) -> Dict[str, Any]:
        """Validate a complete set of mappings for an integration.
        
        Args:
            db: Database session
            integration_id: Integration ID
            
        Returns:
            Validation results
        """
        mappings = await self.get_mappings_for_integration(db, integration_id=integration_id)
        
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "mapping_count": len(mappings),
            "required_mappings": len([m for m in mappings if m.is_required]),
        }
        
        # Check for duplicate target fields
        target_fields = [m.target_field for m in mappings]
        duplicates = [field for field in set(target_fields) if target_fields.count(field) > 1]
        if duplicates:
            validation_results["errors"].append(f"Duplicate target fields: {duplicates}")
            validation_results["is_valid"] = False
        
        # Validate individual mappings
        for mapping in mappings:
            if mapping.transformation_rules:
                try:
                    self._validate_transformation_rules(mapping.transformation_rules)
                except ValueError as e:
                    validation_results["errors"].append(
                        f"Invalid transformation rules for {mapping.target_field}: {str(e)}"
                    )
                    validation_results["is_valid"] = False
            
            if mapping.validation_rules:
                try:
                    self._validate_validation_rules(mapping.validation_rules)
                except ValueError as e:
                    validation_results["errors"].append(
                        f"Invalid validation rules for {mapping.target_field}: {str(e)}"
                    )
                    validation_results["is_valid"] = False
        
        return validation_results

    def _extract_source_value(self, source_data: Dict[str, Any], field_path: str) -> Any:
        """Extract value from source data using field path.
        
        Args:
            source_data: Source data dictionary
            field_path: Field path (supports dot notation)
            
        Returns:
            Extracted value or None if not found
        """
        try:
            value = source_data
            for part in field_path.split('.'):
                if isinstance(value, dict):
                    value = value.get(part)
                elif isinstance(value, list) and part.isdigit():
                    index = int(part)
                    value = value[index] if 0 <= index < len(value) else None
                else:
                    return None
            return value
        except (KeyError, IndexError, TypeError):
            return None

    def _set_target_value(self, target_data: Dict[str, Any], field_path: str, value: Any):
        """Set value in target data using field path.
        
        Args:
            target_data: Target data dictionary
            field_path: Field path (supports dot notation)
            value: Value to set
        """
        parts = field_path.split('.')
        current = target_data
        
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        
        current[parts[-1]] = value

    def _apply_transformations(self, value: Any, transformation_rules: Dict[str, Any]) -> Any:
        """Apply transformation rules to a value.
        
        Args:
            value: Value to transform
            transformation_rules: Transformation rules
            
        Returns:
            Transformed value
        """
        if value is None:
            return value
        
        for rule_type, rule_config in transformation_rules.items():
            if rule_type == "type_conversion":
                value = self._convert_type(value, rule_config)
            elif rule_type == "string_operations":
                value = self._apply_string_operations(value, rule_config)
            elif rule_type == "numeric_operations":
                value = self._apply_numeric_operations(value, rule_config)
            elif rule_type == "date_operations":
                value = self._apply_date_operations(value, rule_config)
            elif rule_type == "mapping":
                value = self._apply_value_mapping(value, rule_config)
            elif rule_type == "regex":
                value = self._apply_regex_transformation(value, rule_config)
        
        return value

    def _convert_type(self, value: Any, target_type: str) -> Any:
        """Convert value to target type.
        
        Args:
            value: Value to convert
            target_type: Target type
            
        Returns:
            Converted value
        """
        if target_type == "string":
            return str(value)
        elif target_type == "integer":
            return int(float(str(value)))
        elif target_type == "float":
            return float(str(value))
        elif target_type == "boolean":
            if isinstance(value, str):
                return value.lower() in ("true", "yes", "1", "on")
            return bool(value)
        else:
            return value

    def _apply_string_operations(self, value: Any, operations: Dict[str, Any]) -> str:
        """Apply string operations.
        
        Args:
            value: Value to process
            operations: String operations
            
        Returns:
            Processed string
        """
        result = str(value)
        
        if operations.get("trim"):
            result = result.strip()
        if operations.get("upper"):
            result = result.upper()
        if operations.get("lower"):
            result = result.lower()
        if operations.get("replace"):
            for old, new in operations["replace"].items():
                result = result.replace(old, new)
        
        return result

    def _apply_numeric_operations(self, value: Any, operations: Dict[str, Any]) -> Union[int, float]:
        """Apply numeric operations.
        
        Args:
            value: Value to process
            operations: Numeric operations
            
        Returns:
            Processed number
        """
        result = float(value)
        
        if "multiply" in operations:
            result *= operations["multiply"]
        if "divide" in operations:
            result /= operations["divide"]
        if "add" in operations:
            result += operations["add"]
        if "subtract" in operations:
            result -= operations["subtract"]
        if "round" in operations:
            result = round(result, operations["round"])
        
        return result

    def _apply_date_operations(self, value: Any, operations: Dict[str, Any]) -> str:
        """Apply date operations.
        
        Args:
            value: Value to process
            operations: Date operations
            
        Returns:
            Processed date string
        """
        # This would implement date parsing and formatting
        # For now, return as string
        return str(value)

    def _apply_value_mapping(self, value: Any, mapping: Dict[str, Any]) -> Any:
        """Apply value mapping.
        
        Args:
            value: Value to map
            mapping: Value mapping dictionary
            
        Returns:
            Mapped value
        """
        return mapping.get(str(value), value)

    def _apply_regex_transformation(self, value: Any, regex_config: Dict[str, Any]) -> str:
        """Apply regex transformation.
        
        Args:
            value: Value to process
            regex_config: Regex configuration
            
        Returns:
            Processed string
        """
        pattern = regex_config.get("pattern")
        replacement = regex_config.get("replacement", "")
        
        if pattern:
            return re.sub(pattern, replacement, str(value))
        
        return str(value)

    def _validate_value(self, value: Any, validation_rules: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """Validate a value against validation rules.
        
        Args:
            value: Value to validate
            validation_rules: Validation rules
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        for rule_type, rule_config in validation_rules.items():
            if rule_type == "required" and rule_config and value is None:
                return False, "Value is required"
            elif rule_type == "min_length" and len(str(value)) < rule_config:
                return False, f"Value must be at least {rule_config} characters"
            elif rule_type == "max_length" and len(str(value)) > rule_config:
                return False, f"Value must be at most {rule_config} characters"
            elif rule_type == "pattern" and not re.match(rule_config, str(value)):
                return False, f"Value does not match pattern {rule_config}"
            elif rule_type == "min_value" and float(value) < rule_config:
                return False, f"Value must be at least {rule_config}"
            elif rule_type == "max_value" and float(value) > rule_config:
                return False, f"Value must be at most {rule_config}"
        
        return True, None

    async def _validate_mapping(self, db: AsyncSession, mapping_data: DataMappingCreate):
        """Validate a data mapping.
        
        Args:
            db: Database session
            mapping_data: Mapping data to validate
        """
        # Check if integration exists
        integration_query = select(Integration).where(Integration.id == mapping_data.integration_id)
        result = await db.execute(integration_query)
        integration = result.scalar_one_or_none()
        
        if not integration:
            raise ValueError("Integration not found")
        
        # Validate transformation rules
        if mapping_data.transformation_rules:
            self._validate_transformation_rules(mapping_data.transformation_rules)
        
        # Validate validation rules
        if mapping_data.validation_rules:
            self._validate_validation_rules(mapping_data.validation_rules)

    def _validate_transformation_rules(self, transformation_rules: Dict[str, Any]):
        """Validate transformation rules.
        
        Args:
            transformation_rules: Transformation rules to validate
        """
        valid_rule_types = {
            "type_conversion", "string_operations", "numeric_operations",
            "date_operations", "mapping", "regex"
        }
        
        for rule_type in transformation_rules.keys():
            if rule_type not in valid_rule_types:
                raise ValueError(f"Invalid transformation rule type: {rule_type}")

    def _validate_validation_rules(self, validation_rules: Dict[str, Any]):
        """Validate validation rules.
        
        Args:
            validation_rules: Validation rules to validate
        """
        valid_rule_types = {
            "required", "min_length", "max_length", "pattern",
            "min_value", "max_value"
        }
        
        for rule_type in validation_rules.keys():
            if rule_type not in valid_rule_types:
                raise ValueError(f"Invalid validation rule type: {rule_type}")
