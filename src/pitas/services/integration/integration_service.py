"""Integration service for managing external system connections."""

import asyncio
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

import structlog
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.models.integration import Integration, IntegrationSyncLog, IntegrationType, IntegrationStatus, SyncStatus
from pitas.schemas.integration import (
    IntegrationCreate, IntegrationUpdate, IntegrationTestRequest, IntegrationTestResult
)
from pitas.services.base import BaseService
from pitas.core.config import settings

logger = structlog.get_logger(__name__)


class IntegrationService(BaseService[Integration, IntegrationCreate, IntegrationUpdate]):
    """Service for managing integrations."""

    def __init__(self):
        super().__init__(Integration)

    async def create_integration(
        self,
        db: AsyncSession,
        *,
        integration_data: IntegrationCreate,
    ) -> Integration:
        """Create a new integration with encrypted credentials.
        
        Args:
            db: Database session
            integration_data: Integration creation data
            
        Returns:
            Created integration
        """
        # Encrypt sensitive fields before storing
        encrypted_data = integration_data.model_dump()
        if encrypted_data.get("password"):
            encrypted_data["password"] = self._encrypt_credential(encrypted_data["password"])
        if encrypted_data.get("api_key"):
            encrypted_data["api_key"] = self._encrypt_credential(encrypted_data["api_key"])
        
        # Set next sync time
        if encrypted_data.get("sync_interval"):
            encrypted_data["next_sync"] = datetime.utcnow() + timedelta(
                seconds=encrypted_data["sync_interval"]
            )
        
        integration = await self.create(db, obj_in=IntegrationCreate(**encrypted_data))
        
        logger.info(
            "Integration created",
            integration_id=integration.id,
            integration_type=integration.integration_type,
            name=integration.name
        )
        
        return integration

    async def update_integration(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
        integration_data: IntegrationUpdate,
    ) -> Optional[Integration]:
        """Update an existing integration.
        
        Args:
            db: Database session
            integration_id: Integration ID
            integration_data: Update data
            
        Returns:
            Updated integration or None if not found
        """
        integration = await self.get(db, id=integration_id)
        if not integration:
            return None
        
        # Encrypt sensitive fields if provided
        update_data = integration_data.model_dump(exclude_unset=True)
        if "password" in update_data and update_data["password"]:
            update_data["password"] = self._encrypt_credential(update_data["password"])
        if "api_key" in update_data and update_data["api_key"]:
            update_data["api_key"] = self._encrypt_credential(update_data["api_key"])
        
        # Update next sync time if interval changed
        if "sync_interval" in update_data:
            update_data["next_sync"] = datetime.utcnow() + timedelta(
                seconds=update_data["sync_interval"]
            )
        
        updated_integration = await self.update(db, db_obj=integration, obj_in=update_data)
        
        logger.info(
            "Integration updated",
            integration_id=integration.id,
            changes=list(update_data.keys())
        )
        
        return updated_integration

    async def test_integration(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
        test_request: IntegrationTestRequest,
    ) -> IntegrationTestResult:
        """Test integration connectivity.
        
        Args:
            db: Database session
            integration_id: Integration ID
            test_request: Test request parameters
            
        Returns:
            Test result
        """
        integration = await self.get(db, id=integration_id)
        if not integration:
            return IntegrationTestResult(
                success=False,
                message="Integration not found",
                timestamp=datetime.utcnow()
            )
        
        start_time = datetime.utcnow()
        
        try:
            # Import the appropriate connector based on integration type
            connector = await self._get_connector(integration)
            
            # Perform the test
            test_result = await connector.test_connection(test_request.test_parameters or {})
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            logger.info(
                "Integration test completed",
                integration_id=integration.id,
                success=test_result.get("success", False),
                response_time=response_time
            )
            
            return IntegrationTestResult(
                success=test_result.get("success", False),
                message=test_result.get("message", "Test completed"),
                response_time=response_time,
                details=test_result.get("details"),
                timestamp=start_time
            )
            
        except Exception as e:
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            logger.error(
                "Integration test failed",
                integration_id=integration.id,
                error=str(e),
                response_time=response_time
            )
            
            return IntegrationTestResult(
                success=False,
                message=f"Test failed: {str(e)}",
                response_time=response_time,
                timestamp=start_time
            )

    async def get_integrations_by_type(
        self,
        db: AsyncSession,
        *,
        integration_type: IntegrationType,
        enabled_only: bool = True,
    ) -> List[Integration]:
        """Get integrations by type.
        
        Args:
            db: Database session
            integration_type: Type of integration
            enabled_only: Only return enabled integrations
            
        Returns:
            List of integrations
        """
        query = select(Integration).where(Integration.integration_type == integration_type)
        
        if enabled_only:
            query = query.where(Integration.is_enabled == True)
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def get_integrations_due_for_sync(
        self,
        db: AsyncSession,
    ) -> List[Integration]:
        """Get integrations that are due for synchronization.
        
        Args:
            db: Database session
            
        Returns:
            List of integrations due for sync
        """
        now = datetime.utcnow()
        
        query = select(Integration).where(
            and_(
                Integration.is_enabled == True,
                Integration.status == IntegrationStatus.ACTIVE,
                Integration.next_sync <= now
            )
        )
        
        result = await db.execute(query)
        return list(result.scalars().all())

    async def update_sync_status(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
        status: IntegrationStatus,
        last_sync: Optional[datetime] = None,
        next_sync: Optional[datetime] = None,
        error_message: Optional[str] = None,
    ) -> Optional[Integration]:
        """Update integration sync status.
        
        Args:
            db: Database session
            integration_id: Integration ID
            status: New status
            last_sync: Last sync time
            next_sync: Next sync time
            error_message: Error message if failed
            
        Returns:
            Updated integration or None if not found
        """
        integration = await self.get(db, id=integration_id)
        if not integration:
            return None
        
        update_data = {"status": status}
        
        if last_sync:
            update_data["last_sync"] = last_sync
        
        if next_sync:
            update_data["next_sync"] = next_sync
        elif status == IntegrationStatus.ACTIVE and integration.sync_interval:
            # Schedule next sync
            update_data["next_sync"] = datetime.utcnow() + timedelta(
                seconds=integration.sync_interval
            )
        
        if error_message:
            update_data["last_error"] = error_message
            update_data["retry_count"] = integration.retry_count + 1
        else:
            update_data["retry_count"] = 0
            update_data["last_error"] = None
        
        return await self.update(db, db_obj=integration, obj_in=update_data)

    def _encrypt_credential(self, credential: str) -> str:
        """Encrypt a credential for storage.
        
        Args:
            credential: Credential to encrypt
            
        Returns:
            Encrypted credential
        """
        # Simple hash for now - in production, use proper encryption
        return hashlib.sha256(f"{settings.secret_key}{credential}".encode()).hexdigest()

    async def _get_connector(self, integration: Integration):
        """Get the appropriate connector for an integration.
        
        Args:
            integration: Integration instance
            
        Returns:
            Connector instance
        """
        # Dynamic import based on integration type
        if integration.integration_type == IntegrationType.OBSIDIAN:
            from pitas.services.obsidian.obsidian_connector import ObsidianConnector
            return ObsidianConnector(integration)
        elif integration.integration_type in [
            IntegrationType.CMDB_SERVICENOW,
            IntegrationType.CMDB_BMC_REMEDY
        ]:
            from pitas.services.cmdb.cmdb_connector import CMDBConnector
            return CMDBConnector(integration)
        elif integration.integration_type in [
            IntegrationType.SIEM_SPLUNK,
            IntegrationType.SIEM_QRADAR,
            IntegrationType.SIEM_SENTINEL
        ]:
            from pitas.services.security_tools.siem_connector import SIEMConnector
            return SIEMConnector(integration)
        elif integration.integration_type in [
            IntegrationType.VULN_NESSUS,
            IntegrationType.VULN_QUALYS,
            IntegrationType.VULN_RAPID7
        ]:
            from pitas.services.security_tools.vulnerability_connector import VulnerabilityConnector
            return VulnerabilityConnector(integration)
        else:
            raise ValueError(f"Unsupported integration type: {integration.integration_type}")

    async def get_integration_health(
        self,
        db: AsyncSession,
        *,
        integration_id: UUID,
    ) -> Dict[str, Any]:
        """Get integration health metrics.
        
        Args:
            db: Database session
            integration_id: Integration ID
            
        Returns:
            Health metrics
        """
        integration = await self.get(db, id=integration_id)
        if not integration:
            return {"error": "Integration not found"}
        
        # Get recent sync logs
        recent_logs_query = select(IntegrationSyncLog).where(
            and_(
                IntegrationSyncLog.integration_id == integration_id,
                IntegrationSyncLog.started_at >= datetime.utcnow() - timedelta(days=7)
            )
        ).order_by(IntegrationSyncLog.started_at.desc()).limit(10)
        
        result = await db.execute(recent_logs_query)
        recent_logs = list(result.scalars().all())
        
        # Calculate health metrics
        total_syncs = len(recent_logs)
        successful_syncs = len([log for log in recent_logs if log.status == SyncStatus.SUCCESS])
        failed_syncs = len([log for log in recent_logs if log.status == SyncStatus.FAILED])
        
        success_rate = (successful_syncs / total_syncs * 100) if total_syncs > 0 else 0
        
        # Calculate average sync time
        completed_logs = [log for log in recent_logs if log.completed_at]
        avg_sync_time = None
        if completed_logs:
            sync_times = [
                (log.completed_at - log.started_at).total_seconds()
                for log in completed_logs
            ]
            avg_sync_time = sum(sync_times) / len(sync_times)
        
        return {
            "integration_id": integration_id,
            "status": integration.status,
            "is_enabled": integration.is_enabled,
            "last_sync": integration.last_sync,
            "next_sync": integration.next_sync,
            "retry_count": integration.retry_count,
            "last_error": integration.last_error,
            "health_metrics": {
                "total_syncs_7d": total_syncs,
                "successful_syncs_7d": successful_syncs,
                "failed_syncs_7d": failed_syncs,
                "success_rate_7d": round(success_rate, 2),
                "avg_sync_time_seconds": round(avg_sync_time, 2) if avg_sync_time else None,
            }
        }
