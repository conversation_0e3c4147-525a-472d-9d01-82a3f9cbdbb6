"""Analytics schemas for Phase 9: Advanced Analytics and Reporting Engine."""

from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict

from pitas.schemas.base import BaseSchema, BaseDBSchema


class ModelType(str, Enum):
    """Machine learning model types."""
    VULNERABILITY_PREDICTION = "vulnerability_prediction"
    THREAT_CLASSIFICATION = "threat_classification"
    REMEDIATION_TIMELINE = "remediation_timeline"
    TEAM_OPTIMIZATION = "team_optimization"
    ANOMALY_DETECTION = "anomaly_detection"
    RISK_SCORING = "risk_scoring"


class TrainingStatus(str, Enum):
    """Model training job status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AlertSeverity(str, Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, Enum):
    """Alert status."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class ReportType(str, Enum):
    """Report types."""
    EXECUTIVE = "executive"
    TECHNICAL = "technical"
    COMPLIANCE = "compliance"
    CLIENT = "client"
    OPERATIONAL = "operational"


class ReportFormat(str, Enum):
    """Report formats."""
    PDF = "pdf"
    HTML = "html"
    JSON = "json"
    EXCEL = "excel"
    CSV = "csv"


# Analytics Model Schemas
class AnalyticsModelBase(BaseSchema):
    """Base analytics model schema."""
    name: str = Field(..., description="Model name", max_length=255)
    model_type: ModelType = Field(..., description="Type of ML model")
    version: str = Field(..., description="Model version", max_length=50)
    description: Optional[str] = Field(None, description="Model description")
    
    hyperparameters: Optional[Dict[str, Any]] = Field(None, description="Model hyperparameters")
    feature_columns: Optional[List[str]] = Field(None, description="Feature column names")
    target_column: Optional[str] = Field(None, description="Target column name")
    
    is_active: bool = Field(default=False, description="Whether model is active")


class AnalyticsModelCreate(AnalyticsModelBase):
    """Schema for creating analytics models."""
    pass


class AnalyticsModelUpdate(BaseSchema):
    """Schema for updating analytics models."""
    name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = Field(None)
    hyperparameters: Optional[Dict[str, Any]] = Field(None)
    is_active: Optional[bool] = Field(None)


class AnalyticsModelResponse(AnalyticsModelBase, BaseDBSchema):
    """Analytics model response schema."""
    training_data_size: Optional[int] = Field(None, description="Training dataset size")
    training_duration_seconds: Optional[float] = Field(None, description="Training duration")
    accuracy_score: Optional[float] = Field(None, description="Model accuracy")
    precision_score: Optional[float] = Field(None, description="Model precision")
    recall_score: Optional[float] = Field(None, description="Model recall")
    f1_score: Optional[float] = Field(None, description="Model F1 score")
    
    is_trained: bool = Field(default=False, description="Whether model is trained")
    last_trained_at: Optional[datetime] = Field(None, description="Last training time")
    last_prediction_at: Optional[datetime] = Field(None, description="Last prediction time")


# Training Job Schemas
class ModelTrainingJobBase(BaseSchema):
    """Base training job schema."""
    job_name: str = Field(..., description="Training job name", max_length=255)
    training_config: Optional[Dict[str, Any]] = Field(None, description="Training configuration")
    dataset_size: Optional[int] = Field(None, description="Dataset size")
    validation_split: float = Field(default=0.2, description="Validation split ratio")


class ModelTrainingJobCreate(ModelTrainingJobBase):
    """Schema for creating training jobs."""
    model_id: UUID = Field(..., description="Model ID")


class ModelTrainingJobResponse(ModelTrainingJobBase, BaseDBSchema):
    """Training job response schema."""
    model_id: UUID = Field(..., description="Model ID")
    status: TrainingStatus = Field(..., description="Job status")
    started_at: Optional[datetime] = Field(None, description="Job start time")
    completed_at: Optional[datetime] = Field(None, description="Job completion time")
    
    final_accuracy: Optional[float] = Field(None, description="Final accuracy")
    final_loss: Optional[float] = Field(None, description="Final loss")
    training_metrics: Optional[Dict[str, Any]] = Field(None, description="Training metrics")
    validation_metrics: Optional[Dict[str, Any]] = Field(None, description="Validation metrics")
    
    error_message: Optional[str] = Field(None, description="Error message if failed")
    cpu_hours: Optional[float] = Field(None, description="CPU hours used")
    memory_peak_gb: Optional[float] = Field(None, description="Peak memory usage")
    gpu_hours: Optional[float] = Field(None, description="GPU hours used")


# Prediction Schemas
class AnalyticsPredictionBase(BaseSchema):
    """Base prediction schema."""
    prediction_type: str = Field(..., description="Type of prediction", max_length=100)
    input_data: Dict[str, Any] = Field(..., description="Input features")
    entity_type: Optional[str] = Field(None, description="Entity type", max_length=100)
    entity_id: Optional[UUID] = Field(None, description="Entity ID")


class AnalyticsPredictionCreate(AnalyticsPredictionBase):
    """Schema for creating predictions."""
    model_id: UUID = Field(..., description="Model ID")


class AnalyticsPredictionResponse(AnalyticsPredictionBase, BaseDBSchema):
    """Prediction response schema."""
    model_id: UUID = Field(..., description="Model ID")
    prediction_result: Dict[str, Any] = Field(..., description="Prediction output")
    confidence_score: Optional[float] = Field(None, description="Confidence score")
    
    actual_outcome: Optional[Dict[str, Any]] = Field(None, description="Actual outcome")
    is_validated: bool = Field(default=False, description="Whether prediction is validated")
    validation_accuracy: Optional[float] = Field(None, description="Validation accuracy")
    
    feature_importance: Optional[Dict[str, float]] = Field(None, description="Feature importance")


# Report Schemas
class AnalyticsReportBase(BaseSchema):
    """Base report schema."""
    report_name: str = Field(..., description="Report name", max_length=255)
    report_type: ReportType = Field(..., description="Report type")
    report_format: ReportFormat = Field(..., description="Report format")
    
    data_period_start: datetime = Field(..., description="Data period start")
    data_period_end: datetime = Field(..., description="Data period end")
    
    recipients: Optional[List[str]] = Field(None, description="Report recipients")
    report_config: Optional[Dict[str, Any]] = Field(None, description="Report configuration")


class AnalyticsReportCreate(AnalyticsReportBase):
    """Schema for creating reports."""
    pass


class AnalyticsReportResponse(AnalyticsReportBase, BaseDBSchema):
    """Report response schema."""
    generated_by: UUID = Field(..., description="User who generated report")
    generation_time_seconds: Optional[float] = Field(None, description="Generation time")
    
    report_summary: Optional[str] = Field(None, description="Report summary")
    key_findings: Optional[List[str]] = Field(None, description="Key findings")
    recommendations: Optional[List[str]] = Field(None, description="Recommendations")
    
    distribution_status: str = Field(default="pending", description="Distribution status")
    sent_at: Optional[datetime] = Field(None, description="Send time")
    
    file_path: Optional[str] = Field(None, description="Report file path")
    file_size_bytes: Optional[int] = Field(None, description="File size")


# Alert Schemas
class AnalyticsAlertBase(BaseSchema):
    """Base alert schema."""
    alert_type: str = Field(..., description="Alert type", max_length=100)
    severity: AlertSeverity = Field(..., description="Alert severity")
    title: str = Field(..., description="Alert title", max_length=255)
    description: Optional[str] = Field(None, description="Alert description")
    
    trigger_condition: Optional[Dict[str, Any]] = Field(None, description="Trigger condition")
    threshold_value: Optional[float] = Field(None, description="Threshold value")
    actual_value: Optional[float] = Field(None, description="Actual value")
    
    entity_type: Optional[str] = Field(None, description="Entity type", max_length=100)
    entity_id: Optional[UUID] = Field(None, description="Entity ID")


class AnalyticsAlertCreate(AnalyticsAlertBase):
    """Schema for creating alerts."""
    pass


class AnalyticsAlertUpdate(BaseSchema):
    """Schema for updating alerts."""
    status: Optional[AlertStatus] = Field(None, description="Alert status")
    acknowledged_by: Optional[UUID] = Field(None, description="User who acknowledged")
    actions_taken: Optional[List[str]] = Field(None, description="Actions taken")


class AnalyticsAlertResponse(AnalyticsAlertBase, BaseDBSchema):
    """Alert response schema."""
    status: AlertStatus = Field(default=AlertStatus.ACTIVE, description="Alert status")
    acknowledged_by: Optional[UUID] = Field(None, description="User who acknowledged")
    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment time")
    resolved_at: Optional[datetime] = Field(None, description="Resolution time")
    
    escalation_level: int = Field(default=0, description="Escalation level")
    escalated_at: Optional[datetime] = Field(None, description="Escalation time")
    escalated_to: Optional[List[str]] = Field(None, description="Escalation recipients")
    
    recommended_actions: Optional[List[str]] = Field(None, description="Recommended actions")
    actions_taken: Optional[List[str]] = Field(None, description="Actions taken")


# Dashboard Schemas
class AnalyticsDashboardBase(BaseSchema):
    """Base dashboard schema."""
    dashboard_name: str = Field(..., description="Dashboard name", max_length=255)
    dashboard_type: str = Field(..., description="Dashboard type", max_length=100)
    description: Optional[str] = Field(None, description="Dashboard description")
    
    layout_config: Optional[Dict[str, Any]] = Field(None, description="Layout configuration")
    widget_configs: Optional[List[Dict[str, Any]]] = Field(None, description="Widget configurations")
    data_sources: Optional[List[str]] = Field(None, description="Data sources")
    refresh_interval_seconds: int = Field(default=300, description="Refresh interval")
    
    is_public: bool = Field(default=False, description="Whether dashboard is public")
    allowed_users: Optional[List[UUID]] = Field(None, description="Allowed users")
    allowed_roles: Optional[List[str]] = Field(None, description="Allowed roles")


class AnalyticsDashboardCreate(AnalyticsDashboardBase):
    """Schema for creating dashboards."""
    pass


class AnalyticsDashboardUpdate(BaseSchema):
    """Schema for updating dashboards."""
    dashboard_name: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = Field(None)
    layout_config: Optional[Dict[str, Any]] = Field(None)
    widget_configs: Optional[List[Dict[str, Any]]] = Field(None)
    refresh_interval_seconds: Optional[int] = Field(None)
    is_public: Optional[bool] = Field(None)


class AnalyticsDashboardResponse(AnalyticsDashboardBase, BaseDBSchema):
    """Dashboard response schema."""
    created_by: UUID = Field(..., description="Dashboard creator")
    view_count: int = Field(default=0, description="View count")
    last_viewed_at: Optional[datetime] = Field(None, description="Last view time")
    last_modified_by: Optional[UUID] = Field(None, description="Last modifier")
    tags: Optional[List[str]] = Field(None, description="Dashboard tags")


# Predictive Insights Schemas
class PredictiveInsights(BaseSchema):
    """Comprehensive predictive insights."""
    vulnerability_forecast: Optional[Dict[str, Any]] = Field(None, description="Vulnerability predictions")
    threat_evolution: Optional[Dict[str, Any]] = Field(None, description="Threat evolution analysis")
    remediation_estimates: Optional[Dict[str, Any]] = Field(None, description="Remediation time estimates")
    resource_optimization: Optional[Dict[str, Any]] = Field(None, description="Resource optimization recommendations")
    
    confidence_scores: Optional[Dict[str, float]] = Field(None, description="Prediction confidence scores")
    risk_factors: Optional[List[str]] = Field(None, description="Key risk factors identified")
    recommendations: Optional[List[str]] = Field(None, description="Actionable recommendations")
    
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")
    valid_until: Optional[datetime] = Field(None, description="Validity period")


# Analytics Summary Schemas
class AnalyticsSummary(BaseSchema):
    """Analytics summary for dashboards."""
    total_models: int = Field(..., description="Total number of models")
    active_models: int = Field(..., description="Number of active models")
    total_predictions: int = Field(..., description="Total predictions made")
    average_accuracy: float = Field(..., description="Average model accuracy")
    
    recent_alerts: List[AnalyticsAlertResponse] = Field(..., description="Recent alerts")
    top_insights: List[str] = Field(..., description="Top insights")
    performance_metrics: Dict[str, float] = Field(..., description="Performance metrics")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update time")
