"""Pydantic schemas package."""

# Import all schemas for easy access
from .base import (
    BaseSchema,
    TimestampMixin,
    IDMixin,
    BaseDBSchema,
    PaginationParams,
    PaginatedResponse,
    HealthCheck,
    ErrorResponse,
)

from .user import (
    User,
    UserCreate,
    UserUpdate,
    UserPasswordUpdate,
    UserLogin,
    Token,
    TokenData,
    UserProfile,
    UserCareerPath,
    UserTeam,
    UserSearchFilters,
    UserListResponse,
    UserWithRelations,
)

# Phase 5: Training schemas
from .training import (
    # Base schemas
    CompetencyFrameworkBase,
    CompetencyBase,
    SkillAssessmentBase,
    TrainingCourseBase,
    LearningPathBase,
    TrainingEnrollmentBase,
    CertificationBase,
    CertificationAchievementBase,
    CTFChallengeBase,
    CTFSubmissionBase,
    MentorshipPairBase,
    MentorshipSessionBase,

    # Create schemas
    CompetencyFrameworkCreate,
    CompetencyCreate,
    Skill<PERSON>sessment<PERSON>reate,
    TrainingCourse<PERSON>reate,
    LearningPathCreate,
    TrainingEnrollmentCreate,
    CertificationCreate,
    Certification<PERSON><PERSON>evementCreate,
    CTFChallengeCreate,
    CTFSubmissionCreate,
    MentorshipPairCreate,
    MentorshipSessionCreate,

    # Update schemas
    CompetencyFrameworkUpdate,
    CompetencyUpdate,
    SkillAssessmentUpdate,
    TrainingCourseUpdate,
    LearningPathUpdate,
    TrainingEnrollmentUpdate,
    CertificationUpdate,
    CertificationAchievementUpdate,
    CTFChallengeUpdate,
    MentorshipPairUpdate,
    MentorshipSessionUpdate,

    # Response schemas
    CompetencyFramework,
    Competency,
    SkillAssessment,
    TrainingCourse,
    LearningPath,
    TrainingEnrollment,
    Certification,
    CertificationAchievement,
    CTFChallenge,
    CTFSubmission,
    MentorshipPair,
    MentorshipSession,

    # Specialized schemas
    SkillGapAnalysis,
    LearningProgress,
    CTFLeaderboard,
    TrainingROIAnalysis,
    CompetencyMatrix,
    CertificationPathway,
)

# Phase 6: Career development schemas
from .career import (
    IDP,
    IDPCreate,
    IDPUpdate,
    IDPWithGoals,
    DevelopmentGoal,
    DevelopmentGoalCreate,
    DevelopmentGoalUpdate,
    DevelopmentGoalWithActivities,
    DevelopmentActivity,
    DevelopmentActivityCreate,
    DevelopmentActivityUpdate,
    CareerProgressSummary,
)

from .recognition import (
    Recognition,
    RecognitionCreate,
    RecognitionUpdate,
    PeerNomination,
    PeerNominationCreate,
    PeerNominationUpdate,
    PeerNominationWithVotes,
    NominationVote,
    NominationVoteCreate,
    NominationVoteUpdate,
    Reward,
    RewardCreate,
    RewardUpdate,
    RecognitionStats,
    RewardStats,
)

from .wellness import (
    WellnessCheck,
    WellnessCheckCreate,
    WellnessCheckUpdate,
    WellnessCheckWithAlerts,
    WellnessAlert,
    WellnessAlertCreate,
    WellnessAlertUpdate,
    WorkSchedule,
    WorkScheduleCreate,
    WorkScheduleUpdate,
    WellnessResource,
    WellnessResourceCreate,
    WellnessResourceUpdate,
    WellnessMetrics,
)

from .mentorship import (
    Mentorship,
    MentorshipCreate,
    MentorshipUpdate,
    MentorshipWithSessions,
    MentorshipSession,
    MentorshipSessionCreate,
    MentorshipSessionUpdate,
    MentorProfile,
    MentorProfileCreate,
    MentorProfileUpdate,
    MentorshipRequest,
    MentorshipRequestCreate,
    MentorshipRequestUpdate,
    MentorshipStats,
)

# Phase 8: Compliance and Audit Trail schemas
from .compliance import (
    ComplianceMappingCreate,
    ComplianceMappingUpdate,
    ComplianceMappingResponse,
    AuditTrailCreate,
    AuditTrailResponse,
    AuditTrailSearchFilters,
    AuditTrailSearchResponse,
    ComplianceReportCreate,
    ComplianceReportResponse,
    ComplianceEvidenceCreate,
    ComplianceEvidenceResponse,
    ComplianceDashboard,
    ControlTestRequest,
)

__all__ = [
    # Base schemas
    "BaseSchema",
    "TimestampMixin",
    "IDMixin",
    "BaseDBSchema",
    "PaginationParams",
    "PaginatedResponse",
    "HealthCheck",
    "ErrorResponse",
    # User schemas
    "User",
    "UserCreate",
    "UserUpdate",
    "UserPasswordUpdate",
    "UserLogin",
    "Token",
    "TokenData",
    "UserProfile",
    "UserCareerPath",
    "UserTeam",
    "UserSearchFilters",
    "UserListResponse",
    "UserWithRelations",
    # Phase 5: Training schemas
    "CompetencyFrameworkBase",
    "CompetencyBase",
    "SkillAssessmentBase",
    "TrainingCourseBase",
    "LearningPathBase",
    "TrainingEnrollmentBase",
    "CertificationBase",
    "CertificationAchievementBase",
    "CTFChallengeBase",
    "CTFSubmissionBase",
    "MentorshipPairBase",
    "MentorshipSessionBase",
    "CompetencyFrameworkCreate",
    "CompetencyCreate",
    "SkillAssessmentCreate",
    "TrainingCourseCreate",
    "LearningPathCreate",
    "TrainingEnrollmentCreate",
    "CertificationCreate",
    "CertificationAchievementCreate",
    "CTFChallengeCreate",
    "CTFSubmissionCreate",
    "MentorshipPairCreate",
    "MentorshipSessionCreate",
    "CompetencyFrameworkUpdate",
    "CompetencyUpdate",
    "SkillAssessmentUpdate",
    "TrainingCourseUpdate",
    "LearningPathUpdate",
    "TrainingEnrollmentUpdate",
    "CertificationUpdate",
    "CertificationAchievementUpdate",
    "CTFChallengeUpdate",
    "MentorshipPairUpdate",
    "MentorshipSessionUpdate",
    "CompetencyFramework",
    "Competency",
    "SkillAssessment",
    "TrainingCourse",
    "LearningPath",
    "TrainingEnrollment",
    "Certification",
    "CertificationAchievement",
    "CTFChallenge",
    "CTFSubmission",
    "MentorshipPair",
    "MentorshipSession",
    "SkillGapAnalysis",
    "LearningProgress",
    "CTFLeaderboard",
    "TrainingROIAnalysis",
    "CompetencyMatrix",
    "CertificationPathway",
    # Phase 6: Career development schemas
    "IDP",
    "IDPCreate",
    "IDPUpdate",
    "IDPWithGoals",
    "DevelopmentGoal",
    "DevelopmentGoalCreate",
    "DevelopmentGoalUpdate",
    "DevelopmentGoalWithActivities",
    "DevelopmentActivity",
    "DevelopmentActivityCreate",
    "DevelopmentActivityUpdate",
    "CareerProgressSummary",
    # Recognition schemas
    "Recognition",
    "RecognitionCreate",
    "RecognitionUpdate",
    "PeerNomination",
    "PeerNominationCreate",
    "PeerNominationUpdate",
    "PeerNominationWithVotes",
    "NominationVote",
    "NominationVoteCreate",
    "NominationVoteUpdate",
    "Reward",
    "RewardCreate",
    "RewardUpdate",
    "RecognitionStats",
    "RewardStats",
    # Wellness schemas
    "WellnessCheck",
    "WellnessCheckCreate",
    "WellnessCheckUpdate",
    "WellnessCheckWithAlerts",
    "WellnessAlert",
    "WellnessAlertCreate",
    "WellnessAlertUpdate",
    "WorkSchedule",
    "WorkScheduleCreate",
    "WorkScheduleUpdate",
    "WellnessResource",
    "WellnessResourceCreate",
    "WellnessResourceUpdate",
    "WellnessMetrics",
    # Mentorship schemas
    "Mentorship",
    "MentorshipCreate",
    "MentorshipUpdate",
    "MentorshipWithSessions",
    "MentorshipSession",
    "MentorshipSessionCreate",
    "MentorshipSessionUpdate",
    "MentorProfile",
    "MentorProfileCreate",
    "MentorProfileUpdate",
    "MentorshipRequest",
    "MentorshipRequestCreate",
    "MentorshipRequestUpdate",
    "MentorshipStats",
    # Phase 8: Compliance schemas
    "ComplianceMappingCreate",
    "ComplianceMappingUpdate",
    "ComplianceMappingResponse",
    "AuditTrailCreate",
    "AuditTrailResponse",
    "AuditTrailSearchFilters",
    "AuditTrailSearchResponse",
    "ComplianceReportCreate",
    "ComplianceReportResponse",
    "ComplianceEvidenceCreate",
    "ComplianceEvidenceResponse",
    "ComplianceDashboard",
    "ControlTestRequest",
]