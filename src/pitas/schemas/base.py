"""Base Pydantic schemas."""

from datetime import datetime
from typing import Any, Dict, Optional, Generic, TypeVar, List
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

T = TypeVar('T')


class BaseSchema(BaseModel):
    """Base schema with common configuration.

    Provides standard configuration for all Pydantic models.
    """

    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
        use_enum_values=True,
    )


class TimestampMixin:
    """Mixin for models with timestamp fields."""

    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class IDMixin:
    """Mixin for models with UUID primary key."""

    id: UUID = Field(..., description="Unique identifier")


class BaseDBSchema(BaseSchema, IDMixin, TimestampMixin):
    """Base schema for database models with ID and timestamps."""
    pass


class PaginationParams(BaseSchema):
    """Parameters for pagination."""

    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Maximum number of records to return")


class PaginatedResponse(BaseSchema, Generic[T]):
    """Response schema for paginated results."""

    items: List[T] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    skip: int = Field(..., description="Number of items skipped")
    limit: int = Field(..., description="Maximum number of items returned")

    @property
    def has_next(self) -> bool:
        """Check if there are more items available."""
        return self.skip + self.limit < self.total

    @property
    def has_previous(self) -> bool:
        """Check if there are previous items available."""
        return self.skip > 0


class HealthCheck(BaseSchema):
    """Health check response schema."""

    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(..., description="Check timestamp")
    version: str = Field(..., description="Application version")
    environment: str = Field(..., description="Environment name")


class BaseResponse(BaseSchema):
    """Base response schema."""

    success: bool = Field(default=True, description="Operation success status")
    message: Optional[str] = Field(None, description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")


class ErrorResponse(BaseSchema):
    """Error response schema."""

    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    error_code: Optional[str] = Field(None, description="Error code")
    timestamp: datetime = Field(..., description="Error timestamp")