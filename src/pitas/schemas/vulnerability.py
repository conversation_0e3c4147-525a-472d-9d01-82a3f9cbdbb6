
"""Pydantic schemas for vulnerability management."""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator

from pitas.schemas.base import BaseSchema, BaseDBSchema

from pitas.db.models.vulnerability import (
    VulnerabilitySeverity,
    VulnerabilityStatus,
)
from pitas.db.models.asset import (
    CriticalityLevel as AssetCriticality,
)


class VulnerabilityBase(BaseModel):
    """Base vulnerability schema."""
    
    cve_id: Optional[str] = Field(None, max_length=20, description="CVE identifier")
    title: str = Field(..., max_length=500, description="Vulnerability title")
    description: Optional[str] = Field(None, description="Detailed description")
    cvss_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0, description="CVSS base score")
    cvss_vector: Optional[str] = Field(None, max_length=200, description="CVSS vector string")
    severity: VulnerabilitySeverity = Field(default=VulnerabilitySeverity.MEDIUM)
    status: VulnerabilityStatus = Field(default=VulnerabilityStatus.DISCOVERED)
    discovery_date: datetime = Field(..., description="Discovery timestamp")
    business_impact_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    exploitability_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    true_risk_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    source: Optional[str] = Field(None, max_length=100, description="Vulnerability source")
    tags: Optional[List[str]] = Field(default_factory=list)
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict)

    @validator('cve_id')
    def validate_cve_id(cls, v):
        if v and not v.startswith('CVE-'):
            raise ValueError('CVE ID must start with "CVE-"')
        return v



class VulnerabilityCreate(VulnerabilityBase):
    """Schema for creating vulnerabilities."""
    pass


class VulnerabilityUpdate(BaseModel):
    """Schema for updating vulnerabilities."""
    
    title: Optional[str] = Field(None, description="Vulnerability title", max_length=500)
    description: Optional[str] = Field(None, description="Vulnerability description")
    status: Optional[VulnerabilityStatus] = Field(None, description="Vulnerability status")
    cve_id: Optional[str] = Field(None, description="CVE ID", max_length=20)
    cwe_id: Optional[str] = Field(None, description="CWE ID", max_length=20)
    plugin_id: Optional[str] = Field(None, description="Scanner plugin ID", max_length=50)
    severity: Optional[VulnerabilitySeverity] = Field(None, description="Vulnerability severity")
    cvss_v2_score: Optional[float] = Field(None, description="CVSS v2 score", ge=0.0, le=10.0)
    cvss_v3_score: Optional[float] = Field(None, description="CVSS v3 score", ge=0.0, le=10.0)
    cvss_v4_score: Optional[float] = Field(None, description="CVSS v4 score", ge=0.0, le=10.0)
    cvss_vector: Optional[str] = Field(None, description="CVSS vector", max_length=200)
    risk_score: Optional[float] = Field(None, description="Risk score", ge=0.0, le=10.0)
    exploitability_score: Optional[float] = Field(None, description="Exploitability score", ge=0.0, le=10.0)
    business_impact_score: Optional[float] = Field(None, description="Business impact score", ge=0.0, le=10.0)
    source: Optional[str] = Field(None, description="Vulnerability source", max_length=100)
    source_id: Optional[str] = Field(None, description="Source system ID", max_length=255)
    scanner_name: Optional[str] = Field(None, description="Scanner name", max_length=100)
    affected_software: Optional[str] = Field(None, description="Affected software", max_length=500)
    affected_versions: Optional[str] = Field(None, description="Affected versions", max_length=500)
    port: Optional[int] = Field(None, description="Affected port", ge=1, le=65535)
    protocol: Optional[str] = Field(None, description="Protocol", max_length=20)
    service: Optional[str] = Field(None, description="Service", max_length=100)
    evidence: Optional[str] = Field(None, description="Evidence")
    proof_of_concept: Optional[str] = Field(None, description="Proof of concept")
    solution: Optional[str] = Field(None, description="Solution")
    workaround: Optional[str] = Field(None, description="Workaround")
    remediation_effort: Optional[str] = Field(None, description="Remediation effort", max_length=20)
    references: Optional[List[str]] = Field(None, description="References")
    external_references: Optional[Dict[str, Any]] = Field(None, description="External references")
    mitre_techniques: Optional[List[str]] = Field(None, description="MITRE ATT&CK techniques")
    mitre_tactics: Optional[List[str]] = Field(None, description="MITRE ATT&CK tactics")
    tags: Optional[List[str]] = Field(None, description="Tags")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields")
    last_seen: Optional[datetime] = Field(None, description="Last seen time")


class Vulnerability(BaseDBSchema):
    """Vulnerability response schema."""
    
    title: str
    description: str
    status: VulnerabilityStatus
    cve_id: Optional[str]
    cwe_id: Optional[str]
    plugin_id: Optional[str]
    severity: VulnerabilitySeverity
    cvss_v2_score: Optional[float]
    cvss_v3_score: Optional[float]
    cvss_v4_score: Optional[float]
    cvss_vector: Optional[str]
    risk_score: Optional[float]
    exploitability_score: Optional[float]
    business_impact_score: Optional[float]
    source: str
    source_id: Optional[str]
    scanner_name: Optional[str]
    first_discovered: datetime
    last_seen: Optional[datetime]
    affected_software: Optional[str]
    affected_versions: Optional[str]
    port: Optional[int]
    protocol: Optional[str]
    service: Optional[str]
    evidence: Optional[str]
    proof_of_concept: Optional[str]
    solution: Optional[str]
    workaround: Optional[str]
    remediation_effort: Optional[str]
    references: Optional[List[str]]
    external_references: Optional[Dict[str, Any]]
    mitre_techniques: Optional[List[str]]
    mitre_tactics: Optional[List[str]]
    tags: Optional[List[str]]
    custom_fields: Optional[Dict[str, Any]]
    
    model_config = ConfigDict(from_attributes=True)


class VulnerabilityFindingBase(BaseSchema):
    """Base vulnerability finding schema."""
    
    vulnerability_id: UUID = Field(..., description="Vulnerability ID")
    asset_id: UUID = Field(..., description="Asset ID")
    status: VulnerabilityStatus = Field(VulnerabilityStatus.DISCOVERED, description="Finding status")
    scan_id: Optional[str] = Field(None, description="Scan ID", max_length=255)
    scanner_finding_id: Optional[str] = Field(None, description="Scanner finding ID", max_length=255)
    asset_specific_evidence: Optional[str] = Field(None, description="Asset-specific evidence")
    asset_specific_impact: Optional[str] = Field(None, description="Asset-specific impact")
    assigned_to: Optional[str] = Field(None, description="Assigned to", max_length=255)
    remediation_status: Optional[str] = Field(None, description="Remediation status", max_length=50)
    remediation_notes: Optional[str] = Field(None, description="Remediation notes")
    remediation_deadline: Optional[datetime] = Field(None, description="Remediation deadline")
    verified_by: Optional[str] = Field(None, description="Verified by", max_length=255)
    verification_notes: Optional[str] = Field(None, description="Verification notes")
    business_context: Optional[str] = Field(None, description="Business context")
    environmental_score: Optional[float] = Field(None, description="Environmental score", ge=0.0, le=10.0)


class VulnerabilityFindingCreate(VulnerabilityFindingBase):
    """Schema for creating vulnerability findings."""
    
    discovered_at: datetime = Field(..., description="Discovery time")
    last_verified: Optional[datetime] = Field(None, description="Last verification time")


class VulnerabilityFindingUpdate(BaseSchema):
    """Schema for updating vulnerability findings."""
    
    status: Optional[VulnerabilityStatus] = Field(None, description="Finding status")
    asset_specific_evidence: Optional[str] = Field(None, description="Asset-specific evidence")
    asset_specific_impact: Optional[str] = Field(None, description="Asset-specific impact")
    assigned_to: Optional[str] = Field(None, description="Assigned to", max_length=255)
    remediation_status: Optional[str] = Field(None, description="Remediation status", max_length=50)
    remediation_notes: Optional[str] = Field(None, description="Remediation notes")
    remediation_deadline: Optional[datetime] = Field(None, description="Remediation deadline")
    resolved_at: Optional[datetime] = Field(None, description="Resolution time")
    verified_by: Optional[str] = Field(None, description="Verified by", max_length=255)
    verification_notes: Optional[str] = Field(None, description="Verification notes")
    business_context: Optional[str] = Field(None, description="Business context")
    environmental_score: Optional[float] = Field(None, description="Environmental score", ge=0.0, le=10.0)
    last_verified: Optional[datetime] = Field(None, description="Last verification time")


class VulnerabilityFinding(BaseDBSchema, VulnerabilityFindingBase):
    """Vulnerability finding response schema."""
    
    discovered_at: datetime
    last_verified: Optional[datetime]
    resolved_at: Optional[datetime]


class ThreatIntelligenceBase(BaseSchema):
    """Base threat intelligence schema."""
    
    threat_id: str = Field(..., description="Threat ID", max_length=255)
    threat_type: str = Field(..., description="Threat type", max_length=100)
    threat_name: str = Field(..., description="Threat name", max_length=500)
    cve_ids: Optional[List[str]] = Field(None, description="Associated CVE IDs")
    description: str = Field(..., description="Threat description")
    severity: VulnerabilitySeverity = Field(..., description="Threat severity")
    confidence: float = Field(..., description="Confidence level", ge=0.0, le=1.0)
    iocs: Optional[Dict[str, Any]] = Field(None, description="Indicators of Compromise")
    mitre_techniques: Optional[List[str]] = Field(None, description="MITRE ATT&CK techniques")
    mitre_tactics: Optional[List[str]] = Field(None, description="MITRE ATT&CK tactics")
    source: str = Field(..., description="Intelligence source", max_length=100)
    source_confidence: Optional[float] = Field(None, description="Source confidence", ge=0.0, le=1.0)
    tags: Optional[List[str]] = Field(None, description="Tags")
    references: Optional[List[str]] = Field(None, description="References")
    enrichment_data: Optional[Dict[str, Any]] = Field(None, description="Enrichment data")


class ThreatIntelligenceCreate(ThreatIntelligenceBase):
    """Schema for creating threat intelligence."""
    
    first_seen: datetime = Field(..., description="First seen time")
    last_seen: Optional[datetime] = Field(None, description="Last seen time")


class ThreatIntelligenceUpdate(BaseSchema):
    """Schema for updating threat intelligence."""
    
    threat_name: Optional[str] = Field(None, description="Threat name", max_length=500)
    cve_ids: Optional[List[str]] = Field(None, description="Associated CVE IDs")
    description: Optional[str] = Field(None, description="Threat description")
    severity: Optional[VulnerabilitySeverity] = Field(None, description="Threat severity")
    confidence: Optional[float] = Field(None, description="Confidence level", ge=0.0, le=1.0)
    iocs: Optional[Dict[str, Any]] = Field(None, description="Indicators of Compromise")
    mitre_techniques: Optional[List[str]] = Field(None, description="MITRE ATT&CK techniques")
    mitre_tactics: Optional[List[str]] = Field(None, description="MITRE ATT&CK tactics")
    source_confidence: Optional[float] = Field(None, description="Source confidence", ge=0.0, le=1.0)
    tags: Optional[List[str]] = Field(None, description="Tags")
    references: Optional[List[str]] = Field(None, description="References")
    enrichment_data: Optional[Dict[str, Any]] = Field(None, description="Enrichment data")
    last_seen: Optional[datetime] = Field(None, description="Last seen time")


class ThreatIntelligence(BaseDBSchema, ThreatIntelligenceBase):
    """Threat intelligence response schema."""
    
    first_seen: datetime
    last_seen: Optional[datetime]


class SecurityEventBase(BaseSchema):
    """Base security event schema."""
    
    event_id: str = Field(..., description="Event ID", max_length=255)
    source_system: str = Field(..., description="Source system", max_length=100)
    event_type: str = Field(..., description="Event type", max_length=100)
    event_name: str = Field(..., description="Event name", max_length=500)
    description: str = Field(..., description="Event description")
    severity: VulnerabilitySeverity = Field(..., description="Event severity")
    category: str = Field(..., description="Event category", max_length=100)
    event_time: datetime = Field(..., description="Event time")
    source_ip: Optional[str] = Field(None, description="Source IP", max_length=45)
    source_hostname: Optional[str] = Field(None, description="Source hostname", max_length=255)
    destination_ip: Optional[str] = Field(None, description="Destination IP", max_length=45)
    destination_hostname: Optional[str] = Field(None, description="Destination hostname", max_length=255)
    raw_event: Optional[Dict[str, Any]] = Field(None, description="Raw event data")
    parsed_fields: Optional[Dict[str, Any]] = Field(None, description="Parsed fields")
    correlation_id: Optional[str] = Field(None, description="Correlation ID", max_length=255)
    related_vulnerabilities: Optional[List[str]] = Field(None, description="Related vulnerabilities")
    mitre_techniques: Optional[List[str]] = Field(None, description="MITRE ATT&CK techniques")
    mitre_tactics: Optional[List[str]] = Field(None, description="MITRE ATT&CK tactics")


class SecurityEventCreate(SecurityEventBase):
    """Schema for creating security events."""
    
    ingested_at: datetime = Field(..., description="Ingestion time")


class SecurityEvent(BaseDBSchema, SecurityEventBase):
    """Security event response schema."""
    
    ingested_at: datetime


# Vulnerability analytics schemas
class VulnerabilityAnalytics(BaseSchema):
    """Schema for vulnerability analytics."""
    
    total_vulnerabilities: int = Field(..., description="Total vulnerabilities")
    vulnerabilities_by_severity: Dict[str, int] = Field(..., description="Vulnerabilities by severity")
    vulnerabilities_by_status: Dict[str, int] = Field(..., description="Vulnerabilities by status")
    vulnerabilities_by_source: Dict[str, int] = Field(..., description="Vulnerabilities by source")
    top_cves: List[Dict[str, Any]] = Field(..., description="Top CVEs")
    remediation_metrics: Dict[str, Any] = Field(..., description="Remediation metrics")
    trend_data: Dict[str, List[Any]] = Field(..., description="Trend data")
    risk_distribution: Dict[str, int] = Field(..., description="Risk score distribution")


# Vulnerability import schemas
class VulnerabilityImportRequest(BaseSchema):
    """Schema for vulnerability import requests."""
    
    source: str = Field(..., description="Import source", max_length=100)
    import_type: str = Field("full", description="Import type (full, incremental)")
    file_path: Optional[str] = Field(None, description="File path for file-based imports")
    scan_id: Optional[str] = Field(None, description="Scan ID for scanner imports")
    import_options: Optional[Dict[str, Any]] = Field(None, description="Import options")


class VulnerabilityImportResult(BaseSchema):
    """Schema for vulnerability import results."""
    
    success: bool = Field(..., description="Whether import was successful")
    vulnerabilities_imported: int = Field(0, description="Vulnerabilities imported")
    vulnerabilities_updated: int = Field(0, description="Vulnerabilities updated")
    vulnerabilities_skipped: int = Field(0, description="Vulnerabilities skipped")
    findings_created: int = Field(0, description="Findings created")
    import_time: float = Field(..., description="Import time in seconds")
    errors: Optional[List[str]] = Field(None, description="Import errors")
    warnings: Optional[List[str]] = Field(None, description="Import warnings")


class VulnerabilityUpdate(BaseModel):
    """Schema for updating vulnerabilities."""
    
    title: Optional[str] = Field(None, max_length=500)
    description: Optional[str] = None
    cvss_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    cvss_vector: Optional[str] = Field(None, max_length=200)
    severity: Optional[VulnerabilitySeverity] = None
    status: Optional[VulnerabilityStatus] = None
    remediation_date: Optional[datetime] = None
    verification_date: Optional[datetime] = None
    business_impact_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    exploitability_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    true_risk_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    tags: Optional[List[str]] = None
    extra_data: Optional[Dict[str, Any]] = None


class VulnerabilityResponse(VulnerabilityBase):
    """Schema for vulnerability responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    remediation_date: Optional[datetime] = None
    verification_date: Optional[datetime] = None


class AssetBase(BaseModel):
    """Base asset schema."""
    
    name: str = Field(..., max_length=255, description="Asset name")
    asset_type: str = Field(..., max_length=50, description="Asset type")
    ip_address: Optional[str] = Field(None, max_length=45, description="IP address")
    hostname: Optional[str] = Field(None, max_length=255, description="Hostname")
    business_criticality: AssetCriticality = Field(default=AssetCriticality.MEDIUM)
    business_processes: Optional[List[str]] = Field(default_factory=list)
    compliance_requirements: Optional[List[str]] = Field(default_factory=list)
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class AssetCreate(AssetBase):
    """Schema for creating assets."""
    pass


class AssetUpdate(BaseModel):
    """Schema for updating assets."""
    
    name: Optional[str] = Field(None, max_length=255)
    asset_type: Optional[str] = Field(None, max_length=50)
    ip_address: Optional[str] = Field(None, max_length=45)
    hostname: Optional[str] = Field(None, max_length=255)
    business_criticality: Optional[AssetCriticality] = None
    business_processes: Optional[List[str]] = None
    compliance_requirements: Optional[List[str]] = None
    extra_data: Optional[Dict[str, Any]] = None


class AssetResponse(AssetBase):
    """Schema for asset responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]


class AssetVulnerabilityBase(BaseModel):
    """Base asset-vulnerability association schema."""
    
    asset_id: UUID
    vulnerability_id: UUID
    impact_level: str = Field(..., max_length=20)
    exploitability_likelihood: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    remediation_priority: Optional[int] = Field(None, ge=1)
    remediation_effort: Optional[str] = Field(None, max_length=20)


class AssetVulnerabilityCreate(AssetVulnerabilityBase):
    """Schema for creating asset-vulnerability associations."""
    pass


class AssetVulnerabilityUpdate(BaseModel):
    """Schema for updating asset-vulnerability associations."""
    
    impact_level: Optional[str] = Field(None, max_length=20)
    exploitability_likelihood: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    remediation_priority: Optional[int] = Field(None, ge=1)
    remediation_effort: Optional[str] = Field(None, max_length=20)


class AssetVulnerabilityResponse(AssetVulnerabilityBase):
    """Schema for asset-vulnerability responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]


class VulnerabilityMetricBase(BaseModel):
    """Base vulnerability metric schema."""
    
    metric_type: str = Field(..., max_length=50)
    timestamp: datetime
    value: Decimal = Field(..., decimal_places=2)
    asset_id: Optional[UUID] = None
    vulnerability_id: Optional[UUID] = None
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class VulnerabilityMetricCreate(VulnerabilityMetricBase):
    """Schema for creating vulnerability metrics."""
    pass


class VulnerabilityMetricResponse(VulnerabilityMetricBase):
    """Schema for vulnerability metric responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]


# Aggregated schemas for complex responses
class VulnerabilityWithAssets(VulnerabilityResponse):
    """Vulnerability with associated assets."""
    
    assets: List[AssetResponse] = Field(default_factory=list)


class AssetWithVulnerabilities(AssetResponse):
    """Asset with associated vulnerabilities."""
    
    vulnerabilities: List[VulnerabilityResponse] = Field(default_factory=list)


class VulnerabilityDashboard(BaseModel):
    """Dashboard summary for vulnerabilities."""
    
    total_vulnerabilities: int
    by_severity: Dict[str, int]
    by_status: Dict[str, int]
    critical_assets_affected: int
    average_remediation_time_days: Optional[float]
    vulnerability_density: Optional[float]
    trend_data: List[Dict[str, Any]] = Field(default_factory=list)


class VulnerabilitySearchFilters(BaseModel):
    """Search filters for vulnerabilities."""
    
    severity: Optional[List[VulnerabilitySeverity]] = None
    status: Optional[List[VulnerabilityStatus]] = None
    asset_criticality: Optional[List[AssetCriticality]] = None
    cvss_score_min: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    cvss_score_max: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    discovery_date_from: Optional[datetime] = None
    discovery_date_to: Optional[datetime] = None
    tags: Optional[List[str]] = None
    source: Optional[str] = None
    has_exploit: Optional[bool] = None
    asset_ids: Optional[List[UUID]] = None


class VulnerabilitySearchResponse(BaseModel):
    """Response for vulnerability search."""
    
    vulnerabilities: List[VulnerabilityResponse]
    total_count: int
    page: int
    page_size: int
    filters_applied: VulnerabilitySearchFilters

