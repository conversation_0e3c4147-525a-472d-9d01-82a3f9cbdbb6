"""Resource allocation schemas for Phase 2."""

from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID

from pydantic import Field, validator

from .base import BaseSchema, BaseDBSchema
from ..db.models.resource_allocation import AllocationStatus, AllocationRole


class ResourceAllocationBase(BaseSchema):
    """Base schema for resource allocation."""
    
    pentester_id: UUID = Field(..., description="Reference to pentester profile")
    project_id: UUID = Field(..., description="Reference to project")
    status: AllocationStatus = Field(default=AllocationStatus.PROPOSED, description="Current allocation status")
    role: AllocationRole = Field(default=AllocationRole.PENTESTER, description="Role of pentester in this project")
    allocated_hours: float = Field(..., ge=0, description="Total hours allocated to this project")
    hours_per_week: Optional[float] = Field(None, ge=0, description="Expected hours per week on this project")
    allocation_start_date: datetime = Field(..., description="Start date of allocation")
    allocation_end_date: datetime = Field(..., description="End date of allocation")
    utilization_percentage: float = Field(..., ge=0, le=100, description="Percentage of pentester's capacity allocated")
    skill_match_score: Optional[float] = Field(None, ge=0, le=100, description="How well pentester skills match project requirements")
    primary_skills_used: Optional[str] = Field(None, description="Primary skills utilized in this allocation")
    can_work_remotely: bool = Field(default=True, description="Whether pentester can work remotely")
    requires_security_clearance: bool = Field(default=False, description="Whether this allocation requires security clearance")
    timezone_constraints: Optional[str] = Field(None, description="Timezone constraints for this allocation")
    hourly_rate: Optional[float] = Field(None, ge=0, description="Hourly rate for this allocation")
    is_billable: bool = Field(default=True, description="Whether this allocation is billable")
    is_primary_assignment: bool = Field(default=False, description="Whether this is the pentester's primary assignment")
    requires_mentoring: bool = Field(default=False, description="Whether this allocation requires mentoring")
    
    @validator('allocation_end_date')
    def end_date_after_start_date(cls, v, values):
        """Validate that end date is after start date."""
        if 'allocation_start_date' in values and v <= values['allocation_start_date']:
            raise ValueError('End date must be after start date')
        return v


class ResourceAllocationCreate(ResourceAllocationBase):
    """Schema for creating a resource allocation."""
    
    requested_by: Optional[str] = Field(None, description="Who requested this allocation")


class ResourceAllocationUpdate(BaseSchema):
    """Schema for updating a resource allocation."""
    
    status: Optional[AllocationStatus] = Field(None, description="Current allocation status")
    role: Optional[AllocationRole] = Field(None, description="Role of pentester in this project")
    allocated_hours: Optional[float] = Field(None, ge=0, description="Total hours allocated to this project")
    hours_per_week: Optional[float] = Field(None, ge=0, description="Expected hours per week on this project")
    actual_hours: Optional[float] = Field(None, ge=0, description="Actual hours worked on this project")
    allocation_start_date: Optional[datetime] = Field(None, description="Start date of allocation")
    allocation_end_date: Optional[datetime] = Field(None, description="End date of allocation")
    actual_start_date: Optional[datetime] = Field(None, description="Actual start date of work")
    actual_end_date: Optional[datetime] = Field(None, description="Actual end date of work")
    utilization_percentage: Optional[float] = Field(None, ge=0, le=100, description="Percentage of pentester's capacity allocated")
    performance_rating: Optional[float] = Field(None, ge=1, le=5, description="Performance rating for this allocation")
    efficiency_score: Optional[float] = Field(None, description="Efficiency score")
    skill_match_score: Optional[float] = Field(None, ge=0, le=100, description="How well pentester skills match project requirements")
    primary_skills_used: Optional[str] = Field(None, description="Primary skills utilized in this allocation")
    can_work_remotely: Optional[bool] = Field(None, description="Whether pentester can work remotely")
    requires_security_clearance: Optional[bool] = Field(None, description="Whether this allocation requires security clearance")
    timezone_constraints: Optional[str] = Field(None, description="Timezone constraints for this allocation")
    hourly_rate: Optional[float] = Field(None, ge=0, description="Hourly rate for this allocation")
    total_cost: Optional[float] = Field(None, ge=0, description="Total cost of this allocation")
    client_feedback: Optional[str] = Field(None, description="Client feedback for this allocation")
    internal_notes: Optional[str] = Field(None, description="Internal notes about this allocation")
    lessons_learned: Optional[str] = Field(None, description="Lessons learned from this allocation")
    allocation_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional allocation metadata")
    is_billable: Optional[bool] = Field(None, description="Whether this allocation is billable")
    is_primary_assignment: Optional[bool] = Field(None, description="Whether this is the pentester's primary assignment")
    requires_mentoring: Optional[bool] = Field(None, description="Whether this allocation requires mentoring")


class ResourceAllocation(ResourceAllocationBase, BaseDBSchema):
    """Schema for resource allocation response."""
    
    actual_hours: float = Field(..., description="Actual hours worked on this project")
    actual_start_date: Optional[datetime] = Field(None, description="Actual start date of work")
    actual_end_date: Optional[datetime] = Field(None, description="Actual end date of work")
    performance_rating: Optional[float] = Field(None, description="Performance rating for this allocation")
    efficiency_score: Optional[float] = Field(None, description="Efficiency score")
    approved_by: Optional[str] = Field(None, description="Who approved this allocation")
    approved_at: Optional[datetime] = Field(None, description="When the allocation was approved")
    requested_by: Optional[str] = Field(None, description="Who requested this allocation")
    total_cost: Optional[float] = Field(None, description="Total cost of this allocation")
    client_feedback: Optional[str] = Field(None, description="Client feedback for this allocation")
    internal_notes: Optional[str] = Field(None, description="Internal notes about this allocation")
    lessons_learned: Optional[str] = Field(None, description="Lessons learned from this allocation")
    allocation_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional allocation metadata")
    allocation_duration_days: int = Field(..., description="Allocation duration in days")
    is_active: bool = Field(..., description="Whether allocation is currently active")
    is_overallocated: bool = Field(..., description="Whether actual hours exceed allocated hours")
    completion_percentage: float = Field(..., description="Completion percentage based on hours")
    cost_to_date: Optional[float] = Field(None, description="Cost based on actual hours worked")


class ResourceAllocationSummary(BaseSchema):
    """Summary schema for resource allocation."""
    
    id: UUID = Field(..., description="Unique identifier")
    pentester_id: UUID = Field(..., description="Pentester identifier")
    project_id: UUID = Field(..., description="Project identifier")
    pentester_name: str = Field(..., description="Pentester full name")
    project_name: str = Field(..., description="Project name")
    status: AllocationStatus = Field(..., description="Current allocation status")
    role: AllocationRole = Field(..., description="Role in project")
    allocated_hours: float = Field(..., description="Total hours allocated")
    actual_hours: float = Field(..., description="Actual hours worked")
    utilization_percentage: float = Field(..., description="Utilization percentage")
    allocation_start_date: datetime = Field(..., description="Start date of allocation")
    allocation_end_date: datetime = Field(..., description="End date of allocation")
    is_active: bool = Field(..., description="Whether allocation is currently active")
    completion_percentage: float = Field(..., description="Completion percentage")


class AllocationApproval(BaseSchema):
    """Schema for allocation approval."""
    
    allocation_id: UUID = Field(..., description="Allocation identifier")
    approved: bool = Field(..., description="Whether to approve the allocation")
    approved_by: str = Field(..., description="Who is approving this allocation")
    notes: Optional[str] = Field(None, description="Approval notes")


class AllocationConflict(BaseSchema):
    """Schema for allocation conflicts."""
    
    pentester_id: UUID = Field(..., description="Pentester identifier")
    pentester_name: str = Field(..., description="Pentester full name")
    conflict_type: str = Field(..., description="Type of conflict")
    conflicting_allocations: list[UUID] = Field(..., description="List of conflicting allocation IDs")
    total_utilization: float = Field(..., description="Total utilization percentage")
    recommended_action: str = Field(..., description="Recommended action to resolve conflict")
    severity: str = Field(..., description="Conflict severity level")


class TeamAllocation(BaseSchema):
    """Schema for team allocation overview."""
    
    project_id: UUID = Field(..., description="Project identifier")
    project_name: str = Field(..., description="Project name")
    team_members: list[ResourceAllocationSummary] = Field(..., description="Team member allocations")
    total_allocated_hours: float = Field(..., description="Total hours allocated to project")
    total_actual_hours: float = Field(..., description="Total actual hours worked")
    average_utilization: float = Field(..., description="Average team utilization")
    team_lead: Optional[ResourceAllocationSummary] = Field(None, description="Team lead allocation")
    skill_coverage: Dict[str, int] = Field(..., description="Skill coverage by domain")
    capacity_status: str = Field(..., description="Team capacity status")
