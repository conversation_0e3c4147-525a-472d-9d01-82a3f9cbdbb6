"""Remediation workflow Pydantic schemas."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.core.escalation import SeverityLevel, EscalationLevel, EscalationStatus
from pitas.db.models.remediation import RemediationStatus, TicketingSystem


class RemediationBase(BaseModel):
    """Base remediation schema."""
    title: str = Field(..., description="Remediation title", max_length=255)
    description: str = Field(..., description="Detailed description of the issue")
    severity: SeverityLevel = Field(..., description="Severity level")
    priority: int = Field(3, description="Priority level (1=highest, 5=lowest)", ge=1, le=5)
    cvss_score: Optional[float] = Field(None, description="CVSS score if applicable", ge=0.0, le=10.0)
    assigned_team: Optional[str] = Field(None, description="Assigned team name", max_length=100)
    system_owner: Optional[str] = Field(None, description="System owner contact", max_length=255)
    affected_systems: Optional[Dict[str, Any]] = Field(None, description="List of affected systems")
    remediation_steps: Optional[str] = Field(None, description="Recommended remediation steps")
    business_impact: Optional[str] = Field(None, description="Business impact description")
    technical_details: Optional[Dict[str, Any]] = Field(None, description="Technical details and metadata")
    verification_required: bool = Field(True, description="Whether verification testing is required")


class RemediationCreate(RemediationBase):
    """Schema for creating a remediation."""
    project_id: UUID = Field(..., description="Associated project ID")
    vulnerability_id: Optional[UUID] = Field(None, description="Associated vulnerability ID")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")


class RemediationUpdate(BaseModel):
    """Schema for updating a remediation."""
    title: Optional[str] = Field(None, description="Remediation title", max_length=255)
    description: Optional[str] = Field(None, description="Detailed description of the issue")
    severity: Optional[SeverityLevel] = Field(None, description="Severity level")
    priority: Optional[int] = Field(None, description="Priority level (1=highest, 5=lowest)", ge=1, le=5)
    cvss_score: Optional[float] = Field(None, description="CVSS score if applicable", ge=0.0, le=10.0)
    status: Optional[RemediationStatus] = Field(None, description="Current remediation status")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")
    assigned_team: Optional[str] = Field(None, description="Assigned team name", max_length=100)
    system_owner: Optional[str] = Field(None, description="System owner contact", max_length=255)
    affected_systems: Optional[Dict[str, Any]] = Field(None, description="List of affected systems")
    remediation_steps: Optional[str] = Field(None, description="Recommended remediation steps")
    business_impact: Optional[str] = Field(None, description="Business impact description")
    technical_details: Optional[Dict[str, Any]] = Field(None, description="Technical details and metadata")
    verification_required: Optional[bool] = Field(None, description="Whether verification testing is required")
    verification_notes: Optional[str] = Field(None, description="Verification notes and results")


class RemediationResponse(RemediationBase):
    """Schema for remediation responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Remediation ID")
    project_id: UUID = Field(..., description="Associated project ID")
    vulnerability_id: Optional[UUID] = Field(None, description="Associated vulnerability ID")
    status: RemediationStatus = Field(..., description="Current remediation status")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")
    assigned_at: Optional[datetime] = Field(None, description="Assignment timestamp")
    response_due: datetime = Field(..., description="Response due date")
    resolution_due: datetime = Field(..., description="Resolution due date")
    first_response_at: Optional[datetime] = Field(None, description="First response timestamp")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    sla_breached: bool = Field(..., description="Whether SLA has been breached")
    breach_reason: Optional[str] = Field(None, description="Reason for SLA breach")
    external_ticket_id: Optional[str] = Field(None, description="External ticket system ID")
    external_ticket_url: Optional[str] = Field(None, description="External ticket URL")
    ticketing_system: Optional[TicketingSystem] = Field(None, description="External ticketing system")
    verified_by: Optional[UUID] = Field(None, description="User who verified the fix")
    verified_at: Optional[datetime] = Field(None, description="Verification timestamp")
    verification_notes: Optional[str] = Field(None, description="Verification notes and results")
    closed_by: Optional[UUID] = Field(None, description="User who closed the remediation")
    closed_at: Optional[datetime] = Field(None, description="Closure timestamp")
    closure_reason: Optional[str] = Field(None, description="Reason for closure")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class RemediationEscalationResponse(BaseModel):
    """Schema for remediation escalation responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Escalation ID")
    remediation_id: UUID = Field(..., description="Remediation ID")
    level: EscalationLevel = Field(..., description="Escalation level")
    status: EscalationStatus = Field(..., description="Escalation status")
    triggered_at: datetime = Field(..., description="Escalation trigger timestamp")
    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment timestamp")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    assignee_id: Optional[UUID] = Field(None, description="Escalation assignee ID")
    message: str = Field(..., description="Escalation message")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional escalation metadata")
    created_at: datetime = Field(..., description="Creation timestamp")


class RemediationCommentBase(BaseModel):
    """Base remediation comment schema."""
    content: str = Field(..., description="Comment content")
    comment_type: str = Field("comment", description="Type of comment", max_length=50)
    is_internal: bool = Field(False, description="Whether comment is internal only")


class RemediationCommentCreate(RemediationCommentBase):
    """Schema for creating a remediation comment."""
    remediation_id: UUID = Field(..., description="Remediation ID")


class RemediationCommentResponse(RemediationCommentBase):
    """Schema for remediation comment responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Comment ID")
    remediation_id: UUID = Field(..., description="Remediation ID")
    author_id: UUID = Field(..., description="Comment author ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class RemediationAttachmentResponse(BaseModel):
    """Schema for remediation attachment responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Attachment ID")
    remediation_id: UUID = Field(..., description="Remediation ID")
    filename: str = Field(..., description="Original filename")
    file_path: str = Field(..., description="File storage path")
    file_size: int = Field(..., description="File size in bytes", ge=0)
    content_type: str = Field(..., description="MIME content type")
    uploaded_by: UUID = Field(..., description="User who uploaded the file")
    uploaded_at: datetime = Field(..., description="Upload timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")


class RemediationAssignRequest(BaseModel):
    """Schema for assigning a remediation."""
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")
    assigned_team: Optional[str] = Field(None, description="Assigned team name", max_length=100)
    system_owner: Optional[str] = Field(None, description="System owner contact", max_length=255)
    notes: Optional[str] = Field(None, description="Assignment notes")


class RemediationVerifyRequest(BaseModel):
    """Schema for verifying a remediation fix."""
    verification_notes: str = Field(..., description="Verification notes and results")
    is_verified: bool = Field(..., description="Whether the fix is verified")


class RemediationCloseRequest(BaseModel):
    """Schema for closing a remediation."""
    closure_reason: str = Field(..., description="Reason for closure", max_length=255)
    notes: Optional[str] = Field(None, description="Additional closure notes")


class EscalationAcknowledgeRequest(BaseModel):
    """Schema for acknowledging an escalation."""
    notes: Optional[str] = Field(None, description="Acknowledgment notes")


class RemediationSummary(BaseModel):
    """Schema for remediation summary information."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Remediation ID")
    title: str = Field(..., description="Remediation title")
    severity: SeverityLevel = Field(..., description="Severity level")
    status: RemediationStatus = Field(..., description="Current remediation status")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")
    response_due: datetime = Field(..., description="Response due date")
    resolution_due: datetime = Field(..., description="Resolution due date")
    sla_breached: bool = Field(..., description="Whether SLA has been breached")
    escalation_count: int = Field(0, description="Number of escalations", ge=0)
    created_at: datetime = Field(..., description="Creation timestamp")


class RemediationDashboard(BaseModel):
    """Schema for remediation dashboard data."""
    remediation: RemediationResponse = Field(..., description="Remediation details")
    escalations: List[RemediationEscalationResponse] = Field(..., description="Escalation history")
    comments: List[RemediationCommentResponse] = Field(..., description="Recent comments")
    attachments: List[RemediationAttachmentResponse] = Field(..., description="Attachments")
    sla_metrics: Dict[str, Any] = Field(..., description="SLA metrics")
    timeline: List[Dict[str, Any]] = Field(..., description="Activity timeline")


class RemediationListResponse(BaseModel):
    """Schema for paginated remediation list response."""
    remediations: List[RemediationSummary] = Field(..., description="List of remediations")
    total: int = Field(..., description="Total number of remediations", ge=0)
    page: int = Field(..., description="Current page number", ge=1)
    per_page: int = Field(..., description="Items per page", ge=1)
    total_pages: int = Field(..., description="Total number of pages", ge=1)


class RemediationMetrics(BaseModel):
    """Schema for remediation metrics."""
    total_remediations: int = Field(..., description="Total number of remediations", ge=0)
    by_severity: Dict[SeverityLevel, int] = Field(..., description="Count by severity level")
    by_status: Dict[RemediationStatus, int] = Field(..., description="Count by status")
    sla_compliance_rate: float = Field(..., description="SLA compliance rate", ge=0.0, le=1.0)
    average_resolution_time_hours: float = Field(..., description="Average resolution time in hours", ge=0.0)
    escalation_rate: float = Field(..., description="Escalation rate", ge=0.0, le=1.0)
    overdue_count: int = Field(..., description="Number of overdue remediations", ge=0)


class TicketingIntegrationRequest(BaseModel):
    """Schema for creating external tickets."""
    ticketing_system: TicketingSystem = Field(..., description="Target ticketing system")
    project_key: Optional[str] = Field(None, description="Project key for ticket creation")
    assignee: Optional[str] = Field(None, description="Default assignee")
    labels: Optional[List[str]] = Field(None, description="Ticket labels")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields for the ticket")


class TicketingIntegrationResponse(BaseModel):
    """Schema for external ticket creation response."""
    success: bool = Field(..., description="Whether ticket creation was successful")
    external_ticket_id: Optional[str] = Field(None, description="External ticket ID")
    external_ticket_url: Optional[str] = Field(None, description="External ticket URL")
    message: str = Field(..., description="Result message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Error details if creation failed")
