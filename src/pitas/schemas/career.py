"""Career development and progression schemas."""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.schemas.base import BaseDBSchema, BaseSchema
from pitas.db.models.career import IDPStatus, GoalStatus, GoalPriority, ActivityType, ActivityStatus


# Individual Development Plan Schemas
class IDPBase(BaseSchema):
    """Base schema for Individual Development Plans."""
    title: str = Field(..., description="IDP title")
    description: Optional[str] = Field(None, description="IDP description")
    status: IDPStatus = Field(default=IDPStatus.DRAFT, description="IDP status")
    start_date: datetime = Field(..., description="IDP start date")
    end_date: datetime = Field(..., description="IDP end date")
    current_tier: str = Field(..., description="Current career tier")
    target_tier: str = Field(..., description="Target career tier")
    career_track: Optional[str] = Field(None, description="Career track")
    overall_progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall progress (0.0-1.0)")
    last_review_notes: Optional[str] = Field(None, description="Last review notes")
    next_review_date: Optional[datetime] = Field(None, description="Next review date")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class IDPCreate(IDPBase):
    """Schema for creating an Individual Development Plan."""
    user_id: UUID = Field(..., description="User ID")
    manager_id: Optional[UUID] = Field(None, description="Manager ID")


class IDPUpdate(BaseSchema):
    """Schema for updating an Individual Development Plan."""
    title: Optional[str] = Field(None, description="IDP title")
    description: Optional[str] = Field(None, description="IDP description")
    status: Optional[IDPStatus] = Field(None, description="IDP status")
    end_date: Optional[datetime] = Field(None, description="IDP end date")
    target_tier: Optional[str] = Field(None, description="Target career tier")
    career_track: Optional[str] = Field(None, description="Career track")
    overall_progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="Overall progress")
    last_review_notes: Optional[str] = Field(None, description="Last review notes")
    next_review_date: Optional[datetime] = Field(None, description="Next review date")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class IDP(IDPBase, BaseDBSchema):
    """Schema for Individual Development Plan response."""
    user_id: UUID = Field(..., description="User ID")
    manager_id: Optional[UUID] = Field(None, description="Manager ID")
    review_date: Optional[datetime] = Field(None, description="Review date")


class IDPWithGoals(IDP):
    """Schema for IDP with associated goals."""
    goals: List["DevelopmentGoal"] = Field(default_factory=list, description="Development goals")


# Development Goal Schemas
class DevelopmentGoalBase(BaseSchema):
    """Base schema for Development Goals."""
    title: str = Field(..., description="Goal title")
    description: Optional[str] = Field(None, description="Goal description")
    status: GoalStatus = Field(default=GoalStatus.NOT_STARTED, description="Goal status")
    priority: GoalPriority = Field(default=GoalPriority.MEDIUM, description="Goal priority")
    target_date: Optional[datetime] = Field(None, description="Target completion date")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Goal progress (0.0-1.0)")
    success_criteria: Optional[str] = Field(None, description="Success criteria")
    measurement_method: Optional[str] = Field(None, description="How to measure progress")
    required_resources: Optional[Dict[str, Any]] = Field(None, description="Required resources")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated cost")
    assigned_mentor: Optional[UUID] = Field(None, description="Assigned mentor ID")
    target_skills: Optional[Dict[str, Any]] = Field(None, description="Target skills to develop")
    current_skill_level: Optional[Dict[str, Any]] = Field(None, description="Current skill levels")
    target_skill_level: Optional[Dict[str, Any]] = Field(None, description="Target skill levels")
    notes: Optional[str] = Field(None, description="General notes")
    last_update_notes: Optional[str] = Field(None, description="Last update notes")


class DevelopmentGoalCreate(DevelopmentGoalBase):
    """Schema for creating a Development Goal."""
    idp_id: UUID = Field(..., description="IDP ID")


class DevelopmentGoalUpdate(BaseSchema):
    """Schema for updating a Development Goal."""
    title: Optional[str] = Field(None, description="Goal title")
    description: Optional[str] = Field(None, description="Goal description")
    status: Optional[GoalStatus] = Field(None, description="Goal status")
    priority: Optional[GoalPriority] = Field(None, description="Goal priority")
    target_date: Optional[datetime] = Field(None, description="Target completion date")
    progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="Goal progress")
    success_criteria: Optional[str] = Field(None, description="Success criteria")
    measurement_method: Optional[str] = Field(None, description="Measurement method")
    required_resources: Optional[Dict[str, Any]] = Field(None, description="Required resources")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated cost")
    assigned_mentor: Optional[UUID] = Field(None, description="Assigned mentor ID")
    target_skills: Optional[Dict[str, Any]] = Field(None, description="Target skills")
    current_skill_level: Optional[Dict[str, Any]] = Field(None, description="Current skill levels")
    target_skill_level: Optional[Dict[str, Any]] = Field(None, description="Target skill levels")
    notes: Optional[str] = Field(None, description="General notes")
    last_update_notes: Optional[str] = Field(None, description="Last update notes")


class DevelopmentGoal(DevelopmentGoalBase, BaseDBSchema):
    """Schema for Development Goal response."""
    idp_id: UUID = Field(..., description="IDP ID")
    completed_date: Optional[datetime] = Field(None, description="Completion date")


class DevelopmentGoalWithActivities(DevelopmentGoal):
    """Schema for Development Goal with associated activities."""
    activities: List["DevelopmentActivity"] = Field(default_factory=list, description="Development activities")


# Development Activity Schemas
class DevelopmentActivityBase(BaseSchema):
    """Base schema for Development Activities."""
    title: str = Field(..., description="Activity title")
    description: Optional[str] = Field(None, description="Activity description")
    activity_type: ActivityType = Field(..., description="Activity type")
    status: ActivityStatus = Field(default=ActivityStatus.PLANNED, description="Activity status")
    planned_start_date: Optional[datetime] = Field(None, description="Planned start date")
    planned_end_date: Optional[datetime] = Field(None, description="Planned end date")
    estimated_hours: Optional[float] = Field(None, ge=0, description="Estimated hours")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated cost")
    provider: Optional[str] = Field(None, description="Training provider or organization")
    url: Optional[str] = Field(None, description="Related URL")
    location: Optional[str] = Field(None, description="Location")
    completion_percentage: float = Field(default=0.0, ge=0.0, le=100.0, description="Completion percentage")
    completion_notes: Optional[str] = Field(None, description="Completion notes")
    outcome: Optional[str] = Field(None, description="Activity outcome")
    certificate_url: Optional[str] = Field(None, description="Certificate URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class DevelopmentActivityCreate(DevelopmentActivityBase):
    """Schema for creating a Development Activity."""
    goal_id: UUID = Field(..., description="Goal ID")


class DevelopmentActivityUpdate(BaseSchema):
    """Schema for updating a Development Activity."""
    title: Optional[str] = Field(None, description="Activity title")
    description: Optional[str] = Field(None, description="Activity description")
    activity_type: Optional[ActivityType] = Field(None, description="Activity type")
    status: Optional[ActivityStatus] = Field(None, description="Activity status")
    planned_start_date: Optional[datetime] = Field(None, description="Planned start date")
    planned_end_date: Optional[datetime] = Field(None, description="Planned end date")
    actual_start_date: Optional[datetime] = Field(None, description="Actual start date")
    actual_end_date: Optional[datetime] = Field(None, description="Actual end date")
    estimated_hours: Optional[float] = Field(None, ge=0, description="Estimated hours")
    actual_hours: Optional[float] = Field(None, ge=0, description="Actual hours")
    estimated_cost: Optional[float] = Field(None, ge=0, description="Estimated cost")
    actual_cost: Optional[float] = Field(None, ge=0, description="Actual cost")
    provider: Optional[str] = Field(None, description="Provider")
    url: Optional[str] = Field(None, description="URL")
    location: Optional[str] = Field(None, description="Location")
    completion_percentage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Completion percentage")
    completion_notes: Optional[str] = Field(None, description="Completion notes")
    outcome: Optional[str] = Field(None, description="Outcome")
    certificate_url: Optional[str] = Field(None, description="Certificate URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class DevelopmentActivity(DevelopmentActivityBase, BaseDBSchema):
    """Schema for Development Activity response."""
    goal_id: UUID = Field(..., description="Goal ID")
    actual_start_date: Optional[datetime] = Field(None, description="Actual start date")
    actual_end_date: Optional[datetime] = Field(None, description="Actual end date")
    actual_hours: Optional[float] = Field(None, description="Actual hours")
    actual_cost: Optional[float] = Field(None, description="Actual cost")


# Career Progress Summary Schemas
class CareerProgressSummary(BaseSchema):
    """Summary of career development progress."""
    user_id: UUID = Field(..., description="User ID")
    current_tier: str = Field(..., description="Current career tier")
    target_tier: str = Field(..., description="Target career tier")
    career_track: Optional[str] = Field(None, description="Career track")
    active_idps: int = Field(..., description="Number of active IDPs")
    completed_idps: int = Field(..., description="Number of completed IDPs")
    total_goals: int = Field(..., description="Total number of goals")
    completed_goals: int = Field(..., description="Number of completed goals")
    in_progress_goals: int = Field(..., description="Number of in-progress goals")
    overall_progress: float = Field(..., ge=0.0, le=1.0, description="Overall career progress")
    last_review_date: Optional[datetime] = Field(None, description="Last review date")
    next_review_date: Optional[datetime] = Field(None, description="Next review date")
    development_budget_used: float = Field(default=0.0, ge=0, description="Development budget used")
    development_budget_remaining: float = Field(default=0.0, ge=0, description="Development budget remaining")


# Update forward references
IDPWithGoals.model_rebuild()
DevelopmentGoalWithActivities.model_rebuild()
