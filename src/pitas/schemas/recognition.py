"""Recognition and rewards system schemas."""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.schemas.base import BaseDBSchema, BaseSchema
from pitas.db.models.recognition import (
    RecognitionType, RecognitionStatus, AchievementCategory,
    RewardType, RewardStatus
)


# Recognition Schemas
class RecognitionBase(BaseSchema):
    """Base schema for Recognition."""
    title: str = Field(..., description="Recognition title")
    description: str = Field(..., description="Recognition description")
    recognition_type: RecognitionType = Field(..., description="Type of recognition")
    status: RecognitionStatus = Field(default=RecognitionStatus.PENDING, description="Recognition status")
    points_awarded: int = Field(default=0, ge=0, description="Points awarded")
    monetary_value: Optional[float] = Field(None, ge=0, description="Monetary value in USD")
    achievement_category: Optional[AchievementCategory] = Field(None, description="Achievement category")
    is_public: bool = Field(default=True, description="Whether recognition is public")
    is_featured: bool = Field(default=False, description="Whether recognition is featured")
    share_externally: bool = Field(default=False, description="Whether to share externally")
    evidence_urls: Optional[Dict[str, Any]] = Field(None, description="Supporting evidence URLs")
    impact_metrics: Optional[Dict[str, Any]] = Field(None, description="Quantifiable impact metrics")
    testimonials: Optional[Dict[str, Any]] = Field(None, description="Client/peer testimonials")
    tags: Optional[Dict[str, Any]] = Field(None, description="Searchable tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class RecognitionCreate(RecognitionBase):
    """Schema for creating a Recognition."""
    recipient_id: UUID = Field(..., description="Recipient user ID")
    nominator_id: Optional[UUID] = Field(None, description="Nominator user ID")


class RecognitionUpdate(BaseSchema):
    """Schema for updating a Recognition."""
    title: Optional[str] = Field(None, description="Recognition title")
    description: Optional[str] = Field(None, description="Recognition description")
    status: Optional[RecognitionStatus] = Field(None, description="Recognition status")
    points_awarded: Optional[int] = Field(None, ge=0, description="Points awarded")
    monetary_value: Optional[float] = Field(None, ge=0, description="Monetary value")
    achievement_category: Optional[AchievementCategory] = Field(None, description="Achievement category")
    is_public: Optional[bool] = Field(None, description="Public visibility")
    is_featured: Optional[bool] = Field(None, description="Featured status")
    share_externally: Optional[bool] = Field(None, description="External sharing")
    evidence_urls: Optional[Dict[str, Any]] = Field(None, description="Evidence URLs")
    impact_metrics: Optional[Dict[str, Any]] = Field(None, description="Impact metrics")
    testimonials: Optional[Dict[str, Any]] = Field(None, description="Testimonials")
    tags: Optional[Dict[str, Any]] = Field(None, description="Tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")
    approver_id: Optional[UUID] = Field(None, description="Approver user ID")
    approval_date: Optional[datetime] = Field(None, description="Approval date")
    presentation_date: Optional[datetime] = Field(None, description="Presentation date")


class Recognition(RecognitionBase, BaseDBSchema):
    """Schema for Recognition response."""
    recipient_id: UUID = Field(..., description="Recipient user ID")
    nominator_id: Optional[UUID] = Field(None, description="Nominator user ID")
    approver_id: Optional[UUID] = Field(None, description="Approver user ID")
    nomination_date: datetime = Field(..., description="Nomination date")
    approval_date: Optional[datetime] = Field(None, description="Approval date")
    presentation_date: Optional[datetime] = Field(None, description="Presentation date")


# Peer Nomination Schemas
class PeerNominationBase(BaseSchema):
    """Base schema for Peer Nominations."""
    title: str = Field(..., description="Nomination title")
    description: str = Field(..., description="Nomination description")
    achievement_category: AchievementCategory = Field(..., description="Achievement category")
    impact_description: str = Field(..., description="Impact description")
    specific_examples: Optional[str] = Field(None, description="Specific examples")
    votes_required: int = Field(default=3, ge=1, description="Votes required for approval")
    voting_deadline: Optional[datetime] = Field(None, description="Voting deadline")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class PeerNominationCreate(PeerNominationBase):
    """Schema for creating a Peer Nomination."""
    nominee_id: UUID = Field(..., description="Nominee user ID")
    nominator_id: UUID = Field(..., description="Nominator user ID")


class PeerNominationUpdate(BaseSchema):
    """Schema for updating a Peer Nomination."""
    title: Optional[str] = Field(None, description="Nomination title")
    description: Optional[str] = Field(None, description="Nomination description")
    impact_description: Optional[str] = Field(None, description="Impact description")
    specific_examples: Optional[str] = Field(None, description="Specific examples")
    status: Optional[RecognitionStatus] = Field(None, description="Nomination status")
    voting_deadline: Optional[datetime] = Field(None, description="Voting deadline")
    decision_date: Optional[datetime] = Field(None, description="Decision date")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class PeerNomination(PeerNominationBase, BaseDBSchema):
    """Schema for Peer Nomination response."""
    nominee_id: UUID = Field(..., description="Nominee user ID")
    nominator_id: UUID = Field(..., description="Nominator user ID")
    status: RecognitionStatus = Field(..., description="Nomination status")
    votes_received: int = Field(..., description="Votes received")
    submission_date: datetime = Field(..., description="Submission date")
    decision_date: Optional[datetime] = Field(None, description="Decision date")


class PeerNominationWithVotes(PeerNomination):
    """Schema for Peer Nomination with votes."""
    votes: List["NominationVote"] = Field(default_factory=list, description="Nomination votes")


# Nomination Vote Schemas
class NominationVoteBase(BaseSchema):
    """Base schema for Nomination Votes."""
    vote: bool = Field(..., description="Vote (True for support, False for oppose)")
    comment: Optional[str] = Field(None, description="Vote comment")


class NominationVoteCreate(NominationVoteBase):
    """Schema for creating a Nomination Vote."""
    nomination_id: UUID = Field(..., description="Nomination ID")
    voter_id: UUID = Field(..., description="Voter user ID")


class NominationVoteUpdate(BaseSchema):
    """Schema for updating a Nomination Vote."""
    vote: Optional[bool] = Field(None, description="Vote")
    comment: Optional[str] = Field(None, description="Comment")


class NominationVote(NominationVoteBase, BaseDBSchema):
    """Schema for Nomination Vote response."""
    nomination_id: UUID = Field(..., description="Nomination ID")
    voter_id: UUID = Field(..., description="Voter user ID")
    vote_date: datetime = Field(..., description="Vote date")


# Reward Schemas
class RewardBase(BaseSchema):
    """Base schema for Rewards."""
    title: str = Field(..., description="Reward title")
    description: Optional[str] = Field(None, description="Reward description")
    reward_type: RewardType = Field(..., description="Reward type")
    status: RewardStatus = Field(default=RewardStatus.PENDING, description="Reward status")
    monetary_value: Optional[float] = Field(None, ge=0, description="Monetary value")
    points_cost: int = Field(default=0, ge=0, description="Points cost")
    quantity: int = Field(default=1, ge=1, description="Quantity")
    expiration_date: Optional[datetime] = Field(None, description="Expiration date")
    fulfillment_notes: Optional[str] = Field(None, description="Fulfillment notes")
    vendor_information: Optional[Dict[str, Any]] = Field(None, description="Vendor information")
    tracking_information: Optional[Dict[str, Any]] = Field(None, description="Tracking information")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class RewardCreate(RewardBase):
    """Schema for creating a Reward."""
    recipient_id: UUID = Field(..., description="Recipient user ID")
    authorized_by_id: Optional[UUID] = Field(None, description="Authorized by user ID")
    recognition_id: Optional[UUID] = Field(None, description="Related recognition ID")


class RewardUpdate(BaseSchema):
    """Schema for updating a Reward."""
    title: Optional[str] = Field(None, description="Reward title")
    description: Optional[str] = Field(None, description="Reward description")
    status: Optional[RewardStatus] = Field(None, description="Reward status")
    monetary_value: Optional[float] = Field(None, ge=0, description="Monetary value")
    points_cost: Optional[int] = Field(None, ge=0, description="Points cost")
    quantity: Optional[int] = Field(None, ge=1, description="Quantity")
    expiration_date: Optional[datetime] = Field(None, description="Expiration date")
    fulfilled_date: Optional[datetime] = Field(None, description="Fulfilled date")
    fulfillment_notes: Optional[str] = Field(None, description="Fulfillment notes")
    vendor_information: Optional[Dict[str, Any]] = Field(None, description="Vendor information")
    tracking_information: Optional[Dict[str, Any]] = Field(None, description="Tracking information")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class Reward(RewardBase, BaseDBSchema):
    """Schema for Reward response."""
    recipient_id: UUID = Field(..., description="Recipient user ID")
    authorized_by_id: Optional[UUID] = Field(None, description="Authorized by user ID")
    recognition_id: Optional[UUID] = Field(None, description="Related recognition ID")
    awarded_date: datetime = Field(..., description="Awarded date")
    fulfilled_date: Optional[datetime] = Field(None, description="Fulfilled date")


# Recognition Analytics Schemas
class RecognitionStats(BaseSchema):
    """Recognition statistics for a user or organization."""
    user_id: Optional[UUID] = Field(None, description="User ID (if user-specific)")
    total_recognitions: int = Field(..., description="Total recognitions received")
    total_points: int = Field(..., description="Total points earned")
    total_monetary_value: float = Field(..., description="Total monetary value received")
    recognitions_by_type: Dict[str, int] = Field(..., description="Recognitions by type")
    recognitions_by_category: Dict[str, int] = Field(..., description="Recognitions by category")
    peer_nominations_received: int = Field(..., description="Peer nominations received")
    peer_nominations_given: int = Field(..., description="Peer nominations given")
    recent_recognitions: List[Recognition] = Field(..., description="Recent recognitions")
    recognition_trend: Dict[str, int] = Field(..., description="Recognition trend over time")


class RewardStats(BaseSchema):
    """Reward statistics for a user or organization."""
    user_id: Optional[UUID] = Field(None, description="User ID (if user-specific)")
    total_rewards: int = Field(..., description="Total rewards received")
    total_value: float = Field(..., description="Total value of rewards")
    rewards_by_type: Dict[str, int] = Field(..., description="Rewards by type")
    pending_rewards: int = Field(..., description="Pending rewards")
    fulfilled_rewards: int = Field(..., description="Fulfilled rewards")
    recent_rewards: List[Reward] = Field(..., description="Recent rewards")


# Update forward references
PeerNominationWithVotes.model_rebuild()
