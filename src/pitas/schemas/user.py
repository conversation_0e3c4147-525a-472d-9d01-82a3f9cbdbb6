"""User schemas with Phase 6 career development and retention features."""

from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, EmailStr, ConfigDict

from pitas.schemas.base import BaseDBSchema, BaseSchema
from pitas.db.models.user import UserRole, CareerTier, CareerTrack


# User Base Schemas
class UserBase(BaseSchema):
    """Base schema for User."""
    email: EmailStr = Field(..., description="User email address")
    username: str = Field(..., min_length=3, max_length=100, description="Username")
    full_name: str = Field(..., min_length=1, max_length=255, description="Full name")
    is_active: bool = Field(default=True, description="Is user active")
    is_superuser: bool = Field(default=False, description="Is superuser")
    role: UserRole = Field(default=UserRole.JUNIOR_PENTESTER, description="User role")
    career_tier: CareerTier = Field(default=CareerTier.ENTRY, description="Career tier")
    career_track: Optional[CareerTrack] = Field(None, description="Career track")
    years_experience: int = Field(default=0, ge=0, description="Years of experience")
    department: Optional[str] = Field(None, max_length=100, description="Department")
    location: Optional[str] = Field(None, max_length=100, description="Location")
    timezone: Optional[str] = Field(None, max_length=50, description="Timezone")
    manager_id: Optional[UUID] = Field(None, description="Manager user ID")
    professional_dev_budget: int = Field(default=5000, ge=0, description="Annual professional development budget")
    certifications: Optional[Dict[str, Any]] = Field(None, description="Certifications")
    skills: Optional[Dict[str, Any]] = Field(None, description="Skills and proficiency levels")
    preferred_work_hours: Optional[Dict[str, Any]] = Field(None, description="Preferred work hours")
    wellness_score: Optional[int] = Field(None, ge=1, le=100, description="Wellness score (1-100)")
    total_recognition_points: int = Field(default=0, ge=0, description="Total recognition points")
    bio: Optional[str] = Field(None, description="User biography")
    avatar_url: Optional[str] = Field(None, max_length=500, description="Avatar URL")
    preferences: Optional[Dict[str, Any]] = Field(None, description="User preferences")


class UserCreate(UserBase):
    """Schema for creating a User."""
    password: str = Field(..., min_length=8, description="User password")
    hire_date: Optional[datetime] = Field(None, description="Hire date")


class UserUpdate(BaseSchema):
    """Schema for updating a User."""
    email: Optional[EmailStr] = Field(None, description="Email address")
    username: Optional[str] = Field(None, min_length=3, max_length=100, description="Username")
    full_name: Optional[str] = Field(None, min_length=1, max_length=255, description="Full name")
    is_active: Optional[bool] = Field(None, description="Is active")
    role: Optional[UserRole] = Field(None, description="User role")
    career_tier: Optional[CareerTier] = Field(None, description="Career tier")
    career_track: Optional[CareerTrack] = Field(None, description="Career track")
    years_experience: Optional[int] = Field(None, ge=0, description="Years of experience")
    department: Optional[str] = Field(None, max_length=100, description="Department")
    location: Optional[str] = Field(None, max_length=100, description="Location")
    timezone: Optional[str] = Field(None, max_length=50, description="Timezone")
    manager_id: Optional[UUID] = Field(None, description="Manager user ID")
    professional_dev_budget: Optional[int] = Field(None, ge=0, description="Development budget")
    certifications: Optional[Dict[str, Any]] = Field(None, description="Certifications")
    skills: Optional[Dict[str, Any]] = Field(None, description="Skills")
    preferred_work_hours: Optional[Dict[str, Any]] = Field(None, description="Preferred work hours")
    wellness_score: Optional[int] = Field(None, ge=1, le=100, description="Wellness score")
    total_recognition_points: Optional[int] = Field(None, ge=0, description="Recognition points")
    last_promotion_date: Optional[datetime] = Field(None, description="Last promotion date")
    bio: Optional[str] = Field(None, description="Biography")
    avatar_url: Optional[str] = Field(None, max_length=500, description="Avatar URL")
    preferences: Optional[Dict[str, Any]] = Field(None, description="Preferences")


class UserPasswordUpdate(BaseSchema):
    """Schema for updating user password."""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")


class User(UserBase, BaseDBSchema):
    """Schema for User response."""
    hire_date: Optional[datetime] = Field(None, description="Hire date")
    last_wellness_check: Optional[datetime] = Field(None, description="Last wellness check")
    last_promotion_date: Optional[datetime] = Field(None, description="Last promotion date")


class UserWithRelations(User):
    """Schema for User with related data."""
    # These will be populated by services when needed
    active_idps_count: Optional[int] = Field(None, description="Number of active IDPs")
    total_recognitions: Optional[int] = Field(None, description="Total recognitions received")
    active_mentorships_count: Optional[int] = Field(None, description="Number of active mentorships")
    wellness_alerts_count: Optional[int] = Field(None, description="Number of active wellness alerts")


# Authentication Schemas
class UserLogin(BaseSchema):
    """Schema for user login."""
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")


class Token(BaseSchema):
    """Schema for authentication token."""
    access_token: str = Field(..., description="Access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class TokenData(BaseSchema):
    """Schema for token data."""
    username: Optional[str] = Field(None, description="Username")
    user_id: Optional[UUID] = Field(None, description="User ID")


# User Profile and Career Schemas
class UserProfile(BaseSchema):
    """Comprehensive user profile schema."""
    user: User = Field(..., description="User information")
    career_summary: Optional[Dict[str, Any]] = Field(None, description="Career development summary")
    recognition_summary: Optional[Dict[str, Any]] = Field(None, description="Recognition summary")
    wellness_summary: Optional[Dict[str, Any]] = Field(None, description="Wellness summary")
    mentorship_summary: Optional[Dict[str, Any]] = Field(None, description="Mentorship summary")


class UserCareerPath(BaseSchema):
    """User career path and progression."""
    user_id: UUID = Field(..., description="User ID")
    current_tier: CareerTier = Field(..., description="Current career tier")
    target_tier: CareerTier = Field(..., description="Target career tier")
    career_track: Optional[CareerTrack] = Field(None, description="Career track")
    years_in_current_tier: Optional[float] = Field(None, description="Years in current tier")
    progression_percentage: float = Field(..., ge=0.0, le=1.0, description="Progression percentage")
    next_milestone: Optional[str] = Field(None, description="Next career milestone")
    required_skills: Optional[Dict[str, Any]] = Field(None, description="Required skills for advancement")
    skill_gaps: Optional[Dict[str, Any]] = Field(None, description="Identified skill gaps")
    recommended_activities: Optional[List[str]] = Field(None, description="Recommended development activities")


class UserTeam(BaseSchema):
    """User team information."""
    user_id: UUID = Field(..., description="User ID")
    team_name: Optional[str] = Field(None, description="Team name")
    team_members: List[User] = Field(default_factory=list, description="Team members")
    manager: Optional[User] = Field(None, description="Manager information")
    direct_reports: List[User] = Field(default_factory=list, description="Direct reports")


# User Search and Filtering
class UserSearchFilters(BaseSchema):
    """Filters for user search."""
    role: Optional[UserRole] = Field(None, description="Filter by role")
    career_tier: Optional[CareerTier] = Field(None, description="Filter by career tier")
    career_track: Optional[CareerTrack] = Field(None, description="Filter by career track")
    department: Optional[str] = Field(None, description="Filter by department")
    location: Optional[str] = Field(None, description="Filter by location")
    manager_id: Optional[UUID] = Field(None, description="Filter by manager")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    min_experience: Optional[int] = Field(None, ge=0, description="Minimum years of experience")
    max_experience: Optional[int] = Field(None, ge=0, description="Maximum years of experience")
    has_mentor_profile: Optional[bool] = Field(None, description="Has mentor profile")
    available_for_mentoring: Optional[bool] = Field(None, description="Available for mentoring")
    skills: Optional[List[str]] = Field(None, description="Filter by skills")
    certifications: Optional[List[str]] = Field(None, description="Filter by certifications")


class UserListResponse(BaseSchema):
    """Response schema for user list with pagination."""
    users: List[User] = Field(..., description="List of users")
    total: int = Field(..., description="Total number of users")
    page: int = Field(..., description="Current page")
    per_page: int = Field(..., description="Items per page")
    has_next: bool = Field(..., description="Has next page")
    has_previous: bool = Field(..., description="Has previous page")