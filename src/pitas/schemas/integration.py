"""Integration schemas for Phase 7: Enterprise Systems Integration."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import Field, ConfigDict

from pitas.schemas.base import BaseSchema, BaseDBSchema
from pitas.db.models.integration import IntegrationType, IntegrationStatus, SyncStatus


class IntegrationBase(BaseSchema):
    """Base integration schema."""
    
    name: str = Field(..., description="Integration name", max_length=255)
    description: Optional[str] = Field(None, description="Integration description")
    integration_type: IntegrationType = Field(..., description="Type of integration")
    api_url: Optional[str] = Field(None, description="API URL", max_length=500)
    sync_interval: int = Field(300, description="Sync interval in seconds", ge=60)
    is_enabled: bool = Field(True, description="Whether integration is enabled")
    max_retries: int = Field(3, description="Maximum retry attempts", ge=0, le=10)


class IntegrationCreate(IntegrationBase):
    """Schema for creating integrations."""
    
    api_key: Optional[str] = Field(None, description="API key for authentication")
    username: Optional[str] = Field(None, description="Username for authentication")
    password: Optional[str] = Field(None, description="Password for authentication")
    config: Optional[Dict[str, Any]] = Field(None, description="Integration configuration")


class IntegrationUpdate(BaseSchema):
    """Schema for updating integrations."""
    
    name: Optional[str] = Field(None, description="Integration name", max_length=255)
    description: Optional[str] = Field(None, description="Integration description")
    api_url: Optional[str] = Field(None, description="API URL", max_length=500)
    api_key: Optional[str] = Field(None, description="API key for authentication")
    username: Optional[str] = Field(None, description="Username for authentication")
    password: Optional[str] = Field(None, description="Password for authentication")
    config: Optional[Dict[str, Any]] = Field(None, description="Integration configuration")
    sync_interval: Optional[int] = Field(None, description="Sync interval in seconds", ge=60)
    is_enabled: Optional[bool] = Field(None, description="Whether integration is enabled")
    max_retries: Optional[int] = Field(None, description="Maximum retry attempts", ge=0, le=10)


class Integration(BaseDBSchema):
    """Integration response schema."""
    
    name: str
    description: Optional[str]
    integration_type: IntegrationType
    status: IntegrationStatus
    api_url: Optional[str]
    sync_interval: int
    last_sync: Optional[datetime]
    next_sync: Optional[datetime]
    is_enabled: bool
    retry_count: int
    max_retries: int
    last_error: Optional[str]
    
    # Exclude sensitive fields from response
    model_config = ConfigDict(from_attributes=True)


class IntegrationSyncLogBase(BaseSchema):
    """Base sync log schema."""
    
    status: SyncStatus = Field(..., description="Sync operation status")
    started_at: datetime = Field(..., description="Sync start time")
    completed_at: Optional[datetime] = Field(None, description="Sync completion time")
    records_processed: int = Field(0, description="Number of records processed", ge=0)
    records_created: int = Field(0, description="Number of records created", ge=0)
    records_updated: int = Field(0, description="Number of records updated", ge=0)
    records_failed: int = Field(0, description="Number of records failed", ge=0)
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Detailed error information")
    sync_metadata: Optional[Dict[str, Any]] = Field(None, description="Sync metadata")


class IntegrationSyncLogCreate(IntegrationSyncLogBase):
    """Schema for creating sync logs."""
    
    integration_id: UUID = Field(..., description="Integration ID")


class IntegrationSyncLog(BaseDBSchema, IntegrationSyncLogBase):
    """Sync log response schema."""
    
    integration_id: UUID


class DataMappingBase(BaseSchema):
    """Base data mapping schema."""
    
    source_field: str = Field(..., description="Source field name", max_length=255)
    target_field: str = Field(..., description="Target field name", max_length=255)
    field_type: str = Field(..., description="Field data type", max_length=50)
    transformation_rules: Optional[Dict[str, Any]] = Field(None, description="Transformation rules")
    is_required: bool = Field(False, description="Whether field is required")
    default_value: Optional[str] = Field(None, description="Default value", max_length=500)
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")


class DataMappingCreate(DataMappingBase):
    """Schema for creating data mappings."""
    
    integration_id: UUID = Field(..., description="Integration ID")


class DataMappingUpdate(BaseSchema):
    """Schema for updating data mappings."""
    
    source_field: Optional[str] = Field(None, description="Source field name", max_length=255)
    target_field: Optional[str] = Field(None, description="Target field name", max_length=255)
    field_type: Optional[str] = Field(None, description="Field data type", max_length=50)
    transformation_rules: Optional[Dict[str, Any]] = Field(None, description="Transformation rules")
    is_required: Optional[bool] = Field(None, description="Whether field is required")
    default_value: Optional[str] = Field(None, description="Default value", max_length=500)
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")


class DataMapping(BaseDBSchema, DataMappingBase):
    """Data mapping response schema."""
    
    integration_id: UUID


# Integration-specific schemas
class ObsidianIntegrationConfig(BaseSchema):
    """Obsidian-specific integration configuration."""
    
    vault_path: str = Field(..., description="Path to Obsidian vault")
    template_folder: str = Field("templates", description="Template folder name")
    output_folder: str = Field("generated", description="Output folder for generated documents")
    auto_sync: bool = Field(True, description="Enable automatic synchronization")
    sync_tags: List[str] = Field(default_factory=list, description="Tags to sync")
    exclude_folders: List[str] = Field(default_factory=list, description="Folders to exclude from sync")


class CMDBIntegrationConfig(BaseSchema):
    """CMDB-specific integration configuration."""
    
    cmdb_type: str = Field(..., description="CMDB system type")
    table_name: str = Field("cmdb_ci", description="CMDB table name")
    query_filter: Optional[str] = Field(None, description="Query filter for CI selection")
    sync_relationships: bool = Field(True, description="Sync CI relationships")
    relationship_types: List[str] = Field(default_factory=list, description="Relationship types to sync")
    custom_fields: Dict[str, str] = Field(default_factory=dict, description="Custom field mappings")


class SIEMIntegrationConfig(BaseSchema):
    """SIEM-specific integration configuration."""
    
    siem_type: str = Field(..., description="SIEM system type")
    index_name: Optional[str] = Field(None, description="Index or data source name")
    query_timeframe: int = Field(3600, description="Query timeframe in seconds")
    event_types: List[str] = Field(default_factory=list, description="Event types to collect")
    severity_filter: List[str] = Field(default_factory=list, description="Severity levels to include")
    correlation_rules: Dict[str, Any] = Field(default_factory=dict, description="Event correlation rules")


class VulnerabilityIntegrationConfig(BaseSchema):
    """Vulnerability scanner integration configuration."""
    
    scanner_type: str = Field(..., description="Scanner type")
    scan_policies: List[str] = Field(default_factory=list, description="Scan policies to include")
    asset_groups: List[str] = Field(default_factory=list, description="Asset groups to scan")
    severity_threshold: str = Field("medium", description="Minimum severity to import")
    auto_remediation: bool = Field(False, description="Enable automatic remediation assignment")
    remediation_sla: Dict[str, int] = Field(default_factory=dict, description="SLA by severity level")


# Integration test schemas
class IntegrationTestRequest(BaseSchema):
    """Schema for testing integration connectivity."""
    
    test_type: str = Field("connectivity", description="Type of test to perform")
    test_parameters: Optional[Dict[str, Any]] = Field(None, description="Test parameters")


class IntegrationTestResult(BaseSchema):
    """Schema for integration test results."""
    
    success: bool = Field(..., description="Whether test was successful")
    message: str = Field(..., description="Test result message")
    response_time: Optional[float] = Field(None, description="Response time in seconds")
    details: Optional[Dict[str, Any]] = Field(None, description="Detailed test results")
    timestamp: datetime = Field(..., description="Test execution timestamp")


# Sync operation schemas
class SyncOperationRequest(BaseSchema):
    """Schema for requesting sync operations."""
    
    sync_type: str = Field("full", description="Type of sync (full, incremental)")
    force_sync: bool = Field(False, description="Force sync even if recently synced")
    sync_options: Optional[Dict[str, Any]] = Field(None, description="Sync options")


class SyncOperationStatus(BaseSchema):
    """Schema for sync operation status."""
    
    operation_id: str = Field(..., description="Sync operation ID")
    status: SyncStatus = Field(..., description="Current sync status")
    progress: float = Field(..., description="Sync progress (0.0 to 1.0)")
    message: str = Field(..., description="Current status message")
    started_at: datetime = Field(..., description="Sync start time")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    records_processed: int = Field(0, description="Records processed so far")
    total_records: Optional[int] = Field(None, description="Total records to process")
