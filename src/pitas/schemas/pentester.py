"""Pentester profile schemas for Phase 2."""

from datetime import datetime, time
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import Field, EmailStr, validator

from .base import BaseSchema, BaseDBSchema
from ..db.models.pentester import AvailabilityStatus, WorkingTimeZone


class PentesterProfileBase(BaseSchema):
    """Base schema for pentester profile."""
    
    employee_id: str = Field(..., description="Unique employee identifier")
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    email: EmailStr = Field(..., description="Email address")
    availability_hours: int = Field(default=40, ge=0, le=168, description="Available hours per week")
    current_utilization: float = Field(default=0.0, ge=0.0, le=100.0, description="Current utilization percentage")
    max_concurrent_projects: int = Field(default=3, ge=1, le=10, description="Maximum concurrent projects")
    availability_status: AvailabilityStatus = Field(default=AvailabilityStatus.AVAILABLE, description="Current availability status")
    location: Optional[str] = Field(None, description="Geographic location")
    working_timezone: WorkingTimeZone = Field(default=WorkingTimeZone.UTC, description="Primary working timezone")
    working_hours_start: Optional[time] = Field(None, description="Daily working hours start time")
    working_hours_end: Optional[time] = Field(None, description="Daily working hours end time")
    performance_rating: Optional[float] = Field(None, ge=1.0, le=5.0, description="Overall performance rating")
    projects_completed: int = Field(default=0, ge=0, description="Total projects completed")
    average_project_rating: Optional[float] = Field(None, ge=1.0, le=5.0, description="Average client rating")
    primary_specializations: Optional[List[str]] = Field(None, description="Primary security domain specializations")
    secondary_specializations: Optional[List[str]] = Field(None, description="Secondary security domain specializations")
    preferred_project_types: Optional[List[str]] = Field(None, description="Preferred types of pentesting projects")
    seniority_level: str = Field(default="junior", description="Seniority level")
    hire_date: Optional[datetime] = Field(None, description="Date of hire")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
    extra_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_active: bool = Field(default=True, description="Whether the pentester is active")
    is_team_lead: bool = Field(default=False, description="Whether the pentester is a team lead")
    can_mentor: bool = Field(default=False, description="Whether the pentester can mentor others")


class PentesterProfileCreate(PentesterProfileBase):
    """Schema for creating a pentester profile."""
    
    user_id: Optional[UUID] = Field(None, description="Reference to user account")


class PentesterProfileUpdate(BaseSchema):
    """Schema for updating a pentester profile."""
    
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    email: Optional[EmailStr] = Field(None, description="Email address")
    availability_hours: Optional[int] = Field(None, ge=0, le=168, description="Available hours per week")
    current_utilization: Optional[float] = Field(None, ge=0.0, le=100.0, description="Current utilization percentage")
    max_concurrent_projects: Optional[int] = Field(None, ge=1, le=10, description="Maximum concurrent projects")
    availability_status: Optional[AvailabilityStatus] = Field(None, description="Current availability status")
    location: Optional[str] = Field(None, description="Geographic location")
    working_timezone: Optional[WorkingTimeZone] = Field(None, description="Primary working timezone")
    working_hours_start: Optional[time] = Field(None, description="Daily working hours start time")
    working_hours_end: Optional[time] = Field(None, description="Daily working hours end time")
    performance_rating: Optional[float] = Field(None, ge=1.0, le=5.0, description="Overall performance rating")
    projects_completed: Optional[int] = Field(None, ge=0, description="Total projects completed")
    average_project_rating: Optional[float] = Field(None, ge=1.0, le=5.0, description="Average client rating")
    primary_specializations: Optional[List[str]] = Field(None, description="Primary security domain specializations")
    secondary_specializations: Optional[List[str]] = Field(None, description="Secondary security domain specializations")
    preferred_project_types: Optional[List[str]] = Field(None, description="Preferred types of pentesting projects")
    seniority_level: Optional[str] = Field(None, description="Seniority level")
    hire_date: Optional[datetime] = Field(None, description="Date of hire")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
    extra_metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    is_active: Optional[bool] = Field(None, description="Whether the pentester is active")
    is_team_lead: Optional[bool] = Field(None, description="Whether the pentester is a team lead")
    can_mentor: Optional[bool] = Field(None, description="Whether the pentester can mentor others")


class PentesterProfile(PentesterProfileBase, BaseDBSchema):
    """Schema for pentester profile response."""
    
    user_id: Optional[UUID] = Field(None, description="Reference to user account")
    full_name: str = Field(..., description="Full name")
    available_hours_remaining: float = Field(..., description="Remaining available hours")


class PentesterProfileSummary(BaseSchema):
    """Summary schema for pentester profile."""
    
    id: UUID = Field(..., description="Unique identifier")
    employee_id: str = Field(..., description="Employee identifier")
    full_name: str = Field(..., description="Full name")
    email: EmailStr = Field(..., description="Email address")
    availability_status: AvailabilityStatus = Field(..., description="Current availability status")
    current_utilization: float = Field(..., description="Current utilization percentage")
    seniority_level: str = Field(..., description="Seniority level")
    primary_specializations: Optional[List[str]] = Field(None, description="Primary specializations")
    is_active: bool = Field(..., description="Whether the pentester is active")


class PentesterAvailability(BaseSchema):
    """Schema for pentester availability information."""
    
    pentester_id: UUID = Field(..., description="Pentester identifier")
    full_name: str = Field(..., description="Full name")
    availability_status: AvailabilityStatus = Field(..., description="Current availability status")
    current_utilization: float = Field(..., description="Current utilization percentage")
    available_hours_remaining: float = Field(..., description="Remaining available hours")
    working_timezone: WorkingTimeZone = Field(..., description="Working timezone")
    max_concurrent_projects: int = Field(..., description="Maximum concurrent projects")
    active_projects: int = Field(..., description="Number of active projects")


class PentesterPerformance(BaseSchema):
    """Schema for pentester performance metrics."""
    
    pentester_id: UUID = Field(..., description="Pentester identifier")
    full_name: str = Field(..., description="Full name")
    performance_rating: Optional[float] = Field(None, description="Overall performance rating")
    projects_completed: int = Field(..., description="Total projects completed")
    average_project_rating: Optional[float] = Field(None, description="Average client rating")
    current_utilization: float = Field(..., description="Current utilization percentage")
    efficiency_score: Optional[float] = Field(None, description="Efficiency score")
    skill_match_average: Optional[float] = Field(None, description="Average skill match score")
