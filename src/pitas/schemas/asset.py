"""Asset schemas for Phase 7: CMDB Integration."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import Field, ConfigDict, IPvAnyAddress

from pitas.schemas.base import BaseSchema, BaseDBSchema
from pitas.db.models.asset import AssetType, AssetStatus, CriticalityLevel, DependencyType


class AssetBase(BaseSchema):
    """Base asset schema."""
    
    name: str = Field(..., description="Asset name", max_length=255)
    description: Optional[str] = Field(None, description="Asset description")
    asset_type: AssetType = Field(..., description="Type of asset")
    hostname: Optional[str] = Field(None, description="Asset hostname", max_length=255)
    ip_address: Optional[str] = Field(None, description="IP address", max_length=45)
    mac_address: Optional[str] = Field(None, description="MAC address", max_length=17)
    operating_system: Optional[str] = Field(None, description="Operating system", max_length=255)
    os_version: Optional[str] = Field(None, description="OS version", max_length=100)
    business_service: Optional[str] = Field(None, description="Business service", max_length=255)
    owner: Optional[str] = Field(None, description="Asset owner", max_length=255)
    department: Optional[str] = Field(None, description="Department", max_length=255)
    location: Optional[str] = Field(None, description="Physical location", max_length=255)
    criticality: CriticalityLevel = Field(CriticalityLevel.UNKNOWN, description="Asset criticality")
    configuration: Optional[Dict[str, Any]] = Field(None, description="Asset configuration")
    installed_software: Optional[List[Dict[str, Any]]] = Field(None, description="Installed software")
    network_interfaces: Optional[List[Dict[str, Any]]] = Field(None, description="Network interfaces")


class AssetCreate(AssetBase):
    """Schema for creating assets."""
    
    status: AssetStatus = Field(AssetStatus.ACTIVE, description="Asset status")
    cmdb_id: Optional[str] = Field(None, description="CMDB ID", max_length=255)
    cmdb_class: Optional[str] = Field(None, description="CMDB class", max_length=100)
    risk_score: Optional[float] = Field(None, description="Risk score", ge=0.0, le=10.0)
    compliance_status: Optional[str] = Field(None, description="Compliance status", max_length=50)
    is_monitored: bool = Field(True, description="Whether asset is monitored")


class AssetUpdate(BaseSchema):
    """Schema for updating assets."""
    
    name: Optional[str] = Field(None, description="Asset name", max_length=255)
    description: Optional[str] = Field(None, description="Asset description")
    asset_type: Optional[AssetType] = Field(None, description="Type of asset")
    status: Optional[AssetStatus] = Field(None, description="Asset status")
    hostname: Optional[str] = Field(None, description="Asset hostname", max_length=255)
    ip_address: Optional[str] = Field(None, description="IP address", max_length=45)
    mac_address: Optional[str] = Field(None, description="MAC address", max_length=17)
    operating_system: Optional[str] = Field(None, description="Operating system", max_length=255)
    os_version: Optional[str] = Field(None, description="OS version", max_length=100)
    business_service: Optional[str] = Field(None, description="Business service", max_length=255)
    owner: Optional[str] = Field(None, description="Asset owner", max_length=255)
    department: Optional[str] = Field(None, description="Department", max_length=255)
    location: Optional[str] = Field(None, description="Physical location", max_length=255)
    criticality: Optional[CriticalityLevel] = Field(None, description="Asset criticality")
    risk_score: Optional[float] = Field(None, description="Risk score", ge=0.0, le=10.0)
    compliance_status: Optional[str] = Field(None, description="Compliance status", max_length=50)
    configuration: Optional[Dict[str, Any]] = Field(None, description="Asset configuration")
    installed_software: Optional[List[Dict[str, Any]]] = Field(None, description="Installed software")
    network_interfaces: Optional[List[Dict[str, Any]]] = Field(None, description="Network interfaces")
    is_monitored: Optional[bool] = Field(None, description="Whether asset is monitored")


class Asset(BaseDBSchema):
    """Asset response schema."""
    
    name: str
    description: Optional[str]
    asset_type: AssetType
    status: AssetStatus
    cmdb_id: Optional[str]
    cmdb_class: Optional[str]
    cmdb_last_sync: Optional[datetime]
    hostname: Optional[str]
    ip_address: Optional[str]
    mac_address: Optional[str]
    operating_system: Optional[str]
    os_version: Optional[str]
    business_service: Optional[str]
    owner: Optional[str]
    department: Optional[str]
    location: Optional[str]
    criticality: CriticalityLevel
    risk_score: Optional[float]
    compliance_status: Optional[str]
    configuration: Optional[Dict[str, Any]]
    installed_software: Optional[List[Dict[str, Any]]]
    network_interfaces: Optional[List[Dict[str, Any]]]
    is_monitored: bool
    last_seen: Optional[datetime]
    
    model_config = ConfigDict(from_attributes=True)


class AssetDependencyBase(BaseSchema):
    """Base asset dependency schema."""
    
    target_asset_id: UUID = Field(..., description="Target asset ID")
    dependency_type: DependencyType = Field(..., description="Type of dependency")
    description: Optional[str] = Field(None, description="Dependency description")
    criticality: CriticalityLevel = Field(CriticalityLevel.MEDIUM, description="Dependency criticality")
    is_active: bool = Field(True, description="Whether dependency is active")


class AssetDependencyCreate(AssetDependencyBase):
    """Schema for creating asset dependencies."""
    
    source_asset_id: UUID = Field(..., description="Source asset ID")
    cmdb_relationship_id: Optional[str] = Field(None, description="CMDB relationship ID", max_length=255)


class AssetDependencyUpdate(BaseSchema):
    """Schema for updating asset dependencies."""
    
    dependency_type: Optional[DependencyType] = Field(None, description="Type of dependency")
    description: Optional[str] = Field(None, description="Dependency description")
    criticality: Optional[CriticalityLevel] = Field(None, description="Dependency criticality")
    is_active: Optional[bool] = Field(None, description="Whether dependency is active")
    cmdb_relationship_id: Optional[str] = Field(None, description="CMDB relationship ID", max_length=255)


class AssetDependency(BaseDBSchema, AssetDependencyBase):
    """Asset dependency response schema."""
    
    source_asset_id: UUID
    cmdb_relationship_id: Optional[str]


class AssetVulnerabilityBase(BaseSchema):
    """Base asset vulnerability schema."""
    
    vulnerability_id: UUID = Field(..., description="Vulnerability ID")
    cve_id: Optional[str] = Field(None, description="CVE ID", max_length=20)
    severity: str = Field(..., description="Vulnerability severity", max_length=20)
    cvss_score: Optional[float] = Field(None, description="CVSS score", ge=0.0, le=10.0)
    status: str = Field("open", description="Vulnerability status", max_length=50)
    scanner_name: Optional[str] = Field(None, description="Scanner name", max_length=100)
    scan_id: Optional[str] = Field(None, description="Scan ID", max_length=255)
    remediation_status: Optional[str] = Field(None, description="Remediation status", max_length=50)
    remediation_notes: Optional[str] = Field(None, description="Remediation notes")
    assigned_to: Optional[str] = Field(None, description="Assigned to", max_length=255)
    business_impact: Optional[str] = Field(None, description="Business impact", max_length=20)
    exploitability: Optional[str] = Field(None, description="Exploitability", max_length=20)


class AssetVulnerabilityCreate(AssetVulnerabilityBase):
    """Schema for creating asset vulnerabilities."""
    
    asset_id: UUID = Field(..., description="Asset ID")
    first_detected: datetime = Field(..., description="First detection time")
    last_detected: Optional[datetime] = Field(None, description="Last detection time")


class AssetVulnerabilityUpdate(BaseSchema):
    """Schema for updating asset vulnerabilities."""
    
    status: Optional[str] = Field(None, description="Vulnerability status", max_length=50)
    remediation_status: Optional[str] = Field(None, description="Remediation status", max_length=50)
    remediation_notes: Optional[str] = Field(None, description="Remediation notes")
    assigned_to: Optional[str] = Field(None, description="Assigned to", max_length=255)
    business_impact: Optional[str] = Field(None, description="Business impact", max_length=20)
    exploitability: Optional[str] = Field(None, description="Exploitability", max_length=20)
    resolved_at: Optional[datetime] = Field(None, description="Resolution time")


class AssetVulnerability(BaseDBSchema, AssetVulnerabilityBase):
    """Asset vulnerability response schema."""
    
    asset_id: UUID
    first_detected: datetime
    last_detected: Optional[datetime]
    resolved_at: Optional[datetime]


class ConfigurationItemBase(BaseSchema):
    """Base configuration item schema."""
    
    cmdb_id: str = Field(..., description="CMDB ID", max_length=255)
    cmdb_class: str = Field(..., description="CMDB class", max_length=100)
    cmdb_subclass: Optional[str] = Field(None, description="CMDB subclass", max_length=100)
    name: str = Field(..., description="CI name", max_length=255)
    display_name: Optional[str] = Field(None, description="Display name", max_length=255)
    description: Optional[str] = Field(None, description="CI description")
    operational_status: Optional[str] = Field(None, description="Operational status", max_length=50)
    lifecycle_status: Optional[str] = Field(None, description="Lifecycle status", max_length=50)
    attributes: Optional[Dict[str, Any]] = Field(None, description="CMDB attributes")


class ConfigurationItemCreate(ConfigurationItemBase):
    """Schema for creating configuration items."""
    
    mapped_asset_id: Optional[UUID] = Field(None, description="Mapped asset ID")
    sync_hash: Optional[str] = Field(None, description="Sync hash", max_length=64)


class ConfigurationItemUpdate(BaseSchema):
    """Schema for updating configuration items."""
    
    cmdb_class: Optional[str] = Field(None, description="CMDB class", max_length=100)
    cmdb_subclass: Optional[str] = Field(None, description="CMDB subclass", max_length=100)
    name: Optional[str] = Field(None, description="CI name", max_length=255)
    display_name: Optional[str] = Field(None, description="Display name", max_length=255)
    description: Optional[str] = Field(None, description="CI description")
    operational_status: Optional[str] = Field(None, description="Operational status", max_length=50)
    lifecycle_status: Optional[str] = Field(None, description="Lifecycle status", max_length=50)
    attributes: Optional[Dict[str, Any]] = Field(None, description="CMDB attributes")
    mapped_asset_id: Optional[UUID] = Field(None, description="Mapped asset ID")
    sync_hash: Optional[str] = Field(None, description="Sync hash", max_length=64)


class ConfigurationItem(BaseDBSchema, ConfigurationItemBase):
    """Configuration item response schema."""
    
    last_sync: Optional[datetime]
    sync_hash: Optional[str]
    mapped_asset_id: Optional[UUID]


# Asset discovery and import schemas
class AssetDiscoveryRequest(BaseSchema):
    """Schema for asset discovery requests."""
    
    discovery_type: str = Field(..., description="Discovery type (network, cmdb, cloud)")
    target_range: Optional[str] = Field(None, description="Target IP range or filter")
    discovery_options: Optional[Dict[str, Any]] = Field(None, description="Discovery options")
    auto_import: bool = Field(False, description="Automatically import discovered assets")


class AssetDiscoveryResult(BaseSchema):
    """Schema for asset discovery results."""
    
    success: bool = Field(..., description="Whether discovery was successful")
    assets_discovered: int = Field(0, description="Number of assets discovered")
    assets_imported: int = Field(0, description="Number of assets imported")
    discovery_time: float = Field(..., description="Discovery time in seconds")
    discovered_assets: List[Dict[str, Any]] = Field(..., description="Discovered asset details")
    errors: Optional[List[str]] = Field(None, description="Discovery errors")


# Asset analytics schemas
class AssetAnalytics(BaseSchema):
    """Schema for asset analytics."""
    
    total_assets: int = Field(..., description="Total number of assets")
    assets_by_type: Dict[str, int] = Field(..., description="Assets by type")
    assets_by_status: Dict[str, int] = Field(..., description="Assets by status")
    assets_by_criticality: Dict[str, int] = Field(..., description="Assets by criticality")
    vulnerability_summary: Dict[str, int] = Field(..., description="Vulnerability summary")
    compliance_summary: Dict[str, int] = Field(..., description="Compliance summary")
    risk_distribution: Dict[str, int] = Field(..., description="Risk score distribution")
    recent_changes: List[Dict[str, Any]] = Field(..., description="Recent asset changes")


# Business impact analysis schemas
class BusinessImpactAnalysisRequest(BaseSchema):
    """Schema for business impact analysis requests."""
    
    asset_id: UUID = Field(..., description="Asset ID to analyze")
    scenario: str = Field(..., description="Impact scenario (outage, compromise, etc.)")
    analysis_depth: int = Field(3, description="Dependency analysis depth", ge=1, le=10)


class BusinessImpactAnalysisResult(BaseSchema):
    """Schema for business impact analysis results."""
    
    asset_id: UUID = Field(..., description="Analyzed asset ID")
    impact_score: float = Field(..., description="Overall impact score", ge=0.0, le=10.0)
    affected_services: List[str] = Field(..., description="Affected business services")
    dependent_assets: List[UUID] = Field(..., description="Dependent asset IDs")
    impact_details: Dict[str, Any] = Field(..., description="Detailed impact analysis")
    recommendations: List[str] = Field(..., description="Mitigation recommendations")
