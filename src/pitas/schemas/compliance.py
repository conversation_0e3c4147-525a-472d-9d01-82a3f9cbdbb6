"""Compliance and audit trail schemas for Phase 8."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import Field, validator

from .base import BaseDBSchema, BaseSchema
from ..db.models.compliance import ComplianceFramework, AuditEventType, ControlStatus


class ComplianceMappingBase(BaseSchema):
    """Base schema for compliance mappings."""
    
    framework: ComplianceFramework = Field(..., description="Compliance framework")
    control_id: str = Field(..., max_length=100, description="Control identifier")
    control_name: str = Field(..., max_length=255, description="Control name")
    control_description: str = Field(..., description="Control description")
    implementation_status: ControlStatus = Field(
        default=ControlStatus.NOT_IMPLEMENTED,
        description="Implementation status"
    )
    implementation_notes: Optional[str] = Field(None, description="Implementation notes")
    control_owner: Optional[UUID] = Field(None, description="Control owner user ID")
    responsible_team: Optional[str] = Field(None, max_length=100, description="Responsible team")
    testing_frequency_days: int = Field(default=90, ge=1, le=365, description="Testing frequency in days")
    evidence_location: Optional[str] = Field(None, max_length=500, description="Evidence location")
    documentation_links: Optional[Dict[str, Any]] = Field(None, description="Documentation links")
    risk_rating: Optional[str] = Field(None, max_length=20, description="Risk rating")
    compensating_controls: Optional[Dict[str, Any]] = Field(None, description="Compensating controls")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Custom fields")


class ComplianceMappingCreate(ComplianceMappingBase):
    """Schema for creating compliance mappings."""
    pass


class ComplianceMappingUpdate(BaseSchema):
    """Schema for updating compliance mappings."""
    
    control_name: Optional[str] = Field(None, max_length=255)
    control_description: Optional[str] = None
    implementation_status: Optional[ControlStatus] = None
    implementation_notes: Optional[str] = None
    control_owner: Optional[UUID] = None
    responsible_team: Optional[str] = Field(None, max_length=100)
    testing_frequency_days: Optional[int] = Field(None, ge=1, le=365)
    evidence_location: Optional[str] = Field(None, max_length=500)
    documentation_links: Optional[Dict[str, Any]] = None
    risk_rating: Optional[str] = Field(None, max_length=20)
    compensating_controls: Optional[Dict[str, Any]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class ComplianceMappingResponse(ComplianceMappingBase, BaseDBSchema):
    """Schema for compliance mapping responses."""
    
    last_tested_at: Optional[datetime] = Field(None, description="Last testing timestamp")
    last_tested_by: Optional[UUID] = Field(None, description="Last tester user ID")
    next_test_due: Optional[datetime] = Field(None, description="Next test due date")
    
    class Config:
        from_attributes = True


class AuditTrailBase(BaseSchema):
    """Base schema for audit trail entries."""
    
    event_type: AuditEventType = Field(..., description="Event type")
    event_name: str = Field(..., max_length=255, description="Event name")
    event_description: str = Field(..., description="Event description")
    user_id: Optional[UUID] = Field(None, description="User ID")
    session_id: Optional[str] = Field(None, max_length=255, description="Session ID")
    ip_address: Optional[str] = Field(None, max_length=45, description="IP address")
    user_agent: Optional[str] = Field(None, max_length=500, description="User agent")
    resource_type: Optional[str] = Field(None, max_length=100, description="Resource type")
    resource_id: Optional[UUID] = Field(None, description="Resource ID")
    resource_name: Optional[str] = Field(None, max_length=255, description="Resource name")
    old_values: Optional[Dict[str, Any]] = Field(None, description="Old values")
    new_values: Optional[Dict[str, Any]] = Field(None, description="New values")
    project_id: Optional[UUID] = Field(None, description="Project ID")
    client_id: Optional[UUID] = Field(None, description="Client ID")
    compliance_mapping_id: Optional[UUID] = Field(None, description="Compliance mapping ID")
    event_metadata: Optional[Dict[str, Any]] = Field(None, description="Event metadata")
    tags: Optional[Dict[str, Any]] = Field(None, description="Event tags")


class AuditTrailCreate(AuditTrailBase):
    """Schema for creating audit trail entries."""
    pass


class AuditTrailResponse(AuditTrailBase, BaseDBSchema):
    """Schema for audit trail responses."""
    
    event_timestamp: datetime = Field(..., description="Event timestamp")
    checksum: Optional[str] = Field(None, description="Integrity checksum")
    signature: Optional[str] = Field(None, description="Digital signature")
    
    class Config:
        from_attributes = True


class ComplianceReportBase(BaseSchema):
    """Base schema for compliance reports."""
    
    title: str = Field(..., max_length=255, description="Report title")
    framework: ComplianceFramework = Field(..., description="Compliance framework")
    report_type: str = Field(..., max_length=100, description="Report type")
    project_id: Optional[UUID] = Field(None, description="Project ID")
    client_id: Optional[UUID] = Field(None, description="Client ID")
    scope_description: Optional[str] = Field(None, description="Scope description")
    period_start: datetime = Field(..., description="Period start date")
    period_end: datetime = Field(..., description="Period end date")
    executive_summary: Optional[str] = Field(None, description="Executive summary")
    findings: Optional[Dict[str, Any]] = Field(None, description="Findings")
    recommendations: Optional[Dict[str, Any]] = Field(None, description="Recommendations")
    control_results: Optional[Dict[str, Any]] = Field(None, description="Control results")
    file_format: str = Field(default="pdf", max_length=20, description="File format")


class ComplianceReportCreate(ComplianceReportBase):
    """Schema for creating compliance reports."""
    pass


class ComplianceReportResponse(ComplianceReportBase, BaseDBSchema):
    """Schema for compliance report responses."""
    
    generated_by: UUID = Field(..., description="Generator user ID")
    generated_at: datetime = Field(..., description="Generation timestamp")
    file_path: Optional[str] = Field(None, description="File path")
    is_final: bool = Field(default=False, description="Is finalized")
    approved_by: Optional[UUID] = Field(None, description="Approver user ID")
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    
    class Config:
        from_attributes = True


class ComplianceEvidenceBase(BaseSchema):
    """Base schema for compliance evidence."""
    
    compliance_mapping_id: UUID = Field(..., description="Compliance mapping ID")
    title: str = Field(..., max_length=255, description="Evidence title")
    description: str = Field(..., description="Evidence description")
    evidence_type: str = Field(..., max_length=100, description="Evidence type")
    filename: Optional[str] = Field(None, max_length=255, description="Filename")
    file_path: Optional[str] = Field(None, max_length=500, description="File path")
    file_size: Optional[int] = Field(None, ge=0, description="File size")
    content_type: Optional[str] = Field(None, max_length=100, description="Content type")


class ComplianceEvidenceCreate(ComplianceEvidenceBase):
    """Schema for creating compliance evidence."""
    pass


class ComplianceEvidenceResponse(ComplianceEvidenceBase, BaseDBSchema):
    """Schema for compliance evidence responses."""
    
    collected_by: UUID = Field(..., description="Collector user ID")
    collected_at: datetime = Field(..., description="Collection timestamp")
    validated_by: Optional[UUID] = Field(None, description="Validator user ID")
    validated_at: Optional[datetime] = Field(None, description="Validation timestamp")
    is_valid: bool = Field(default=True, description="Is valid")
    validation_notes: Optional[str] = Field(None, description="Validation notes")
    
    class Config:
        from_attributes = True


class ComplianceDashboard(BaseSchema):
    """Schema for compliance dashboard data."""
    
    total_controls: int = Field(..., description="Total number of controls")
    compliance_percentage: float = Field(..., description="Overall compliance percentage")
    status_breakdown: Dict[str, int] = Field(..., description="Status breakdown")
    overdue_tests: int = Field(..., description="Number of overdue tests")
    framework: str = Field(..., description="Framework filter")
    last_updated: str = Field(..., description="Last update timestamp")


class ControlTestRequest(BaseSchema):
    """Schema for control testing requests."""
    
    test_results: Dict[str, Any] = Field(..., description="Test results")
    next_test_days: Optional[int] = Field(None, ge=1, le=365, description="Days until next test")
    
    @validator('test_results')
    def validate_test_results(cls, v):
        if 'passed' not in v:
            raise ValueError('test_results must include "passed" boolean field')
        return v


class AuditTrailSearchFilters(BaseSchema):
    """Schema for audit trail search filters."""
    
    user_id: Optional[UUID] = None
    resource_type: Optional[str] = None
    resource_id: Optional[UUID] = None
    event_type: Optional[AuditEventType] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


class AuditTrailSearchResponse(BaseSchema):
    """Schema for audit trail search responses."""
    
    entries: List[AuditTrailResponse] = Field(..., description="Audit trail entries")
    total_count: int = Field(..., description="Total number of entries")
    filters_applied: AuditTrailSearchFilters = Field(..., description="Applied filters")
