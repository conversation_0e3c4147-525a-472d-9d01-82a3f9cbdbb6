"""Pydantic schemas for risk assessment and threat intelligence."""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict, validator

from pitas.db.models.risk_assessment import (
    RiskLevel,
    ThreatActorType,
)


class RiskAssessmentBase(BaseModel):
    """Base risk assessment schema."""
    
    vulnerability_id: UUID
    asset_id: UUID
    risk_score: Decimal = Field(..., ge=0.0, le=10.0, description="Overall risk score")
    risk_level: RiskLevel
    true_risk_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    business_impact: Decimal = Field(..., ge=0.0, le=10.0)
    financial_impact: Optional[Decimal] = Field(None, description="Financial impact in USD")
    regulatory_impact: Optional[str] = Field(None, max_length=100)
    reputation_impact: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    threat_likelihood: Decimal = Field(..., ge=0.0, le=10.0)
    exploit_availability: bool = Field(default=False)
    active_exploitation: bool = Field(default=False)
    threat_actors: Optional[List[str]] = Field(default_factory=list)
    asset_exposure: Optional[str] = Field(None, max_length=50)
    network_segmentation: Optional[str] = Field(None, max_length=50)
    compensating_controls: Optional[List[str]] = Field(default_factory=list)
    methodology: str = Field(default="RBVM", max_length=50)
    confidence_level: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    notes: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class RiskAssessmentCreate(RiskAssessmentBase):
    """Schema for creating risk assessments."""
    
    assessment_date: datetime = Field(default_factory=datetime.utcnow)
    assessor_id: Optional[UUID] = None


class RiskAssessmentUpdate(BaseModel):
    """Schema for updating risk assessments."""
    
    risk_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    risk_level: Optional[RiskLevel] = None
    true_risk_score: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    business_impact: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    financial_impact: Optional[Decimal] = None
    regulatory_impact: Optional[str] = Field(None, max_length=100)
    reputation_impact: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    threat_likelihood: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    exploit_availability: Optional[bool] = None
    active_exploitation: Optional[bool] = None
    threat_actors: Optional[List[str]] = None
    asset_exposure: Optional[str] = Field(None, max_length=50)
    network_segmentation: Optional[str] = Field(None, max_length=50)
    compensating_controls: Optional[List[str]] = None
    confidence_level: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    notes: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None


class RiskAssessmentResponse(RiskAssessmentBase):
    """Schema for risk assessment responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    assessment_date: datetime
    assessor_id: Optional[UUID]


class ThreatIntelligenceBase(BaseModel):
    """Base threat intelligence schema."""
    
    vulnerability_id: UUID
    source: str = Field(..., max_length=100)
    source_confidence: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    actor_type: Optional[ThreatActorType] = None
    actor_name: Optional[str] = Field(None, max_length=100)
    actor_motivation: Optional[str] = Field(None, max_length=100)
    exploitation_method: Optional[str] = None
    exploit_complexity: Optional[str] = Field(None, max_length=20)
    target_sectors: Optional[List[str]] = Field(default_factory=list)
    target_regions: Optional[List[str]] = Field(default_factory=list)
    iocs: Optional[List[str]] = Field(default_factory=list, description="Indicators of Compromise")
    ttps: Optional[List[str]] = Field(default_factory=list, description="Tactics, Techniques, Procedures")
    mitre_techniques: Optional[List[str]] = Field(default_factory=list)
    raw_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ThreatIntelligenceCreate(ThreatIntelligenceBase):
    """Schema for creating threat intelligence."""
    pass


class ThreatIntelligenceUpdate(BaseModel):
    """Schema for updating threat intelligence."""
    
    source_confidence: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    actor_type: Optional[ThreatActorType] = None
    actor_name: Optional[str] = Field(None, max_length=100)
    actor_motivation: Optional[str] = Field(None, max_length=100)
    exploitation_method: Optional[str] = None
    exploit_complexity: Optional[str] = Field(None, max_length=20)
    target_sectors: Optional[List[str]] = None
    target_regions: Optional[List[str]] = None
    iocs: Optional[List[str]] = None
    ttps: Optional[List[str]] = None
    mitre_techniques: Optional[List[str]] = None
    raw_data: Optional[Dict[str, Any]] = None


class ThreatIntelligenceResponse(ThreatIntelligenceBase):
    """Schema for threat intelligence responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]


class RemediationPlanBase(BaseModel):
    """Base remediation plan schema."""
    
    vulnerability_id: UUID
    priority: int = Field(..., ge=1, description="Priority ranking (1 = highest)")
    status: str = Field(default="planned", max_length=20)
    remediation_type: str = Field(..., max_length=50)
    description: str = Field(..., description="Remediation steps")
    planned_start_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    assigned_to: Optional[UUID] = None
    estimated_effort_hours: Optional[Decimal] = Field(None, decimal_places=2)
    progress_percentage: int = Field(default=0, ge=0, le=100)
    notes: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict)


class RemediationPlanCreate(RemediationPlanBase):
    """Schema for creating remediation plans."""
    pass


class RemediationPlanUpdate(BaseModel):
    """Schema for updating remediation plans."""
    
    priority: Optional[int] = Field(None, ge=1)
    status: Optional[str] = Field(None, max_length=20)
    remediation_type: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    planned_start_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    actual_start_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    assigned_to: Optional[UUID] = None
    estimated_effort_hours: Optional[Decimal] = Field(None, decimal_places=2)
    actual_effort_hours: Optional[Decimal] = Field(None, decimal_places=2)
    progress_percentage: Optional[int] = Field(None, ge=0, le=100)
    notes: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None


class RemediationPlanResponse(RemediationPlanBase):
    """Schema for remediation plan responses."""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime]
    actual_start_date: Optional[datetime]
    completion_date: Optional[datetime]
    actual_effort_hours: Optional[Decimal]


# Aggregated schemas
class VulnerabilityRiskProfile(BaseModel):
    """Complete risk profile for a vulnerability."""
    
    vulnerability_id: UUID
    vulnerability_title: str
    cvss_score: Optional[Decimal]
    severity: str
    risk_assessments: List[RiskAssessmentResponse]
    threat_intelligence: List[ThreatIntelligenceResponse]
    remediation_plans: List[RemediationPlanResponse]
    overall_risk_score: Decimal
    highest_risk_level: RiskLevel
    affected_critical_assets: int


class RiskDashboard(BaseModel):
    """Risk assessment dashboard summary."""
    
    total_assessments: int
    by_risk_level: Dict[str, int]
    average_risk_score: Decimal
    critical_vulnerabilities: int
    high_risk_assets: int
    overdue_remediations: int
    threat_intelligence_alerts: int
    trend_data: List[Dict[str, Any]] = Field(default_factory=list)


class RiskSearchFilters(BaseModel):
    """Search filters for risk assessments."""
    
    risk_levels: Optional[List[RiskLevel]] = None
    risk_score_min: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    risk_score_max: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    business_impact_min: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    threat_likelihood_min: Optional[Decimal] = Field(None, ge=0.0, le=10.0)
    has_exploit: Optional[bool] = None
    active_exploitation: Optional[bool] = None
    assessment_date_from: Optional[datetime] = None
    assessment_date_to: Optional[datetime] = None
    asset_ids: Optional[List[UUID]] = None
    vulnerability_ids: Optional[List[UUID]] = None


class RiskSearchResponse(BaseModel):
    """Response for risk assessment search."""
    
    risk_assessments: List[RiskAssessmentResponse]
    total_count: int
    page: int
    page_size: int
    filters_applied: RiskSearchFilters
