"""Work-life balance and wellness monitoring schemas."""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.schemas.base import BaseDBSchema, BaseSchema
from pitas.db.models.wellness import (
    WellnessMetricType, AlertSeverity, AlertStatus, WorkScheduleType
)


# Wellness Check Schemas
class WellnessCheckBase(BaseSchema):
    """Base schema for Wellness Checks."""
    check_date: date = Field(..., description="Check date")
    overall_wellness_score: int = Field(..., ge=1, le=10, description="Overall wellness score (1-10)")
    stress_level: int = Field(..., ge=1, le=10, description="Stress level (1=low, 10=high)")
    energy_level: int = Field(..., ge=1, le=10, description="Energy level (1=low, 10=high)")
    job_satisfaction: int = Field(..., ge=1, le=10, description="Job satisfaction (1-10)")
    work_life_balance: int = Field(..., ge=1, le=10, description="Work-life balance (1-10)")
    workload_rating: int = Field(..., ge=1, le=10, description="Workload rating (1=light, 10=overwhelming)")
    hours_worked_last_week: Optional[float] = Field(None, ge=0, description="Hours worked last week")
    overtime_hours: Optional[float] = Field(None, ge=0, description="Overtime hours")
    weekend_work_hours: Optional[float] = Field(None, ge=0, description="Weekend work hours")
    engagement_level: int = Field(..., ge=1, le=10, description="Engagement level (1-10)")
    motivation_level: int = Field(..., ge=1, le=10, description="Motivation level (1-10)")
    career_satisfaction: int = Field(..., ge=1, le=10, description="Career satisfaction (1-10)")
    learning_opportunities: int = Field(..., ge=1, le=10, description="Learning opportunities (1-10)")
    team_satisfaction: int = Field(..., ge=1, le=10, description="Team satisfaction (1-10)")
    manager_support: int = Field(..., ge=1, le=10, description="Manager support (1-10)")
    communication_effectiveness: int = Field(..., ge=1, le=10, description="Communication effectiveness (1-10)")
    recognition_satisfaction: int = Field(..., ge=1, le=10, description="Recognition satisfaction (1-10)")
    burnout_risk_score: float = Field(..., ge=0.0, le=1.0, description="Burnout risk score (0.0-1.0)")
    emotional_exhaustion: int = Field(..., ge=1, le=10, description="Emotional exhaustion (1-10)")
    cynicism_level: int = Field(..., ge=1, le=10, description="Cynicism level (1-10)")
    personal_accomplishment: int = Field(..., ge=1, le=10, description="Personal accomplishment (1-10)")
    positive_highlights: Optional[str] = Field(None, description="Positive highlights")
    concerns_challenges: Optional[str] = Field(None, description="Concerns and challenges")
    improvement_suggestions: Optional[str] = Field(None, description="Improvement suggestions")
    support_needed: Optional[str] = Field(None, description="Support needed")
    preferred_work_schedule: Optional[Dict[str, Any]] = Field(None, description="Preferred work schedule")
    workspace_preferences: Optional[Dict[str, Any]] = Field(None, description="Workspace preferences")
    development_interests: Optional[Dict[str, Any]] = Field(None, description="Development interests")
    check_type: str = Field(default="regular", description="Check type")
    completion_time_minutes: Optional[int] = Field(None, ge=0, description="Completion time in minutes")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class WellnessCheckCreate(WellnessCheckBase):
    """Schema for creating a Wellness Check."""
    user_id: UUID = Field(..., description="User ID")


class WellnessCheckUpdate(BaseSchema):
    """Schema for updating a Wellness Check."""
    overall_wellness_score: Optional[int] = Field(None, ge=1, le=10, description="Overall wellness score")
    stress_level: Optional[int] = Field(None, ge=1, le=10, description="Stress level")
    energy_level: Optional[int] = Field(None, ge=1, le=10, description="Energy level")
    job_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Job satisfaction")
    work_life_balance: Optional[int] = Field(None, ge=1, le=10, description="Work-life balance")
    workload_rating: Optional[int] = Field(None, ge=1, le=10, description="Workload rating")
    hours_worked_last_week: Optional[float] = Field(None, ge=0, description="Hours worked")
    overtime_hours: Optional[float] = Field(None, ge=0, description="Overtime hours")
    weekend_work_hours: Optional[float] = Field(None, ge=0, description="Weekend hours")
    engagement_level: Optional[int] = Field(None, ge=1, le=10, description="Engagement level")
    motivation_level: Optional[int] = Field(None, ge=1, le=10, description="Motivation level")
    career_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Career satisfaction")
    learning_opportunities: Optional[int] = Field(None, ge=1, le=10, description="Learning opportunities")
    team_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Team satisfaction")
    manager_support: Optional[int] = Field(None, ge=1, le=10, description="Manager support")
    communication_effectiveness: Optional[int] = Field(None, ge=1, le=10, description="Communication")
    recognition_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Recognition")
    burnout_risk_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Burnout risk")
    emotional_exhaustion: Optional[int] = Field(None, ge=1, le=10, description="Emotional exhaustion")
    cynicism_level: Optional[int] = Field(None, ge=1, le=10, description="Cynicism level")
    personal_accomplishment: Optional[int] = Field(None, ge=1, le=10, description="Personal accomplishment")
    positive_highlights: Optional[str] = Field(None, description="Positive highlights")
    concerns_challenges: Optional[str] = Field(None, description="Concerns and challenges")
    improvement_suggestions: Optional[str] = Field(None, description="Improvement suggestions")
    support_needed: Optional[str] = Field(None, description="Support needed")
    preferred_work_schedule: Optional[Dict[str, Any]] = Field(None, description="Preferred schedule")
    workspace_preferences: Optional[Dict[str, Any]] = Field(None, description="Workspace preferences")
    development_interests: Optional[Dict[str, Any]] = Field(None, description="Development interests")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class WellnessCheck(WellnessCheckBase, BaseDBSchema):
    """Schema for Wellness Check response."""
    user_id: UUID = Field(..., description="User ID")


class WellnessCheckWithAlerts(WellnessCheck):
    """Schema for Wellness Check with associated alerts."""
    alerts: List["WellnessAlert"] = Field(default_factory=list, description="Wellness alerts")


# Wellness Alert Schemas
class WellnessAlertBase(BaseSchema):
    """Base schema for Wellness Alerts."""
    alert_type: str = Field(..., description="Alert type")
    severity: AlertSeverity = Field(..., description="Alert severity")
    status: AlertStatus = Field(default=AlertStatus.ACTIVE, description="Alert status")
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    recommended_actions: Optional[Dict[str, Any]] = Field(None, description="Recommended actions")
    manager_notified: bool = Field(default=False, description="Manager notified")
    hr_notified: bool = Field(default=False, description="HR notified")
    follow_up_required: bool = Field(default=True, description="Follow-up required")
    follow_up_date: Optional[datetime] = Field(None, description="Follow-up date")
    trigger_metrics: Optional[Dict[str, Any]] = Field(None, description="Trigger metrics")
    threshold_values: Optional[Dict[str, Any]] = Field(None, description="Threshold values")
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")
    actions_taken: Optional[Dict[str, Any]] = Field(None, description="Actions taken")


class WellnessAlertCreate(WellnessAlertBase):
    """Schema for creating a Wellness Alert."""
    user_id: UUID = Field(..., description="User ID")
    wellness_check_id: Optional[UUID] = Field(None, description="Wellness check ID")


class WellnessAlertUpdate(BaseSchema):
    """Schema for updating a Wellness Alert."""
    status: Optional[AlertStatus] = Field(None, description="Alert status")
    acknowledged_by_id: Optional[UUID] = Field(None, description="Acknowledged by user ID")
    acknowledged_date: Optional[datetime] = Field(None, description="Acknowledged date")
    resolved_date: Optional[datetime] = Field(None, description="Resolved date")
    manager_notified: Optional[bool] = Field(None, description="Manager notified")
    hr_notified: Optional[bool] = Field(None, description="HR notified")
    follow_up_date: Optional[datetime] = Field(None, description="Follow-up date")
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")
    actions_taken: Optional[Dict[str, Any]] = Field(None, description="Actions taken")


class WellnessAlert(WellnessAlertBase, BaseDBSchema):
    """Schema for Wellness Alert response."""
    user_id: UUID = Field(..., description="User ID")
    wellness_check_id: Optional[UUID] = Field(None, description="Wellness check ID")
    acknowledged_by_id: Optional[UUID] = Field(None, description="Acknowledged by user ID")
    triggered_date: datetime = Field(..., description="Triggered date")
    acknowledged_date: Optional[datetime] = Field(None, description="Acknowledged date")
    resolved_date: Optional[datetime] = Field(None, description="Resolved date")


# Work Schedule Schemas
class WorkScheduleBase(BaseSchema):
    """Base schema for Work Schedules."""
    schedule_name: str = Field(..., description="Schedule name")
    schedule_type: WorkScheduleType = Field(..., description="Schedule type")
    is_active: bool = Field(default=True, description="Is active")
    effective_start_date: date = Field(..., description="Effective start date")
    effective_end_date: Optional[date] = Field(None, description="Effective end date")
    standard_hours_per_week: float = Field(default=40.0, ge=0, description="Standard hours per week")
    core_hours_start: Optional[str] = Field(None, description="Core hours start (HH:MM)")
    core_hours_end: Optional[str] = Field(None, description="Core hours end (HH:MM)")
    timezone: str = Field(..., description="Timezone")
    weekly_schedule: Dict[str, Any] = Field(..., description="Weekly schedule configuration")
    allows_flexible_start: bool = Field(default=False, description="Allows flexible start")
    allows_flexible_end: bool = Field(default=False, description="Allows flexible end")
    allows_remote_work: bool = Field(default=False, description="Allows remote work")
    remote_work_days: Optional[Dict[str, Any]] = Field(None, description="Remote work days")
    lunch_break_duration: int = Field(default=60, ge=0, description="Lunch break duration (minutes)")
    short_break_frequency: Optional[int] = Field(None, ge=0, description="Short break frequency")
    notes: Optional[str] = Field(None, description="Notes")
    employee_comments: Optional[str] = Field(None, description="Employee comments")
    manager_comments: Optional[str] = Field(None, description="Manager comments")


class WorkScheduleCreate(WorkScheduleBase):
    """Schema for creating a Work Schedule."""
    user_id: UUID = Field(..., description="User ID")
    approved_by_id: Optional[UUID] = Field(None, description="Approved by user ID")


class WorkScheduleUpdate(BaseSchema):
    """Schema for updating a Work Schedule."""
    schedule_name: Optional[str] = Field(None, description="Schedule name")
    schedule_type: Optional[WorkScheduleType] = Field(None, description="Schedule type")
    is_active: Optional[bool] = Field(None, description="Is active")
    effective_end_date: Optional[date] = Field(None, description="Effective end date")
    standard_hours_per_week: Optional[float] = Field(None, ge=0, description="Standard hours")
    core_hours_start: Optional[str] = Field(None, description="Core hours start")
    core_hours_end: Optional[str] = Field(None, description="Core hours end")
    timezone: Optional[str] = Field(None, description="Timezone")
    weekly_schedule: Optional[Dict[str, Any]] = Field(None, description="Weekly schedule")
    allows_flexible_start: Optional[bool] = Field(None, description="Flexible start")
    allows_flexible_end: Optional[bool] = Field(None, description="Flexible end")
    allows_remote_work: Optional[bool] = Field(None, description="Remote work")
    remote_work_days: Optional[Dict[str, Any]] = Field(None, description="Remote work days")
    lunch_break_duration: Optional[int] = Field(None, ge=0, description="Lunch break duration")
    short_break_frequency: Optional[int] = Field(None, ge=0, description="Break frequency")
    notes: Optional[str] = Field(None, description="Notes")
    employee_comments: Optional[str] = Field(None, description="Employee comments")
    manager_comments: Optional[str] = Field(None, description="Manager comments")
    approved_by_id: Optional[UUID] = Field(None, description="Approved by user ID")
    approval_date: Optional[datetime] = Field(None, description="Approval date")


class WorkSchedule(WorkScheduleBase, BaseDBSchema):
    """Schema for Work Schedule response."""
    user_id: UUID = Field(..., description="User ID")
    approved_by_id: Optional[UUID] = Field(None, description="Approved by user ID")
    approval_date: Optional[datetime] = Field(None, description="Approval date")


# Wellness Resource Schemas
class WellnessResourceBase(BaseSchema):
    """Base schema for Wellness Resources."""
    title: str = Field(..., description="Resource title")
    description: str = Field(..., description="Resource description")
    resource_type: str = Field(..., description="Resource type")
    category: str = Field(..., description="Resource category")
    content_url: Optional[str] = Field(None, description="Content URL")
    content_text: Optional[str] = Field(None, description="Content text")
    is_active: bool = Field(default=True, description="Is active")
    is_confidential: bool = Field(default=False, description="Is confidential")
    requires_approval: bool = Field(default=False, description="Requires approval")
    target_roles: Optional[Dict[str, Any]] = Field(None, description="Target roles")
    target_conditions: Optional[Dict[str, Any]] = Field(None, description="Target conditions")
    tags: Optional[Dict[str, Any]] = Field(None, description="Tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class WellnessResourceCreate(WellnessResourceBase):
    """Schema for creating a Wellness Resource."""
    pass


class WellnessResourceUpdate(BaseSchema):
    """Schema for updating a Wellness Resource."""
    title: Optional[str] = Field(None, description="Resource title")
    description: Optional[str] = Field(None, description="Resource description")
    resource_type: Optional[str] = Field(None, description="Resource type")
    category: Optional[str] = Field(None, description="Resource category")
    content_url: Optional[str] = Field(None, description="Content URL")
    content_text: Optional[str] = Field(None, description="Content text")
    is_active: Optional[bool] = Field(None, description="Is active")
    is_confidential: Optional[bool] = Field(None, description="Is confidential")
    requires_approval: Optional[bool] = Field(None, description="Requires approval")
    target_roles: Optional[Dict[str, Any]] = Field(None, description="Target roles")
    target_conditions: Optional[Dict[str, Any]] = Field(None, description="Target conditions")
    tags: Optional[Dict[str, Any]] = Field(None, description="Tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class WellnessResource(WellnessResourceBase, BaseDBSchema):
    """Schema for Wellness Resource response."""
    view_count: int = Field(..., description="View count")
    last_accessed: Optional[datetime] = Field(None, description="Last accessed")


# Wellness Analytics Schemas
class WellnessMetrics(BaseSchema):
    """Wellness metrics summary."""
    user_id: Optional[UUID] = Field(None, description="User ID (if user-specific)")
    period_start: date = Field(..., description="Period start date")
    period_end: date = Field(..., description="Period end date")
    average_wellness_score: float = Field(..., description="Average wellness score")
    average_stress_level: float = Field(..., description="Average stress level")
    average_energy_level: float = Field(..., description="Average energy level")
    average_job_satisfaction: float = Field(..., description="Average job satisfaction")
    average_work_life_balance: float = Field(..., description="Average work-life balance")
    average_burnout_risk: float = Field(..., description="Average burnout risk")
    total_checks: int = Field(..., description="Total wellness checks")
    active_alerts: int = Field(..., description="Active alerts")
    resolved_alerts: int = Field(..., description="Resolved alerts")
    wellness_trend: Dict[str, float] = Field(..., description="Wellness trend over time")
    risk_factors: List[str] = Field(..., description="Identified risk factors")
    improvement_areas: List[str] = Field(..., description="Areas for improvement")


# Update forward references
WellnessCheckWithAlerts.model_rebuild()
