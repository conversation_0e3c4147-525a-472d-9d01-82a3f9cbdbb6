"""Mentorship and peer support schemas."""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.schemas.base import BaseDBSchema, BaseSchema
from pitas.db.models.mentorship import (
    MentorshipType, MentorshipStatus, SessionType, SessionStatus
)


# Mentorship Schemas
class MentorshipBase(BaseSchema):
    """Base schema for Mentorships."""
    mentorship_type: MentorshipType = Field(..., description="Mentorship type")
    status: MentorshipStatus = Field(default=MentorshipStatus.REQUESTED, description="Mentorship status")
    start_date: date = Field(..., description="Start date")
    planned_end_date: Optional[date] = Field(None, description="Planned end date")
    primary_goals: Dict[str, Any] = Field(..., description="Primary mentorship goals")
    focus_areas: Dict[str, Any] = Field(..., description="Focus areas and skills")
    success_criteria: Optional[Dict[str, Any]] = Field(None, description="Success criteria")
    meeting_frequency: str = Field(..., description="Meeting frequency")
    preferred_meeting_duration: int = Field(default=60, ge=15, description="Preferred meeting duration (minutes)")
    preferred_meeting_format: str = Field(default="video_call", description="Preferred meeting format")
    overall_progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall progress (0.0-1.0)")
    mentor_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Mentor satisfaction (1-10)")
    mentee_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Mentee satisfaction (1-10)")
    program_id: Optional[str] = Field(None, description="Mentorship program ID")
    initial_notes: Optional[str] = Field(None, description="Initial notes")
    mentor_notes: Optional[str] = Field(None, description="Mentor notes")
    mentee_notes: Optional[str] = Field(None, description="Mentee notes")
    completion_notes: Optional[str] = Field(None, description="Completion notes")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class MentorshipCreate(MentorshipBase):
    """Schema for creating a Mentorship."""
    mentor_id: UUID = Field(..., description="Mentor user ID")
    mentee_id: UUID = Field(..., description="Mentee user ID")
    assigned_by_id: Optional[UUID] = Field(None, description="Assigned by user ID")


class MentorshipUpdate(BaseSchema):
    """Schema for updating a Mentorship."""
    status: Optional[MentorshipStatus] = Field(None, description="Mentorship status")
    planned_end_date: Optional[date] = Field(None, description="Planned end date")
    actual_end_date: Optional[date] = Field(None, description="Actual end date")
    primary_goals: Optional[Dict[str, Any]] = Field(None, description="Primary goals")
    focus_areas: Optional[Dict[str, Any]] = Field(None, description="Focus areas")
    success_criteria: Optional[Dict[str, Any]] = Field(None, description="Success criteria")
    meeting_frequency: Optional[str] = Field(None, description="Meeting frequency")
    preferred_meeting_duration: Optional[int] = Field(None, ge=15, description="Meeting duration")
    preferred_meeting_format: Optional[str] = Field(None, description="Meeting format")
    overall_progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="Overall progress")
    mentor_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Mentor satisfaction")
    mentee_satisfaction: Optional[int] = Field(None, ge=1, le=10, description="Mentee satisfaction")
    mentor_notes: Optional[str] = Field(None, description="Mentor notes")
    mentee_notes: Optional[str] = Field(None, description="Mentee notes")
    completion_notes: Optional[str] = Field(None, description="Completion notes")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class Mentorship(MentorshipBase, BaseDBSchema):
    """Schema for Mentorship response."""
    mentor_id: UUID = Field(..., description="Mentor user ID")
    mentee_id: UUID = Field(..., description="Mentee user ID")
    assigned_by_id: Optional[UUID] = Field(None, description="Assigned by user ID")
    actual_end_date: Optional[date] = Field(None, description="Actual end date")


class MentorshipWithSessions(Mentorship):
    """Schema for Mentorship with associated sessions."""
    sessions: List["MentorshipSession"] = Field(default_factory=list, description="Mentorship sessions")


# Mentorship Session Schemas
class MentorshipSessionBase(BaseSchema):
    """Base schema for Mentorship Sessions."""
    session_type: SessionType = Field(..., description="Session type")
    status: SessionStatus = Field(default=SessionStatus.SCHEDULED, description="Session status")
    scheduled_date: datetime = Field(..., description="Scheduled date and time")
    scheduled_duration: int = Field(..., ge=15, description="Scheduled duration (minutes)")
    agenda: Optional[str] = Field(None, description="Session agenda")
    topics_covered: Optional[Dict[str, Any]] = Field(None, description="Topics covered")
    goals_for_session: Optional[Dict[str, Any]] = Field(None, description="Goals for session")
    meeting_format: str = Field(..., description="Meeting format")
    location: Optional[str] = Field(None, description="Meeting location")
    meeting_link: Optional[str] = Field(None, description="Meeting link")
    session_notes: Optional[str] = Field(None, description="Session notes")
    mentor_feedback: Optional[str] = Field(None, description="Mentor feedback")
    mentee_feedback: Optional[str] = Field(None, description="Mentee feedback")
    action_items: Optional[Dict[str, Any]] = Field(None, description="Action items")
    next_session_goals: Optional[Dict[str, Any]] = Field(None, description="Next session goals")
    mentor_rating: Optional[int] = Field(None, ge=1, le=10, description="Mentor rating (1-10)")
    mentee_rating: Optional[int] = Field(None, ge=1, le=10, description="Mentee rating (1-10)")
    session_effectiveness: Optional[int] = Field(None, ge=1, le=10, description="Session effectiveness (1-10)")
    resources_shared: Optional[Dict[str, Any]] = Field(None, description="Resources shared")
    homework_assigned: Optional[Dict[str, Any]] = Field(None, description="Homework assigned")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class MentorshipSessionCreate(MentorshipSessionBase):
    """Schema for creating a Mentorship Session."""
    mentorship_id: UUID = Field(..., description="Mentorship ID")


class MentorshipSessionUpdate(BaseSchema):
    """Schema for updating a Mentorship Session."""
    status: Optional[SessionStatus] = Field(None, description="Session status")
    scheduled_date: Optional[datetime] = Field(None, description="Scheduled date")
    scheduled_duration: Optional[int] = Field(None, ge=15, description="Scheduled duration")
    actual_start_time: Optional[datetime] = Field(None, description="Actual start time")
    actual_end_time: Optional[datetime] = Field(None, description="Actual end time")
    agenda: Optional[str] = Field(None, description="Session agenda")
    topics_covered: Optional[Dict[str, Any]] = Field(None, description="Topics covered")
    goals_for_session: Optional[Dict[str, Any]] = Field(None, description="Session goals")
    meeting_format: Optional[str] = Field(None, description="Meeting format")
    location: Optional[str] = Field(None, description="Location")
    meeting_link: Optional[str] = Field(None, description="Meeting link")
    session_notes: Optional[str] = Field(None, description="Session notes")
    mentor_feedback: Optional[str] = Field(None, description="Mentor feedback")
    mentee_feedback: Optional[str] = Field(None, description="Mentee feedback")
    action_items: Optional[Dict[str, Any]] = Field(None, description="Action items")
    next_session_goals: Optional[Dict[str, Any]] = Field(None, description="Next session goals")
    mentor_rating: Optional[int] = Field(None, ge=1, le=10, description="Mentor rating")
    mentee_rating: Optional[int] = Field(None, ge=1, le=10, description="Mentee rating")
    session_effectiveness: Optional[int] = Field(None, ge=1, le=10, description="Session effectiveness")
    resources_shared: Optional[Dict[str, Any]] = Field(None, description="Resources shared")
    homework_assigned: Optional[Dict[str, Any]] = Field(None, description="Homework assigned")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class MentorshipSession(MentorshipSessionBase, BaseDBSchema):
    """Schema for Mentorship Session response."""
    mentorship_id: UUID = Field(..., description="Mentorship ID")
    actual_start_time: Optional[datetime] = Field(None, description="Actual start time")
    actual_end_time: Optional[datetime] = Field(None, description="Actual end time")


# Mentor Profile Schemas
class MentorProfileBase(BaseSchema):
    """Base schema for Mentor Profiles."""
    is_active: bool = Field(default=True, description="Is active mentor")
    is_available: bool = Field(default=True, description="Is available for new mentees")
    expertise_areas: Dict[str, Any] = Field(..., description="Areas of expertise")
    mentoring_skills: Dict[str, Any] = Field(..., description="Mentoring-specific skills")
    industry_experience: Optional[Dict[str, Any]] = Field(None, description="Industry experience")
    max_mentees: int = Field(default=3, ge=1, description="Maximum number of mentees")
    preferred_mentee_level: Optional[Dict[str, Any]] = Field(None, description="Preferred mentee level")
    preferred_mentorship_type: Optional[Dict[str, Any]] = Field(None, description="Preferred mentorship type")
    hours_per_week: float = Field(default=2.0, ge=0.5, description="Hours per week available")
    available_time_slots: Optional[Dict[str, Any]] = Field(None, description="Available time slots")
    timezone_preferences: Optional[Dict[str, Any]] = Field(None, description="Timezone preferences")
    mentoring_philosophy: Optional[str] = Field(None, description="Mentoring philosophy")
    success_stories: Optional[str] = Field(None, description="Success stories")
    approach_description: Optional[str] = Field(None, description="Approach description")
    mentoring_certifications: Optional[Dict[str, Any]] = Field(None, description="Mentoring certifications")
    training_completed: Optional[Dict[str, Any]] = Field(None, description="Training completed")
    last_training_date: Optional[date] = Field(None, description="Last training date")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class MentorProfileCreate(MentorProfileBase):
    """Schema for creating a Mentor Profile."""
    user_id: UUID = Field(..., description="User ID")


class MentorProfileUpdate(BaseSchema):
    """Schema for updating a Mentor Profile."""
    is_active: Optional[bool] = Field(None, description="Is active")
    is_available: Optional[bool] = Field(None, description="Is available")
    expertise_areas: Optional[Dict[str, Any]] = Field(None, description="Expertise areas")
    mentoring_skills: Optional[Dict[str, Any]] = Field(None, description="Mentoring skills")
    industry_experience: Optional[Dict[str, Any]] = Field(None, description="Industry experience")
    max_mentees: Optional[int] = Field(None, ge=1, description="Max mentees")
    preferred_mentee_level: Optional[Dict[str, Any]] = Field(None, description="Preferred mentee level")
    preferred_mentorship_type: Optional[Dict[str, Any]] = Field(None, description="Preferred type")
    hours_per_week: Optional[float] = Field(None, ge=0.5, description="Hours per week")
    available_time_slots: Optional[Dict[str, Any]] = Field(None, description="Available time slots")
    timezone_preferences: Optional[Dict[str, Any]] = Field(None, description="Timezone preferences")
    mentoring_philosophy: Optional[str] = Field(None, description="Mentoring philosophy")
    success_stories: Optional[str] = Field(None, description="Success stories")
    approach_description: Optional[str] = Field(None, description="Approach description")
    mentoring_certifications: Optional[Dict[str, Any]] = Field(None, description="Certifications")
    training_completed: Optional[Dict[str, Any]] = Field(None, description="Training completed")
    last_training_date: Optional[date] = Field(None, description="Last training date")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata")


class MentorProfile(MentorProfileBase, BaseDBSchema):
    """Schema for Mentor Profile response."""
    user_id: UUID = Field(..., description="User ID")
    current_mentees: int = Field(..., description="Current number of mentees")
    total_mentorships: int = Field(..., description="Total mentorships")
    completed_mentorships: int = Field(..., description="Completed mentorships")
    average_rating: Optional[float] = Field(None, description="Average rating")
    total_ratings: int = Field(..., description="Total ratings")


# Mentorship Request Schemas
class MentorshipRequestBase(BaseSchema):
    """Base schema for Mentorship Requests."""
    request_type: str = Field(..., description="Request type (mentor_request/mentee_request)")
    status: str = Field(default="pending", description="Request status")
    priority: str = Field(default="medium", description="Request priority")
    desired_expertise: Dict[str, Any] = Field(..., description="Desired expertise areas")
    goals_and_objectives: str = Field(..., description="Goals and objectives")
    preferred_mentor_level: Optional[str] = Field(None, description="Preferred mentor level")
    preferred_meeting_frequency: Optional[str] = Field(None, description="Preferred meeting frequency")
    requested_start_date: Optional[date] = Field(None, description="Requested start date")
    preferred_duration: Optional[int] = Field(None, ge=1, description="Preferred duration (months)")
    location_preference: Optional[str] = Field(None, description="Location preference")
    timezone_preference: Optional[str] = Field(None, description="Timezone preference")
    language_preference: Optional[str] = Field(None, description="Language preference")
    background_info: Optional[str] = Field(None, description="Background information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    previous_mentoring_experience: Optional[str] = Field(None, description="Previous mentoring experience")


class MentorshipRequestCreate(MentorshipRequestBase):
    """Schema for creating a Mentorship Request."""
    requester_id: UUID = Field(..., description="Requester user ID")


class MentorshipRequestUpdate(BaseSchema):
    """Schema for updating a Mentorship Request."""
    status: Optional[str] = Field(None, description="Request status")
    priority: Optional[str] = Field(None, description="Request priority")
    desired_expertise: Optional[Dict[str, Any]] = Field(None, description="Desired expertise")
    goals_and_objectives: Optional[str] = Field(None, description="Goals and objectives")
    preferred_mentor_level: Optional[str] = Field(None, description="Preferred mentor level")
    preferred_meeting_frequency: Optional[str] = Field(None, description="Meeting frequency")
    requested_start_date: Optional[date] = Field(None, description="Requested start date")
    preferred_duration: Optional[int] = Field(None, ge=1, description="Preferred duration")
    location_preference: Optional[str] = Field(None, description="Location preference")
    timezone_preference: Optional[str] = Field(None, description="Timezone preference")
    language_preference: Optional[str] = Field(None, description="Language preference")
    background_info: Optional[str] = Field(None, description="Background info")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    previous_mentoring_experience: Optional[str] = Field(None, description="Previous experience")
    assigned_to_id: Optional[UUID] = Field(None, description="Assigned to user ID")
    matched_with_id: Optional[UUID] = Field(None, description="Matched with user ID")
    matching_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Matching score")
    matching_notes: Optional[str] = Field(None, description="Matching notes")
    processed_date: Optional[datetime] = Field(None, description="Processed date")
    matched_date: Optional[datetime] = Field(None, description="Matched date")


class MentorshipRequest(MentorshipRequestBase, BaseDBSchema):
    """Schema for Mentorship Request response."""
    requester_id: UUID = Field(..., description="Requester user ID")
    assigned_to_id: Optional[UUID] = Field(None, description="Assigned to user ID")
    matched_with_id: Optional[UUID] = Field(None, description="Matched with user ID")
    matching_score: Optional[float] = Field(None, description="Matching score")
    matching_notes: Optional[str] = Field(None, description="Matching notes")
    request_date: datetime = Field(..., description="Request date")
    processed_date: Optional[datetime] = Field(None, description="Processed date")
    matched_date: Optional[datetime] = Field(None, description="Matched date")


# Mentorship Analytics Schemas
class MentorshipStats(BaseSchema):
    """Mentorship statistics for a user or organization."""
    user_id: Optional[UUID] = Field(None, description="User ID (if user-specific)")
    total_mentorships: int = Field(..., description="Total mentorships")
    active_mentorships: int = Field(..., description="Active mentorships")
    completed_mentorships: int = Field(..., description="Completed mentorships")
    mentorships_as_mentor: int = Field(..., description="Mentorships as mentor")
    mentorships_as_mentee: int = Field(..., description="Mentorships as mentee")
    average_mentorship_duration: Optional[float] = Field(None, description="Average duration (months)")
    average_satisfaction_rating: Optional[float] = Field(None, description="Average satisfaction rating")
    total_sessions: int = Field(..., description="Total mentorship sessions")
    average_sessions_per_mentorship: Optional[float] = Field(None, description="Average sessions per mentorship")
    success_rate: Optional[float] = Field(None, description="Success rate (completed/total)")
    mentorship_outcomes: Dict[str, int] = Field(..., description="Mentorship outcomes")


# Update forward references
MentorshipWithSessions.model_rebuild()
