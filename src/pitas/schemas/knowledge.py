"""Knowledge management schemas for Phase 7: Obsidian Integration."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import Field, ConfigDict

from pitas.schemas.base import BaseSchema, BaseDBSchema
from pitas.db.models.knowledge import DocumentType, DocumentStatus, LinkType


class KnowledgeDocumentBase(BaseSchema):
    """Base knowledge document schema."""
    
    title: str = Field(..., description="Document title", max_length=500)
    content: str = Field(..., description="Document content in Markdown")
    document_type: DocumentType = Field(..., description="Type of document")
    file_name: str = Field(..., description="File name", max_length=255)
    tags: Optional[List[str]] = Field(None, description="Document tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Document metadata")


class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    """Schema for creating knowledge documents."""
    
    status: DocumentStatus = Field(DocumentStatus.DRAFT, description="Document status")
    file_path: Optional[str] = Field(None, description="File path", max_length=1000)
    obsidian_path: Optional[str] = Field(None, description="Obsidian vault path", max_length=1000)
    parent_document_id: Optional[UUID] = Field(None, description="Parent document ID")
    author_id: Optional[UUID] = Field(None, description="Author user ID")


class KnowledgeDocumentUpdate(BaseSchema):
    """Schema for updating knowledge documents."""
    
    title: Optional[str] = Field(None, description="Document title", max_length=500)
    content: Optional[str] = Field(None, description="Document content in Markdown")
    document_type: Optional[DocumentType] = Field(None, description="Type of document")
    status: Optional[DocumentStatus] = Field(None, description="Document status")
    file_name: Optional[str] = Field(None, description="File name", max_length=255)
    file_path: Optional[str] = Field(None, description="File path", max_length=1000)
    obsidian_path: Optional[str] = Field(None, description="Obsidian vault path", max_length=1000)
    tags: Optional[List[str]] = Field(None, description="Document tags")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Document metadata")
    reviewer_id: Optional[UUID] = Field(None, description="Reviewer user ID")


class KnowledgeDocument(BaseDBSchema):
    """Knowledge document response schema."""
    
    title: str
    content: str
    document_type: DocumentType
    status: DocumentStatus
    file_path: Optional[str]
    file_name: str
    file_hash: Optional[str]
    obsidian_path: Optional[str]
    obsidian_id: Optional[str]
    last_obsidian_sync: Optional[datetime]
    tags: Optional[List[str]]
    metadata: Optional[Dict[str, Any]]
    version: int
    parent_document_id: Optional[UUID]
    author_id: Optional[UUID]
    reviewer_id: Optional[UUID]
    published_at: Optional[datetime]
    reviewed_at: Optional[datetime]
    
    model_config = ConfigDict(from_attributes=True)


class DocumentLinkBase(BaseSchema):
    """Base document link schema."""
    
    target_document_id: UUID = Field(..., description="Target document ID")
    link_type: LinkType = Field(..., description="Type of link")
    description: Optional[str] = Field(None, description="Link description")
    link_text: Optional[str] = Field(None, description="Link text", max_length=500)
    context: Optional[str] = Field(None, description="Link context")


class DocumentLinkCreate(DocumentLinkBase):
    """Schema for creating document links."""
    
    source_document_id: UUID = Field(..., description="Source document ID")


class DocumentLink(BaseDBSchema, DocumentLinkBase):
    """Document link response schema."""
    
    source_document_id: UUID


class DocumentTemplateBase(BaseSchema):
    """Base document template schema."""
    
    name: str = Field(..., description="Template name", max_length=255)
    description: Optional[str] = Field(None, description="Template description")
    document_type: DocumentType = Field(..., description="Document type for template")
    template_content: str = Field(..., description="Template content with variables")
    template_variables: Optional[Dict[str, Any]] = Field(None, description="Template variables")
    generation_rules: Optional[Dict[str, Any]] = Field(None, description="Generation rules")
    auto_generate: bool = Field(False, description="Enable automatic generation")
    tags: Optional[List[str]] = Field(None, description="Template tags")
    category: Optional[str] = Field(None, description="Template category", max_length=100)


class DocumentTemplateCreate(DocumentTemplateBase):
    """Schema for creating document templates."""
    
    version: str = Field("1.0", description="Template version", max_length=20)
    is_active: bool = Field(True, description="Whether template is active")


class DocumentTemplateUpdate(BaseSchema):
    """Schema for updating document templates."""
    
    name: Optional[str] = Field(None, description="Template name", max_length=255)
    description: Optional[str] = Field(None, description="Template description")
    document_type: Optional[DocumentType] = Field(None, description="Document type for template")
    template_content: Optional[str] = Field(None, description="Template content with variables")
    template_variables: Optional[Dict[str, Any]] = Field(None, description="Template variables")
    generation_rules: Optional[Dict[str, Any]] = Field(None, description="Generation rules")
    auto_generate: Optional[bool] = Field(None, description="Enable automatic generation")
    tags: Optional[List[str]] = Field(None, description="Template tags")
    category: Optional[str] = Field(None, description="Template category", max_length=100)
    version: Optional[str] = Field(None, description="Template version", max_length=20)
    is_active: Optional[bool] = Field(None, description="Whether template is active")


class DocumentTemplate(BaseDBSchema, DocumentTemplateBase):
    """Document template response schema."""
    
    version: str
    is_active: bool


class KnowledgeGraphBase(BaseSchema):
    """Base knowledge graph schema."""
    
    entity_type: str = Field(..., description="Entity type", max_length=100)
    entity_id: str = Field(..., description="Entity ID", max_length=255)
    entity_name: str = Field(..., description="Entity name", max_length=500)
    related_entity_type: str = Field(..., description="Related entity type", max_length=100)
    related_entity_id: str = Field(..., description="Related entity ID", max_length=255)
    related_entity_name: str = Field(..., description="Related entity name", max_length=500)
    relationship_type: str = Field(..., description="Relationship type", max_length=100)
    relationship_strength: Optional[float] = Field(None, description="Relationship strength (0.0-1.0)", ge=0.0, le=1.0)
    context: Optional[Dict[str, Any]] = Field(None, description="Relationship context")


class KnowledgeGraphCreate(KnowledgeGraphBase):
    """Schema for creating knowledge graph relationships."""
    pass


class KnowledgeGraph(BaseDBSchema, KnowledgeGraphBase):
    """Knowledge graph response schema."""
    pass


# Document generation schemas
class DocumentGenerationRequest(BaseSchema):
    """Schema for requesting document generation."""
    
    template_id: UUID = Field(..., description="Template ID to use")
    variables: Dict[str, Any] = Field(..., description="Variables for template")
    output_path: Optional[str] = Field(None, description="Output path for generated document")
    auto_publish: bool = Field(False, description="Automatically publish generated document")


class DocumentGenerationResult(BaseSchema):
    """Schema for document generation results."""
    
    success: bool = Field(..., description="Whether generation was successful")
    document_id: Optional[UUID] = Field(None, description="Generated document ID")
    file_path: Optional[str] = Field(None, description="Generated file path")
    message: str = Field(..., description="Generation result message")
    errors: Optional[List[str]] = Field(None, description="Generation errors")
    warnings: Optional[List[str]] = Field(None, description="Generation warnings")


# Obsidian sync schemas
class ObsidianSyncRequest(BaseSchema):
    """Schema for Obsidian sync requests."""
    
    sync_type: str = Field("bidirectional", description="Sync type (pull, push, bidirectional)")
    folder_filter: Optional[List[str]] = Field(None, description="Folders to sync")
    tag_filter: Optional[List[str]] = Field(None, description="Tags to sync")
    force_sync: bool = Field(False, description="Force sync even if no changes")


class ObsidianSyncResult(BaseSchema):
    """Schema for Obsidian sync results."""
    
    success: bool = Field(..., description="Whether sync was successful")
    documents_pulled: int = Field(0, description="Documents pulled from Obsidian")
    documents_pushed: int = Field(0, description="Documents pushed to Obsidian")
    documents_updated: int = Field(0, description="Documents updated")
    documents_created: int = Field(0, description="Documents created")
    conflicts: int = Field(0, description="Sync conflicts encountered")
    errors: Optional[List[str]] = Field(None, description="Sync errors")
    sync_log_id: Optional[UUID] = Field(None, description="Sync log ID")


# Search and discovery schemas
class KnowledgeSearchRequest(BaseSchema):
    """Schema for knowledge search requests."""
    
    query: str = Field(..., description="Search query", max_length=500)
    document_types: Optional[List[DocumentType]] = Field(None, description="Document types to search")
    tags: Optional[List[str]] = Field(None, description="Tags to filter by")
    status: Optional[List[DocumentStatus]] = Field(None, description="Document status to filter by")
    author_id: Optional[UUID] = Field(None, description="Author to filter by")
    date_from: Optional[datetime] = Field(None, description="Search from date")
    date_to: Optional[datetime] = Field(None, description="Search to date")
    limit: int = Field(20, description="Maximum results to return", ge=1, le=100)
    offset: int = Field(0, description="Results offset", ge=0)


class KnowledgeSearchResult(BaseSchema):
    """Schema for knowledge search results."""
    
    documents: List[KnowledgeDocument] = Field(..., description="Found documents")
    total_count: int = Field(..., description="Total number of matching documents")
    search_time: float = Field(..., description="Search execution time in seconds")
    suggestions: Optional[List[str]] = Field(None, description="Search suggestions")


# Analytics schemas
class KnowledgeAnalytics(BaseSchema):
    """Schema for knowledge analytics."""
    
    total_documents: int = Field(..., description="Total number of documents")
    documents_by_type: Dict[str, int] = Field(..., description="Documents by type")
    documents_by_status: Dict[str, int] = Field(..., description="Documents by status")
    recent_activity: List[Dict[str, Any]] = Field(..., description="Recent document activity")
    top_tags: List[Dict[str, Any]] = Field(..., description="Most used tags")
    authorship_stats: Dict[str, int] = Field(..., description="Documents by author")
    sync_health: Dict[str, Any] = Field(..., description="Obsidian sync health metrics")
