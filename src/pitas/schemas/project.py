"""Project and workflow Pydantic schemas."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.core.workflow import PTESPhase, WorkflowStatus


class ProjectBase(BaseModel):
    """Base project schema."""
    name: str = Field(..., description="Project name", max_length=255)
    description: Optional[str] = Field(None, description="Project description")
    start_date: datetime = Field(..., description="Project start date")
    end_date: datetime = Field(..., description="Project end date")
    estimated_hours: int = Field(0, description="Estimated total hours", ge=0)
    scope_document: Optional[str] = Field(None, description="Project scope document")
    rules_of_engagement: Optional[str] = Field(None, description="Rules of engagement")
    target_systems: Optional[Dict[str, Any]] = Field(None, description="Target systems and networks")
    excluded_systems: Optional[Dict[str, Any]] = Field(None, description="Excluded systems and networks")
    peer_review_required: bool = Field(True, description="Whether peer review is required")
    compliance_frameworks: Optional[Dict[str, Any]] = Field(None, description="Applicable compliance frameworks")
    audit_trail_enabled: bool = Field(True, description="Whether audit trail is enabled")


class ProjectCreate(ProjectBase):
    """Schema for creating a project."""
    client_id: UUID = Field(..., description="Client ID")
    project_manager_id: UUID = Field(..., description="Project manager user ID")


class ProjectUpdate(BaseModel):
    """Schema for updating a project."""
    name: Optional[str] = Field(None, description="Project name", max_length=255)
    description: Optional[str] = Field(None, description="Project description")
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    estimated_hours: Optional[int] = Field(None, description="Estimated total hours", ge=0)
    scope_document: Optional[str] = Field(None, description="Project scope document")
    rules_of_engagement: Optional[str] = Field(None, description="Rules of engagement")
    target_systems: Optional[Dict[str, Any]] = Field(None, description="Target systems and networks")
    excluded_systems: Optional[Dict[str, Any]] = Field(None, description="Excluded systems and networks")
    peer_review_required: Optional[bool] = Field(None, description="Whether peer review is required")
    compliance_frameworks: Optional[Dict[str, Any]] = Field(None, description="Applicable compliance frameworks")
    audit_trail_enabled: Optional[bool] = Field(None, description="Whether audit trail is enabled")
    project_manager_id: Optional[UUID] = Field(None, description="Project manager user ID")


class ProjectResponse(ProjectBase):
    """Schema for project responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Project ID")
    client_id: UUID = Field(..., description="Client ID")
    project_manager_id: UUID = Field(..., description="Project manager user ID")
    current_phase: PTESPhase = Field(..., description="Current PTES phase")
    workflow_status: WorkflowStatus = Field(..., description="Overall workflow status")
    progress_percentage: float = Field(..., description="Project progress percentage", ge=0.0, le=1.0)
    actual_hours: int = Field(..., description="Actual hours spent", ge=0)
    quality_score: Optional[float] = Field(None, description="Quality score", ge=0.0, le=1.0)
    is_active: bool = Field(..., description="Whether project is active")
    is_archived: bool = Field(..., description="Whether project is archived")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class PhaseTransitionBase(BaseModel):
    """Base phase transition schema."""
    from_phase: Optional[PTESPhase] = Field(None, description="Previous phase")
    to_phase: PTESPhase = Field(..., description="New phase")
    transition_reason: Optional[str] = Field(None, description="Reason for transition")
    validation_data: Optional[Dict[str, Any]] = Field(None, description="Validation data for the transition")


class PhaseTransitionCreate(PhaseTransitionBase):
    """Schema for creating a phase transition."""
    project_id: UUID = Field(..., description="Project ID")


class PhaseTransitionResponse(PhaseTransitionBase):
    """Schema for phase transition responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Transition ID")
    project_id: UUID = Field(..., description="Project ID")
    transitioned_by: UUID = Field(..., description="User who performed the transition")
    approved_by: Optional[UUID] = Field(None, description="User who approved the transition")
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")


class ProjectDeliverableBase(BaseModel):
    """Base project deliverable schema."""
    phase: PTESPhase = Field(..., description="PTES phase")
    name: str = Field(..., description="Deliverable name", max_length=255)
    description: Optional[str] = Field(None, description="Deliverable description")
    due_date: Optional[datetime] = Field(None, description="Due date")
    file_path: Optional[str] = Field(None, description="File path for deliverable", max_length=500)


class ProjectDeliverableCreate(ProjectDeliverableBase):
    """Schema for creating a project deliverable."""
    project_id: UUID = Field(..., description="Project ID")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")


class ProjectDeliverableUpdate(BaseModel):
    """Schema for updating a project deliverable."""
    name: Optional[str] = Field(None, description="Deliverable name", max_length=255)
    description: Optional[str] = Field(None, description="Deliverable description")
    status: Optional[WorkflowStatus] = Field(None, description="Deliverable status")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")
    due_date: Optional[datetime] = Field(None, description="Due date")
    file_path: Optional[str] = Field(None, description="File path for deliverable", max_length=500)
    quality_checked: Optional[bool] = Field(None, description="Whether quality check is completed")


class ProjectDeliverableResponse(ProjectDeliverableBase):
    """Schema for project deliverable responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Deliverable ID")
    project_id: UUID = Field(..., description="Project ID")
    status: WorkflowStatus = Field(..., description="Deliverable status")
    assigned_to: Optional[UUID] = Field(None, description="Assigned user ID")
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")
    quality_checked: bool = Field(..., description="Whether quality check is completed")
    quality_checked_by: Optional[UUID] = Field(None, description="User who performed quality check")
    quality_checked_at: Optional[datetime] = Field(None, description="Quality check timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class ProjectTeamAssignmentBase(BaseModel):
    """Base project team assignment schema."""
    role: str = Field(..., description="Role in project", max_length=100)
    allocated_hours: Optional[int] = Field(None, description="Allocated hours for this assignment", ge=0)


class ProjectTeamAssignmentCreate(ProjectTeamAssignmentBase):
    """Schema for creating a project team assignment."""
    project_id: UUID = Field(..., description="Project ID")
    user_id: UUID = Field(..., description="User ID")


class ProjectTeamAssignmentUpdate(BaseModel):
    """Schema for updating a project team assignment."""
    role: Optional[str] = Field(None, description="Role in project", max_length=100)
    allocated_hours: Optional[int] = Field(None, description="Allocated hours for this assignment", ge=0)
    is_active: Optional[bool] = Field(None, description="Whether assignment is active")


class ProjectTeamAssignmentResponse(ProjectTeamAssignmentBase):
    """Schema for project team assignment responses."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Assignment ID")
    project_id: UUID = Field(..., description="Project ID")
    user_id: UUID = Field(..., description="User ID")
    assigned_at: datetime = Field(..., description="Assignment timestamp")
    unassigned_at: Optional[datetime] = Field(None, description="Unassignment timestamp")
    is_active: bool = Field(..., description="Whether assignment is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class WorkflowAdvanceRequest(BaseModel):
    """Schema for advancing workflow phase."""
    deliverables: List[str] = Field(..., description="List of completed deliverables")
    quality_checks: Dict[str, bool] = Field(..., description="Quality check results")
    reviewer_id: Optional[UUID] = Field(None, description="ID of the reviewer")
    notes: Optional[str] = Field(None, description="Additional notes for the transition")


class WorkflowAdvanceResponse(BaseModel):
    """Schema for workflow advance response."""
    success: bool = Field(..., description="Whether the advance was successful")
    from_phase: PTESPhase = Field(..., description="Previous phase")
    to_phase: Optional[PTESPhase] = Field(None, description="New phase")
    message: str = Field(..., description="Result message")
    validation_issues: List[str] = Field(default_factory=list, description="Validation issues if any")
    transition_id: Optional[UUID] = Field(None, description="Transition record ID")


class ProjectSummary(BaseModel):
    """Schema for project summary information."""
    model_config = ConfigDict(from_attributes=True)
    
    id: UUID = Field(..., description="Project ID")
    name: str = Field(..., description="Project name")
    client_name: str = Field(..., description="Client name")
    current_phase: PTESPhase = Field(..., description="Current PTES phase")
    workflow_status: WorkflowStatus = Field(..., description="Overall workflow status")
    progress_percentage: float = Field(..., description="Project progress percentage", ge=0.0, le=1.0)
    start_date: datetime = Field(..., description="Project start date")
    end_date: datetime = Field(..., description="Project end date")
    estimated_hours: int = Field(..., description="Estimated total hours", ge=0)
    actual_hours: int = Field(..., description="Actual hours spent", ge=0)
    team_size: int = Field(..., description="Number of team members", ge=0)
    is_active: bool = Field(..., description="Whether project is active")


class ProjectDashboard(BaseModel):
    """Schema for project dashboard data."""
    project: ProjectResponse = Field(..., description="Project details")
    current_phase_info: Dict[str, Any] = Field(..., description="Current phase information")
    team_assignments: List[ProjectTeamAssignmentResponse] = Field(..., description="Team assignments")
    recent_deliverables: List[ProjectDeliverableResponse] = Field(..., description="Recent deliverables")
    recent_transitions: List[PhaseTransitionResponse] = Field(..., description="Recent phase transitions")
    progress_metrics: Dict[str, Any] = Field(..., description="Progress metrics")
    upcoming_milestones: List[Dict[str, Any]] = Field(..., description="Upcoming milestones")


class ProjectListResponse(BaseModel):
    """Schema for paginated project list response."""
    projects: List[ProjectSummary] = Field(..., description="List of projects")
    total: int = Field(..., description="Total number of projects", ge=0)
    page: int = Field(..., description="Current page number", ge=1)
    per_page: int = Field(..., description="Items per page", ge=1)
    total_pages: int = Field(..., description="Total number of pages", ge=1)
