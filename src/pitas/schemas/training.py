"""Training and competency management schemas."""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

from pitas.db.models.training import (
    CompetencyLevel,
    TrainingStatus,
    CertificationStatus,
)


# Base schemas
class CompetencyFrameworkBase(BaseModel):
    """Base competency framework schema."""
    name: str = Field(..., description="Framework name")
    description: Optional[str] = Field(None, description="Framework description")
    version: str = Field(..., description="Framework version")
    work_role_id: str = Field(..., description="NICE work role ID")
    specialty_area: str = Field(..., description="Specialty area")
    category: str = Field(..., description="Category")


class CompetencyBase(BaseModel):
    """Base competency schema."""
    competency_id: str = Field(..., description="NICE competency ID")
    name: str = Field(..., description="Competency name")
    description: Optional[str] = Field(None, description="Competency description")
    knowledge_statements: Optional[List[str]] = Field(None, description="Knowledge requirements")
    skill_statements: Optional[List[str]] = Field(None, description="Skill requirements")
    ability_statements: Optional[List[str]] = Field(None, description="Ability requirements")


class SkillAssessmentBase(BaseModel):
    """Base skill assessment schema."""
    current_level: CompetencyLevel = Field(..., description="Current competency level")
    target_level: CompetencyLevel = Field(..., description="Target competency level")
    notes: Optional[str] = Field(None, description="Assessment notes")
    evidence: Optional[Dict[str, Any]] = Field(None, description="Supporting evidence")


class TrainingCourseBase(BaseModel):
    """Base training course schema."""
    title: str = Field(..., description="Course title")
    description: Optional[str] = Field(None, description="Course description")
    provider: Optional[str] = Field(None, description="Training provider")
    course_code: Optional[str] = Field(None, description="Course code")
    duration_hours: Optional[int] = Field(None, description="Duration in hours")
    difficulty_level: CompetencyLevel = Field(..., description="Difficulty level")
    prerequisites: Optional[List[UUID]] = Field(None, description="Prerequisite course IDs")
    learning_objectives: Optional[List[str]] = Field(None, description="Learning objectives")
    competencies_addressed: Optional[List[UUID]] = Field(None, description="Competency IDs addressed")
    is_certification_prep: bool = Field(False, description="Is certification preparation")
    cost: Optional[float] = Field(None, description="Course cost")
    is_active: bool = Field(True, description="Is course active")


class LearningPathBase(BaseModel):
    """Base learning path schema."""
    name: str = Field(..., description="Learning path name")
    description: Optional[str] = Field(None, description="Learning path description")
    target_role: Optional[str] = Field(None, description="Target role")
    estimated_duration_weeks: Optional[int] = Field(None, description="Estimated duration in weeks")
    course_sequence: Optional[List[UUID]] = Field(None, description="Ordered course IDs")
    competency_goals: Optional[Dict[str, CompetencyLevel]] = Field(None, description="Target competency levels")
    is_active: bool = Field(True, description="Is learning path active")


class TrainingEnrollmentBase(BaseModel):
    """Base training enrollment schema."""
    start_date: Optional[datetime] = Field(None, description="Start date")
    status: TrainingStatus = Field(TrainingStatus.NOT_STARTED, description="Training status")
    progress_percentage: float = Field(0.0, description="Progress percentage")
    assessment_scores: Optional[Dict[str, float]] = Field(None, description="Assessment scores")
    practical_scores: Optional[Dict[str, float]] = Field(None, description="Practical scores")
    time_spent_hours: float = Field(0.0, description="Time spent in hours")


class CertificationBase(BaseModel):
    """Base certification schema."""
    name: str = Field(..., description="Certification name")
    abbreviation: str = Field(..., description="Certification abbreviation")
    provider: str = Field(..., description="Certification provider")
    description: Optional[str] = Field(None, description="Certification description")
    level: CompetencyLevel = Field(..., description="Certification level")
    prerequisites: Optional[List[UUID]] = Field(None, description="Prerequisite certification IDs")
    renewal_period_years: Optional[int] = Field(None, description="Renewal period in years")
    cpe_credits_required: Optional[int] = Field(None, description="CPE credits required")
    exam_cost: Optional[float] = Field(None, description="Exam cost")
    is_active: bool = Field(True, description="Is certification active")


class CertificationAchievementBase(BaseModel):
    """Base certification achievement schema."""
    status: CertificationStatus = Field(CertificationStatus.NOT_STARTED, description="Achievement status")
    achievement_date: Optional[datetime] = Field(None, description="Achievement date")
    expiration_date: Optional[datetime] = Field(None, description="Expiration date")
    credential_id: Optional[str] = Field(None, description="External credential ID")
    cpe_credits_earned: int = Field(0, description="CPE credits earned")
    cost_reimbursed: Optional[float] = Field(None, description="Cost reimbursed")


class CTFChallengeBase(BaseModel):
    """Base CTF challenge schema."""
    title: str = Field(..., description="Challenge title")
    description: Optional[str] = Field(None, description="Challenge description")
    category: str = Field(..., description="Challenge category")
    difficulty: CompetencyLevel = Field(..., description="Challenge difficulty")
    points: int = Field(..., description="Points awarded")
    hints: Optional[List[str]] = Field(None, description="Challenge hints")
    files: Optional[List[str]] = Field(None, description="Challenge files")
    competencies_tested: Optional[List[UUID]] = Field(None, description="Competencies tested")
    is_active: bool = Field(True, description="Is challenge active")


class CTFSubmissionBase(BaseModel):
    """Base CTF submission schema."""
    submitted_flag: str = Field(..., description="Submitted flag")
    is_correct: bool = Field(..., description="Is submission correct")
    points_awarded: int = Field(0, description="Points awarded")
    time_to_solve_minutes: Optional[int] = Field(None, description="Time to solve in minutes")


class MentorshipPairBase(BaseModel):
    """Base mentorship pair schema."""
    goals: Optional[List[str]] = Field(None, description="Mentorship goals")
    meeting_frequency: Optional[str] = Field(None, description="Meeting frequency")
    is_active: bool = Field(True, description="Is mentorship active")
    satisfaction_rating: Optional[float] = Field(None, description="Satisfaction rating (1-5)")


class MentorshipSessionBase(BaseModel):
    """Base mentorship session schema."""
    session_date: datetime = Field(..., description="Session date")
    duration_minutes: Optional[int] = Field(None, description="Session duration in minutes")
    topics_discussed: Optional[List[str]] = Field(None, description="Topics discussed")
    action_items: Optional[List[str]] = Field(None, description="Action items")
    mentor_notes: Optional[str] = Field(None, description="Mentor notes")
    mentee_feedback: Optional[str] = Field(None, description="Mentee feedback")


# Create schemas
class CompetencyFrameworkCreate(CompetencyFrameworkBase):
    """Schema for creating competency framework."""
    pass


class CompetencyCreate(CompetencyBase):
    """Schema for creating competency."""
    framework_id: UUID = Field(..., description="Framework ID")


class SkillAssessmentCreate(SkillAssessmentBase):
    """Schema for creating skill assessment."""
    user_id: UUID = Field(..., description="User ID")
    competency_id: UUID = Field(..., description="Competency ID")
    assessor_id: Optional[UUID] = Field(None, description="Assessor ID")


class TrainingCourseCreate(TrainingCourseBase):
    """Schema for creating training course."""
    certification_id: Optional[UUID] = Field(None, description="Certification ID")


class LearningPathCreate(LearningPathBase):
    """Schema for creating learning path."""
    user_id: UUID = Field(..., description="User ID")


class TrainingEnrollmentCreate(TrainingEnrollmentBase):
    """Schema for creating training enrollment."""
    user_id: UUID = Field(..., description="User ID")
    course_id: UUID = Field(..., description="Course ID")
    learning_path_id: Optional[UUID] = Field(None, description="Learning path ID")


class CertificationCreate(CertificationBase):
    """Schema for creating certification."""
    pass


class CertificationAchievementCreate(CertificationAchievementBase):
    """Schema for creating certification achievement."""
    user_id: UUID = Field(..., description="User ID")
    certification_id: UUID = Field(..., description="Certification ID")


class CTFChallengeCreate(CTFChallengeBase):
    """Schema for creating CTF challenge."""
    flag: str = Field(..., description="Challenge flag")
    created_by: UUID = Field(..., description="Creator user ID")


class CTFSubmissionCreate(CTFSubmissionBase):
    """Schema for creating CTF submission."""
    user_id: UUID = Field(..., description="User ID")
    challenge_id: UUID = Field(..., description="Challenge ID")


class MentorshipPairCreate(MentorshipPairBase):
    """Schema for creating mentorship pair."""
    mentor_id: UUID = Field(..., description="Mentor user ID")
    mentee_id: UUID = Field(..., description="Mentee user ID")


class MentorshipSessionCreate(MentorshipSessionBase):
    """Schema for creating mentorship session."""
    pair_id: UUID = Field(..., description="Mentorship pair ID")


# Update schemas
class CompetencyFrameworkUpdate(BaseModel):
    """Schema for updating competency framework."""
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    work_role_id: Optional[str] = None
    specialty_area: Optional[str] = None
    category: Optional[str] = None


class CompetencyUpdate(BaseModel):
    """Schema for updating competency."""
    competency_id: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    knowledge_statements: Optional[List[str]] = None
    skill_statements: Optional[List[str]] = None
    ability_statements: Optional[List[str]] = None


class SkillAssessmentUpdate(BaseModel):
    """Schema for updating skill assessment."""
    current_level: Optional[CompetencyLevel] = None
    target_level: Optional[CompetencyLevel] = None
    notes: Optional[str] = None
    evidence: Optional[Dict[str, Any]] = None


class TrainingCourseUpdate(BaseModel):
    """Schema for updating training course."""
    title: Optional[str] = None
    description: Optional[str] = None
    provider: Optional[str] = None
    course_code: Optional[str] = None
    duration_hours: Optional[int] = None
    difficulty_level: Optional[CompetencyLevel] = None
    prerequisites: Optional[List[UUID]] = None
    learning_objectives: Optional[List[str]] = None
    competencies_addressed: Optional[List[UUID]] = None
    is_certification_prep: Optional[bool] = None
    certification_id: Optional[UUID] = None
    cost: Optional[float] = None
    is_active: Optional[bool] = None


class LearningPathUpdate(BaseModel):
    """Schema for updating learning path."""
    name: Optional[str] = None
    description: Optional[str] = None
    target_role: Optional[str] = None
    estimated_duration_weeks: Optional[int] = None
    course_sequence: Optional[List[UUID]] = None
    competency_goals: Optional[Dict[str, CompetencyLevel]] = None
    is_active: Optional[bool] = None
    completion_percentage: Optional[float] = None


class TrainingEnrollmentUpdate(BaseModel):
    """Schema for updating training enrollment."""
    start_date: Optional[datetime] = None
    completion_date: Optional[datetime] = None
    status: Optional[TrainingStatus] = None
    progress_percentage: Optional[float] = None
    assessment_scores: Optional[Dict[str, float]] = None
    practical_scores: Optional[Dict[str, float]] = None
    time_spent_hours: Optional[float] = None


class CertificationUpdate(BaseModel):
    """Schema for updating certification."""
    name: Optional[str] = None
    abbreviation: Optional[str] = None
    provider: Optional[str] = None
    description: Optional[str] = None
    level: Optional[CompetencyLevel] = None
    prerequisites: Optional[List[UUID]] = None
    renewal_period_years: Optional[int] = None
    cpe_credits_required: Optional[int] = None
    exam_cost: Optional[float] = None
    is_active: Optional[bool] = None


class CertificationAchievementUpdate(BaseModel):
    """Schema for updating certification achievement."""
    status: Optional[CertificationStatus] = None
    achievement_date: Optional[datetime] = None
    expiration_date: Optional[datetime] = None
    credential_id: Optional[str] = None
    cpe_credits_earned: Optional[int] = None
    renewal_reminder_sent: Optional[bool] = None
    cost_reimbursed: Optional[float] = None


class CTFChallengeUpdate(BaseModel):
    """Schema for updating CTF challenge."""
    title: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    difficulty: Optional[CompetencyLevel] = None
    points: Optional[int] = None
    flag: Optional[str] = None
    hints: Optional[List[str]] = None
    files: Optional[List[str]] = None
    competencies_tested: Optional[List[UUID]] = None
    is_active: Optional[bool] = None


class MentorshipPairUpdate(BaseModel):
    """Schema for updating mentorship pair."""
    end_date: Optional[datetime] = None
    goals: Optional[List[str]] = None
    meeting_frequency: Optional[str] = None
    is_active: Optional[bool] = None
    satisfaction_rating: Optional[float] = None


class MentorshipSessionUpdate(BaseModel):
    """Schema for updating mentorship session."""
    session_date: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    topics_discussed: Optional[List[str]] = None
    action_items: Optional[List[str]] = None
    mentor_notes: Optional[str] = None
    mentee_feedback: Optional[str] = None


# Response schemas
class CompetencyFramework(CompetencyFrameworkBase):
    """Competency framework response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime


class Competency(CompetencyBase):
    """Competency response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    framework_id: UUID
    created_at: datetime
    updated_at: datetime


class SkillAssessment(SkillAssessmentBase):
    """Skill assessment response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    user_id: UUID
    competency_id: UUID
    assessment_date: datetime
    assessor_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime


class TrainingCourse(TrainingCourseBase):
    """Training course response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    certification_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime


class LearningPath(LearningPathBase):
    """Learning path response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    user_id: UUID
    completion_percentage: float
    created_at: datetime
    updated_at: datetime


class TrainingEnrollment(TrainingEnrollmentBase):
    """Training enrollment response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    user_id: UUID
    course_id: UUID
    learning_path_id: Optional[UUID] = None
    enrollment_date: datetime
    completion_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class Certification(CertificationBase):
    """Certification response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime


class CertificationAchievement(CertificationAchievementBase):
    """Certification achievement response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    user_id: UUID
    certification_id: UUID
    renewal_reminder_sent: bool
    created_at: datetime
    updated_at: datetime


class CTFChallenge(CTFChallengeBase):
    """CTF challenge response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_by: UUID
    created_at: datetime
    updated_at: datetime


class CTFSubmission(CTFSubmissionBase):
    """CTF submission response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    user_id: UUID
    challenge_id: UUID
    submission_time: datetime
    created_at: datetime
    updated_at: datetime


class MentorshipPair(MentorshipPairBase):
    """Mentorship pair response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    mentor_id: UUID
    mentee_id: UUID
    start_date: datetime
    end_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class MentorshipSession(MentorshipSessionBase):
    """Mentorship session response schema."""
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    pair_id: UUID
    created_at: datetime
    updated_at: datetime


# Specialized schemas for complex operations
class SkillGapAnalysis(BaseModel):
    """Skill gap analysis result schema."""
    user_id: UUID
    current_competencies: Dict[str, CompetencyLevel]
    target_competencies: Dict[str, CompetencyLevel]
    skill_gaps: Dict[str, CompetencyLevel]
    recommended_training: List[UUID]
    estimated_completion_weeks: int


class LearningProgress(BaseModel):
    """Learning progress tracking schema."""
    user_id: UUID
    course_id: UUID
    completion_percentage: float
    assessment_scores: Dict[str, float]
    practical_demonstrations: Dict[str, bool]
    peer_feedback: Optional[str] = None
    time_spent_hours: float
    projected_completion_date: Optional[datetime] = None


class CTFLeaderboard(BaseModel):
    """CTF leaderboard entry schema."""
    user_id: UUID
    username: str
    total_points: int
    challenges_solved: int
    rank: int
    last_submission: Optional[datetime] = None


class TrainingROIAnalysis(BaseModel):
    """Training ROI analysis schema."""
    training_investment: float
    productivity_improvement: float
    retention_improvement: float
    client_satisfaction_improvement: float
    roi_ratio: float
    payback_period_months: int


class CompetencyMatrix(BaseModel):
    """Team competency matrix schema."""
    team_id: Optional[UUID] = None
    competency_coverage: Dict[str, List[UUID]]  # competency_id -> user_ids
    skill_gaps: Dict[str, int]  # competency_id -> gap_count
    training_recommendations: List[UUID]  # course_ids
    overall_maturity: CompetencyLevel


class CertificationPathway(BaseModel):
    """Certification pathway schema."""
    user_id: UUID
    current_certifications: List[UUID]
    target_certifications: List[UUID]
    recommended_sequence: List[UUID]
    estimated_timeline_months: int
    total_cost: float
    prerequisites_met: Dict[UUID, bool]
