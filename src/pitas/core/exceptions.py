"""Custom application exceptions."""

from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class AppException(Exception):
    """Base application exception.

    Args:
        message: Error message
        details: Additional error details
    """

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Initialize the exception.

        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ValidationError(AppException):
    """Validation error exception."""
    pass


class NotFoundError(AppException):
    """Resource not found exception."""
    pass


class UnauthorizedError(AppException):
    """Unauthorized access exception."""
    pass


class ForbiddenError(AppException):
    """Forbidden access exception."""
    pass


class ConflictError(AppException):
    """Resource conflict exception."""
    pass


class SecurityError(AppException):
    """Security-related exception."""
    pass


class VulnerabilityError(AppException):
    """Vulnerability-related exception."""
    pass


class IntegrationError(AppException):
    """External integration error exception."""
    pass


# HTTP Exception mapping
def create_http_exception(
    status_code: int,
    message: str,
    details: Optional[Dict[str, Any]] = None,
) -> HTTPException:
    """Create an HTTPException with consistent format.

    Args:
        status_code: HTTP status code
        message: Error message
        details: Additional error details

    Returns:
        HTTPException instance
    """
    content = {"message": message}
    if details:
        content["details"] = details

    return HTTPException(
        status_code=status_code,
        detail=content,
    )


def not_found_exception(message: str = "Resource not found") -> HTTPException:
    """Create a 404 Not Found exception."""
    return create_http_exception(status.HTTP_404_NOT_FOUND, message)


def validation_exception(
    message: str = "Validation error",
    details: Optional[Dict[str, Any]] = None,
) -> HTTPException:
    """Create a 422 Validation Error exception."""
    return create_http_exception(
        status.HTTP_422_UNPROCESSABLE_ENTITY,
        message,
        details,
    )


def unauthorized_exception(message: str = "Unauthorized") -> HTTPException:
    """Create a 401 Unauthorized exception."""
    return create_http_exception(status.HTTP_401_UNAUTHORIZED, message)


def forbidden_exception(message: str = "Forbidden") -> HTTPException:
    """Create a 403 Forbidden exception."""
    return create_http_exception(status.HTTP_403_FORBIDDEN, message)


def conflict_exception(message: str = "Resource conflict") -> HTTPException:
    """Create a 409 Conflict exception."""
    return create_http_exception(status.HTTP_409_CONFLICT, message)


def security_exception(message: str = "Security violation") -> HTTPException:
    """Create a 403 Security exception."""
    return create_http_exception(status.HTTP_403_FORBIDDEN, message)


def rate_limit_exception(message: str = "Rate limit exceeded") -> HTTPException:
    """Create a 429 Rate Limit exception."""
    return create_http_exception(status.HTTP_429_TOO_MANY_REQUESTS, message)