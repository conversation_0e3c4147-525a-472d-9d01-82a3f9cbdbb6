"""Performance monitoring and metrics collection."""

import time
import psutil
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps
import asyncio
import structlog

from prometheus_client import (
    Counter, Histogram, Gauge, Info, CollectorRegistry, 
    generate_latest, CONTENT_TYPE_LATEST
)
from opentelemetry import trace, metrics
from opentelemetry.exporter.prometheus import PrometheusMetricReader
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.jaeger.thrift import JaegerExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

from pitas.core.config import settings

logger = structlog.get_logger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter(
    'pitas_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'pitas_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

DATABASE_QUERY_DURATION = Histogram(
    'pitas_database_query_duration_seconds',
    'Database query duration in seconds',
    ['query_type', 'table']
)

CACHE_OPERATIONS = Counter(
    'pitas_cache_operations_total',
    'Total cache operations',
    ['operation', 'result']
)

ACTIVE_CONNECTIONS = Gauge(
    'pitas_active_connections',
    'Number of active database connections'
)

MEMORY_USAGE = Gauge(
    'pitas_memory_usage_bytes',
    'Memory usage in bytes'
)

CPU_USAGE = Gauge(
    'pitas_cpu_usage_percent',
    'CPU usage percentage'
)

VULNERABILITY_PROCESSING_TIME = Histogram(
    'pitas_vulnerability_processing_seconds',
    'Time to process vulnerability data',
    ['severity', 'type']
)

API_RESPONSE_SIZE = Histogram(
    'pitas_api_response_size_bytes',
    'API response size in bytes',
    ['endpoint']
)


class PerformanceMonitor:
    """Application performance monitoring system."""
    
    def __init__(self):
        self.tracer = trace.get_tracer(__name__)
        self.meter = metrics.get_meter(__name__)
        self._system_metrics_task: Optional[asyncio.Task] = None
        
        # Custom metrics
        self.request_counter = self.meter.create_counter(
            "requests_total",
            description="Total number of requests"
        )
        
        self.response_time_histogram = self.meter.create_histogram(
            "response_time_seconds",
            description="Response time in seconds"
        )
        
    async def start_monitoring(self):
        """Start performance monitoring."""
        logger.info("Starting performance monitoring")
        
        # Start system metrics collection
        self._system_metrics_task = asyncio.create_task(
            self._collect_system_metrics()
        )
        
    async def stop_monitoring(self):
        """Stop performance monitoring."""
        logger.info("Stopping performance monitoring")
        
        if self._system_metrics_task:
            self._system_metrics_task.cancel()
            try:
                await self._system_metrics_task
            except asyncio.CancelledError:
                pass
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics."""
        while True:
            try:
                # Memory usage
                memory = psutil.virtual_memory()
                MEMORY_USAGE.set(memory.used)
                
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                CPU_USAGE.set(cpu_percent)
                
                # Log metrics
                logger.debug(
                    "System metrics collected",
                    memory_used=memory.used,
                    memory_percent=memory.percent,
                    cpu_percent=cpu_percent
                )
                
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def track_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Track HTTP request metrics."""
        REQUEST_COUNT.labels(
            method=method,
            endpoint=endpoint,
            status_code=status_code
        ).inc()
        
        REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
        
        # OpenTelemetry metrics
        self.request_counter.add(1, {
            "method": method,
            "endpoint": endpoint,
            "status_code": str(status_code)
        })
        
        self.response_time_histogram.record(duration, {
            "method": method,
            "endpoint": endpoint
        })
    
    def track_database_query(self, query_type: str, table: str, duration: float):
        """Track database query performance."""
        DATABASE_QUERY_DURATION.labels(
            query_type=query_type,
            table=table
        ).observe(duration)
    
    def track_cache_operation(self, operation: str, result: str):
        """Track cache operation metrics."""
        CACHE_OPERATIONS.labels(
            operation=operation,
            result=result
        ).inc()
    
    def track_vulnerability_processing(self, severity: str, vuln_type: str, duration: float):
        """Track vulnerability processing time."""
        VULNERABILITY_PROCESSING_TIME.labels(
            severity=severity,
            type=vuln_type
        ).observe(duration)
    
    def track_api_response_size(self, endpoint: str, size_bytes: int):
        """Track API response size."""
        API_RESPONSE_SIZE.labels(endpoint=endpoint).observe(size_bytes)
    
    def create_span(self, name: str, attributes: Dict[str, Any] = None):
        """Create a new tracing span."""
        span = self.tracer.start_span(name)
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        return span
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary metrics."""
        try:
            # System metrics
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Network stats
            network = psutil.net_io_counters()
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'system': {
                    'memory': {
                        'total': memory.total,
                        'used': memory.used,
                        'available': memory.available,
                        'percent': memory.percent
                    },
                    'cpu': {
                        'percent': cpu_percent,
                        'count': psutil.cpu_count()
                    },
                    'disk': {
                        'total': disk.total,
                        'used': disk.used,
                        'free': disk.free,
                        'percent': (disk.used / disk.total) * 100
                    },
                    'network': {
                        'bytes_sent': network.bytes_sent,
                        'bytes_recv': network.bytes_recv,
                        'packets_sent': network.packets_sent,
                        'packets_recv': network.packets_recv
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {'error': str(e)}


class DatabasePerformanceMonitor:
    """Database-specific performance monitoring."""
    
    def __init__(self):
        self.slow_query_threshold = 1.0  # 1 second
        self.slow_queries: List[Dict[str, Any]] = []
        self.max_slow_queries = 100
    
    def track_query(self, query: str, duration: float, params: Dict[str, Any] = None):
        """Track database query performance."""
        if duration > self.slow_query_threshold:
            slow_query = {
                'query': query[:500],  # Truncate long queries
                'duration': duration,
                'params': params,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            self.slow_queries.append(slow_query)
            
            # Keep only recent slow queries
            if len(self.slow_queries) > self.max_slow_queries:
                self.slow_queries = self.slow_queries[-self.max_slow_queries:]
            
            logger.warning(
                "Slow query detected",
                duration=duration,
                query=query[:100]
            )
    
    def get_slow_queries(self) -> List[Dict[str, Any]]:
        """Get list of slow queries."""
        return self.slow_queries.copy()
    
    def get_query_stats(self) -> Dict[str, Any]:
        """Get query performance statistics."""
        if not self.slow_queries:
            return {
                'total_slow_queries': 0,
                'average_duration': 0,
                'max_duration': 0
            }
        
        durations = [q['duration'] for q in self.slow_queries]
        
        return {
            'total_slow_queries': len(self.slow_queries),
            'average_duration': sum(durations) / len(durations),
            'max_duration': max(durations),
            'min_duration': min(durations)
        }


def monitor_performance(operation_name: str = None):
    """Decorator to monitor function performance."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            with performance_monitor.create_span(operation) as span:
                try:
                    result = await func(*args, **kwargs)
                    span.set_attribute("success", True)
                    return result
                except Exception as e:
                    span.set_attribute("success", False)
                    span.set_attribute("error", str(e))
                    raise
                finally:
                    duration = time.time() - start_time
                    span.set_attribute("duration", duration)
                    
                    logger.debug(
                        "Operation completed",
                        operation=operation,
                        duration=duration
                    )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            with performance_monitor.create_span(operation) as span:
                try:
                    result = func(*args, **kwargs)
                    span.set_attribute("success", True)
                    return result
                except Exception as e:
                    span.set_attribute("success", False)
                    span.set_attribute("error", str(e))
                    raise
                finally:
                    duration = time.time() - start_time
                    span.set_attribute("duration", duration)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


# Global monitoring instances
performance_monitor = PerformanceMonitor()
db_performance_monitor = DatabasePerformanceMonitor()


def setup_tracing():
    """Setup OpenTelemetry tracing."""
    # Configure tracer provider
    trace.set_tracer_provider(TracerProvider())
    
    # Configure Jaeger exporter
    jaeger_exporter = JaegerExporter(
        agent_host_name=settings.jaeger_agent_host,
        agent_port=settings.jaeger_agent_port,
    )
    
    # Add span processor
    span_processor = BatchSpanProcessor(jaeger_exporter)
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    logger.info("OpenTelemetry tracing configured")


def setup_metrics():
    """Setup OpenTelemetry metrics."""
    # Configure Prometheus metric reader
    reader = PrometheusMetricReader()
    
    # Configure meter provider
    metrics.set_meter_provider(MeterProvider(metric_readers=[reader]))
    
    logger.info("OpenTelemetry metrics configured")


async def startup_monitoring():
    """Initialize monitoring on startup."""
    setup_tracing()
    setup_metrics()
    await performance_monitor.start_monitoring()


async def shutdown_monitoring():
    """Cleanup monitoring on shutdown."""
    await performance_monitor.stop_monitoring()
