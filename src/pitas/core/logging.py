"""Structured logging configuration."""

import logging
import sys
from typing import Any, Dict

import structlog
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from pitas.core.config import settings


def setup_logging() -> None:
    """Configure structured logging with rich output."""
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        datefmt="[%X]",
        handlers=[
            RichHandler(
                console=Console(stderr=True),
                rich_tracebacks=True,
                tracebacks_show_locals=settings.debug,
            )
        ],
        level=getattr(logging, settings.log_level.upper()),
        force=True,
    )

    # Configure structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            structlog.dev.set_exc_info,
            structlog.processors.TimeStamper(fmt="iso"),
        ]
        + (
            [structlog.dev.Console<PERSON><PERSON>()]
            if not settings.is_production
            else [structlog.processors.JSONRenderer()]
        ),
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.log_level.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a configured logger instance.

    Args:
        name: Logger name

    Returns:
        BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)