"""Performance optimization middleware for FastAPI."""

import time
import asyncio
from typing import Callable, Dict, Any
from datetime import datetime
import structlog

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp

from pitas.core.monitoring import performance_monitor
from pitas.core.cache import cache_manager

logger = structlog.get_logger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for performance monitoring and optimization."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.request_count = 0
        self.slow_request_threshold = 2.0  # 2 seconds
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with performance monitoring."""
        start_time = time.time()
        self.request_count += 1
        
        # Extract request information
        method = request.method
        path = request.url.path
        client_ip = request.client.host if request.client else "unknown"
        
        # Add request ID for tracing
        request_id = f"{int(time.time())}-{self.request_count}"
        
        # Create performance monitoring span
        with performance_monitor.create_span(
            f"{method} {path}",
            attributes={
                "http.method": method,
                "http.url": str(request.url),
                "http.client_ip": client_ip,
                "request.id": request_id
            }
        ) as span:
            
            try:
                # Process request
                response = await call_next(request)
                
                # Calculate response time
                end_time = time.time()
                response_time = end_time - start_time
                
                # Track metrics
                performance_monitor.track_request(
                    method=method,
                    endpoint=path,
                    status_code=response.status_code,
                    duration=response_time
                )
                
                # Track response size if available
                if hasattr(response, 'body') and response.body:
                    response_size = len(response.body)
                    performance_monitor.track_api_response_size(path, response_size)
                
                # Log slow requests
                if response_time > self.slow_request_threshold:
                    logger.warning(
                        "Slow request detected",
                        method=method,
                        path=path,
                        response_time=response_time,
                        status_code=response.status_code,
                        client_ip=client_ip,
                        request_id=request_id
                    )
                
                # Add performance headers
                response.headers["X-Response-Time"] = f"{response_time:.3f}s"
                response.headers["X-Request-ID"] = request_id
                
                # Update span attributes
                span.set_attribute("http.status_code", response.status_code)
                span.set_attribute("http.response_time", response_time)
                
                return response
                
            except Exception as e:
                end_time = time.time()
                response_time = end_time - start_time
                
                # Track failed request
                performance_monitor.track_request(
                    method=method,
                    endpoint=path,
                    status_code=500,
                    duration=response_time
                )
                
                # Log error
                logger.error(
                    "Request failed",
                    method=method,
                    path=path,
                    response_time=response_time,
                    error=str(e),
                    client_ip=client_ip,
                    request_id=request_id
                )
                
                # Update span with error
                span.set_attribute("error", True)
                span.set_attribute("error.message", str(e))
                
                # Return error response
                return JSONResponse(
                    status_code=500,
                    content={"error": "Internal server error", "request_id": request_id},
                    headers={"X-Request-ID": request_id}
                )


class CacheMiddleware(BaseHTTPMiddleware):
    """Middleware for intelligent response caching."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.cacheable_methods = {"GET"}
        self.cacheable_paths = {
            "/api/v1/vulnerabilities",
            "/api/v1/assets",
            "/api/v1/projects",
            "/api/v1/pentesters",
            "/health"
        }
        self.cache_ttl = {
            "/health": 60,  # 1 minute
            "/api/v1/vulnerabilities": 300,  # 5 minutes
            "/api/v1/assets": 600,  # 10 minutes
            "/api/v1/projects": 300,  # 5 minutes
            "/api/v1/pentesters": 600,  # 10 minutes
        }
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with intelligent caching."""
        method = request.method
        path = request.url.path
        
        # Only cache GET requests for specific endpoints
        if method not in self.cacheable_methods or not self._is_cacheable_path(path):
            return await call_next(request)
        
        # Generate cache key
        cache_key = self._generate_cache_key(request)
        
        # Try to get cached response
        try:
            cached_response = await cache_manager.get(cache_key, namespace="responses")
            if cached_response:
                performance_monitor.track_cache_operation("get", "hit")
                
                # Return cached response
                return JSONResponse(
                    content=cached_response["content"],
                    status_code=cached_response["status_code"],
                    headers={
                        **cached_response.get("headers", {}),
                        "X-Cache": "HIT",
                        "X-Cache-Key": cache_key[:16]  # Truncated for security
                    }
                )
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
        
        performance_monitor.track_cache_operation("get", "miss")
        
        # Process request
        response = await call_next(request)
        
        # Cache successful responses
        if response.status_code == 200 and hasattr(response, 'body'):
            try:
                # Get TTL for this path
                ttl = self._get_cache_ttl(path)
                
                # Prepare response data for caching
                cache_data = {
                    "content": response.body.decode() if isinstance(response.body, bytes) else response.body,
                    "status_code": response.status_code,
                    "headers": dict(response.headers),
                    "cached_at": datetime.utcnow().isoformat()
                }
                
                # Cache the response
                await cache_manager.set(
                    cache_key,
                    cache_data,
                    ttl=ttl,
                    namespace="responses"
                )
                
                performance_monitor.track_cache_operation("set", "success")
                
                # Add cache headers
                response.headers["X-Cache"] = "MISS"
                response.headers["X-Cache-TTL"] = str(ttl)
                
            except Exception as e:
                logger.warning(f"Cache set error: {e}")
                performance_monitor.track_cache_operation("set", "error")
        
        return response
    
    def _is_cacheable_path(self, path: str) -> bool:
        """Check if path is cacheable."""
        return any(path.startswith(cacheable_path) for cacheable_path in self.cacheable_paths)
    
    def _generate_cache_key(self, request: Request) -> str:
        """Generate cache key for request."""
        # Include method, path, and query parameters
        key_parts = [
            request.method,
            request.url.path,
            str(sorted(request.query_params.items()))
        ]
        
        # Include user context if available (for user-specific caching)
        if hasattr(request.state, 'user_id'):
            key_parts.append(f"user:{request.state.user_id}")
        
        return ":".join(key_parts)
    
    def _get_cache_ttl(self, path: str) -> int:
        """Get cache TTL for specific path."""
        for cached_path, ttl in self.cache_ttl.items():
            if path.startswith(cached_path):
                return ttl
        return 300  # Default 5 minutes


class CompressionMiddleware(BaseHTTPMiddleware):
    """Middleware for response compression optimization."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.compressible_types = {
            "application/json",
            "text/html",
            "text/plain",
            "text/css",
            "text/javascript",
            "application/javascript"
        }
        self.min_size = 1024  # Only compress responses larger than 1KB
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with compression optimization."""
        response = await call_next(request)
        
        # Check if compression is beneficial
        if not self._should_compress(request, response):
            return response
        
        # Add compression hint header
        response.headers["X-Compression-Eligible"] = "true"
        
        return response
    
    def _should_compress(self, request: Request, response: Response) -> bool:
        """Determine if response should be compressed."""
        # Check if client accepts compression
        accept_encoding = request.headers.get("accept-encoding", "")
        if "gzip" not in accept_encoding.lower():
            return False
        
        # Check content type
        content_type = response.headers.get("content-type", "")
        if not any(ct in content_type for ct in self.compressible_types):
            return False
        
        # Check response size
        if hasattr(response, 'body') and response.body:
            if len(response.body) < self.min_size:
                return False
        
        return True


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Enhanced rate limiting middleware with performance optimization."""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.rate_limits = {
            "/api/v1/performance/optimize": {"requests": 5, "window": 300},  # 5 requests per 5 minutes
            "/api/v1/performance/cache/clear": {"requests": 2, "window": 600},  # 2 requests per 10 minutes
            "default": {"requests": 100, "window": 60}  # 100 requests per minute
        }
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with rate limiting."""
        client_ip = request.client.host if request.client else "unknown"
        path = request.url.path
        
        # Get rate limit for this path
        rate_limit = self._get_rate_limit(path)
        
        # Check rate limit
        if await self._is_rate_limited(client_ip, path, rate_limit):
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "retry_after": rate_limit["window"]
                },
                headers={
                    "Retry-After": str(rate_limit["window"]),
                    "X-RateLimit-Limit": str(rate_limit["requests"]),
                    "X-RateLimit-Window": str(rate_limit["window"])
                }
            )
        
        return await call_next(request)
    
    def _get_rate_limit(self, path: str) -> Dict[str, int]:
        """Get rate limit configuration for path."""
        for rate_path, limit in self.rate_limits.items():
            if rate_path != "default" and path.startswith(rate_path):
                return limit
        return self.rate_limits["default"]
    
    async def _is_rate_limited(self, client_ip: str, path: str, rate_limit: Dict[str, int]) -> bool:
        """Check if client is rate limited."""
        try:
            # Use Redis for distributed rate limiting
            key = f"rate_limit:{client_ip}:{path}"
            current_requests = await cache_manager.get(key, namespace="rate_limits")
            
            if current_requests is None:
                # First request in window
                await cache_manager.set(
                    key,
                    1,
                    ttl=rate_limit["window"],
                    namespace="rate_limits"
                )
                return False
            
            if current_requests >= rate_limit["requests"]:
                return True
            
            # Increment counter
            await cache_manager.set(
                key,
                current_requests + 1,
                ttl=rate_limit["window"],
                namespace="rate_limits"
            )
            
            return False
            
        except Exception as e:
            logger.warning(f"Rate limiting error: {e}")
            # Fail open - don't block requests if rate limiting fails
            return False
