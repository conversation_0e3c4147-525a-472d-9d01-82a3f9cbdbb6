"""SLA management and escalation procedures."""

from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field


class SeverityLevel(str, Enum):
    """Vulnerability severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class EscalationLevel(str, Enum):
    """Escalation levels."""
    LEVEL_1 = "level_1"  # Team Lead
    LEVEL_2 = "level_2"  # Manager
    LEVEL_3 = "level_3"  # Executive


class EscalationStatus(str, Enum):
    """Escalation status."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    CANCELLED = "cancelled"


class SLAConfiguration(BaseModel):
    """SLA configuration for different severity levels."""
    severity: SeverityLevel
    response_time_hours: int
    resolution_time_hours: int
    escalation_intervals: List[int] = Field(default_factory=list)  # Hours between escalations
    auto_escalate: bool = True


class EscalationRule(BaseModel):
    """Escalation rule definition."""
    level: EscalationLevel
    trigger_after_hours: int
    notification_channels: List[str] = Field(default_factory=list)
    assignee_roles: List[str] = Field(default_factory=list)
    auto_assign: bool = True


class EscalationEvent(BaseModel):
    """Escalation event record."""
    id: UUID
    remediation_id: UUID
    level: EscalationLevel
    triggered_at: datetime
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    status: EscalationStatus = EscalationStatus.ACTIVE
    assignee_id: Optional[UUID] = None
    message: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SLATracker(BaseModel):
    """SLA tracking for remediation items."""
    remediation_id: UUID
    severity: SeverityLevel
    created_at: datetime
    response_due: datetime
    resolution_due: datetime
    first_response_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    sla_breached: bool = False
    breach_reason: Optional[str] = None
    escalations: List[EscalationEvent] = Field(default_factory=list)


class EscalationEngine:
    """Manages SLA tracking and escalation procedures."""
    
    def __init__(self):
        """Initialize the escalation engine."""
        self.sla_configs = self._initialize_sla_configs()
        self.escalation_rules = self._initialize_escalation_rules()
    
    def _initialize_sla_configs(self) -> Dict[SeverityLevel, SLAConfiguration]:
        """Initialize default SLA configurations."""
        return {
            SeverityLevel.CRITICAL: SLAConfiguration(
                severity=SeverityLevel.CRITICAL,
                response_time_hours=1,
                resolution_time_hours=4,
                escalation_intervals=[1, 2, 4],
                auto_escalate=True
            ),
            SeverityLevel.HIGH: SLAConfiguration(
                severity=SeverityLevel.HIGH,
                response_time_hours=4,
                resolution_time_hours=24,
                escalation_intervals=[4, 8, 12],
                auto_escalate=True
            ),
            SeverityLevel.MEDIUM: SLAConfiguration(
                severity=SeverityLevel.MEDIUM,
                response_time_hours=8,
                resolution_time_hours=72,
                escalation_intervals=[12, 24, 48],
                auto_escalate=True
            ),
            SeverityLevel.LOW: SLAConfiguration(
                severity=SeverityLevel.LOW,
                response_time_hours=24,
                resolution_time_hours=168,  # 1 week
                escalation_intervals=[48, 96, 144],
                auto_escalate=False
            ),
            SeverityLevel.INFO: SLAConfiguration(
                severity=SeverityLevel.INFO,
                response_time_hours=72,
                resolution_time_hours=336,  # 2 weeks
                escalation_intervals=[],
                auto_escalate=False
            )
        }
    
    def _initialize_escalation_rules(self) -> Dict[EscalationLevel, EscalationRule]:
        """Initialize escalation rules."""
        return {
            EscalationLevel.LEVEL_1: EscalationRule(
                level=EscalationLevel.LEVEL_1,
                trigger_after_hours=1,
                notification_channels=["email", "slack"],
                assignee_roles=["team_lead", "senior_engineer"],
                auto_assign=True
            ),
            EscalationLevel.LEVEL_2: EscalationRule(
                level=EscalationLevel.LEVEL_2,
                trigger_after_hours=4,
                notification_channels=["email", "slack", "sms"],
                assignee_roles=["manager", "director"],
                auto_assign=True
            ),
            EscalationLevel.LEVEL_3: EscalationRule(
                level=EscalationLevel.LEVEL_3,
                trigger_after_hours=8,
                notification_channels=["email", "phone", "executive_dashboard"],
                assignee_roles=["ciso", "cto", "executive"],
                auto_assign=False
            )
        }
    
    def create_sla_tracker(
        self,
        remediation_id: UUID,
        severity: SeverityLevel,
        created_at: Optional[datetime] = None
    ) -> SLATracker:
        """Create a new SLA tracker for a remediation item.
        
        Args:
            remediation_id: ID of the remediation item
            severity: Severity level
            created_at: Creation timestamp (defaults to now)
            
        Returns:
            SLA tracker instance
        """
        if created_at is None:
            created_at = datetime.utcnow()
        
        sla_config = self.sla_configs[severity]
        
        response_due = created_at + timedelta(hours=sla_config.response_time_hours)
        resolution_due = created_at + timedelta(hours=sla_config.resolution_time_hours)
        
        return SLATracker(
            remediation_id=remediation_id,
            severity=severity,
            created_at=created_at,
            response_due=response_due,
            resolution_due=resolution_due
        )
    
    def check_sla_breach(self, tracker: SLATracker, current_time: Optional[datetime] = None) -> bool:
        """Check if SLA has been breached.
        
        Args:
            tracker: SLA tracker to check
            current_time: Current timestamp (defaults to now)
            
        Returns:
            True if SLA is breached
        """
        if current_time is None:
            current_time = datetime.utcnow()
        
        # Check response SLA
        if not tracker.first_response_at and current_time > tracker.response_due:
            tracker.sla_breached = True
            tracker.breach_reason = "Response SLA exceeded"
            return True
        
        # Check resolution SLA
        if not tracker.resolved_at and current_time > tracker.resolution_due:
            tracker.sla_breached = True
            tracker.breach_reason = "Resolution SLA exceeded"
            return True
        
        return False
    
    def get_required_escalations(
        self,
        tracker: SLATracker,
        current_time: Optional[datetime] = None
    ) -> List[EscalationLevel]:
        """Get escalations that should be triggered.
        
        Args:
            tracker: SLA tracker
            current_time: Current timestamp (defaults to now)
            
        Returns:
            List of escalation levels to trigger
        """
        if current_time is None:
            current_time = datetime.utcnow()
        
        sla_config = self.sla_configs[tracker.severity]
        if not sla_config.auto_escalate:
            return []
        
        hours_elapsed = (current_time - tracker.created_at).total_seconds() / 3600
        required_escalations = []
        
        # Check which escalation intervals have been exceeded
        for i, interval_hours in enumerate(sla_config.escalation_intervals):
            if hours_elapsed >= interval_hours:
                escalation_level = list(EscalationLevel)[i]
                
                # Check if this escalation has already been triggered
                existing_escalation = next(
                    (e for e in tracker.escalations if e.level == escalation_level),
                    None
                )
                
                if not existing_escalation:
                    required_escalations.append(escalation_level)
        
        return required_escalations
    
    def trigger_escalation(
        self,
        tracker: SLATracker,
        level: EscalationLevel,
        assignee_id: Optional[UUID] = None,
        message: Optional[str] = None
    ) -> EscalationEvent:
        """Trigger an escalation event.
        
        Args:
            tracker: SLA tracker
            level: Escalation level
            assignee_id: Optional assignee ID
            message: Optional escalation message
            
        Returns:
            Escalation event
        """
        from uuid import uuid4
        
        if message is None:
            message = f"Escalation triggered for {tracker.severity} severity remediation"
        
        escalation = EscalationEvent(
            id=uuid4(),
            remediation_id=tracker.remediation_id,
            level=level,
            triggered_at=datetime.utcnow(),
            assignee_id=assignee_id,
            message=message,
            metadata={
                "severity": tracker.severity,
                "hours_elapsed": (datetime.utcnow() - tracker.created_at).total_seconds() / 3600,
                "sla_breached": tracker.sla_breached
            }
        )
        
        tracker.escalations.append(escalation)
        return escalation
    
    def acknowledge_escalation(
        self,
        tracker: SLATracker,
        escalation_id: UUID,
        acknowledger_id: UUID
    ) -> bool:
        """Acknowledge an escalation.
        
        Args:
            tracker: SLA tracker
            escalation_id: Escalation event ID
            acknowledger_id: ID of person acknowledging
            
        Returns:
            True if acknowledgment was successful
        """
        escalation = next(
            (e for e in tracker.escalations if e.id == escalation_id),
            None
        )
        
        if escalation and escalation.status == EscalationStatus.ACTIVE:
            escalation.status = EscalationStatus.ACKNOWLEDGED
            escalation.acknowledged_at = datetime.utcnow()
            escalation.assignee_id = acknowledger_id
            return True
        
        return False
    
    def resolve_escalation(
        self,
        tracker: SLATracker,
        escalation_id: UUID
    ) -> bool:
        """Resolve an escalation.
        
        Args:
            tracker: SLA tracker
            escalation_id: Escalation event ID
            
        Returns:
            True if resolution was successful
        """
        escalation = next(
            (e for e in tracker.escalations if e.id == escalation_id),
            None
        )
        
        if escalation and escalation.status in [EscalationStatus.ACTIVE, EscalationStatus.ACKNOWLEDGED]:
            escalation.status = EscalationStatus.RESOLVED
            escalation.resolved_at = datetime.utcnow()
            return True
        
        return False
    
    def get_escalation_summary(self, tracker: SLATracker) -> Dict[str, Any]:
        """Get escalation summary for a tracker.
        
        Args:
            tracker: SLA tracker
            
        Returns:
            Escalation summary
        """
        active_escalations = [e for e in tracker.escalations if e.status == EscalationStatus.ACTIVE]
        acknowledged_escalations = [e for e in tracker.escalations if e.status == EscalationStatus.ACKNOWLEDGED]
        resolved_escalations = [e for e in tracker.escalations if e.status == EscalationStatus.RESOLVED]
        
        return {
            "total_escalations": len(tracker.escalations),
            "active_escalations": len(active_escalations),
            "acknowledged_escalations": len(acknowledged_escalations),
            "resolved_escalations": len(resolved_escalations),
            "highest_escalation_level": max([e.level for e in tracker.escalations], default=None),
            "sla_breached": tracker.sla_breached,
            "breach_reason": tracker.breach_reason,
            "time_to_first_response": (
                (tracker.first_response_at - tracker.created_at).total_seconds() / 3600
                if tracker.first_response_at else None
            ),
            "time_to_resolution": (
                (tracker.resolved_at - tracker.created_at).total_seconds() / 3600
                if tracker.resolved_at else None
            )
        }
