"""PTES workflow engine and phase management."""

from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import UUID

from pydantic import BaseModel, Field


class PTESPhase(str, Enum):
    """PTES methodology phases."""
    PRE_ENGAGEMENT = "pre_engagement"
    INTELLIGENCE_GATHERING = "intelligence_gathering"
    THREAT_MODELING = "threat_modeling"
    VULNERABILITY_ANALYSIS = "vulnerability_analysis"
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    REPORTING = "reporting"


class WorkflowStatus(str, Enum):
    """Workflow status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PENDING_REVIEW = "pending_review"
    COMPLETED = "completed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class ValidationResult(BaseModel):
    """Phase validation result."""
    approved: bool
    issues: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    reviewer_id: Optional[UUID] = None
    reviewed_at: Optional[datetime] = None


class WorkflowTransition(BaseModel):
    """Workflow phase transition result."""
    success: bool
    from_phase: PTESPhase
    to_phase: Optional[PTESPhase] = None
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    transition_id: Optional[UUID] = None


class PhaseRequirement(BaseModel):
    """Requirements for completing a PTES phase."""
    phase: PTESPhase
    required_deliverables: List[str]
    quality_checks: List[str]
    approval_required: bool = False
    estimated_hours: int = 0
    dependencies: List[PTESPhase] = Field(default_factory=list)


class PTESWorkflowEngine:
    """Implements Penetration Testing Execution Standard workflow."""
    
    def __init__(self):
        """Initialize the PTES workflow engine."""
        self.phases = [
            PTESPhase.PRE_ENGAGEMENT,
            PTESPhase.INTELLIGENCE_GATHERING,
            PTESPhase.THREAT_MODELING,
            PTESPhase.VULNERABILITY_ANALYSIS,
            PTESPhase.EXPLOITATION,
            PTESPhase.POST_EXPLOITATION,
            PTESPhase.REPORTING
        ]
        
        self.phase_requirements = self._initialize_phase_requirements()
    
    def _initialize_phase_requirements(self) -> Dict[PTESPhase, PhaseRequirement]:
        """Initialize phase requirements and dependencies."""
        return {
            PTESPhase.PRE_ENGAGEMENT: PhaseRequirement(
                phase=PTESPhase.PRE_ENGAGEMENT,
                required_deliverables=[
                    "scope_document",
                    "rules_of_engagement",
                    "contact_information",
                    "timeline_agreement"
                ],
                quality_checks=[
                    "scope_clarity",
                    "legal_approval",
                    "technical_feasibility"
                ],
                approval_required=True,
                estimated_hours=8,
                dependencies=[]
            ),
            PTESPhase.INTELLIGENCE_GATHERING: PhaseRequirement(
                phase=PTESPhase.INTELLIGENCE_GATHERING,
                required_deliverables=[
                    "network_reconnaissance",
                    "service_enumeration",
                    "osint_report",
                    "target_profiling"
                ],
                quality_checks=[
                    "data_accuracy",
                    "scope_compliance",
                    "information_completeness"
                ],
                approval_required=False,
                estimated_hours=16,
                dependencies=[PTESPhase.PRE_ENGAGEMENT]
            ),
            PTESPhase.THREAT_MODELING: PhaseRequirement(
                phase=PTESPhase.THREAT_MODELING,
                required_deliverables=[
                    "attack_surface_analysis",
                    "threat_scenarios",
                    "risk_assessment",
                    "attack_vectors"
                ],
                quality_checks=[
                    "scenario_realism",
                    "risk_accuracy",
                    "coverage_completeness"
                ],
                approval_required=True,
                estimated_hours=12,
                dependencies=[PTESPhase.INTELLIGENCE_GATHERING]
            ),
            PTESPhase.VULNERABILITY_ANALYSIS: PhaseRequirement(
                phase=PTESPhase.VULNERABILITY_ANALYSIS,
                required_deliverables=[
                    "vulnerability_scan_results",
                    "manual_testing_results",
                    "vulnerability_validation",
                    "impact_assessment"
                ],
                quality_checks=[
                    "false_positive_validation",
                    "severity_accuracy",
                    "exploitability_assessment"
                ],
                approval_required=False,
                estimated_hours=24,
                dependencies=[PTESPhase.THREAT_MODELING]
            ),
            PTESPhase.EXPLOITATION: PhaseRequirement(
                phase=PTESPhase.EXPLOITATION,
                required_deliverables=[
                    "proof_of_concept",
                    "exploitation_evidence",
                    "access_documentation",
                    "impact_demonstration"
                ],
                quality_checks=[
                    "poc_reliability",
                    "evidence_quality",
                    "scope_compliance",
                    "damage_prevention"
                ],
                approval_required=True,
                estimated_hours=20,
                dependencies=[PTESPhase.VULNERABILITY_ANALYSIS]
            ),
            PTESPhase.POST_EXPLOITATION: PhaseRequirement(
                phase=PTESPhase.POST_EXPLOITATION,
                required_deliverables=[
                    "privilege_escalation",
                    "lateral_movement",
                    "data_access_proof",
                    "persistence_mechanisms"
                ],
                quality_checks=[
                    "stealth_maintenance",
                    "evidence_collection",
                    "impact_documentation"
                ],
                approval_required=True,
                estimated_hours=16,
                dependencies=[PTESPhase.EXPLOITATION]
            ),
            PTESPhase.REPORTING: PhaseRequirement(
                phase=PTESPhase.REPORTING,
                required_deliverables=[
                    "executive_summary",
                    "technical_report",
                    "remediation_recommendations",
                    "appendices"
                ],
                quality_checks=[
                    "report_clarity",
                    "technical_accuracy",
                    "recommendation_feasibility",
                    "presentation_quality"
                ],
                approval_required=True,
                estimated_hours=20,
                dependencies=[PTESPhase.POST_EXPLOITATION]
            )
        }
    
    def get_next_phase(self, current_phase: PTESPhase) -> Optional[PTESPhase]:
        """Get the next phase in the PTES workflow.
        
        Args:
            current_phase: Current PTES phase
            
        Returns:
            Next phase or None if at the end
        """
        try:
            current_index = self.phases.index(current_phase)
            if current_index < len(self.phases) - 1:
                return self.phases[current_index + 1]
            return None
        except ValueError:
            return None
    
    def get_previous_phase(self, current_phase: PTESPhase) -> Optional[PTESPhase]:
        """Get the previous phase in the PTES workflow.
        
        Args:
            current_phase: Current PTES phase
            
        Returns:
            Previous phase or None if at the beginning
        """
        try:
            current_index = self.phases.index(current_phase)
            if current_index > 0:
                return self.phases[current_index - 1]
            return None
        except ValueError:
            return None
    
    def validate_phase_completion(
        self,
        phase: PTESPhase,
        deliverables: List[str],
        quality_checks: Dict[str, bool],
        reviewer_id: Optional[UUID] = None
    ) -> ValidationResult:
        """Validate if a phase can be completed.
        
        Args:
            phase: PTES phase to validate
            deliverables: List of completed deliverables
            quality_checks: Quality check results
            reviewer_id: ID of the reviewer
            
        Returns:
            Validation result
        """
        requirements = self.phase_requirements.get(phase)
        if not requirements:
            return ValidationResult(
                approved=False,
                issues=[f"Unknown phase: {phase}"]
            )
        
        issues = []
        warnings = []
        
        # Check required deliverables
        missing_deliverables = set(requirements.required_deliverables) - set(deliverables)
        if missing_deliverables:
            issues.extend([f"Missing deliverable: {d}" for d in missing_deliverables])
        
        # Check quality requirements
        failed_checks = [
            check for check in requirements.quality_checks
            if not quality_checks.get(check, False)
        ]
        if failed_checks:
            issues.extend([f"Failed quality check: {c}" for c in failed_checks])
        
        # Check approval requirement
        if requirements.approval_required and not reviewer_id:
            issues.append("Approval required but no reviewer specified")
        
        return ValidationResult(
            approved=len(issues) == 0,
            issues=issues,
            warnings=warnings,
            reviewer_id=reviewer_id,
            reviewed_at=datetime.utcnow() if reviewer_id else None
        )
    
    def advance_phase(
        self,
        current_phase: PTESPhase,
        validation_result: ValidationResult
    ) -> WorkflowTransition:
        """Advance project to next PTES phase with validation.
        
        Args:
            current_phase: Current phase
            validation_result: Phase validation result
            
        Returns:
            Workflow transition result
        """
        if not validation_result.approved:
            return WorkflowTransition(
                success=False,
                from_phase=current_phase,
                message=f"Phase validation failed: {', '.join(validation_result.issues)}"
            )
        
        next_phase = self.get_next_phase(current_phase)
        if not next_phase:
            return WorkflowTransition(
                success=True,
                from_phase=current_phase,
                message="Project completed - no more phases"
            )
        
        return WorkflowTransition(
            success=True,
            from_phase=current_phase,
            to_phase=next_phase,
            message=f"Successfully transitioned from {current_phase} to {next_phase}"
        )
    
    def get_phase_progress(self, current_phase: PTESPhase) -> float:
        """Calculate overall project progress based on current phase.
        
        Args:
            current_phase: Current PTES phase
            
        Returns:
            Progress percentage (0.0 to 1.0)
        """
        try:
            current_index = self.phases.index(current_phase)
            return (current_index + 1) / len(self.phases)
        except ValueError:
            return 0.0
    
    def estimate_remaining_time(self, current_phase: PTESPhase) -> int:
        """Estimate remaining hours for project completion.
        
        Args:
            current_phase: Current PTES phase
            
        Returns:
            Estimated remaining hours
        """
        try:
            current_index = self.phases.index(current_phase)
            remaining_phases = self.phases[current_index + 1:]
            
            total_hours = sum(
                self.phase_requirements[phase].estimated_hours
                for phase in remaining_phases
            )
            
            return total_hours
        except ValueError:
            return 0
