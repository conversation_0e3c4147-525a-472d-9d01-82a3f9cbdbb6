"""Redis cache manager for performance optimization."""

import json
import hashlib
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
from functools import wraps
import asyncio
import structlog

import redis.asyncio as redis
from redis.asyncio.cluster import RedisCluster

from pitas.core.config import settings

logger = structlog.get_logger(__name__)


class CacheManager:
    """Redis cache manager with intelligent caching strategies."""
    
    def __init__(self):
        self._redis: Optional[Union[redis.Redis, RedisCluster]] = None
        self._connected = False
        self.default_ttl = 3600  # 1 hour
        self.key_prefix = "pitas:"
        
    async def connect(self) -> None:
        """Establish connection to Redis."""
        try:
            # Check if cluster mode is enabled
            if hasattr(settings, 'redis_cluster_nodes') and settings.redis_cluster_nodes:
                # Redis Cluster mode
                startup_nodes = [
                    {"host": node.split(':')[0], "port": int(node.split(':')[1])}
                    for node in settings.redis_cluster_nodes.split(',')
                ]
                self._redis = RedisCluster(
                    startup_nodes=startup_nodes,
                    decode_responses=True,
                    skip_full_coverage_check=True,
                    max_connections=20,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
            else:
                # Single Redis instance
                self._redis = redis.from_url(
                    settings.redis_url,
                    decode_responses=True,
                    max_connections=20,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
            
            # Test connection
            await self._redis.ping()
            self._connected = True
            logger.info("Successfully connected to Redis cache")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._connected = False
            raise
    
    async def disconnect(self) -> None:
        """Close Redis connection."""
        if self._redis:
            await self._redis.close()
            self._connected = False
            logger.info("Disconnected from Redis cache")
    
    def _generate_cache_key(self, key: str, namespace: str = "") -> str:
        """Generate cache key with prefix and namespace."""
        if namespace:
            return f"{self.key_prefix}{namespace}:{key}"
        return f"{self.key_prefix}{key}"
    
    def _serialize_value(self, value: Any) -> str:
        """Serialize value for Redis storage."""
        if isinstance(value, (str, int, float, bool)):
            return json.dumps(value)
        return json.dumps(value, default=str)
    
    def _deserialize_value(self, value: str) -> Any:
        """Deserialize value from Redis."""
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def get(self, key: str, namespace: str = "") -> Optional[Any]:
        """Get value from cache."""
        if not self._connected or not self._redis:
            return None
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            value = await self._redis.get(cache_key)
            
            if value is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return self._deserialize_value(value)
            
            logger.debug(f"Cache miss for key: {cache_key}")
            return None
            
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None, 
        namespace: str = ""
    ) -> bool:
        """Set value in cache with TTL."""
        if not self._connected or not self._redis:
            return False
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            serialized_value = self._serialize_value(value)
            ttl = ttl or self.default_ttl
            
            await self._redis.setex(cache_key, ttl, serialized_value)
            logger.debug(f"Cache set for key: {cache_key}, TTL: {ttl}")
            return True
            
        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str, namespace: str = "") -> bool:
        """Delete value from cache."""
        if not self._connected or not self._redis:
            return False
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            result = await self._redis.delete(cache_key)
            logger.debug(f"Cache delete for key: {cache_key}")
            return bool(result)
            
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str, namespace: str = "") -> bool:
        """Check if key exists in cache."""
        if not self._connected or not self._redis:
            return False
        
        try:
            cache_key = self._generate_cache_key(key, namespace)
            result = await self._redis.exists(cache_key)
            return bool(result)
            
        except Exception as e:
            logger.warning(f"Cache exists error for key {key}: {e}")
            return False
    
    async def invalidate_pattern(self, pattern: str, namespace: str = "") -> int:
        """Invalidate all keys matching pattern."""
        if not self._connected or not self._redis:
            return 0
        
        try:
            cache_pattern = self._generate_cache_key(pattern, namespace)
            keys = await self._redis.keys(cache_pattern)
            
            if keys:
                deleted = await self._redis.delete(*keys)
                logger.info(f"Invalidated {deleted} cache keys matching pattern: {cache_pattern}")
                return deleted
            
            return 0
            
        except Exception as e:
            logger.warning(f"Cache invalidate pattern error for {pattern}: {e}")
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self._connected or not self._redis:
            return {}
        
        try:
            info = await self._redis.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(
                    info.get('keyspace_hits', 0),
                    info.get('keyspace_misses', 0)
                )
            }
            
        except Exception as e:
            logger.warning(f"Cache stats error: {e}")
            return {}
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate percentage."""
        total = hits + misses
        if total == 0:
            return 0.0
        return (hits / total) * 100


class QueryCache:
    """Specialized cache for database query results."""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
        self.namespace = "queries"
        self.default_ttl = 300  # 5 minutes for query cache
    
    def _generate_query_hash(self, query: str, params: Dict[str, Any] = None) -> str:
        """Generate hash for query and parameters."""
        query_data = {
            'query': query,
            'params': params or {}
        }
        query_string = json.dumps(query_data, sort_keys=True)
        return hashlib.md5(query_string.encode()).hexdigest()
    
    async def get_cached_result(self, query: str, params: Dict[str, Any] = None) -> Optional[Any]:
        """Get cached query result."""
        query_hash = self._generate_query_hash(query, params)
        return await self.cache.get(query_hash, self.namespace)
    
    async def cache_result(
        self, 
        query: str, 
        result: Any, 
        params: Dict[str, Any] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """Cache query result."""
        query_hash = self._generate_query_hash(query, params)
        return await self.cache.set(
            query_hash, 
            result, 
            ttl or self.default_ttl, 
            self.namespace
        )
    
    async def invalidate_table_cache(self, table_name: str) -> int:
        """Invalidate all cached queries for a table."""
        pattern = f"*{table_name}*"
        return await self.cache.invalidate_pattern(pattern, self.namespace)


def cached(expire: int = 3600, namespace: str = ""):
    """Decorator for caching function results."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key from function name and arguments
            cache_key_data = {
                'func': func.__name__,
                'args': str(args),
                'kwargs': str(sorted(kwargs.items()))
            }
            cache_key = hashlib.md5(
                json.dumps(cache_key_data, sort_keys=True).encode()
            ).hexdigest()
            
            # Try to get from cache
            cached_result = await cache_manager.get(cache_key, namespace)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, expire, namespace)
            
            return result
        return wrapper
    return decorator


# Global cache manager instance
cache_manager = CacheManager()
query_cache = QueryCache(cache_manager)


async def startup_cache():
    """Initialize cache connection on startup."""
    await cache_manager.connect()


async def shutdown_cache():
    """Close cache connection on shutdown."""
    await cache_manager.disconnect()
