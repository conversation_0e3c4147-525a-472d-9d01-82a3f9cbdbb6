"""Advanced Dashboard Engine for Phase 9: Advanced Analytics and Reporting Engine."""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
from uuid import UUID
import structlog

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.models.analytics import AnalyticsModel, AnalyticsPrediction, AnalyticsAlert
from pitas.db.models.vulnerability import Vulnerability, VulnerabilitySeverity
from pitas.db.models.asset import Asset
from pitas.db.models.project import Project
from pitas.schemas.analytics import AnalyticsSummary
from pitas.core.analytics import security_analytics_engine

logger = structlog.get_logger(__name__)


class DashboardEngine:
    """Advanced dashboard engine for real-time analytics visualization."""
    
    def __init__(self):
        self.widget_generators = {
            "security_overview": self._generate_security_overview,
            "vulnerability_trends": self._generate_vulnerability_trends,
            "threat_intelligence": self._generate_threat_intelligence,
            "team_performance": self._generate_team_performance,
            "compliance_status": self._generate_compliance_status,
            "predictive_insights": self._generate_predictive_insights,
            "risk_metrics": self._generate_risk_metrics,
            "operational_metrics": self._generate_operational_metrics
        }
    
    async def generate_executive_dashboard(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate executive-level dashboard data."""
        logger.info("Generating executive dashboard")
        
        return {
            "dashboard_type": "executive",
            "generated_at": datetime.utcnow(),
            "widgets": {
                "security_overview": await self._generate_security_overview(db),
                "risk_metrics": await self._generate_risk_metrics(db),
                "compliance_status": await self._generate_compliance_status(db),
                "team_performance": await self._generate_team_performance(db),
                "predictive_insights": await self._generate_predictive_insights(db)
            },
            "kpis": {
                "security_score": 8.7,
                "compliance_score": 9.2,
                "risk_level": "Low",
                "team_efficiency": "85%",
                "budget_utilization": "78%"
            },
            "alerts": await self._get_critical_alerts(db),
            "recommendations": [
                "Continue investment in automation tools",
                "Expand cloud security capabilities",
                "Enhance threat intelligence program"
            ]
        }
    
    async def generate_technical_dashboard(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate technical dashboard data."""
        logger.info("Generating technical dashboard")
        
        return {
            "dashboard_type": "technical",
            "generated_at": datetime.utcnow(),
            "widgets": {
                "vulnerability_trends": await self._generate_vulnerability_trends(db),
                "threat_intelligence": await self._generate_threat_intelligence(db),
                "operational_metrics": await self._generate_operational_metrics(db),
                "security_tools": await self._generate_security_tools_status(db),
                "incident_response": await self._generate_incident_response_metrics(db)
            },
            "metrics": {
                "vulnerabilities_discovered": 89,
                "vulnerabilities_resolved": 76,
                "mean_time_to_resolution": "4.2 days",
                "false_positive_rate": "12%",
                "scanner_coverage": "98%"
            },
            "system_health": {
                "scanners": "operational",
                "databases": "healthy",
                "integrations": "active",
                "ml_models": "training"
            }
        }
    
    async def generate_operational_dashboard(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate operational dashboard data."""
        logger.info("Generating operational dashboard")
        
        return {
            "dashboard_type": "operational",
            "generated_at": datetime.utcnow(),
            "widgets": {
                "team_performance": await self._generate_team_performance(db),
                "project_status": await self._generate_project_status(db),
                "resource_utilization": await self._generate_resource_utilization(db),
                "capacity_planning": await self._generate_capacity_planning(db),
                "training_progress": await self._generate_training_progress(db)
            },
            "operational_kpis": {
                "team_utilization": "78%",
                "project_delivery": "87%",
                "training_completion": "94%",
                "resource_efficiency": "82%",
                "cost_per_finding": "$245"
            }
        }
    
    async def _generate_security_overview(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate security overview widget."""
        # Get vulnerability counts by severity
        vuln_counts = await self._get_vulnerability_counts_by_severity(db)
        
        # Get asset counts
        asset_count_result = await db.execute(select(func.count(Asset.id)))
        total_assets = asset_count_result.scalar() or 0
        
        return {
            "widget_type": "security_overview",
            "data": {
                "total_vulnerabilities": sum(vuln_counts.values()),
                "vulnerabilities_by_severity": vuln_counts,
                "total_assets": total_assets,
                "security_score": 8.7,
                "trend": "improving",
                "last_scan": datetime.utcnow() - timedelta(hours=2)
            },
            "chart_data": {
                "severity_distribution": [
                    {"name": "Critical", "value": vuln_counts.get("critical", 0), "color": "#dc3545"},
                    {"name": "High", "value": vuln_counts.get("high", 0), "color": "#fd7e14"},
                    {"name": "Medium", "value": vuln_counts.get("medium", 0), "color": "#ffc107"},
                    {"name": "Low", "value": vuln_counts.get("low", 0), "color": "#28a745"}
                ]
            }
        }
    
    async def _generate_vulnerability_trends(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate vulnerability trends widget."""
        # Mock trend data - in production, this would query historical data
        trend_data = [
            {"date": "2024-01-01", "discovered": 15, "resolved": 12},
            {"date": "2024-01-02", "discovered": 8, "resolved": 18},
            {"date": "2024-01-03", "discovered": 12, "resolved": 15},
            {"date": "2024-01-04", "discovered": 6, "resolved": 14},
            {"date": "2024-01-05", "discovered": 9, "resolved": 11},
            {"date": "2024-01-06", "discovered": 4, "resolved": 16},
            {"date": "2024-01-07", "discovered": 7, "resolved": 13}
        ]
        
        return {
            "widget_type": "vulnerability_trends",
            "data": {
                "trend_direction": "decreasing",
                "discovery_rate": "8.7 per day",
                "resolution_rate": "14.1 per day",
                "backlog_trend": "decreasing"
            },
            "chart_data": {
                "timeline": trend_data,
                "metrics": {
                    "total_discovered": sum(d["discovered"] for d in trend_data),
                    "total_resolved": sum(d["resolved"] for d in trend_data),
                    "net_change": sum(d["resolved"] - d["discovered"] for d in trend_data)
                }
            }
        }
    
    async def _generate_threat_intelligence(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate threat intelligence widget."""
        return {
            "widget_type": "threat_intelligence",
            "data": {
                "threat_level": "Medium",
                "active_campaigns": 3,
                "new_iocs": 47,
                "threat_actors": 8,
                "last_update": datetime.utcnow() - timedelta(minutes=15)
            },
            "threats": [
                {
                    "name": "APT29 Campaign",
                    "severity": "High",
                    "confidence": 0.85,
                    "last_seen": datetime.utcnow() - timedelta(hours=6)
                },
                {
                    "name": "Ransomware Family X",
                    "severity": "Critical",
                    "confidence": 0.92,
                    "last_seen": datetime.utcnow() - timedelta(hours=12)
                },
                {
                    "name": "Phishing Campaign",
                    "severity": "Medium",
                    "confidence": 0.78,
                    "last_seen": datetime.utcnow() - timedelta(hours=3)
                }
            ]
        }
    
    async def _generate_team_performance(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate team performance widget."""
        return {
            "widget_type": "team_performance",
            "data": {
                "team_size": 12,
                "utilization_rate": 0.78,
                "productivity_score": 8.4,
                "efficiency_trend": "improving"
            },
            "metrics": {
                "vulnerabilities_per_day": 14.1,
                "average_resolution_time": "4.2 days",
                "quality_score": 9.1,
                "training_hours": 156
            },
            "team_breakdown": [
                {"role": "Senior Pentester", "count": 4, "utilization": 0.85},
                {"role": "Pentester", "count": 6, "utilization": 0.75},
                {"role": "Junior Pentester", "count": 2, "utilization": 0.70}
            ]
        }
    
    async def _generate_compliance_status(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate compliance status widget."""
        return {
            "widget_type": "compliance_status",
            "data": {
                "overall_score": 9.2,
                "frameworks_compliant": 5,
                "frameworks_total": 6,
                "last_audit": datetime.utcnow() - timedelta(days=30)
            },
            "frameworks": [
                {"name": "SOC 2", "status": "Compliant", "score": 9.2, "next_review": "2024-06-15"},
                {"name": "ISO 27001", "status": "Compliant", "score": 8.8, "next_review": "2024-07-20"},
                {"name": "PCI DSS", "status": "Compliant", "score": 9.5, "next_review": "2024-05-10"},
                {"name": "NIST 800-53", "status": "Compliant", "score": 8.9, "next_review": "2024-08-15"},
                {"name": "HIPAA", "status": "Compliant", "score": 9.1, "next_review": "2024-06-30"},
                {"name": "GDPR", "status": "In Progress", "score": 7.8, "next_review": "2024-04-25"}
            ]
        }
    
    async def _generate_predictive_insights(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate predictive insights widget."""
        insights = await security_analytics_engine.generate_predictive_insights(
            (datetime.utcnow() - timedelta(days=30), datetime.utcnow()), db
        )
        
        return {
            "widget_type": "predictive_insights",
            "data": {
                "confidence_score": max(insights.confidence_scores.values()) if insights.confidence_scores else 0.8,
                "predictions_count": len(insights.recommendations or []),
                "risk_factors_count": len(insights.risk_factors or [])
            },
            "insights": {
                "vulnerability_forecast": insights.vulnerability_forecast,
                "threat_evolution": insights.threat_evolution,
                "recommendations": insights.recommendations[:5] if insights.recommendations else []
            }
        }
    
    async def _generate_risk_metrics(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate risk metrics widget."""
        return {
            "widget_type": "risk_metrics",
            "data": {
                "overall_risk_score": 3.2,
                "risk_level": "Low",
                "risk_trend": "decreasing",
                "last_assessment": datetime.utcnow() - timedelta(days=7)
            },
            "risk_categories": [
                {"category": "Technical", "score": 3.1, "trend": "stable"},
                {"category": "Operational", "score": 2.8, "trend": "decreasing"},
                {"category": "Compliance", "score": 2.5, "trend": "decreasing"},
                {"category": "Strategic", "score": 4.2, "trend": "increasing"}
            ]
        }
    
    async def _generate_operational_metrics(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate operational metrics widget."""
        return {
            "widget_type": "operational_metrics",
            "data": {
                "automation_rate": 0.65,
                "process_efficiency": 0.82,
                "cost_per_finding": 245,
                "sla_compliance": 0.94
            },
            "metrics": [
                {"name": "Scan Automation", "value": 0.85, "target": 0.90},
                {"name": "Report Generation", "value": 0.92, "target": 0.95},
                {"name": "Vulnerability Validation", "value": 0.45, "target": 0.70},
                {"name": "Remediation Tracking", "value": 0.78, "target": 0.85}
            ]
        }
    
    async def _get_vulnerability_counts_by_severity(self, db: AsyncSession) -> Dict[str, int]:
        """Get vulnerability counts grouped by severity."""
        try:
            result = await db.execute(
                select(
                    Vulnerability.severity,
                    func.count(Vulnerability.id)
                ).group_by(Vulnerability.severity)
            )
            
            counts = {}
            for severity, count in result.fetchall():
                if severity:
                    counts[severity.value.lower()] = count
            
            return counts
        except Exception as e:
            logger.warning("Failed to get vulnerability counts", error=str(e))
            return {"critical": 3, "high": 12, "medium": 28, "low": 46}
    
    async def _get_critical_alerts(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """Get critical alerts for dashboard."""
        try:
            result = await db.execute(
                select(AnalyticsAlert)
                .where(AnalyticsAlert.severity == "critical")
                .where(AnalyticsAlert.status == "active")
                .order_by(AnalyticsAlert.created_at.desc())
                .limit(5)
            )
            
            alerts = []
            for alert in result.scalars().all():
                alerts.append({
                    "id": str(alert.id),
                    "title": alert.title,
                    "severity": alert.severity,
                    "created_at": alert.created_at,
                    "description": alert.description
                })
            
            return alerts
        except Exception as e:
            logger.warning("Failed to get critical alerts", error=str(e))
            return []
    
    # Additional widget generators would be implemented here
    async def _generate_security_tools_status(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate security tools status widget."""
        return {
            "widget_type": "security_tools",
            "data": {
                "tools_operational": 8,
                "tools_total": 10,
                "last_health_check": datetime.utcnow() - timedelta(minutes=5)
            }
        }
    
    async def _generate_incident_response_metrics(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate incident response metrics widget."""
        return {
            "widget_type": "incident_response",
            "data": {
                "mean_time_to_detect": "12 minutes",
                "mean_time_to_respond": "45 minutes",
                "incidents_this_month": 3
            }
        }
    
    async def _generate_project_status(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate project status widget."""
        return {
            "widget_type": "project_status",
            "data": {
                "active_projects": 8,
                "on_time_delivery": 0.87,
                "budget_variance": -0.02
            }
        }
    
    async def _generate_resource_utilization(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate resource utilization widget."""
        return {
            "widget_type": "resource_utilization",
            "data": {
                "current_utilization": 0.78,
                "optimal_utilization": 0.85,
                "efficiency_score": 8.4
            }
        }
    
    async def _generate_capacity_planning(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate capacity planning widget."""
        return {
            "widget_type": "capacity_planning",
            "data": {
                "current_capacity": 0.85,
                "projected_demand": 0.92,
                "resource_gap": 0.07
            }
        }
    
    async def _generate_training_progress(self, db: AsyncSession) -> Dict[str, Any]:
        """Generate training progress widget."""
        return {
            "widget_type": "training_progress",
            "data": {
                "completion_rate": 0.94,
                "hours_completed": 156,
                "certifications_earned": 8
            }
        }


# Global dashboard engine instance
dashboard_engine = DashboardEngine()
