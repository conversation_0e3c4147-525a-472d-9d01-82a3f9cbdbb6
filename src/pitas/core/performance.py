"""Performance optimization engine for PITAS."""

import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import structlog

from sqlalchemy.ext.asyncio import AsyncSession

from pitas.core.cache import cache_manager, query_cache
from pitas.core.monitoring import performance_monitor, db_performance_monitor
from pitas.core.query_optimizer import query_optimizer, QueryAnalysis
from pitas.core.config import settings

logger = structlog.get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics snapshot."""
    timestamp: datetime
    api_response_time_p95: float
    database_query_time_avg: float
    cache_hit_rate: float
    memory_usage_percent: float
    cpu_usage_percent: float
    active_connections: int
    throughput_rps: float


@dataclass
class OptimizationResult:
    """Result of performance optimization."""
    optimization_type: str
    before_metrics: Dict[str, Any]
    after_metrics: Dict[str, Any]
    improvement_percent: float
    recommendations: List[str]


class PerformanceOptimizer:
    """Main performance optimization engine."""
    
    def __init__(self):
        self.optimization_history: List[OptimizationResult] = []
        self.performance_baseline: Optional[PerformanceMetrics] = None
        self.optimization_targets = {
            'api_response_time_p95': 0.2,  # 200ms
            'database_query_time_avg': 0.1,  # 100ms
            'cache_hit_rate': 0.8,  # 80%
            'memory_usage_percent': 0.8,  # 80%
            'cpu_usage_percent': 0.7,  # 70%
        }
        
    async def establish_baseline(self) -> PerformanceMetrics:
        """Establish performance baseline metrics."""
        logger.info("Establishing performance baseline")
        
        # Collect current metrics
        system_metrics = await performance_monitor.get_performance_summary()
        cache_stats = await cache_manager.get_stats()
        query_stats = db_performance_monitor.get_query_stats()
        
        baseline = PerformanceMetrics(
            timestamp=datetime.utcnow(),
            api_response_time_p95=0.0,  # Will be calculated from historical data
            database_query_time_avg=query_stats.get('average_duration', 0),
            cache_hit_rate=cache_stats.get('hit_rate', 0) / 100,
            memory_usage_percent=system_metrics['system']['memory']['percent'] / 100,
            cpu_usage_percent=system_metrics['system']['cpu']['percent'] / 100,
            active_connections=0,  # Will be updated from connection pool
            throughput_rps=0.0  # Will be calculated from request metrics
        )
        
        self.performance_baseline = baseline
        logger.info("Performance baseline established", baseline=baseline)
        
        return baseline
    
    async def analyze_performance(self) -> Dict[str, Any]:
        """Analyze current performance against targets."""
        current_metrics = await self.get_current_metrics()
        
        analysis = {
            'timestamp': datetime.utcnow().isoformat(),
            'current_metrics': current_metrics,
            'baseline_metrics': self.performance_baseline,
            'target_compliance': {},
            'bottlenecks': [],
            'recommendations': []
        }
        
        # Check against targets
        if current_metrics:
            for metric, target in self.optimization_targets.items():
                current_value = getattr(current_metrics, metric, 0)
                compliance = current_value <= target if 'time' in metric or 'usage' in metric else current_value >= target
                
                analysis['target_compliance'][metric] = {
                    'current': current_value,
                    'target': target,
                    'compliant': compliance,
                    'deviation_percent': ((current_value - target) / target) * 100
                }
                
                if not compliance:
                    analysis['bottlenecks'].append({
                        'metric': metric,
                        'severity': 'high' if abs((current_value - target) / target) > 0.5 else 'medium'
                    })
        
        # Generate recommendations
        analysis['recommendations'] = await self._generate_optimization_recommendations(analysis)
        
        return analysis
    
    async def optimize_vulnerability_queries(self, db: AsyncSession) -> OptimizationResult:
        """Optimize vulnerability-related database queries."""
        logger.info("Starting vulnerability query optimization")
        
        before_metrics = await self.get_current_metrics()
        
        # Common vulnerability queries to optimize
        vulnerability_queries = [
            "SELECT * FROM vulnerabilities WHERE severity = 'critical'",
            "SELECT * FROM vulnerabilities WHERE created_at > NOW() - INTERVAL '30 days'",
            "SELECT v.*, a.name FROM vulnerabilities v JOIN assets a ON v.asset_id = a.id",
        ]
        
        optimizations_applied = []
        
        for query in vulnerability_queries:
            try:
                analysis = await query_optimizer.analyze_query(db, query)
                
                if analysis.complexity_score > 50:
                    optimized_query, notes = await query_optimizer.optimize_query(db, query)
                    optimizations_applied.extend(notes['optimizations_applied'])
                    
                    # Cache frequently accessed vulnerability data
                    if 'severity' in query and 'critical' in query:
                        await self._warm_vulnerability_cache(db)
                
            except Exception as e:
                logger.error(f"Failed to optimize query: {e}")
        
        after_metrics = await self.get_current_metrics()
        
        improvement = self._calculate_improvement(before_metrics, after_metrics)
        
        result = OptimizationResult(
            optimization_type="vulnerability_queries",
            before_metrics=before_metrics.__dict__ if before_metrics else {},
            after_metrics=after_metrics.__dict__ if after_metrics else {},
            improvement_percent=improvement,
            recommendations=optimizations_applied
        )
        
        self.optimization_history.append(result)
        
        logger.info("Vulnerability query optimization completed", improvement=improvement)
        return result
    
    async def optimize_cache_strategy(self) -> OptimizationResult:
        """Optimize caching strategy for better performance."""
        logger.info("Starting cache strategy optimization")
        
        before_metrics = await self.get_current_metrics()
        cache_stats_before = await cache_manager.get_stats()
        
        optimizations = []
        
        # Implement intelligent cache warming
        await self._warm_critical_caches()
        optimizations.append("Warmed critical data caches")
        
        # Optimize cache TTL based on access patterns
        await self._optimize_cache_ttl()
        optimizations.append("Optimized cache TTL values")
        
        # Clean up stale cache entries
        cleaned_keys = await cache_manager.invalidate_pattern("*:expired:*")
        if cleaned_keys > 0:
            optimizations.append(f"Cleaned {cleaned_keys} stale cache entries")
        
        after_metrics = await self.get_current_metrics()
        improvement = self._calculate_improvement(before_metrics, after_metrics)
        
        result = OptimizationResult(
            optimization_type="cache_strategy",
            before_metrics=before_metrics.__dict__ if before_metrics else {},
            after_metrics=after_metrics.__dict__ if after_metrics else {},
            improvement_percent=improvement,
            recommendations=optimizations
        )
        
        self.optimization_history.append(result)
        
        logger.info("Cache strategy optimization completed", improvement=improvement)
        return result
    
    async def _warm_vulnerability_cache(self, db: AsyncSession):
        """Warm cache with frequently accessed vulnerability data."""
        try:
            # Cache critical vulnerabilities
            critical_vulns_query = """
            SELECT id, title, severity, status, created_at 
            FROM vulnerabilities 
            WHERE severity = 'critical' 
            AND status IN ('open', 'in_progress')
            ORDER BY created_at DESC 
            LIMIT 100
            """
            
            result = await db.execute(critical_vulns_query)
            critical_vulns = result.fetchall()
            
            await cache_manager.set(
                "critical_vulnerabilities",
                [dict(row) for row in critical_vulns],
                ttl=300,  # 5 minutes
                namespace="vulnerabilities"
            )
            
            logger.debug("Warmed vulnerability cache with critical vulnerabilities")
            
        except Exception as e:
            logger.error(f"Failed to warm vulnerability cache: {e}")
    
    async def _warm_critical_caches(self):
        """Warm caches with critical application data."""
        try:
            # This would be implemented based on application-specific data
            # For now, we'll simulate cache warming
            critical_data = {
                'user_permissions': {},
                'system_config': {},
                'active_projects': []
            }
            
            for key, data in critical_data.items():
                await cache_manager.set(
                    key,
                    data,
                    ttl=3600,  # 1 hour
                    namespace="critical"
                )
            
            logger.debug("Warmed critical application caches")
            
        except Exception as e:
            logger.error(f"Failed to warm critical caches: {e}")
    
    async def _optimize_cache_ttl(self):
        """Optimize cache TTL values based on access patterns."""
        try:
            # This would analyze cache access patterns and adjust TTL
            # For now, we'll implement basic optimization
            
            # Extend TTL for frequently accessed data
            cache_stats = await cache_manager.get_stats()
            hit_rate = cache_stats.get('hit_rate', 0)
            
            if hit_rate > 80:
                # High hit rate - extend TTL for stable data
                cache_manager.default_ttl = 7200  # 2 hours
            elif hit_rate < 50:
                # Low hit rate - reduce TTL to keep cache fresh
                cache_manager.default_ttl = 1800  # 30 minutes
            
            logger.debug("Optimized cache TTL values", hit_rate=hit_rate)
            
        except Exception as e:
            logger.error(f"Failed to optimize cache TTL: {e}")
    
    async def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """Get current performance metrics."""
        try:
            system_metrics = await performance_monitor.get_performance_summary()
            cache_stats = await cache_manager.get_stats()
            query_stats = db_performance_monitor.get_query_stats()
            
            return PerformanceMetrics(
                timestamp=datetime.utcnow(),
                api_response_time_p95=0.0,  # Would be calculated from request metrics
                database_query_time_avg=query_stats.get('average_duration', 0),
                cache_hit_rate=cache_stats.get('hit_rate', 0) / 100,
                memory_usage_percent=system_metrics['system']['memory']['percent'] / 100,
                cpu_usage_percent=system_metrics['system']['cpu']['percent'] / 100,
                active_connections=0,  # Would be from connection pool
                throughput_rps=0.0  # Would be calculated from request metrics
            )
            
        except Exception as e:
            logger.error(f"Failed to get current metrics: {e}")
            return None
    
    def _calculate_improvement(
        self, 
        before: Optional[PerformanceMetrics], 
        after: Optional[PerformanceMetrics]
    ) -> float:
        """Calculate performance improvement percentage."""
        if not before or not after:
            return 0.0
        
        # Calculate improvement in key metrics
        improvements = []
        
        if before.database_query_time_avg > 0:
            db_improvement = (before.database_query_time_avg - after.database_query_time_avg) / before.database_query_time_avg
            improvements.append(db_improvement)
        
        cache_improvement = (after.cache_hit_rate - before.cache_hit_rate) / max(before.cache_hit_rate, 0.01)
        improvements.append(cache_improvement)
        
        if before.memory_usage_percent > 0:
            memory_improvement = (before.memory_usage_percent - after.memory_usage_percent) / before.memory_usage_percent
            improvements.append(memory_improvement)
        
        return (sum(improvements) / len(improvements)) * 100 if improvements else 0.0
    
    async def _generate_optimization_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate optimization recommendations based on analysis."""
        recommendations = []
        
        bottlenecks = analysis.get('bottlenecks', [])
        
        for bottleneck in bottlenecks:
            metric = bottleneck['metric']
            severity = bottleneck['severity']
            
            if metric == 'api_response_time_p95':
                recommendations.append("Consider implementing response caching and query optimization")
            elif metric == 'database_query_time_avg':
                recommendations.append("Analyze slow queries and add appropriate database indexes")
            elif metric == 'cache_hit_rate':
                recommendations.append("Review cache strategy and implement intelligent cache warming")
            elif metric == 'memory_usage_percent':
                recommendations.append("Investigate memory leaks and optimize data structures")
            elif metric == 'cpu_usage_percent':
                recommendations.append("Profile CPU-intensive operations and consider horizontal scaling")
        
        # Add general recommendations
        if len(bottlenecks) > 2:
            recommendations.append("Consider implementing auto-scaling to handle increased load")
        
        return recommendations
    
    async def get_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization report."""
        current_analysis = await self.analyze_performance()
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'current_analysis': current_analysis,
            'optimization_history': [
                {
                    'type': opt.optimization_type,
                    'improvement': opt.improvement_percent,
                    'recommendations_count': len(opt.recommendations)
                }
                for opt in self.optimization_history[-10:]  # Last 10 optimizations
            ],
            'baseline_metrics': self.performance_baseline.__dict__ if self.performance_baseline else None,
            'targets': self.optimization_targets,
            'overall_health_score': self._calculate_health_score(current_analysis)
        }
    
    def _calculate_health_score(self, analysis: Dict[str, Any]) -> float:
        """Calculate overall system health score (0-100)."""
        compliance = analysis.get('target_compliance', {})
        
        if not compliance:
            return 50.0  # Neutral score if no data
        
        compliant_metrics = sum(1 for metric_data in compliance.values() if metric_data['compliant'])
        total_metrics = len(compliance)
        
        base_score = (compliant_metrics / total_metrics) * 100
        
        # Adjust for severity of deviations
        severe_deviations = sum(
            1 for metric_data in compliance.values() 
            if not metric_data['compliant'] and abs(metric_data['deviation_percent']) > 50
        )
        
        penalty = severe_deviations * 10
        
        return max(0, base_score - penalty)


# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()
