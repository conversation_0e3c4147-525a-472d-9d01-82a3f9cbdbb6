"""Advanced Alerting System for Phase 9: Advanced Analytics and Reporting Engine."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from uuid import UUID
import structlog

from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.db.models.analytics import AnalyticsAlert, AnalyticsMetric
from pitas.db.models.vulnerability import Vulnerability, VulnerabilitySeverity
from pitas.schemas.analytics import (
    AnalyticsAlertCreate, AlertSeverity, AlertStatus,
    AnalyticsAlertResponse
)
from pitas.services.analytics import AnalyticsService

logger = structlog.get_logger(__name__)


class AlertingEngine:
    """Advanced alerting engine with intelligent escalation and correlation."""
    
    def __init__(self):
        self.alert_rules = {}
        self.escalation_policies = {}
        self.notification_channels = {}
        self.correlation_rules = {}
        
        # Initialize default alert rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default alerting rules."""
        self.alert_rules = {
            "critical_vulnerability_discovered": {
                "condition": lambda data: data.get("severity") == "critical",
                "severity": AlertSeverity.CRITICAL,
                "title": "Critical Vulnerability Discovered",
                "escalation_minutes": 15,
                "auto_escalate": True
            },
            "sla_breach_imminent": {
                "condition": lambda data: data.get("time_remaining_hours", 24) < 4,
                "severity": AlertSeverity.HIGH,
                "title": "SLA Breach Imminent",
                "escalation_minutes": 30,
                "auto_escalate": True
            },
            "performance_anomaly": {
                "condition": lambda data: data.get("performance_score", 1.0) < 0.5,
                "severity": AlertSeverity.MEDIUM,
                "title": "Performance Anomaly Detected",
                "escalation_minutes": 60,
                "auto_escalate": False
            },
            "compliance_deviation": {
                "condition": lambda data: data.get("compliance_score", 1.0) < 0.8,
                "severity": AlertSeverity.HIGH,
                "title": "Compliance Deviation Detected",
                "escalation_minutes": 45,
                "auto_escalate": True
            },
            "team_utilization_low": {
                "condition": lambda data: data.get("utilization_rate", 1.0) < 0.6,
                "severity": AlertSeverity.LOW,
                "title": "Team Utilization Below Threshold",
                "escalation_minutes": 120,
                "auto_escalate": False
            },
            "model_accuracy_degraded": {
                "condition": lambda data: data.get("accuracy", 1.0) < 0.7,
                "severity": AlertSeverity.MEDIUM,
                "title": "ML Model Accuracy Degraded",
                "escalation_minutes": 90,
                "auto_escalate": False
            }
        }
        
        self.escalation_policies = {
            AlertSeverity.CRITICAL: {
                "levels": [
                    {"minutes": 0, "recipients": ["security-team", "on-call"]},
                    {"minutes": 15, "recipients": ["security-manager", "ciso"]},
                    {"minutes": 30, "recipients": ["executive-team"]}
                ]
            },
            AlertSeverity.HIGH: {
                "levels": [
                    {"minutes": 0, "recipients": ["security-team"]},
                    {"minutes": 30, "recipients": ["security-manager"]},
                    {"minutes": 60, "recipients": ["ciso"]}
                ]
            },
            AlertSeverity.MEDIUM: {
                "levels": [
                    {"minutes": 0, "recipients": ["security-team"]},
                    {"minutes": 60, "recipients": ["security-manager"]}
                ]
            },
            AlertSeverity.LOW: {
                "levels": [
                    {"minutes": 0, "recipients": ["security-team"]}
                ]
            }
        }
    
    async def evaluate_conditions(self, db: AsyncSession) -> List[AnalyticsAlertResponse]:
        """Evaluate all alerting conditions and generate alerts."""
        logger.info("Evaluating alerting conditions")
        
        alerts_created = []
        
        # Check vulnerability-based conditions
        vuln_alerts = await self._check_vulnerability_conditions(db)
        alerts_created.extend(vuln_alerts)
        
        # Check performance conditions
        perf_alerts = await self._check_performance_conditions(db)
        alerts_created.extend(perf_alerts)
        
        # Check compliance conditions
        compliance_alerts = await self._check_compliance_conditions(db)
        alerts_created.extend(compliance_alerts)
        
        # Check team utilization conditions
        team_alerts = await self._check_team_conditions(db)
        alerts_created.extend(team_alerts)
        
        # Check ML model conditions
        model_alerts = await self._check_model_conditions(db)
        alerts_created.extend(model_alerts)
        
        logger.info("Alert evaluation completed", alerts_created=len(alerts_created))
        return alerts_created
    
    async def _check_vulnerability_conditions(self, db: AsyncSession) -> List[AnalyticsAlertResponse]:
        """Check vulnerability-related alerting conditions."""
        alerts = []
        
        try:
            # Check for new critical vulnerabilities
            critical_vulns_result = await db.execute(
                select(func.count(Vulnerability.id))
                .where(Vulnerability.severity == VulnerabilitySeverity.CRITICAL)
                .where(Vulnerability.created_at >= datetime.utcnow() - timedelta(hours=1))
            )
            critical_count = critical_vulns_result.scalar() or 0
            
            if critical_count > 0:
                alert_data = AnalyticsAlertCreate(
                    alert_type="critical_vulnerability_discovered",
                    severity=AlertSeverity.CRITICAL,
                    title=f"{critical_count} Critical Vulnerabilities Discovered",
                    description=f"Detected {critical_count} new critical vulnerabilities in the last hour",
                    trigger_condition={"rule": "critical_vulnerability_discovered"},
                    actual_value=float(critical_count),
                    threshold_value=0.0,
                    entity_type="vulnerability"
                )
                
                service = AnalyticsService(db)
                alert = await service.create_alert(alert_data)
                alerts.append(alert)
                
                # Trigger immediate escalation for critical vulnerabilities
                await self._trigger_escalation(alert, db)
        
        except Exception as e:
            logger.error("Error checking vulnerability conditions", error=str(e))
        
        return alerts
    
    async def _check_performance_conditions(self, db: AsyncSession) -> List[AnalyticsAlertResponse]:
        """Check performance-related alerting conditions."""
        alerts = []
        
        try:
            # Mock performance check - in production, this would query actual metrics
            performance_score = 0.45  # Simulated low performance
            
            if performance_score < 0.5:
                alert_data = AnalyticsAlertCreate(
                    alert_type="performance_anomaly",
                    severity=AlertSeverity.MEDIUM,
                    title="System Performance Anomaly Detected",
                    description=f"System performance score ({performance_score:.2f}) is below threshold",
                    trigger_condition={"rule": "performance_anomaly", "threshold": 0.5},
                    actual_value=performance_score,
                    threshold_value=0.5,
                    entity_type="system"
                )
                
                service = AnalyticsService(db)
                alert = await service.create_alert(alert_data)
                alerts.append(alert)
        
        except Exception as e:
            logger.error("Error checking performance conditions", error=str(e))
        
        return alerts
    
    async def _check_compliance_conditions(self, db: AsyncSession) -> List[AnalyticsAlertResponse]:
        """Check compliance-related alerting conditions."""
        alerts = []
        
        try:
            # Mock compliance check
            compliance_score = 0.75  # Simulated low compliance
            
            if compliance_score < 0.8:
                alert_data = AnalyticsAlertCreate(
                    alert_type="compliance_deviation",
                    severity=AlertSeverity.HIGH,
                    title="Compliance Score Below Threshold",
                    description=f"Overall compliance score ({compliance_score:.2f}) is below acceptable threshold",
                    trigger_condition={"rule": "compliance_deviation", "threshold": 0.8},
                    actual_value=compliance_score,
                    threshold_value=0.8,
                    entity_type="compliance"
                )
                
                service = AnalyticsService(db)
                alert = await service.create_alert(alert_data)
                alerts.append(alert)
        
        except Exception as e:
            logger.error("Error checking compliance conditions", error=str(e))
        
        return alerts
    
    async def _check_team_conditions(self, db: AsyncSession) -> List[AnalyticsAlertResponse]:
        """Check team utilization conditions."""
        alerts = []
        
        try:
            # Mock team utilization check
            utilization_rate = 0.55  # Simulated low utilization
            
            if utilization_rate < 0.6:
                alert_data = AnalyticsAlertCreate(
                    alert_type="team_utilization_low",
                    severity=AlertSeverity.LOW,
                    title="Team Utilization Below Optimal",
                    description=f"Team utilization rate ({utilization_rate:.2f}) is below optimal threshold",
                    trigger_condition={"rule": "team_utilization_low", "threshold": 0.6},
                    actual_value=utilization_rate,
                    threshold_value=0.6,
                    entity_type="team"
                )
                
                service = AnalyticsService(db)
                alert = await service.create_alert(alert_data)
                alerts.append(alert)
        
        except Exception as e:
            logger.error("Error checking team conditions", error=str(e))
        
        return alerts
    
    async def _check_model_conditions(self, db: AsyncSession) -> List[AnalyticsAlertResponse]:
        """Check ML model performance conditions."""
        alerts = []
        
        try:
            # Check for models with degraded accuracy
            from pitas.db.models.analytics import AnalyticsModel
            
            result = await db.execute(
                select(AnalyticsModel)
                .where(AnalyticsModel.is_active == True)
                .where(AnalyticsModel.accuracy_score < 0.7)
            )
            
            degraded_models = result.scalars().all()
            
            for model in degraded_models:
                alert_data = AnalyticsAlertCreate(
                    alert_type="model_accuracy_degraded",
                    severity=AlertSeverity.MEDIUM,
                    title=f"ML Model Accuracy Degraded: {model.name}",
                    description=f"Model {model.name} accuracy ({model.accuracy_score:.2f}) is below threshold",
                    trigger_condition={"rule": "model_accuracy_degraded", "threshold": 0.7},
                    actual_value=model.accuracy_score or 0.0,
                    threshold_value=0.7,
                    entity_type="model",
                    entity_id=model.id
                )
                
                service = AnalyticsService(db)
                alert = await service.create_alert(alert_data)
                alerts.append(alert)
        
        except Exception as e:
            logger.error("Error checking model conditions", error=str(e))
        
        return alerts
    
    async def _trigger_escalation(self, alert: AnalyticsAlertResponse, db: AsyncSession):
        """Trigger alert escalation based on severity and policies."""
        logger.info("Triggering alert escalation", alert_id=alert.id, severity=alert.severity)
        
        escalation_policy = self.escalation_policies.get(alert.severity)
        if not escalation_policy:
            return
        
        # Schedule escalation levels
        for level in escalation_policy["levels"]:
            delay_minutes = level["minutes"]
            recipients = level["recipients"]
            
            # In production, this would schedule actual notifications
            logger.info(
                "Scheduling escalation",
                alert_id=alert.id,
                delay_minutes=delay_minutes,
                recipients=recipients
            )
            
            # For now, just log the escalation
            if delay_minutes == 0:
                await self._send_notification(alert, recipients)
    
    async def _send_notification(self, alert: AnalyticsAlertResponse, recipients: List[str]):
        """Send notification to specified recipients."""
        logger.info(
            "Sending alert notification",
            alert_id=alert.id,
            title=alert.title,
            recipients=recipients
        )
        
        # In production, this would integrate with:
        # - Email systems (SMTP, SendGrid, etc.)
        # - Slack/Teams webhooks
        # - SMS services (Twilio, etc.)
        # - PagerDuty/OpsGenie
        # - Custom webhook endpoints
        
        notification_data = {
            "alert_id": str(alert.id),
            "title": alert.title,
            "severity": alert.severity,
            "description": alert.description,
            "created_at": alert.created_at,
            "recipients": recipients
        }
        
        # Mock notification sending
        for recipient in recipients:
            logger.info("Notification sent", recipient=recipient, alert_data=notification_data)
    
    async def correlate_alerts(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """Correlate related alerts to reduce noise."""
        logger.info("Correlating alerts")
        
        # Get recent active alerts
        result = await db.execute(
            select(AnalyticsAlert)
            .where(AnalyticsAlert.status == AlertStatus.ACTIVE)
            .where(AnalyticsAlert.created_at >= datetime.utcnow() - timedelta(hours=24))
            .order_by(AnalyticsAlert.created_at.desc())
        )
        
        alerts = result.scalars().all()
        
        # Group alerts by type and entity
        correlations = []
        alert_groups = {}
        
        for alert in alerts:
            key = f"{alert.alert_type}_{alert.entity_type}_{alert.entity_id or 'global'}"
            if key not in alert_groups:
                alert_groups[key] = []
            alert_groups[key].append(alert)
        
        # Identify correlated alert groups
        for group_key, group_alerts in alert_groups.items():
            if len(group_alerts) > 1:
                correlations.append({
                    "correlation_id": f"corr_{group_key}_{datetime.utcnow().timestamp()}",
                    "alert_count": len(group_alerts),
                    "alert_type": group_alerts[0].alert_type,
                    "entity_type": group_alerts[0].entity_type,
                    "severity": max(alert.severity for alert in group_alerts),
                    "first_occurrence": min(alert.created_at for alert in group_alerts),
                    "last_occurrence": max(alert.created_at for alert in group_alerts),
                    "alert_ids": [str(alert.id) for alert in group_alerts]
                })
        
        logger.info("Alert correlation completed", correlations_found=len(correlations))
        return correlations
    
    async def suppress_duplicate_alerts(self, db: AsyncSession, alert_data: AnalyticsAlertCreate) -> bool:
        """Check if similar alert already exists and suppress if needed."""
        # Look for similar alerts in the last hour
        result = await db.execute(
            select(AnalyticsAlert)
            .where(AnalyticsAlert.alert_type == alert_data.alert_type)
            .where(AnalyticsAlert.entity_type == alert_data.entity_type)
            .where(AnalyticsAlert.entity_id == alert_data.entity_id)
            .where(AnalyticsAlert.status == AlertStatus.ACTIVE)
            .where(AnalyticsAlert.created_at >= datetime.utcnow() - timedelta(hours=1))
        )
        
        existing_alerts = result.scalars().all()
        
        if existing_alerts:
            logger.info(
                "Suppressing duplicate alert",
                alert_type=alert_data.alert_type,
                existing_count=len(existing_alerts)
            )
            return True
        
        return False
    
    def add_custom_rule(self, rule_name: str, rule_config: Dict[str, Any]):
        """Add custom alerting rule."""
        self.alert_rules[rule_name] = rule_config
        logger.info("Custom alert rule added", rule_name=rule_name)
    
    def remove_rule(self, rule_name: str):
        """Remove alerting rule."""
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            logger.info("Alert rule removed", rule_name=rule_name)


# Global alerting engine instance
alerting_engine = AlertingEngine()
