"""Database query optimization and analysis."""

import re
import time
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import structlog

from sqlalchemy import text, inspect
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.engine import Result

from pitas.core.cache import query_cache
from pitas.core.monitoring import db_performance_monitor

logger = structlog.get_logger(__name__)


@dataclass
class QueryAnalysis:
    """Query analysis results."""
    query_hash: str
    execution_time: float
    rows_examined: int
    rows_returned: int
    index_usage: List[str]
    recommendations: List[str]
    complexity_score: int
    cache_hit: bool


@dataclass
class IndexRecommendation:
    """Database index recommendation."""
    table_name: str
    columns: List[str]
    index_type: str
    estimated_benefit: float
    reason: str


class DatabaseQueryOptimizer:
    """Database query optimization and analysis engine."""
    
    def __init__(self):
        self.query_patterns = {
            'select_all': re.compile(r'SELECT\s+\*\s+FROM', re.IGNORECASE),
            'no_where': re.compile(r'SELECT.*FROM\s+\w+(?:\s+\w+)?\s*(?:ORDER|GROUP|LIMIT|$)', re.IGNORECASE),
            'no_limit': re.compile(r'SELECT.*FROM.*WHERE.*(?!.*LIMIT)', re.IGNORECASE),
            'cartesian_join': re.compile(r'FROM\s+\w+\s*,\s*\w+', re.IGNORECASE),
            'subquery_in_select': re.compile(r'SELECT.*\(.*SELECT.*\).*FROM', re.IGNORECASE),
            'function_in_where': re.compile(r'WHERE.*\w+\s*\(.*\)\s*[=<>]', re.IGNORECASE)
        }
        
        self.optimization_cache = {}
        self.slow_query_threshold = 1.0  # 1 second
        
    async def analyze_query(
        self, 
        db: AsyncSession, 
        query: str, 
        params: Dict[str, Any] = None
    ) -> QueryAnalysis:
        """Analyze query performance and provide optimization recommendations."""
        query_hash = self._generate_query_hash(query, params)
        
        # Check cache first
        cached_result = await query_cache.get_cached_result(query, params)
        cache_hit = cached_result is not None
        
        start_time = time.time()
        
        try:
            # Execute query with EXPLAIN ANALYZE
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            result = await db.execute(text(explain_query), params or {})
            explain_data = result.fetchone()[0]
            
            execution_time = time.time() - start_time
            
            # Parse execution plan
            plan = explain_data[0]['Plan']
            
            analysis = QueryAnalysis(
                query_hash=query_hash,
                execution_time=execution_time,
                rows_examined=plan.get('Actual Rows', 0),
                rows_returned=plan.get('Actual Rows', 0),
                index_usage=self._extract_index_usage(plan),
                recommendations=self._generate_recommendations(query, plan),
                complexity_score=self._calculate_complexity_score(query, plan),
                cache_hit=cache_hit
            )
            
            # Track performance
            db_performance_monitor.track_query(query, execution_time, params)
            
            # Cache analysis results
            self.optimization_cache[query_hash] = analysis
            
            return analysis
            
        except Exception as e:
            logger.error(f"Query analysis failed: {e}")
            return QueryAnalysis(
                query_hash=query_hash,
                execution_time=time.time() - start_time,
                rows_examined=0,
                rows_returned=0,
                index_usage=[],
                recommendations=[f"Analysis failed: {str(e)}"],
                complexity_score=100,
                cache_hit=cache_hit
            )
    
    def _generate_query_hash(self, query: str, params: Dict[str, Any] = None) -> str:
        """Generate unique hash for query and parameters."""
        query_data = f"{query}:{params or {}}"
        return hashlib.md5(query_data.encode()).hexdigest()
    
    def _extract_index_usage(self, plan: Dict[str, Any]) -> List[str]:
        """Extract index usage information from execution plan."""
        indexes = []
        
        def extract_from_node(node):
            if 'Index Name' in node:
                indexes.append(node['Index Name'])
            
            if 'Plans' in node:
                for child_plan in node['Plans']:
                    extract_from_node(child_plan)
        
        extract_from_node(plan)
        return indexes
    
    def _generate_recommendations(self, query: str, plan: Dict[str, Any]) -> List[str]:
        """Generate optimization recommendations based on query analysis."""
        recommendations = []
        
        # Check for common anti-patterns
        if self.query_patterns['select_all'].search(query):
            recommendations.append("Avoid SELECT * - specify only needed columns")
        
        if self.query_patterns['no_where'].search(query):
            recommendations.append("Consider adding WHERE clause to limit results")
        
        if self.query_patterns['no_limit'].search(query):
            recommendations.append("Consider adding LIMIT clause for large result sets")
        
        if self.query_patterns['cartesian_join'].search(query):
            recommendations.append("Potential cartesian join detected - use explicit JOIN syntax")
        
        if self.query_patterns['function_in_where'].search(query):
            recommendations.append("Avoid functions in WHERE clause - consider functional indexes")
        
        # Analyze execution plan
        if plan.get('Actual Total Time', 0) > self.slow_query_threshold * 1000:
            recommendations.append("Query execution time is high - consider optimization")
        
        if 'Seq Scan' in plan.get('Node Type', ''):
            recommendations.append("Sequential scan detected - consider adding appropriate indexes")
        
        if plan.get('Actual Rows', 0) > 10000:
            recommendations.append("Large result set - consider pagination or filtering")
        
        # Check for nested loops with high cost
        if plan.get('Node Type') == 'Nested Loop' and plan.get('Total Cost', 0) > 1000:
            recommendations.append("Expensive nested loop - consider hash join or merge join")
        
        return recommendations
    
    def _calculate_complexity_score(self, query: str, plan: Dict[str, Any]) -> int:
        """Calculate query complexity score (0-100)."""
        score = 0
        
        # Base complexity from query structure
        if 'JOIN' in query.upper():
            score += 20
        if 'SUBQUERY' in query.upper() or '(' in query:
            score += 15
        if 'GROUP BY' in query.upper():
            score += 10
        if 'ORDER BY' in query.upper():
            score += 5
        
        # Execution plan complexity
        total_cost = plan.get('Total Cost', 0)
        if total_cost > 1000:
            score += 30
        elif total_cost > 100:
            score += 15
        elif total_cost > 10:
            score += 5
        
        # Rows processed
        rows = plan.get('Actual Rows', 0)
        if rows > 100000:
            score += 20
        elif rows > 10000:
            score += 10
        elif rows > 1000:
            score += 5
        
        return min(score, 100)
    
    async def optimize_query(
        self, 
        db: AsyncSession, 
        query: str, 
        params: Dict[str, Any] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Optimize query and return optimized version."""
        analysis = await self.analyze_query(db, query, params)
        
        optimized_query = query
        optimization_notes = {
            'original_complexity': analysis.complexity_score,
            'optimizations_applied': []
        }
        
        # Apply basic optimizations
        if self.query_patterns['select_all'].search(query):
            # This would require schema knowledge to implement properly
            optimization_notes['optimizations_applied'].append(
                "SELECT * detected - manual column specification recommended"
            )
        
        # Add LIMIT if missing and no aggregation
        if (self.query_patterns['no_limit'].search(query) and 
            'GROUP BY' not in query.upper() and 
            'COUNT(' not in query.upper()):
            if 'ORDER BY' in query.upper():
                optimized_query = query + ' LIMIT 1000'
            else:
                optimized_query = query + ' LIMIT 1000'
            optimization_notes['optimizations_applied'].append("Added LIMIT clause")
        
        return optimized_query, optimization_notes
    
    async def get_index_recommendations(
        self, 
        db: AsyncSession, 
        table_name: str = None
    ) -> List[IndexRecommendation]:
        """Generate index recommendations based on query patterns."""
        recommendations = []
        
        try:
            # Get table statistics
            if table_name:
                tables = [table_name]
            else:
                # Get all tables
                inspector = inspect(db.bind)
                tables = inspector.get_table_names()
            
            for table in tables:
                # Analyze missing indexes
                missing_indexes_query = """
                SELECT 
                    schemaname,
                    tablename,
                    attname,
                    n_distinct,
                    correlation
                FROM pg_stats 
                WHERE tablename = :table_name
                AND n_distinct > 100
                ORDER BY n_distinct DESC
                """
                
                result = await db.execute(
                    text(missing_indexes_query), 
                    {'table_name': table}
                )
                
                for row in result:
                    if row.n_distinct > 1000:  # High cardinality column
                        recommendations.append(IndexRecommendation(
                            table_name=table,
                            columns=[row.attname],
                            index_type='btree',
                            estimated_benefit=min(row.n_distinct / 10000, 1.0),
                            reason=f"High cardinality column ({row.n_distinct} distinct values)"
                        ))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Failed to generate index recommendations: {e}")
            return []
    
    async def get_optimization_stats(self) -> Dict[str, Any]:
        """Get query optimization statistics."""
        total_queries = len(self.optimization_cache)
        
        if total_queries == 0:
            return {
                'total_queries_analyzed': 0,
                'average_complexity': 0,
                'slow_queries': 0,
                'cache_hit_rate': 0
            }
        
        complexities = [analysis.complexity_score for analysis in self.optimization_cache.values()]
        slow_queries = sum(1 for analysis in self.optimization_cache.values() 
                          if analysis.execution_time > self.slow_query_threshold)
        cache_hits = sum(1 for analysis in self.optimization_cache.values() 
                        if analysis.cache_hit)
        
        return {
            'total_queries_analyzed': total_queries,
            'average_complexity': sum(complexities) / len(complexities),
            'slow_queries': slow_queries,
            'cache_hit_rate': (cache_hits / total_queries) * 100,
            'recommendations_generated': sum(
                len(analysis.recommendations) for analysis in self.optimization_cache.values()
            )
        }


# Global query optimizer instance
query_optimizer = DatabaseQueryOptimizer()
