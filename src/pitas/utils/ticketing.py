"""External ticketing system integrations."""

import json
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from uuid import UUID

import httpx
from pydantic import BaseModel

from pitas.core.config import settings
from pitas.db.models.remediation import TicketingSystem
from pitas.schemas.remediation import TicketingIntegrationRequest, TicketingIntegrationResponse


class TicketData(BaseModel):
    """Standard ticket data structure."""
    title: str
    description: str
    priority: str
    severity: str
    assignee: Optional[str] = None
    labels: List[str] = []
    custom_fields: Dict[str, Any] = {}


class TicketingIntegration(ABC):
    """Abstract base class for ticketing system integrations."""
    
    @abstractmethod
    async def create_ticket(self, ticket_data: TicketData) -> TicketingIntegrationResponse:
        """Create a ticket in the external system.
        
        Args:
            ticket_data: Ticket data to create
            
        Returns:
            Integration response with ticket details
        """
        pass
    
    @abstractmethod
    async def update_ticket(self, ticket_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing ticket.
        
        Args:
            ticket_id: External ticket ID
            updates: Fields to update
            
        Returns:
            True if update was successful
        """
        pass
    
    @abstractmethod
    async def get_ticket_status(self, ticket_id: str) -> Optional[str]:
        """Get the current status of a ticket.
        
        Args:
            ticket_id: External ticket ID
            
        Returns:
            Ticket status or None if not found
        """
        pass
    
    @abstractmethod
    async def close_ticket(self, ticket_id: str, resolution: str) -> bool:
        """Close a ticket.
        
        Args:
            ticket_id: External ticket ID
            resolution: Closure resolution
            
        Returns:
            True if closure was successful
        """
        pass


class JiraIntegration(TicketingIntegration):
    """Jira ticketing system integration."""
    
    def __init__(self):
        """Initialize Jira integration."""
        self.base_url = settings.jira_url
        self.username = settings.jira_username
        self.api_token = settings.jira_api_token
        self.enabled = settings.jira_enabled
    
    async def create_ticket(self, ticket_data: TicketData) -> TicketingIntegrationResponse:
        """Create a Jira issue.
        
        Args:
            ticket_data: Ticket data to create
            
        Returns:
            Integration response with issue details
        """
        if not self.enabled or not all([self.base_url, self.username, self.api_token]):
            return TicketingIntegrationResponse(
                success=False,
                message="Jira integration not properly configured"
            )
        
        # Map priority and severity
        priority_mapping = {
            "critical": "Highest",
            "high": "High", 
            "medium": "Medium",
            "low": "Low",
            "info": "Lowest"
        }
        
        jira_priority = priority_mapping.get(ticket_data.severity.lower(), "Medium")
        
        # Prepare Jira issue data
        issue_data = {
            "fields": {
                "project": {"key": ticket_data.custom_fields.get("project_key", "SEC")},
                "summary": ticket_data.title,
                "description": ticket_data.description,
                "issuetype": {"name": "Bug"},
                "priority": {"name": jira_priority},
                "labels": ticket_data.labels
            }
        }
        
        # Add assignee if specified
        if ticket_data.assignee:
            issue_data["fields"]["assignee"] = {"name": ticket_data.assignee}
        
        # Add custom fields
        for field_id, value in ticket_data.custom_fields.items():
            if field_id.startswith("customfield_"):
                issue_data["fields"][field_id] = value
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/rest/api/2/issue",
                    json=issue_data,
                    auth=(self.username, self.api_token),
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 201:
                    result = response.json()
                    return TicketingIntegrationResponse(
                        success=True,
                        external_ticket_id=result["key"],
                        external_ticket_url=f"{self.base_url}/browse/{result['key']}",
                        message=f"Jira issue {result['key']} created successfully"
                    )
                else:
                    return TicketingIntegrationResponse(
                        success=False,
                        message=f"Failed to create Jira issue: {response.status_code} - {response.text}",
                        error_details={"status_code": response.status_code, "response": response.text}
                    )
        
        except Exception as e:
            return TicketingIntegrationResponse(
                success=False,
                message=f"Error creating Jira issue: {str(e)}",
                error_details={"exception": str(e)}
            )
    
    async def update_ticket(self, ticket_id: str, updates: Dict[str, Any]) -> bool:
        """Update a Jira issue.
        
        Args:
            ticket_id: Jira issue key
            updates: Fields to update
            
        Returns:
            True if update was successful
        """
        if not self.enabled:
            return False
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.put(
                    f"{self.base_url}/rest/api/2/issue/{ticket_id}",
                    json={"fields": updates},
                    auth=(self.username, self.api_token),
                    headers={"Content-Type": "application/json"}
                )
                return response.status_code == 204
        
        except Exception:
            return False
    
    async def get_ticket_status(self, ticket_id: str) -> Optional[str]:
        """Get Jira issue status.
        
        Args:
            ticket_id: Jira issue key
            
        Returns:
            Issue status or None if not found
        """
        if not self.enabled:
            return None
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/rest/api/2/issue/{ticket_id}",
                    auth=(self.username, self.api_token)
                )
                
                if response.status_code == 200:
                    issue = response.json()
                    return issue["fields"]["status"]["name"]
        
        except Exception:
            pass
        
        return None
    
    async def close_ticket(self, ticket_id: str, resolution: str) -> bool:
        """Close a Jira issue.
        
        Args:
            ticket_id: Jira issue key
            resolution: Closure resolution
            
        Returns:
            True if closure was successful
        """
        if not self.enabled:
            return False
        
        # Transition to closed status (typically transition ID 2)
        transition_data = {
            "transition": {"id": "2"},
            "fields": {
                "resolution": {"name": "Fixed"}
            }
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/rest/api/2/issue/{ticket_id}/transitions",
                    json=transition_data,
                    auth=(self.username, self.api_token),
                    headers={"Content-Type": "application/json"}
                )
                return response.status_code == 204
        
        except Exception:
            return False


class ServiceNowIntegration(TicketingIntegration):
    """ServiceNow ticketing system integration."""
    
    def __init__(self):
        """Initialize ServiceNow integration."""
        self.base_url = settings.servicenow_url
        self.username = settings.servicenow_username
        self.password = settings.servicenow_password
        self.enabled = settings.servicenow_enabled
    
    async def create_ticket(self, ticket_data: TicketData) -> TicketingIntegrationResponse:
        """Create a ServiceNow incident.
        
        Args:
            ticket_data: Ticket data to create
            
        Returns:
            Integration response with incident details
        """
        if not self.enabled or not all([self.base_url, self.username, self.password]):
            return TicketingIntegrationResponse(
                success=False,
                message="ServiceNow integration not properly configured"
            )
        
        # Map severity to ServiceNow impact/urgency
        impact_mapping = {
            "critical": "1",
            "high": "2",
            "medium": "3",
            "low": "4",
            "info": "4"
        }
        
        impact = impact_mapping.get(ticket_data.severity.lower(), "3")
        
        # Prepare incident data
        incident_data = {
            "short_description": ticket_data.title,
            "description": ticket_data.description,
            "impact": impact,
            "urgency": impact,
            "category": "Security",
            "subcategory": "Vulnerability"
        }
        
        # Add assignee if specified
        if ticket_data.assignee:
            incident_data["assigned_to"] = ticket_data.assignee
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/now/table/incident",
                    json=incident_data,
                    auth=(self.username, self.password),
                    headers={"Content-Type": "application/json", "Accept": "application/json"}
                )
                
                if response.status_code == 201:
                    result = response.json()["result"]
                    return TicketingIntegrationResponse(
                        success=True,
                        external_ticket_id=result["number"],
                        external_ticket_url=f"{self.base_url}/nav_to.do?uri=incident.do?sys_id={result['sys_id']}",
                        message=f"ServiceNow incident {result['number']} created successfully"
                    )
                else:
                    return TicketingIntegrationResponse(
                        success=False,
                        message=f"Failed to create ServiceNow incident: {response.status_code} - {response.text}",
                        error_details={"status_code": response.status_code, "response": response.text}
                    )
        
        except Exception as e:
            return TicketingIntegrationResponse(
                success=False,
                message=f"Error creating ServiceNow incident: {str(e)}",
                error_details={"exception": str(e)}
            )
    
    async def update_ticket(self, ticket_id: str, updates: Dict[str, Any]) -> bool:
        """Update a ServiceNow incident.
        
        Args:
            ticket_id: ServiceNow incident number
            updates: Fields to update
            
        Returns:
            True if update was successful
        """
        if not self.enabled:
            return False
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    f"{self.base_url}/api/now/table/incident",
                    params={"sysparm_query": f"number={ticket_id}"},
                    json=updates,
                    auth=(self.username, self.password),
                    headers={"Content-Type": "application/json"}
                )
                return response.status_code == 200
        
        except Exception:
            return False
    
    async def get_ticket_status(self, ticket_id: str) -> Optional[str]:
        """Get ServiceNow incident status.
        
        Args:
            ticket_id: ServiceNow incident number
            
        Returns:
            Incident status or None if not found
        """
        if not self.enabled:
            return None
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/api/now/table/incident",
                    params={"sysparm_query": f"number={ticket_id}", "sysparm_fields": "state"},
                    auth=(self.username, self.password)
                )
                
                if response.status_code == 200:
                    results = response.json()["result"]
                    if results:
                        return results[0]["state"]
        
        except Exception:
            pass
        
        return None
    
    async def close_ticket(self, ticket_id: str, resolution: str) -> bool:
        """Close a ServiceNow incident.
        
        Args:
            ticket_id: ServiceNow incident number
            resolution: Closure resolution
            
        Returns:
            True if closure was successful
        """
        return await self.update_ticket(ticket_id, {
            "state": "6",  # Resolved
            "close_notes": resolution
        })


class TicketingManager:
    """Manager for all ticketing system integrations."""
    
    def __init__(self):
        """Initialize ticketing manager."""
        self.integrations = {
            TicketingSystem.JIRA: JiraIntegration(),
            TicketingSystem.SERVICENOW: ServiceNowIntegration(),
            # Add more integrations as needed
        }
    
    async def create_ticket(
        self,
        system: TicketingSystem,
        ticket_data: TicketData
    ) -> TicketingIntegrationResponse:
        """Create a ticket in the specified system.
        
        Args:
            system: Target ticketing system
            ticket_data: Ticket data to create
            
        Returns:
            Integration response
        """
        integration = self.integrations.get(system)
        if not integration:
            return TicketingIntegrationResponse(
                success=False,
                message=f"Ticketing system {system} not supported"
            )
        
        return await integration.create_ticket(ticket_data)
    
    async def update_ticket(
        self,
        system: TicketingSystem,
        ticket_id: str,
        updates: Dict[str, Any]
    ) -> bool:
        """Update a ticket in the specified system.
        
        Args:
            system: Target ticketing system
            ticket_id: External ticket ID
            updates: Fields to update
            
        Returns:
            True if update was successful
        """
        integration = self.integrations.get(system)
        if not integration:
            return False
        
        return await integration.update_ticket(ticket_id, updates)
    
    async def get_ticket_status(
        self,
        system: TicketingSystem,
        ticket_id: str
    ) -> Optional[str]:
        """Get ticket status from the specified system.
        
        Args:
            system: Target ticketing system
            ticket_id: External ticket ID
            
        Returns:
            Ticket status or None if not found
        """
        integration = self.integrations.get(system)
        if not integration:
            return None
        
        return await integration.get_ticket_status(ticket_id)
    
    async def close_ticket(
        self,
        system: TicketingSystem,
        ticket_id: str,
        resolution: str
    ) -> bool:
        """Close a ticket in the specified system.
        
        Args:
            system: Target ticketing system
            ticket_id: External ticket ID
            resolution: Closure resolution
            
        Returns:
            True if closure was successful
        """
        integration = self.integrations.get(system)
        if not integration:
            return False
        
        return await integration.close_ticket(ticket_id, resolution)
