"""Asset models for Phase 7: CMDB Integration."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, Text, Integer, Boolean, DateTime, JSON, ForeignKey, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class AssetType(str, Enum):
    """Types of assets in CMDB."""
    SERVER = "server"
    WORKSTATION = "workstation"
    NETWORK_DEVICE = "network_device"
    DATABASE = "database"
    APPLICATION = "application"
    SERVICE = "service"
    VIRTUAL_MACHINE = "virtual_machine"
    CONTAINER = "container"
    CLOUD_RESOURCE = "cloud_resource"
    SECURITY_DEVICE = "security_device"


class AssetStatus(str, Enum):
    """Asset status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    MAINTENANCE = "maintenance"
    DECOMMISSIONED = "decommissioned"
    PLANNED = "planned"
    UNKNOWN = "unknown"


class CriticalityLevel(str, Enum):
    """Asset criticality levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"


class Asset(Base):
    """Asset model for CMDB integration."""
    
    __tablename__ = "assets"
    
    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    asset_type: Mapped[AssetType] = mapped_column(String(50), nullable=False, index=True)
    status: Mapped[AssetStatus] = mapped_column(String(20), default=AssetStatus.ACTIVE)
    
    # CMDB integration
    cmdb_id: Mapped[Optional[str]] = mapped_column(String(255), index=True)
    cmdb_class: Mapped[Optional[str]] = mapped_column(String(100))
    cmdb_last_sync: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Technical details
    hostname: Mapped[Optional[str]] = mapped_column(String(255), index=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String(45), index=True)  # IPv6 compatible
    mac_address: Mapped[Optional[str]] = mapped_column(String(17))
    operating_system: Mapped[Optional[str]] = mapped_column(String(255))
    os_version: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Business information
    business_service: Mapped[Optional[str]] = mapped_column(String(255))
    owner: Mapped[Optional[str]] = mapped_column(String(255))
    department: Mapped[Optional[str]] = mapped_column(String(255))
    location: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Risk and compliance
    criticality: Mapped[CriticalityLevel] = mapped_column(String(20), default=CriticalityLevel.UNKNOWN)
    risk_score: Mapped[Optional[float]] = mapped_column(Float)
    compliance_status: Mapped[Optional[str]] = mapped_column(String(50))
    
    # Configuration
    configuration: Mapped[Optional[dict]] = mapped_column(JSON)
    installed_software: Mapped[Optional[list[dict]]] = mapped_column(JSON)
    network_interfaces: Mapped[Optional[list[dict]]] = mapped_column(JSON)
    
    # Monitoring
    is_monitored: Mapped[bool] = mapped_column(Boolean, default=True)
    last_seen: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Relationships
    dependencies: Mapped[list["AssetDependency"]] = relationship(
        "AssetDependency",
        foreign_keys="AssetDependency.source_asset_id",
        back_populates="source_asset",
        cascade="all, delete-orphan"
    )
    dependents: Mapped[list["AssetDependency"]] = relationship(
        "AssetDependency",
        foreign_keys="AssetDependency.target_asset_id",
        back_populates="target_asset"
    )
    vulnerabilities: Mapped[list["AssetVulnerability"]] = relationship(
        "AssetVulnerability",
        back_populates="asset",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Asset(name='{self.name}', type='{self.asset_type}', status='{self.status}')>"


class DependencyType(str, Enum):
    """Types of asset dependencies."""
    DEPENDS_ON = "depends_on"
    HOSTS = "hosts"
    CONNECTS_TO = "connects_to"
    USES = "uses"
    PROVIDES = "provides"
    MANAGES = "manages"
    MONITORS = "monitors"


class AssetDependency(Base):
    """Asset dependency relationships."""
    
    __tablename__ = "asset_dependencies"
    
    source_asset_id: Mapped[UUID] = mapped_column(
        ForeignKey("assets.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    target_asset_id: Mapped[UUID] = mapped_column(
        ForeignKey("assets.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    dependency_type: Mapped[DependencyType] = mapped_column(String(50), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Dependency metadata
    criticality: Mapped[CriticalityLevel] = mapped_column(String(20), default=CriticalityLevel.MEDIUM)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # CMDB sync
    cmdb_relationship_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Relationships
    source_asset: Mapped["Asset"] = relationship(
        "Asset",
        foreign_keys=[source_asset_id],
        back_populates="dependencies"
    )
    target_asset: Mapped["Asset"] = relationship(
        "Asset",
        foreign_keys=[target_asset_id],
        back_populates="dependents"
    )
    
    def __repr__(self) -> str:
        return f"<AssetDependency(type='{self.dependency_type}', source='{self.source_asset_id}')>"


class AssetVulnerability(Base):
    """Vulnerabilities associated with assets."""
    
    __tablename__ = "asset_vulnerabilities"
    
    asset_id: Mapped[UUID] = mapped_column(
        ForeignKey("assets.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    vulnerability_id: Mapped[UUID] = mapped_column(
        String(36),  # Reference to vulnerability model
        nullable=False,
        index=True
    )
    
    # Vulnerability details
    cve_id: Mapped[Optional[str]] = mapped_column(String(20), index=True)
    severity: Mapped[str] = mapped_column(String(20), nullable=False)
    cvss_score: Mapped[Optional[float]] = mapped_column(Float)
    
    # Status tracking
    status: Mapped[str] = mapped_column(String(50), default="open")
    first_detected: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    last_detected: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    resolved_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Scanner information
    scanner_name: Mapped[Optional[str]] = mapped_column(String(100))
    scan_id: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Remediation
    remediation_status: Mapped[Optional[str]] = mapped_column(String(50))
    remediation_notes: Mapped[Optional[str]] = mapped_column(Text)
    assigned_to: Mapped[Optional[str]] = mapped_column(String(255))
    
    # Risk assessment
    business_impact: Mapped[Optional[str]] = mapped_column(String(20))
    exploitability: Mapped[Optional[str]] = mapped_column(String(20))
    
    # Relationships
    asset: Mapped["Asset"] = relationship("Asset", back_populates="vulnerabilities")
    
    def __repr__(self) -> str:
        return f"<AssetVulnerability(asset_id='{self.asset_id}', cve='{self.cve_id}')>"


class ConfigurationItem(Base):
    """Configuration items from CMDB."""
    
    __tablename__ = "configuration_items"
    
    # CMDB information
    cmdb_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    cmdb_class: Mapped[str] = mapped_column(String(100), nullable=False)
    cmdb_subclass: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    display_name: Mapped[Optional[str]] = mapped_column(String(255))
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Status and lifecycle
    operational_status: Mapped[Optional[str]] = mapped_column(String(50))
    lifecycle_status: Mapped[Optional[str]] = mapped_column(String(50))
    
    # Attributes from CMDB
    attributes: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Sync information
    last_sync: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    sync_hash: Mapped[Optional[str]] = mapped_column(String(64))  # For change detection
    
    # Asset mapping
    mapped_asset_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("assets.id"),
        index=True
    )
    
    # Relationships
    mapped_asset: Mapped[Optional["Asset"]] = relationship("Asset")
    
    def __repr__(self) -> str:
        return f"<ConfigurationItem(cmdb_id='{self.cmdb_id}', name='{self.name}')>"
