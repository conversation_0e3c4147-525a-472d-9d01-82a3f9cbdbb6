"""Career development and progression models."""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
import enum

from sqlalchemy import String, Boolean, DateTime, Text, Integer, Enum as SQLEnum, ForeignKey, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import JSONB

from pitas.db.base import Base


class IDPStatus(str, enum.Enum):
    """Individual Development Plan status."""
    DRAFT = "draft"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class GoalStatus(str, enum.Enum):
    """Development goal status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class GoalPriority(str, enum.Enum):
    """Development goal priority."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class IndividualDevelopmentPlan(Base):
    """Individual Development Plan for career progression."""
    
    __tablename__ = "individual_development_plans"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[IDPStatus] = mapped_column(SQLEnum(IDPStatus), default=IDPStatus.DRAFT, nullable=False)
    
    # User Relationship
    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    manager_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True, index=True)
    
    # Timeline
    start_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    end_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    review_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Career Progression
    current_tier: Mapped[str] = mapped_column(String(50), nullable=False)  # CareerTier enum value
    target_tier: Mapped[str] = mapped_column(String(50), nullable=False)   # CareerTier enum value
    career_track: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # CareerTrack enum value
    
    # Progress Tracking
    overall_progress: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)  # 0.0 to 1.0
    last_review_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    next_review_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id], back_populates="individual_development_plans")
    manager: Mapped[Optional["User"]] = relationship("User", foreign_keys=[manager_id])
    goals: Mapped[List["DevelopmentGoal"]] = relationship("DevelopmentGoal", back_populates="idp", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the IDP."""
        return f"<IndividualDevelopmentPlan(id={self.id}, user_id={self.user_id}, status={self.status})>"


class DevelopmentGoal(Base):
    """Individual development goals within an IDP."""
    
    __tablename__ = "development_goals"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[GoalStatus] = mapped_column(SQLEnum(GoalStatus), default=GoalStatus.NOT_STARTED, nullable=False)
    priority: Mapped[GoalPriority] = mapped_column(SQLEnum(GoalPriority), default=GoalPriority.MEDIUM, nullable=False)
    
    # IDP Relationship
    idp_id: Mapped[UUID] = mapped_column(ForeignKey("individual_development_plans.id"), nullable=False, index=True)
    
    # Timeline
    target_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Progress and Measurement
    progress: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)  # 0.0 to 1.0
    success_criteria: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    measurement_method: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Resources and Support
    required_resources: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    estimated_cost: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    assigned_mentor: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True)
    
    # Skills and Competencies
    target_skills: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Skills to develop
    current_skill_level: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    target_skill_level: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Notes and Updates
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    last_update_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Relationships
    idp: Mapped["IndividualDevelopmentPlan"] = relationship("IndividualDevelopmentPlan", back_populates="goals")
    mentor: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_mentor])
    activities: Mapped[List["DevelopmentActivity"]] = relationship("DevelopmentActivity", back_populates="goal", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the development goal."""
        return f"<DevelopmentGoal(id={self.id}, title={self.title}, status={self.status})>"


class ActivityType(str, enum.Enum):
    """Types of development activities."""
    TRAINING = "training"
    CERTIFICATION = "certification"
    CONFERENCE = "conference"
    MENTORING = "mentoring"
    PROJECT = "project"
    READING = "reading"
    PRACTICE = "practice"
    WORKSHOP = "workshop"
    SHADOWING = "shadowing"
    PRESENTATION = "presentation"


class ActivityStatus(str, enum.Enum):
    """Development activity status."""
    PLANNED = "planned"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    DEFERRED = "deferred"


class DevelopmentActivity(Base):
    """Specific activities to achieve development goals."""
    
    __tablename__ = "development_activities"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    activity_type: Mapped[ActivityType] = mapped_column(SQLEnum(ActivityType), nullable=False)
    status: Mapped[ActivityStatus] = mapped_column(SQLEnum(ActivityStatus), default=ActivityStatus.PLANNED, nullable=False)
    
    # Goal Relationship
    goal_id: Mapped[UUID] = mapped_column(ForeignKey("development_goals.id"), nullable=False, index=True)
    
    # Timeline
    planned_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    planned_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    actual_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Resources and Cost
    estimated_hours: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    actual_hours: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    estimated_cost: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    actual_cost: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # External Information
    provider: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # Training provider, conference name, etc.
    url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    location: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Completion and Results
    completion_percentage: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)
    completion_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    outcome: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    certificate_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    goal: Mapped["DevelopmentGoal"] = relationship("DevelopmentGoal", back_populates="activities")
    
    def __repr__(self) -> str:
        """String representation of the development activity."""
        return f"<DevelopmentActivity(id={self.id}, title={self.title}, type={self.activity_type})>"
