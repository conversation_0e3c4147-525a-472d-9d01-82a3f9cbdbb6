"""Integration models for Phase 7: Enterprise Systems Integration."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, Text, Integer, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class IntegrationType(str, Enum):
    """Types of integrations supported."""
    OBSIDIAN = "obsidian"
    CMDB_SERVICENOW = "cmdb_servicenow"
    CMDB_BMC_REMEDY = "cmdb_bmc_remedy"
    SIEM_SPLUNK = "siem_splunk"
    SIEM_QRADAR = "siem_qradar"
    SIEM_SENTINEL = "siem_sentinel"
    VULN_NESSUS = "vuln_nessus"
    VULN_QUALYS = "vuln_qualys"
    VULN_RAPID7 = "vuln_rapid7"
    THREAT_INTEL = "threat_intel"
    IAM = "iam"


class IntegrationStatus(str, Enum):
    """Integration status values."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    SYNCING = "syncing"
    PENDING = "pending"


class Integration(Base):
    """Integration configuration model."""
    
    __tablename__ = "integrations"
    
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    integration_type: Mapped[IntegrationType] = mapped_column(String(50), nullable=False, index=True)
    status: Mapped[IntegrationStatus] = mapped_column(String(20), default=IntegrationStatus.PENDING)
    
    # Connection details
    api_url: Mapped[Optional[str]] = mapped_column(String(500))
    api_key: Mapped[Optional[str]] = mapped_column(String(500))
    username: Mapped[Optional[str]] = mapped_column(String(255))
    password: Mapped[Optional[str]] = mapped_column(String(500))  # Should be encrypted
    
    # Configuration
    config: Mapped[Optional[dict]] = mapped_column(JSON)
    sync_interval: Mapped[int] = mapped_column(Integer, default=300)  # seconds
    last_sync: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    next_sync: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Monitoring
    is_enabled: Mapped[bool] = mapped_column(Boolean, default=True)
    retry_count: Mapped[int] = mapped_column(Integer, default=0)
    max_retries: Mapped[int] = mapped_column(Integer, default=3)
    last_error: Mapped[Optional[str]] = mapped_column(Text)
    
    # Relationships
    sync_logs: Mapped[list["IntegrationSyncLog"]] = relationship(
        "IntegrationSyncLog", 
        back_populates="integration",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Integration(name='{self.name}', type='{self.integration_type}', status='{self.status}')>"


class SyncStatus(str, Enum):
    """Sync operation status values."""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    IN_PROGRESS = "in_progress"


class IntegrationSyncLog(Base):
    """Log of integration synchronization operations."""
    
    __tablename__ = "integration_sync_logs"
    
    integration_id: Mapped[UUID] = mapped_column(
        ForeignKey("integrations.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    status: Mapped[SyncStatus] = mapped_column(String(20), nullable=False)
    started_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Sync statistics
    records_processed: Mapped[int] = mapped_column(Integer, default=0)
    records_created: Mapped[int] = mapped_column(Integer, default=0)
    records_updated: Mapped[int] = mapped_column(Integer, default=0)
    records_failed: Mapped[int] = mapped_column(Integer, default=0)
    
    # Error details
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    error_details: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Sync metadata
    sync_metadata: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Relationships
    integration: Mapped["Integration"] = relationship("Integration", back_populates="sync_logs")
    
    def __repr__(self) -> str:
        return f"<IntegrationSyncLog(integration_id='{self.integration_id}', status='{self.status}')>"


class DataMapping(Base):
    """Data mapping configuration for integrations."""
    
    __tablename__ = "data_mappings"
    
    integration_id: Mapped[UUID] = mapped_column(
        ForeignKey("integrations.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    source_field: Mapped[str] = mapped_column(String(255), nullable=False)
    target_field: Mapped[str] = mapped_column(String(255), nullable=False)
    field_type: Mapped[str] = mapped_column(String(50), nullable=False)  # string, integer, boolean, etc.
    
    # Transformation rules
    transformation_rules: Mapped[Optional[dict]] = mapped_column(JSON)
    is_required: Mapped[bool] = mapped_column(Boolean, default=False)
    default_value: Mapped[Optional[str]] = mapped_column(String(500))
    
    # Validation rules
    validation_rules: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Relationships
    integration: Mapped["Integration"] = relationship("Integration")
    
    def __repr__(self) -> str:
        return f"<DataMapping(source='{self.source_field}', target='{self.target_field}')>"
