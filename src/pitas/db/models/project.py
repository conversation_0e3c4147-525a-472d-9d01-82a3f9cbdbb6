"""Project and workflow database models."""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlalchemy import String, Text, DateTime, Boolean, Integer, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.core.workflow import PTESPhase, WorkflowStatus
from pitas.db.base import Base


class Project(Base):
    """Project model for managing pentesting engagements."""
    
    __tablename__ = "projects"
    
    # Basic Information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Project name"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Project description"
    )
    
    client_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("clients.id"),
        nullable=False,
        index=True,
        doc="Client ID"
    )
    
    project_manager_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        Foreign<PERSON>ey("users.id"),
        nullable=False,
        index=True,
        doc="Project manager user ID"
    )
    
    # Timeline
    start_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="Project start date"
    )
    
    end_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="Project end date"
    )
    
    estimated_hours: Mapped[int] = mapped_column(
        Integer,
        default=0,
        doc="Estimated total hours"
    )
    
    actual_hours: Mapped[int] = mapped_column(
        Integer,
        default=0,
        doc="Actual hours spent"
    )
    
    # Workflow Status
    current_phase: Mapped[PTESPhase] = mapped_column(
        SQLEnum(PTESPhase),
        default=PTESPhase.PRE_ENGAGEMENT,
        nullable=False,
        index=True,
        doc="Current PTES phase"
    )
    
    workflow_status: Mapped[WorkflowStatus] = mapped_column(
        SQLEnum(WorkflowStatus),
        default=WorkflowStatus.NOT_STARTED,
        nullable=False,
        index=True,
        doc="Overall workflow status"
    )
    
    progress_percentage: Mapped[float] = mapped_column(
        default=0.0,
        doc="Project progress percentage (0.0 to 1.0)"
    )
    
    # Scope and Configuration
    scope_document: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Project scope document"
    )
    
    rules_of_engagement: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Rules of engagement"
    )
    
    target_systems: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Target systems and networks"
    )
    
    excluded_systems: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Excluded systems and networks"
    )
    
    # Quality Assurance
    peer_review_required: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether peer review is required"
    )
    
    quality_score: Mapped[Optional[float]] = mapped_column(
        doc="Quality score (0.0 to 1.0)"
    )
    
    # Compliance and Documentation
    compliance_frameworks: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Applicable compliance frameworks"
    )
    
    audit_trail_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether audit trail is enabled"
    )
    
    # Status Flags
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        index=True,
        doc="Whether project is active"
    )
    
    is_archived: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        index=True,
        doc="Whether project is archived"
    )
    
    # Relationships
    client = relationship("Client", back_populates="projects")
    project_manager = relationship("User", foreign_keys=[project_manager_id])
    phase_transitions = relationship("PhaseTransition", back_populates="project", cascade="all, delete-orphan")
    deliverables = relationship("ProjectDeliverable", back_populates="project", cascade="all, delete-orphan")
    team_assignments = relationship("ProjectTeamAssignment", back_populates="project", cascade="all, delete-orphan")
    remediations = relationship("Remediation", back_populates="project")


class PhaseTransition(Base):
    """Phase transition history for projects."""
    
    __tablename__ = "phase_transitions"
    
    project_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        nullable=False,
        index=True,
        doc="Project ID"
    )
    
    from_phase: Mapped[Optional[PTESPhase]] = mapped_column(
        SQLEnum(PTESPhase),
        doc="Previous phase"
    )
    
    to_phase: Mapped[PTESPhase] = mapped_column(
        SQLEnum(PTESPhase),
        nullable=False,
        doc="New phase"
    )
    
    transitioned_by: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        doc="User who performed the transition"
    )
    
    transition_reason: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Reason for transition"
    )
    
    validation_data: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Validation data for the transition"
    )
    
    approved_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who approved the transition"
    )
    
    approved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Approval timestamp"
    )
    
    # Relationships
    project = relationship("Project", back_populates="phase_transitions")
    transitioned_by_user = relationship("User", foreign_keys=[transitioned_by])
    approved_by_user = relationship("User", foreign_keys=[approved_by])


class ProjectDeliverable(Base):
    """Project deliverables tracking."""
    
    __tablename__ = "project_deliverables"
    
    project_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        nullable=False,
        index=True,
        doc="Project ID"
    )
    
    phase: Mapped[PTESPhase] = mapped_column(
        SQLEnum(PTESPhase),
        nullable=False,
        index=True,
        doc="PTES phase"
    )
    
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Deliverable name"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Deliverable description"
    )
    
    status: Mapped[WorkflowStatus] = mapped_column(
        SQLEnum(WorkflowStatus),
        default=WorkflowStatus.NOT_STARTED,
        nullable=False,
        index=True,
        doc="Deliverable status"
    )
    
    assigned_to: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="Assigned user ID"
    )
    
    due_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Due date"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Completion timestamp"
    )
    
    file_path: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="File path for deliverable"
    )
    
    quality_checked: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether quality check is completed"
    )
    
    quality_checked_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who performed quality check"
    )
    
    quality_checked_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Quality check timestamp"
    )
    
    # Relationships
    project = relationship("Project", back_populates="deliverables")
    assigned_user = relationship("User", foreign_keys=[assigned_to])
    quality_checker = relationship("User", foreign_keys=[quality_checked_by])


class ProjectTeamAssignment(Base):
    """Team member assignments to projects."""
    
    __tablename__ = "project_team_assignments"
    
    project_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        nullable=False,
        index=True,
        doc="Project ID"
    )
    
    user_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True,
        doc="User ID"
    )
    
    role: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Role in project"
    )
    
    assigned_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Assignment timestamp"
    )
    
    unassigned_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Unassignment timestamp"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        index=True,
        doc="Whether assignment is active"
    )
    
    allocated_hours: Mapped[Optional[int]] = mapped_column(
        Integer,
        doc="Allocated hours for this assignment"
    )
    
    # Relationships
    project = relationship("Project", back_populates="team_assignments")
    user = relationship("User")
