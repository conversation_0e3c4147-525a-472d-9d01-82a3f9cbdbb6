"""Analytics and ML models for Phase 9: Advanced Analytics and Reporting Engine."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy import Column, String, DateTime, Float, Integer, Text, JSON, Boolean, Foreign<PERSON>ey
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

from pitas.db.base import Base


class AnalyticsModel(Base):
    """Base class for analytics and ML models."""
    
    __tablename__ = "analytics_models"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    name = Column(String(255), nullable=False, index=True)
    model_type = Column(String(100), nullable=False)  # vulnerability_prediction, threat_classification, etc.
    version = Column(String(50), nullable=False)
    description = Column(Text)
    
    # Model metadata
    training_data_size = Column(Integer)
    training_duration_seconds = Column(Float)
    accuracy_score = Column(Float)
    precision_score = Column(Float)
    recall_score = Column(Float)
    f1_score = Column(Float)
    
    # Model configuration
    hyperparameters = Column(JSON)
    feature_columns = Column(JSON)  # List of feature column names
    target_column = Column(String(255))
    
    # Model status
    is_active = Column(Boolean, default=False)
    is_trained = Column(Boolean, default=False)
    last_trained_at = Column(DateTime)
    last_prediction_at = Column(DateTime)
    
    # Model artifacts
    model_path = Column(String(500))  # Path to serialized model
    preprocessing_config = Column(JSON)
    
    # Relationships
    predictions = relationship("AnalyticsPrediction", back_populates="model")
    training_jobs = relationship("ModelTrainingJob", back_populates="model")


class ModelTrainingJob(Base):
    """Model training job tracking."""

    __tablename__ = "model_training_jobs"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    model_id = Column(PostgresUUID(as_uuid=True), ForeignKey("analytics_models.id"), nullable=False)
    
    # Job details
    job_name = Column(String(255), nullable=False)
    status = Column(String(50), nullable=False)  # pending, running, completed, failed
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Training configuration
    training_config = Column(JSON)
    dataset_size = Column(Integer)
    validation_split = Column(Float, default=0.2)
    
    # Results
    final_accuracy = Column(Float)
    final_loss = Column(Float)
    training_metrics = Column(JSON)
    validation_metrics = Column(JSON)
    
    # Error handling
    error_message = Column(Text)
    error_traceback = Column(Text)
    
    # Resource usage
    cpu_hours = Column(Float)
    memory_peak_gb = Column(Float)
    gpu_hours = Column(Float)
    
    # Relationships
    model = relationship("AnalyticsModel", back_populates="training_jobs")


class AnalyticsPrediction(Base):
    """Individual predictions made by analytics models."""

    __tablename__ = "analytics_predictions"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    model_id = Column(PostgresUUID(as_uuid=True), ForeignKey("analytics_models.id"), nullable=False)
    
    # Prediction details
    prediction_type = Column(String(100), nullable=False)
    input_data = Column(JSON)  # Input features used for prediction
    prediction_result = Column(JSON)  # Prediction output
    confidence_score = Column(Float)
    
    # Context
    entity_type = Column(String(100))  # vulnerability, project, team, etc.
    entity_id = Column(PostgresUUID(as_uuid=True))
    
    # Validation
    actual_outcome = Column(JSON)  # Actual result for validation
    is_validated = Column(Boolean, default=False)
    validation_accuracy = Column(Float)
    
    # Metadata
    prediction_metadata = Column(JSON)
    feature_importance = Column(JSON)
    
    # Relationships
    model = relationship("AnalyticsModel", back_populates="predictions")


class AnalyticsReport(Base):
    """Generated analytics reports."""

    __tablename__ = "analytics_reports"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Report details
    report_name = Column(String(255), nullable=False)
    report_type = Column(String(100), nullable=False)  # executive, technical, compliance, client
    report_format = Column(String(50), nullable=False)  # pdf, html, json, excel
    
    # Generation details
    generated_by = Column(PostgresUUID(as_uuid=True), nullable=False)  # User ID
    generation_time_seconds = Column(Float)
    data_period_start = Column(DateTime)
    data_period_end = Column(DateTime)
    
    # Content
    report_data = Column(JSON)  # Structured report data
    report_summary = Column(Text)
    key_findings = Column(JSON)  # List of key findings
    recommendations = Column(JSON)  # List of recommendations
    
    # Distribution
    recipients = Column(JSON)  # List of recipient emails/IDs
    distribution_status = Column(String(50), default="pending")  # pending, sent, failed
    sent_at = Column(DateTime)
    
    # Storage
    file_path = Column(String(500))  # Path to generated report file
    file_size_bytes = Column(Integer)
    
    # Metadata
    report_config = Column(JSON)  # Configuration used to generate report
    data_sources = Column(JSON)  # List of data sources used


class AnalyticsAlert(Base):
    """Analytics-driven alerts and notifications."""

    __tablename__ = "analytics_alerts"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Alert details
    alert_type = Column(String(100), nullable=False)  # vulnerability, sla_breach, anomaly, compliance
    severity = Column(String(50), nullable=False)  # low, medium, high, critical
    title = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Trigger details
    trigger_condition = Column(JSON)  # Condition that triggered the alert
    trigger_data = Column(JSON)  # Data that triggered the alert
    threshold_value = Column(Float)
    actual_value = Column(Float)
    
    # Context
    entity_type = Column(String(100))  # vulnerability, project, team, etc.
    entity_id = Column(PostgresUUID(as_uuid=True))
    
    # Status
    status = Column(String(50), default="active")  # active, acknowledged, resolved, suppressed
    acknowledged_by = Column(PostgresUUID(as_uuid=True))  # User ID
    acknowledged_at = Column(DateTime)
    resolved_at = Column(DateTime)
    
    # Escalation
    escalation_level = Column(Integer, default=0)
    escalated_at = Column(DateTime)
    escalated_to = Column(JSON)  # List of escalation recipients
    
    # Actions
    recommended_actions = Column(JSON)  # List of recommended actions
    actions_taken = Column(JSON)  # List of actions taken
    
    # Metadata
    alert_metadata = Column(JSON)
    correlation_id = Column(PostgresUUID(as_uuid=True))  # For grouping related alerts


class AnalyticsMetric(Base):
    """Time-series analytics metrics."""

    __tablename__ = "analytics_metrics"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Metric details
    metric_name = Column(String(255), nullable=False, index=True)
    metric_category = Column(String(100), nullable=False)  # security, performance, compliance, team
    metric_type = Column(String(50), nullable=False)  # counter, gauge, histogram, summary
    
    # Value
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(50))
    
    # Context
    entity_type = Column(String(100))  # vulnerability, project, team, etc.
    entity_id = Column(PostgresUUID(as_uuid=True))
    
    # Dimensions
    dimensions = Column(JSON)  # Key-value pairs for metric dimensions
    
    # Aggregation
    aggregation_period = Column(String(50))  # minute, hour, day, week, month
    aggregation_function = Column(String(50))  # sum, avg, min, max, count
    
    # Metadata
    metric_metadata = Column(JSON)
    data_source = Column(String(255))
    collection_method = Column(String(100))


class AnalyticsDashboard(Base):
    """Analytics dashboard configurations."""

    __tablename__ = "analytics_dashboards"

    id = Column(PostgresUUID(as_uuid=True), primary_key=True, default=uuid4)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Dashboard details
    dashboard_name = Column(String(255), nullable=False)
    dashboard_type = Column(String(100), nullable=False)  # executive, technical, operational
    description = Column(Text)
    
    # Configuration
    layout_config = Column(JSON)  # Dashboard layout configuration
    widget_configs = Column(JSON)  # Widget configurations
    data_sources = Column(JSON)  # Data source configurations
    refresh_interval_seconds = Column(Integer, default=300)
    
    # Access control
    created_by = Column(PostgresUUID(as_uuid=True), nullable=False)
    is_public = Column(Boolean, default=False)
    allowed_users = Column(JSON)  # List of user IDs with access
    allowed_roles = Column(JSON)  # List of roles with access
    
    # Usage tracking
    view_count = Column(Integer, default=0)
    last_viewed_at = Column(DateTime)
    last_modified_by = Column(PostgresUUID(as_uuid=True))
    
    # Metadata
    dashboard_metadata = Column(JSON)
    tags = Column(JSON)  # List of tags for categorization
