"""Skill matrix and competency models for Phase 2."""

import enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import String, Integer, Float, <PERSON><PERSON><PERSON>, Enum, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import ARRAY

from .base import Base


class SecurityDomain(str, enum.Enum):
    """Security domains for pentesting specialization."""
    
    WEB_APPLICATION = "web_application"
    NETWORK_INFRASTRUCTURE = "network_infrastructure"
    MOBILE_APPLICATION = "mobile_application"
    CLOUD_SECURITY = "cloud_security"
    WIRELESS_SECURITY = "wireless_security"
    SOCIAL_ENGINEERING = "social_engineering"
    PHYSICAL_SECURITY = "physical_security"
    IOT_SECURITY = "iot_security"
    SCADA_ICS = "scada_ics"
    RED_TEAM_OPERATIONS = "red_team_operations"
    THREAT_HUNTING = "threat_hunting"
    INCIDENT_RESPONSE = "incident_response"
    MALWARE_ANALYSIS = "malware_analysis"
    DIGITAL_FORENSICS = "digital_forensics"
    COMPLIANCE_AUDIT = "compliance_audit"


class CertificationTier(str, enum.Enum):
    """Certification tiers for skill level assessment."""
    
    ENTRY_LEVEL = "entry_level"  # CEH, Security+
    INTERMEDIATE = "intermediate"  # GCIH, GPEN, OSCP
    ADVANCED = "advanced"  # OSCE, GXPN, CISSP
    EXPERT = "expert"  # OSEE, GREM, Custom certifications
    MASTER = "master"  # Industry recognition, speaking, research


class SkillMatrix(Base):
    """Skill matrix for tracking pentester competencies."""
    
    __tablename__ = "skill_matrices"
    
    # Foreign key to pentester profile
    pentester_id: Mapped[UUID] = mapped_column(
        ForeignKey("pentesterprofile.id", ondelete="CASCADE"),
        nullable=False,
        doc="Reference to pentester profile"
    )
    
    # Security domain specialization
    security_domain: Mapped[SecurityDomain] = mapped_column(
        Enum(SecurityDomain),
        nullable=False,
        doc="Security domain specialization"
    )
    
    # Skill level (1-10 scale)
    skill_level: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Skill level on 1-10 scale"
    )
    
    # Certification tier
    certification_tier: Mapped[CertificationTier] = mapped_column(
        Enum(CertificationTier),
        nullable=False,
        doc="Certification tier for this domain"
    )
    
    # Years of experience in this domain
    years_experience: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Years of experience in this domain"
    )
    
    # Specific certifications (JSON array)
    certifications: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="List of specific certifications"
    )
    
    # Tools proficiency (JSON array)
    tools_proficiency: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="List of tools with proficiency"
    )
    
    # Recent training or updates
    recent_training: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Recent training or skill updates"
    )
    
    # Performance score (calculated field)
    performance_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Calculated performance score for this domain"
    )
    
    # Availability for this domain (percentage)
    domain_availability: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=100.0,
        doc="Availability percentage for this domain"
    )
    
    # Relationship back to pentester
    pentester: Mapped["PentesterProfile"] = relationship(
        "PentesterProfile",
        back_populates="skill_matrices"
    )
    
    def __repr__(self) -> str:
        """String representation of the skill matrix."""
        return f"<SkillMatrix(pentester_id={self.pentester_id}, domain={self.security_domain}, level={self.skill_level})>"
