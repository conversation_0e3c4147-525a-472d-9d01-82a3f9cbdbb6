"""Vulnerability tracking models for Phase 3."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional, List
from uuid import UUID

from sqlalchemy import (
    String, Text, DateTime, Numeric, Integer, Boolean, 
    ForeignKey, JSON, Index, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class VulnerabilitySeverity(str, Enum):
    """CVSS severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


class VulnerabilityStatus(str, Enum):
    """Vulnerability lifecycle status."""
    DISCOVERED = "discovered"
    CONFIRMED = "confirmed"
    TRIAGED = "triaged"
    IN_PROGRESS = "in_progress"
    REMEDIATED = "remediated"
    VERIFIED = "verified"
    CLOSED = "closed"
    FALSE_POSITIVE = "false_positive"
    ACCEPTED_RISK = "accepted_risk"


# AssetCriticality enum is defined in asset.py as CriticalityLevel


class Vulnerability(Base):
    """Core vulnerability tracking model."""
    
    __tablename__ = "vulnerabilities"
    __table_args__ = (
        Index("idx_vulnerability_cve", "cve_id"),
        Index("idx_vulnerability_severity", "severity"),
        Index("idx_vulnerability_status", "status"),
        Index("idx_vulnerability_discovery_date", "discovery_date"),
        Index("idx_vulnerability_cvss_score", "cvss_score"),
        CheckConstraint("cvss_score >= 0.0 AND cvss_score <= 10.0", name="cvss_score_range"),
    )

    # Core identification
    cve_id: Mapped[Optional[str]] = mapped_column(
        String(20), unique=True, index=True,
        doc="CVE identifier (e.g., CVE-2023-1234)"
    )
    
    title: Mapped[str] = mapped_column(
        String(500), nullable=False,
        doc="Vulnerability title or summary"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Detailed vulnerability description"
    )

    # CVSS scoring
    cvss_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="CVSS base score (0.0-10.0)"
    )
    
    cvss_vector: Mapped[Optional[str]] = mapped_column(
        String(200),
        doc="CVSS vector string"
    )
    
    severity: Mapped[VulnerabilitySeverity] = mapped_column(
        String(20), nullable=False, default=VulnerabilitySeverity.MEDIUM,
        doc="Vulnerability severity level"
    )

    # Lifecycle tracking
    status: Mapped[VulnerabilityStatus] = mapped_column(
        String(20), nullable=False, default=VulnerabilityStatus.DISCOVERED,
        doc="Current vulnerability status"
    )
    
    discovery_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False,
        doc="When the vulnerability was discovered"
    )
    
    remediation_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="When the vulnerability was remediated"
    )
    
    verification_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="When the remediation was verified"
    )

    # Risk assessment
    business_impact_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Business impact score (0.0-10.0)"
    )
    
    exploitability_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Exploitability score (0.0-10.0)"
    )
    
    true_risk_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="TruRisk methodology score"
    )

    # Metadata
    source: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Vulnerability source (scanner, manual, etc.)"
    )
    
    tags: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Vulnerability tags for categorization"
    )
    
    extra_data: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional vulnerability metadata"
    )

    # Relationships with assets are handled through the asset.py module


# Asset and AssetVulnerability classes are imported from asset.py to avoid duplication


class VulnerabilityMetric(Base):
    """Time-series metrics for vulnerability tracking."""
    
    __tablename__ = "vulnerability_metrics"
    __table_args__ = (
        Index("idx_vuln_metric_timestamp", "timestamp"),
        Index("idx_vuln_metric_type", "metric_type"),
        Index("idx_vuln_metric_asset", "asset_id"),
    )

    metric_type: Mapped[str] = mapped_column(
        String(50), nullable=False,
        doc="Type of metric (discovery_rate, remediation_time, etc.)"
    )
    
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False,
        doc="Metric timestamp"
    )
    
    value: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), nullable=False,
        doc="Metric value"
    )
    
    asset_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("assets.id"),
        doc="Associated asset (if metric is asset-specific)"
    )
    
    vulnerability_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("vulnerabilities.id"),
        doc="Associated vulnerability (if metric is vulnerability-specific)"
    )
    
    extra_data: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional metric metadata"
    )
