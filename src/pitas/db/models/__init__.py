"""Database models package."""

# Import all models to ensure they are registered with SQLAlchemy
from .base import Base
from .user import User, User<PERSON><PERSON>, CareerTier, CareerTrack

# Phase 2: Team Resource Management models
from .pentester import PentesterProfile
from .project import Project, PhaseTransition, ProjectDeliverable, ProjectTeamAssignment
from .resource_allocation import ResourceAllocation
from .skill_matrix import SkillMatrix, SecurityDomain, CertificationTier
from .capacity_plan import CapacityPlan

# Phase 3: Vulnerability Assessment models
from .vulnerability import (
    Vulnerability,
    VulnerabilityStatus,
    VulnerabilitySeverity
)

from .asset import (
    Asset,
    AssetType,
    AssetStatus,
    CriticalityLevel,
    AssetVulnerability,
    AssetDependency,
    DependencyType,
    ConfigurationItem
)

from .risk_assessment import (
    RiskAssessment,
    RemediationPlan,
    RiskLevel,
    ThreatActorType,
)

# Phase 4: Project Workflow and Remediation models
from .client import Client, ClientPortalUser, ClientDocument, ClientCommunication
from .remediation import (
    Remediation,
    RemediationEscalation,
    RemediationComment,
    RemediationAttachment
)
from .compliance import (
    ComplianceMapping,
    AuditTrail,
    ComplianceReport,
    ComplianceEvidence
)

# Phase 5: Training models
from .training import (
    CompetencyFramework,
    Competency,
    SkillAssessment,
    TrainingCourse,
    LearningPath,
    TrainingEnrollment,
    Certification,
    CertificationAchievement,
    CTFChallenge,
    CTFSubmission,
    CompetencyLevel,
    TrainingStatus,
    CertificationStatus,
)

# Phase 6: Career development models
from .career import (
    IndividualDevelopmentPlan,
    DevelopmentGoal,
    DevelopmentActivity,
    IDPStatus,
    GoalStatus,
    GoalPriority,
    ActivityType,
    ActivityStatus,
)
from .recognition import (
    Recognition,
    PeerNomination,
    NominationVote,
    Reward,
    RecognitionType,
    RecognitionStatus,
    AchievementCategory,
    RewardType,
    RewardStatus,
)
from .wellness import (
    WellnessCheck,
    WellnessAlert,
    WorkSchedule,
    WellnessResource,
    WellnessMetricType,
    AlertSeverity,
    AlertStatus,
    WorkScheduleType,
)
from .mentorship import (
    Mentorship,
    MentorshipSession,
    MentorProfile,
    MentorshipRequest,
    MentorshipType,
    MentorshipStatus,
    SessionType,
    SessionStatus,
)

# Phase 7: Integration Layer models
from .integration import (
    Integration,
    IntegrationSyncLog,
    DataMapping,
    IntegrationType,
    IntegrationStatus,
    SyncStatus,
)
from .knowledge import (
    KnowledgeDocument,
    DocumentLink,
    DocumentTemplate,
    KnowledgeGraph,
    DocumentType,
    DocumentStatus,
    LinkType,
)

# Phase 9: Advanced Analytics and Reporting Engine
from .analytics import (
    AnalyticsModel,
    ModelTrainingJob,
    AnalyticsPrediction,
    AnalyticsReport,
    AnalyticsAlert,
    AnalyticsMetric,
    AnalyticsDashboard,
)

__all__ = [
    # Base
    "Base",
    # User models
    "User",
    "UserRole",
    "CareerTier",
    "CareerTrack",
    # Phase 2: Team Resource Management models
    "PentesterProfile",
    "Project",
    "PhaseTransition",
    "ProjectDeliverable",
    "ProjectTeamAssignment",
    "ResourceAllocation",
    "SkillMatrix",
    "SecurityDomain",
    "CertificationTier",
    "CapacityPlan",
    # Phase 3: Vulnerability Assessment models
    "Vulnerability",
    "VulnerabilityStatus",
    "VulnerabilitySeverity",
    "Asset",
    "AssetType",
    "AssetStatus",
    "CriticalityLevel",
    "AssetVulnerability",
    "AssetDependency",
    "DependencyType",
    "ConfigurationItem",
    # Phase 3: Risk assessment models
    "RiskAssessment",
    "RemediationPlan",
    "RiskLevel",
    "ThreatActorType",
    # Phase 4: Project Workflow and Remediation models
    "Client",
    "ClientPortalUser",
    "ClientDocument",
    "ClientCommunication",
    "Remediation",
    "RemediationEscalation",
    "RemediationComment",
    "RemediationAttachment",
    "ComplianceMapping",
    "AuditTrail",
    "ComplianceReport",
    "ComplianceEvidence",
    # Phase 5: Training models
    "CompetencyFramework",
    "Competency",
    "SkillAssessment",
    "TrainingCourse",
    "LearningPath",
    "TrainingEnrollment",
    "Certification",
    "CertificationAchievement",
    "CTFChallenge",
    "CTFSubmission",
    "CompetencyLevel",
    "TrainingStatus",
    "CertificationStatus",
    # Phase 6: Career development models
    "IndividualDevelopmentPlan",
    "DevelopmentGoal",
    "DevelopmentActivity",
    "IDPStatus",
    "GoalStatus",
    "GoalPriority",
    "ActivityType",
    "ActivityStatus",
    # Recognition models
    "Recognition",
    "PeerNomination",
    "NominationVote",
    "Reward",
    "RecognitionType",
    "RecognitionStatus",
    "AchievementCategory",
    "RewardType",
    "RewardStatus",
    # Wellness models
    "WellnessCheck",
    "WellnessAlert",
    "WorkSchedule",
    "WellnessResource",
    "WellnessMetricType",
    "AlertSeverity",
    "AlertStatus",
    "WorkScheduleType",
    # Mentorship models
    "Mentorship",
    "MentorshipSession",
    "MentorProfile",
    "MentorshipRequest",
    "MentorshipType",
    "MentorshipStatus",
    "SessionType",
    "SessionStatus",
    # Phase 7: Integration Layer models
    "Integration",
    "IntegrationSyncLog",
    "DataMapping",
    "IntegrationType",
    "IntegrationStatus",
    "SyncStatus",
    # Knowledge management models
    "KnowledgeDocument",
    "DocumentLink",
    "DocumentTemplate",
    "KnowledgeGraph",
    "DocumentType",
    "DocumentStatus",
    "LinkType",
    # Phase 9: Advanced Analytics and Reporting Engine
    "AnalyticsModel",
    "ModelTrainingJob",
    "AnalyticsPrediction",
    "AnalyticsReport",
    "AnalyticsAlert",
    "AnalyticsMetric",
    "AnalyticsDashboard",
]