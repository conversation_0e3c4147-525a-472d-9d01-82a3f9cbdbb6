"""Capacity planning model for Phase 2 resource optimization."""

import enum
from datetime import datetime, date
from typing import Optional, Dict, Any, List
from uuid import UUID

from sqlalchemy import String, Integer, Float, Boolean, DateTime, Date, Text, JSON, ForeignKey, Enum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import ARRAY

from .base import Base


class CapacityPeriod(str, enum.Enum):
    """Time periods for capacity planning."""
    
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class CapacityStatus(str, enum.Enum):
    """Capacity plan status."""
    
    DRAFT = "draft"
    ACTIVE = "active"
    APPROVED = "approved"
    ARCHIVED = "archived"
    CANCELLED = "cancelled"


class CapacityIndicator(str, enum.Enum):
    """Visual capacity indicators."""
    
    GREEN = "green"      # Under 70% utilization
    YELLOW = "yellow"    # 70-85% utilization
    RED = "red"          # Over 85% utilization
    CRITICAL = "critical" # Over 95% utilization


class CapacityPlan(Base):
    """Capacity planning model for resource forecasting and optimization."""
    
    __tablename__ = "capacity_plan"
    
    # Plan Identification
    plan_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Name of the capacity plan"
    )
    
    plan_code: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique plan code identifier"
    )
    
    # Time Period
    period_type: Mapped[CapacityPeriod] = mapped_column(
        Enum(CapacityPeriod),
        nullable=False,
        doc="Type of planning period"
    )
    
    start_date: Mapped[date] = mapped_column(
        Date,
        nullable=False,
        doc="Start date of the planning period"
    )
    
    end_date: Mapped[date] = mapped_column(
        Date,
        nullable=False,
        doc="End date of the planning period"
    )
    
    # Plan Status
    status: Mapped[CapacityStatus] = mapped_column(
        Enum(CapacityStatus),
        nullable=False,
        default=CapacityStatus.DRAFT,
        doc="Current status of the capacity plan"
    )
    
    # Team Capacity Metrics
    total_team_capacity: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Total team capacity in hours for the period"
    )
    
    allocated_capacity: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Currently allocated capacity in hours"
    )
    
    available_capacity: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Available capacity in hours"
    )
    
    # Utilization Metrics
    target_utilization: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=85.0,
        doc="Target utilization percentage"
    )
    
    current_utilization: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Current utilization percentage"
    )
    
    projected_utilization: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Projected utilization percentage"
    )
    
    # Capacity Indicators
    capacity_indicator: Mapped[CapacityIndicator] = mapped_column(
        Enum(CapacityIndicator),
        nullable=False,
        default=CapacityIndicator.GREEN,
        doc="Visual capacity indicator"
    )
    
    # Team Composition
    total_team_members: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Total number of team members in plan"
    )
    
    available_team_members: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="Number of available team members"
    )
    
    # Skill Distribution
    skill_capacity_breakdown: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Capacity breakdown by skill domain"
    )
    
    skill_demand_forecast: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Forecasted demand by skill domain"
    )
    
    skill_gaps: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="Identified skill gaps"
    )
    
    # Project Demand
    planned_projects: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Number of planned projects"
    )
    
    estimated_project_hours: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Estimated total hours for planned projects"
    )
    
    # Forecasting and Predictions
    historical_utilization_trend: Mapped[Optional[List[float]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Historical utilization trend data"
    )
    
    predicted_demand: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="AI-predicted demand for the period"
    )
    
    confidence_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Confidence score for predictions (0-100)"
    )
    
    # Risk Assessment
    capacity_risks: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="Identified capacity risks"
    )
    
    mitigation_strategies: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="Risk mitigation strategies"
    )
    
    # Optimization Recommendations
    optimization_recommendations: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="AI-generated optimization recommendations"
    )
    
    resource_reallocation_suggestions: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Suggested resource reallocations"
    )
    
    # Performance Metrics
    efficiency_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Overall team efficiency score"
    )
    
    productivity_index: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Team productivity index"
    )
    
    # Approval and Workflow
    created_by: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Who created this capacity plan"
    )
    
    approved_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Who approved this capacity plan"
    )
    
    approved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the plan was approved"
    )
    
    # Additional Information
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Additional notes about the capacity plan"
    )
    
    assumptions: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Assumptions made in this capacity plan"
    )
    
    extra_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Additional capacity plan metadata"
    )
    
    # Status flags
    is_baseline: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is the baseline capacity plan"
    )
    
    auto_update_enabled: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether to auto-update metrics"
    )
    
    @property
    def utilization_percentage(self) -> float:
        """Calculate current utilization percentage."""
        if self.total_team_capacity > 0:
            return (self.allocated_capacity / self.total_team_capacity) * 100
        return 0.0
    
    @property
    def capacity_buffer(self) -> float:
        """Calculate capacity buffer in hours."""
        return self.total_team_capacity - self.allocated_capacity
    
    @property
    def capacity_buffer_percentage(self) -> float:
        """Calculate capacity buffer as percentage."""
        if self.total_team_capacity > 0:
            return (self.capacity_buffer / self.total_team_capacity) * 100
        return 0.0
    
    @property
    def is_over_capacity(self) -> bool:
        """Check if plan is over capacity."""
        return self.allocated_capacity > self.total_team_capacity
    
    @property
    def is_at_target_utilization(self) -> bool:
        """Check if at target utilization."""
        return abs(self.current_utilization - self.target_utilization) <= 5.0
    
    @property
    def days_in_period(self) -> int:
        """Calculate number of days in the planning period."""
        return (self.end_date - self.start_date).days + 1
    
    def update_capacity_indicator(self) -> None:
        """Update capacity indicator based on current utilization."""
        if self.current_utilization >= 95:
            self.capacity_indicator = CapacityIndicator.CRITICAL
        elif self.current_utilization >= 85:
            self.capacity_indicator = CapacityIndicator.RED
        elif self.current_utilization >= 70:
            self.capacity_indicator = CapacityIndicator.YELLOW
        else:
            self.capacity_indicator = CapacityIndicator.GREEN
    
    def __repr__(self) -> str:
        """String representation of the capacity plan."""
        return f"<CapacityPlan(code={self.plan_code}, period={self.period_type}, utilization={self.current_utilization:.1f}%)>"
