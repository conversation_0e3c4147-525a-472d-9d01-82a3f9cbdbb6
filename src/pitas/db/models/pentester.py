"""Pentester profile model for Phase 2 resource management."""

import enum
from datetime import datetime, time
from typing import List, Optional, Dict, Any
from uuid import UUID

from sqlalchemy import String, Integer, Float, Boolean, DateTime, Time, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import ARRAY

from .base import Base


class AvailabilityStatus(str, enum.Enum):
    """Availability status for pentesters."""
    
    AVAILABLE = "available"
    BUSY = "busy"
    PARTIALLY_AVAILABLE = "partially_available"
    ON_LEAVE = "on_leave"
    TRAINING = "training"
    UNAVAILABLE = "unavailable"


class WorkingTimeZone(str, enum.Enum):
    """Working time zones for global team coordination."""
    
    UTC = "UTC"
    EST = "America/New_York"
    PST = "America/Los_Angeles"
    GMT = "Europe/London"
    CET = "Europe/Berlin"
    JST = "Asia/Tokyo"
    AEST = "Australia/Sydney"
    IST = "Asia/Kolkata"


class PentesterProfile(Base):
    """Pentester profile for resource management and optimization."""
    
    __tablename__ = "pentesterprofile"
    
    # Basic Information
    employee_id: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        doc="Unique employee identifier"
    )
    
    # Link to user account
    user_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("user.id", ondelete="SET NULL"),
        nullable=True,
        doc="Reference to user account"
    )
    
    # Personal Information
    first_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="First name"
    )
    
    last_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Last name"
    )
    
    email: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        doc="Email address"
    )
    
    # Availability and Capacity
    availability_hours: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=40,
        doc="Available hours per week"
    )
    
    current_utilization: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Current utilization percentage (0-100)"
    )
    
    max_concurrent_projects: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=3,
        doc="Maximum concurrent projects"
    )
    
    availability_status: Mapped[AvailabilityStatus] = mapped_column(
        String(50),
        nullable=False,
        default=AvailabilityStatus.AVAILABLE,
        doc="Current availability status"
    )
    
    # Geographic and Time Zone Information
    location: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Geographic location"
    )
    
    working_timezone: Mapped[WorkingTimeZone] = mapped_column(
        String(50),
        nullable=False,
        default=WorkingTimeZone.UTC,
        doc="Primary working timezone"
    )
    
    working_hours_start: Mapped[Optional[time]] = mapped_column(
        Time,
        nullable=True,
        doc="Daily working hours start time"
    )
    
    working_hours_end: Mapped[Optional[time]] = mapped_column(
        Time,
        nullable=True,
        doc="Daily working hours end time"
    )
    
    # Performance Metrics
    performance_rating: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Overall performance rating (1-5 scale)"
    )
    
    projects_completed: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        doc="Total projects completed"
    )
    
    average_project_rating: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Average client rating for projects"
    )
    
    # Specialization and Preferences
    primary_specializations: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="Primary security domain specializations"
    )
    
    secondary_specializations: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="Secondary security domain specializations"
    )
    
    preferred_project_types: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        nullable=True,
        doc="Preferred types of pentesting projects"
    )
    
    # Career Development
    seniority_level: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="junior",
        doc="Seniority level (junior, mid, senior, lead, principal)"
    )
    
    hire_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Date of hire"
    )
    
    # Additional metadata
    notes: Mapped[Optional[str]] = mapped_column(
        String(1000),
        nullable=True,
        doc="Additional notes about the pentester"
    )
    
    extra_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Additional metadata as JSON"
    )
    
    # Status flags
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether the pentester is active"
    )
    
    is_team_lead: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether the pentester is a team lead"
    )
    
    can_mentor: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether the pentester can mentor others"
    )
    
    # Relationships
    user: Mapped[Optional["User"]] = relationship(
        "User",
        back_populates="pentester_profile"
    )
    
    skill_matrices: Mapped[List["SkillMatrix"]] = relationship(
        "SkillMatrix",
        back_populates="pentester",
        cascade="all, delete-orphan"
    )
    
    resource_allocations: Mapped[List["ResourceAllocation"]] = relationship(
        "ResourceAllocation",
        back_populates="pentester",
        cascade="all, delete-orphan"
    )
    
    @property
    def full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def available_hours_remaining(self) -> float:
        """Calculate remaining available hours based on current utilization."""
        return self.availability_hours * (1 - self.current_utilization / 100)
    
    def __repr__(self) -> str:
        """String representation of the pentester profile."""
        return f"<PentesterProfile(employee_id={self.employee_id}, name={self.full_name})>"
