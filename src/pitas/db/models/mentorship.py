"""Mentorship and peer support models."""

from datetime import datetime, date
from typing import Optional, List
from uuid import UUID
import enum

from sqlalchemy import String, <PERSON><PERSON><PERSON>, DateTime, Text, Integer, Enum as S<PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, Float, Date
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import JSON<PERSON>

from pitas.db.base import Base


class MentorshipType(str, enum.Enum):
    """Types of mentorship relationships."""
    FORMAL = "formal"  # Officially assigned mentorship
    INFORMAL = "informal"  # Self-organized mentorship
    PEER = "peer"  # Peer-to-peer mentoring
    REVERSE = "reverse"  # Junior mentoring senior (e.g., tech skills)
    GROUP = "group"  # Group mentoring sessions
    CROSS_FUNCTIONAL = "cross_functional"  # Across different teams/departments


class MentorshipStatus(str, enum.Enum):
    """Status of mentorship relationships."""
    REQUESTED = "requested"
    MATCHED = "matched"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class SessionType(str, enum.Enum):
    """Types of mentorship sessions."""
    ONE_ON_ONE = "one_on_one"
    GROUP_SESSION = "group_session"
    SHADOWING = "shadowing"
    PROJECT_REVIEW = "project_review"
    SKILL_DEVELOPMENT = "skill_development"
    CAREER_GUIDANCE = "career_guidance"
    FEEDBACK_SESSION = "feedback_session"


class SessionStatus(str, enum.Enum):
    """Status of mentorship sessions."""
    SCHEDULED = "scheduled"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"
    RESCHEDULED = "rescheduled"


class Mentorship(Base):
    """Mentorship relationships between employees."""
    
    __tablename__ = "mentorships"
    
    # Participants
    mentor_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    mentee_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    
    # Relationship Details
    mentorship_type: Mapped[MentorshipType] = mapped_column(SQLEnum(MentorshipType), nullable=False)
    status: Mapped[MentorshipStatus] = mapped_column(SQLEnum(MentorshipStatus), default=MentorshipStatus.REQUESTED, nullable=False)
    
    # Timeline
    start_date: Mapped[date] = mapped_column(Date, nullable=False)
    planned_end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    actual_end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    
    # Goals and Focus Areas
    primary_goals: Mapped[dict] = mapped_column(JSONB, nullable=False)  # List of mentorship goals
    focus_areas: Mapped[dict] = mapped_column(JSONB, nullable=False)  # Skills/areas to focus on
    success_criteria: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # How to measure success
    
    # Meeting Schedule
    meeting_frequency: Mapped[str] = mapped_column(String(50), nullable=False)  # weekly, biweekly, monthly
    preferred_meeting_duration: Mapped[int] = mapped_column(Integer, default=60, nullable=False)  # Minutes
    preferred_meeting_format: Mapped[str] = mapped_column(String(50), default="video_call", nullable=False)
    
    # Progress Tracking
    overall_progress: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)  # 0.0 to 1.0
    mentor_satisfaction: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-10 scale
    mentee_satisfaction: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-10 scale
    
    # Administrative
    assigned_by_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True)  # Who assigned this mentorship
    program_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # Mentorship program identifier
    
    # Notes and Documentation
    initial_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    mentor_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    mentee_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    completion_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    mentor: Mapped["User"] = relationship("User", foreign_keys=[mentor_id], back_populates="mentorships_as_mentor")
    mentee: Mapped["User"] = relationship("User", foreign_keys=[mentee_id], back_populates="mentorships_as_mentee")
    assigned_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_by_id])
    sessions: Mapped[List["MentorshipSession"]] = relationship("MentorshipSession", back_populates="mentorship", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the mentorship."""
        return f"<Mentorship(id={self.id}, mentor_id={self.mentor_id}, mentee_id={self.mentee_id})>"


class MentorshipSession(Base):
    """Individual mentorship sessions."""
    
    __tablename__ = "mentorship_sessions"
    
    # Basic Information
    mentorship_id: Mapped[UUID] = mapped_column(ForeignKey("mentorships.id"), nullable=False, index=True)
    session_type: Mapped[SessionType] = mapped_column(SQLEnum(SessionType), nullable=False)
    status: Mapped[SessionStatus] = mapped_column(SQLEnum(SessionStatus), default=SessionStatus.SCHEDULED, nullable=False)
    
    # Scheduling
    scheduled_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    scheduled_duration: Mapped[int] = mapped_column(Integer, nullable=False)  # Minutes
    actual_start_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    actual_end_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Session Details
    agenda: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    topics_covered: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    goals_for_session: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Location and Format
    meeting_format: Mapped[str] = mapped_column(String(50), nullable=False)  # in_person, video_call, phone
    location: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    meeting_link: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Outcomes and Follow-up
    session_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    mentor_feedback: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    mentee_feedback: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    action_items: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    next_session_goals: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Ratings and Effectiveness
    mentor_rating: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-10 scale
    mentee_rating: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-10 scale
    session_effectiveness: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-10 scale
    
    # Resources and Materials
    resources_shared: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    homework_assigned: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    mentorship: Mapped["Mentorship"] = relationship("Mentorship", back_populates="sessions")
    
    def __repr__(self) -> str:
        """String representation of the mentorship session."""
        return f"<MentorshipSession(id={self.id}, mentorship_id={self.mentorship_id}, date={self.scheduled_date})>"


class MentorProfile(Base):
    """Mentor profiles and capabilities."""
    
    __tablename__ = "mentor_profiles"
    
    # Basic Information
    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, unique=True, index=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_available: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Mentor Capabilities
    expertise_areas: Mapped[dict] = mapped_column(JSONB, nullable=False)  # Areas of expertise
    mentoring_skills: Mapped[dict] = mapped_column(JSONB, nullable=False)  # Mentoring-specific skills
    industry_experience: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Industry background
    
    # Availability and Preferences
    max_mentees: Mapped[int] = mapped_column(Integer, default=3, nullable=False)
    current_mentees: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    preferred_mentee_level: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Junior, mid, senior
    preferred_mentorship_type: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Time Commitment
    hours_per_week: Mapped[float] = mapped_column(Float, default=2.0, nullable=False)
    available_time_slots: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    timezone_preferences: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Experience and Ratings
    total_mentorships: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    completed_mentorships: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    average_rating: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    total_ratings: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Profile Information
    mentoring_philosophy: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    success_stories: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    approach_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Certifications and Training
    mentoring_certifications: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    training_completed: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    last_training_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="mentor_profile")
    
    def __repr__(self) -> str:
        """String representation of the mentor profile."""
        return f"<MentorProfile(id={self.id}, user_id={self.user_id}, active={self.is_active})>"


class MentorshipRequest(Base):
    """Requests for mentorship matching."""
    
    __tablename__ = "mentorship_requests"
    
    # Requester Information
    requester_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    request_type: Mapped[str] = mapped_column(String(50), nullable=False)  # mentor_request, mentee_request
    
    # Request Details
    status: Mapped[str] = mapped_column(String(50), default="pending", nullable=False)
    priority: Mapped[str] = mapped_column(String(20), default="medium", nullable=False)
    
    # Requirements and Preferences
    desired_expertise: Mapped[dict] = mapped_column(JSONB, nullable=False)
    goals_and_objectives: Mapped[str] = mapped_column(Text, nullable=False)
    preferred_mentor_level: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    preferred_meeting_frequency: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Timeline
    requested_start_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    preferred_duration: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Months
    
    # Matching Criteria
    location_preference: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    timezone_preference: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    language_preference: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Additional Information
    background_info: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    special_requirements: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    previous_mentoring_experience: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Processing
    assigned_to_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True)  # HR/coordinator
    matched_with_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True)
    matching_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    matching_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Timeline
    request_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    processed_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    matched_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    requester: Mapped["User"] = relationship("User", foreign_keys=[requester_id])
    assigned_to: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_to_id])
    matched_with: Mapped[Optional["User"]] = relationship("User", foreign_keys=[matched_with_id])
    
    def __repr__(self) -> str:
        """String representation of the mentorship request."""
        return f"<MentorshipRequest(id={self.id}, requester_id={self.requester_id}, status={self.status})>"
