"""Knowledge management models for Phase 7: Obsidian Integration."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class DocumentType(str, Enum):
    """Types of knowledge documents."""
    VULNERABILITY_REPORT = "vulnerability_report"
    SECURITY_PROCEDURE = "security_procedure"
    INCIDENT_RESPONSE = "incident_response"
    THREAT_ANALYSIS = "threat_analysis"
    REMEDIATION_GUIDE = "remediation_guide"
    ASSESSMENT_REPORT = "assessment_report"
    KNOWLEDGE_ARTICLE = "knowledge_article"
    TEMPLATE = "template"


class DocumentStatus(str, Enum):
    """Document status values."""
    DRAFT = "draft"
    REVIEW = "review"
    APPROVED = "approved"
    PUBLISHED = "published"
    ARCHIVED = "archived"


class KnowledgeDocument(Base):
    """Knowledge document model for Obsidian integration."""
    
    __tablename__ = "knowledge_documents"
    
    title: Mapped[str] = mapped_column(String(500), nullable=False, index=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    document_type: Mapped[DocumentType] = mapped_column(String(50), nullable=False, index=True)
    status: Mapped[DocumentStatus] = mapped_column(String(20), default=DocumentStatus.DRAFT)
    
    # File information
    file_path: Mapped[Optional[str]] = mapped_column(String(1000))
    file_name: Mapped[str] = mapped_column(String(255), nullable=False)
    file_hash: Mapped[Optional[str]] = mapped_column(String(64))  # SHA-256 hash
    
    # Obsidian specific
    obsidian_path: Mapped[Optional[str]] = mapped_column(String(1000))
    obsidian_id: Mapped[Optional[str]] = mapped_column(String(255), index=True)
    last_obsidian_sync: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Metadata
    tags: Mapped[Optional[list[str]]] = mapped_column(JSON)
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Versioning
    version: Mapped[int] = mapped_column(Integer, default=1)
    parent_document_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("knowledge_documents.id"),
        index=True
    )
    
    # Author information
    author_id: Mapped[Optional[UUID]] = mapped_column(String(36))  # User ID
    reviewer_id: Mapped[Optional[UUID]] = mapped_column(String(36))  # User ID
    
    # Timestamps
    published_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    reviewed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    
    # Relationships
    parent_document: Mapped[Optional["KnowledgeDocument"]] = relationship(
        "KnowledgeDocument",
        remote_side="KnowledgeDocument.id",
        back_populates="child_documents"
    )
    child_documents: Mapped[list["KnowledgeDocument"]] = relationship(
        "KnowledgeDocument",
        back_populates="parent_document"
    )
    links: Mapped[list["DocumentLink"]] = relationship(
        "DocumentLink",
        foreign_keys="DocumentLink.source_document_id",
        back_populates="source_document",
        cascade="all, delete-orphan"
    )
    backlinks: Mapped[list["DocumentLink"]] = relationship(
        "DocumentLink",
        foreign_keys="DocumentLink.target_document_id",
        back_populates="target_document"
    )
    
    def __repr__(self) -> str:
        return f"<KnowledgeDocument(title='{self.title}', type='{self.document_type}')>"


class LinkType(str, Enum):
    """Types of document links."""
    REFERENCE = "reference"
    RELATED = "related"
    DEPENDENCY = "dependency"
    SUPERSEDES = "supersedes"
    IMPLEMENTS = "implements"
    MITRE_TECHNIQUE = "mitre_technique"
    CVE_REFERENCE = "cve_reference"


class DocumentLink(Base):
    """Links between knowledge documents."""
    
    __tablename__ = "document_links"
    
    source_document_id: Mapped[UUID] = mapped_column(
        ForeignKey("knowledge_documents.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    target_document_id: Mapped[UUID] = mapped_column(
        ForeignKey("knowledge_documents.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )
    
    link_type: Mapped[LinkType] = mapped_column(String(50), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # Link metadata
    link_text: Mapped[Optional[str]] = mapped_column(String(500))
    context: Mapped[Optional[str]] = mapped_column(Text)
    
    # Relationships
    source_document: Mapped["KnowledgeDocument"] = relationship(
        "KnowledgeDocument",
        foreign_keys=[source_document_id],
        back_populates="links"
    )
    target_document: Mapped["KnowledgeDocument"] = relationship(
        "KnowledgeDocument",
        foreign_keys=[target_document_id],
        back_populates="backlinks"
    )
    
    def __repr__(self) -> str:
        return f"<DocumentLink(type='{self.link_type}', source='{self.source_document_id}')>"


class DocumentTemplate(Base):
    """Templates for automated document generation."""
    
    __tablename__ = "document_templates"
    
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    document_type: Mapped[DocumentType] = mapped_column(String(50), nullable=False)
    
    # Template content
    template_content: Mapped[str] = mapped_column(Text, nullable=False)
    template_variables: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Generation rules
    generation_rules: Mapped[Optional[dict]] = mapped_column(JSON)
    auto_generate: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Template metadata
    tags: Mapped[Optional[list[str]]] = mapped_column(JSON)
    category: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Versioning
    version: Mapped[str] = mapped_column(String(20), default="1.0")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    def __repr__(self) -> str:
        return f"<DocumentTemplate(name='{self.name}', type='{self.document_type}')>"


class KnowledgeGraph(Base):
    """Knowledge graph relationships for enhanced navigation."""
    
    __tablename__ = "knowledge_graph"
    
    entity_type: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    entity_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    entity_name: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # Relationships
    related_entity_type: Mapped[str] = mapped_column(String(100), nullable=False)
    related_entity_id: Mapped[str] = mapped_column(String(255), nullable=False)
    related_entity_name: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # Relationship metadata
    relationship_type: Mapped[str] = mapped_column(String(100), nullable=False)
    relationship_strength: Mapped[Optional[float]] = mapped_column()  # 0.0 to 1.0
    
    # Context
    context: Mapped[Optional[dict]] = mapped_column(JSON)
    
    def __repr__(self) -> str:
        return f"<KnowledgeGraph({self.entity_type}:{self.entity_name} -> {self.related_entity_type}:{self.related_entity_name})>"
