"""Recognition and rewards system models."""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
import enum

from sqlalchemy import String, <PERSON>olean, DateTime, Text, Integer, Enum as SQLE<PERSON>, ForeignKey, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON><PERSON>

from pitas.db.base import Base


class RecognitionType(str, enum.Enum):
    """Types of recognition."""
    PEER_NOMINATION = "peer_nomination"
    MANAGER_RECOGNITION = "manager_recognition"
    ACHIEVEMENT_AWARD = "achievement_award"
    INNOVATION_AWARD = "innovation_award"
    CLIENT_FEEDBACK = "client_feedback"
    MILESTONE_RECOGNITION = "milestone_recognition"
    TEAM_CONTRIBUTION = "team_contribution"
    LEADERSHIP_EXCELLENCE = "leadership_excellence"


class RecognitionStatus(str, enum.Enum):
    """Recognition status."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"


class AchievementCategory(str, enum.Enum):
    """Achievement categories."""
    TECHNICAL_EXCELLENCE = "technical_excellence"
    INNOVATION = "innovation"
    LEADERSHIP = "leadership"
    COLLABORATION = "collaboration"
    CLIENT_SUCCESS = "client_success"
    MENTORING = "mentoring"
    CONTINUOUS_LEARNING = "continuous_learning"
    PROCESS_IMPROVEMENT = "process_improvement"


class Recognition(Base):
    """Recognition and awards given to employees."""
    
    __tablename__ = "recognitions"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    recognition_type: Mapped[RecognitionType] = mapped_column(SQLEnum(RecognitionType), nullable=False)
    status: Mapped[RecognitionStatus] = mapped_column(SQLEnum(RecognitionStatus), default=RecognitionStatus.PENDING, nullable=False)
    
    # Recipient and Nominator
    recipient_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    nominator_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True, index=True)
    approver_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True, index=True)
    
    # Recognition Details
    points_awarded: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    monetary_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # USD value if applicable
    achievement_category: Mapped[Optional[AchievementCategory]] = mapped_column(SQLEnum(AchievementCategory), nullable=True)
    
    # Timeline
    nomination_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    approval_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    presentation_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Visibility and Sharing
    is_public: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_featured: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    share_externally: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Evidence and Documentation
    evidence_urls: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Links to supporting evidence
    impact_metrics: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Quantifiable impact
    testimonials: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Client/peer testimonials
    
    # Additional Information
    tags: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Searchable tags
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    recipient: Mapped["User"] = relationship("User", foreign_keys=[recipient_id], back_populates="recognitions_received")
    nominator: Mapped[Optional["User"]] = relationship("User", foreign_keys=[nominator_id])
    approver: Mapped[Optional["User"]] = relationship("User", foreign_keys=[approver_id])
    
    def __repr__(self) -> str:
        """String representation of the recognition."""
        return f"<Recognition(id={self.id}, recipient_id={self.recipient_id}, type={self.recognition_type})>"


class PeerNomination(Base):
    """Peer-to-peer recognition nominations."""
    
    __tablename__ = "peer_nominations"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Participants
    nominee_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    nominator_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    
    # Recognition Details
    achievement_category: Mapped[AchievementCategory] = mapped_column(SQLEnum(AchievementCategory), nullable=False)
    impact_description: Mapped[str] = mapped_column(Text, nullable=False)
    specific_examples: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Voting and Approval
    status: Mapped[RecognitionStatus] = mapped_column(SQLEnum(RecognitionStatus), default=RecognitionStatus.PENDING, nullable=False)
    votes_received: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    votes_required: Mapped[int] = mapped_column(Integer, default=3, nullable=False)
    
    # Timeline
    submission_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    voting_deadline: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    decision_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    nominee: Mapped["User"] = relationship("User", foreign_keys=[nominee_id])
    nominator: Mapped["User"] = relationship("User", foreign_keys=[nominator_id])
    votes: Mapped[List["NominationVote"]] = relationship("NominationVote", back_populates="nomination", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the peer nomination."""
        return f"<PeerNomination(id={self.id}, nominee_id={self.nominee_id}, category={self.achievement_category})>"


class NominationVote(Base):
    """Votes on peer nominations."""
    
    __tablename__ = "nomination_votes"
    
    # Voting Information
    nomination_id: Mapped[UUID] = mapped_column(ForeignKey("peer_nominations.id"), nullable=False, index=True)
    voter_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    
    # Vote Details
    vote: Mapped[bool] = mapped_column(Boolean, nullable=False)  # True for support, False for oppose
    comment: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    vote_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    
    # Relationships
    nomination: Mapped["PeerNomination"] = relationship("PeerNomination", back_populates="votes")
    voter: Mapped["User"] = relationship("User", foreign_keys=[voter_id])
    
    def __repr__(self) -> str:
        """String representation of the nomination vote."""
        return f"<NominationVote(id={self.id}, nomination_id={self.nomination_id}, vote={self.vote})>"


class RewardType(str, enum.Enum):
    """Types of rewards."""
    MONETARY_BONUS = "monetary_bonus"
    GIFT_CARD = "gift_card"
    EXTRA_PTO = "extra_pto"
    FLEXIBLE_SCHEDULE = "flexible_schedule"
    PROFESSIONAL_DEVELOPMENT = "professional_development"
    EQUIPMENT_UPGRADE = "equipment_upgrade"
    CONFERENCE_ATTENDANCE = "conference_attendance"
    RECOGNITION_CEREMONY = "recognition_ceremony"
    TEAM_CELEBRATION = "team_celebration"
    CUSTOM_REWARD = "custom_reward"


class RewardStatus(str, enum.Enum):
    """Reward fulfillment status."""
    PENDING = "pending"
    APPROVED = "approved"
    FULFILLED = "fulfilled"
    CANCELLED = "cancelled"
    EXPIRED = "expired"


class Reward(Base):
    """Rewards and benefits given to employees."""
    
    __tablename__ = "rewards"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    reward_type: Mapped[RewardType] = mapped_column(SQLEnum(RewardType), nullable=False)
    status: Mapped[RewardStatus] = mapped_column(SQLEnum(RewardStatus), default=RewardStatus.PENDING, nullable=False)
    
    # Recipient and Authorization
    recipient_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    authorized_by_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True, index=True)
    recognition_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("recognitions.id"), nullable=True, index=True)
    
    # Value and Details
    monetary_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    points_cost: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    quantity: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    
    # Timeline
    awarded_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    expiration_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    fulfilled_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Fulfillment Details
    fulfillment_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    vendor_information: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    tracking_information: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Custom metadata
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    recipient: Mapped["User"] = relationship("User", foreign_keys=[recipient_id])
    authorized_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[authorized_by_id])
    recognition: Mapped[Optional["Recognition"]] = relationship("Recognition", foreign_keys=[recognition_id])
    
    def __repr__(self) -> str:
        """String representation of the reward."""
        return f"<Reward(id={self.id}, recipient_id={self.recipient_id}, type={self.reward_type})>"
