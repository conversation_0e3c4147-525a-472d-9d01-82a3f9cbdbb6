"""Risk assessment models for RBVM (Risk-Based Vulnerability Management)."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import (
    String, Text, DateTime, Numeric, Integer, Boolean, 
    ForeignKey, JSON, Index, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class RiskLevel(str, Enum):
    """Risk level classifications."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    NEGLIGIBLE = "negligible"


class ThreatActorType(str, Enum):
    """Types of threat actors."""
    NATION_STATE = "nation_state"
    CYBERCRIMINAL = "cybercriminal"
    HACKTIVIST = "hacktivist"
    INSIDER = "insider"
    SCRIPT_KIDDIE = "script_kiddie"
    UNKNOWN = "unknown"


class RiskAssessment(Base):
    """Risk assessment for vulnerabilities using RBVM methodology."""
    
    __tablename__ = "risk_assessments"
    __table_args__ = (
        Index("idx_risk_assessment_vulnerability", "vulnerability_id"),
        Index("idx_risk_assessment_asset", "asset_id"),
        Index("idx_risk_assessment_level", "risk_level"),
        Index("idx_risk_assessment_score", "risk_score"),
        CheckConstraint("risk_score >= 0.0 AND risk_score <= 10.0", name="risk_score_range"),
        CheckConstraint("business_impact >= 0.0 AND business_impact <= 10.0", name="business_impact_range"),
        CheckConstraint("threat_likelihood >= 0.0 AND threat_likelihood <= 10.0", name="threat_likelihood_range"),
    )

    vulnerability_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("vulnerabilities.id"), nullable=False
    )
    
    asset_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("assets.id"), nullable=False
    )

    # Risk scoring
    risk_score: Mapped[Decimal] = mapped_column(
        Numeric(3, 1), nullable=False,
        doc="Overall risk score (0.0-10.0)"
    )
    
    risk_level: Mapped[RiskLevel] = mapped_column(
        String(20), nullable=False,
        doc="Risk level classification"
    )
    
    true_risk_score: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="TruRisk methodology score"
    )

    # Business impact factors
    business_impact: Mapped[Decimal] = mapped_column(
        Numeric(3, 1), nullable=False,
        doc="Business impact score (0.0-10.0)"
    )
    
    financial_impact: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(12, 2),
        doc="Estimated financial impact in USD"
    )
    
    regulatory_impact: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Regulatory compliance impact"
    )
    
    reputation_impact: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Reputation impact score (0.0-10.0)"
    )

    # Threat intelligence factors
    threat_likelihood: Mapped[Decimal] = mapped_column(
        Numeric(3, 1), nullable=False,
        doc="Threat likelihood score (0.0-10.0)"
    )
    
    exploit_availability: Mapped[bool] = mapped_column(
        Boolean, default=False,
        doc="Whether public exploits are available"
    )
    
    active_exploitation: Mapped[bool] = mapped_column(
        Boolean, default=False,
        doc="Whether vulnerability is being actively exploited"
    )
    
    threat_actors: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Known threat actors targeting this vulnerability"
    )

    # Environmental factors
    asset_exposure: Mapped[Optional[str]] = mapped_column(
        String(50),
        doc="Asset exposure level (internal, dmz, external)"
    )
    
    network_segmentation: Mapped[Optional[str]] = mapped_column(
        String(50),
        doc="Network segmentation effectiveness"
    )
    
    compensating_controls: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Compensating security controls in place"
    )

    # Assessment metadata
    assessment_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False,
        doc="When the risk assessment was performed"
    )
    
    assessor_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        doc="ID of the user who performed the assessment"
    )
    
    methodology: Mapped[str] = mapped_column(
        String(50), nullable=False, default="RBVM",
        doc="Risk assessment methodology used"
    )
    
    confidence_level: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Confidence level in the assessment (0.0-10.0)"
    )
    
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Additional assessment notes"
    )
    
    extra_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        doc="Additional assessment metadata"
    )

    # Relationships
    vulnerability: Mapped["Vulnerability"] = relationship("Vulnerability")
    asset: Mapped["Asset"] = relationship("Asset")


class ThreatIntelligence(Base):
    """Threat intelligence data for vulnerability context."""
    
    __tablename__ = "threat_intelligence"
    __table_args__ = (
        Index("idx_threat_intel_vulnerability", "vulnerability_id"),
        Index("idx_threat_intel_source", "source"),
        Index("idx_threat_intel_timestamp", "timestamp"),
        Index("idx_threat_intel_actor_type", "actor_type"),
    )

    vulnerability_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("vulnerabilities.id"), nullable=False
    )

    # Intelligence source
    source: Mapped[str] = mapped_column(
        String(100), nullable=False,
        doc="Threat intelligence source"
    )
    
    source_confidence: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(3, 1),
        doc="Confidence in the source (0.0-10.0)"
    )
    
    timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False,
        doc="When the intelligence was collected"
    )

    # Threat actor information
    actor_type: Mapped[Optional[ThreatActorType]] = mapped_column(
        String(20),
        doc="Type of threat actor"
    )
    
    actor_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Known threat actor name or group"
    )
    
    actor_motivation: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Threat actor motivation"
    )

    # Exploitation details
    exploitation_method: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Known exploitation methods"
    )
    
    exploit_complexity: Mapped[Optional[str]] = mapped_column(
        String(20),
        doc="Exploitation complexity (low, medium, high)"
    )
    
    target_sectors: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Targeted industry sectors"
    )
    
    target_regions: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Targeted geographical regions"
    )

    # Intelligence metadata
    iocs: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Indicators of Compromise (IoCs)"
    )
    
    ttps: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="Tactics, Techniques, and Procedures"
    )
    
    mitre_techniques: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        doc="MITRE ATT&CK technique IDs"
    )
    
    raw_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        doc="Raw threat intelligence data"
    )

    # Relationships
    vulnerability: Mapped["Vulnerability"] = relationship("Vulnerability")


class RemediationPlan(Base):
    """Remediation planning and tracking."""
    
    __tablename__ = "remediation_plans"
    __table_args__ = (
        Index("idx_remediation_vulnerability", "vulnerability_id"),
        Index("idx_remediation_priority", "priority"),
        Index("idx_remediation_status", "status"),
        Index("idx_remediation_due_date", "due_date"),
    )

    vulnerability_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True), ForeignKey("vulnerabilities.id"), nullable=False
    )

    # Planning details
    priority: Mapped[int] = mapped_column(
        Integer, nullable=False,
        doc="Remediation priority (1 = highest)"
    )
    
    status: Mapped[str] = mapped_column(
        String(20), nullable=False, default="planned",
        doc="Remediation status"
    )
    
    remediation_type: Mapped[str] = mapped_column(
        String(50), nullable=False,
        doc="Type of remediation (patch, configuration, mitigation)"
    )
    
    description: Mapped[str] = mapped_column(
        Text, nullable=False,
        doc="Remediation description and steps"
    )

    # Timeline
    planned_start_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Planned start date for remediation"
    )
    
    due_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Remediation due date"
    )
    
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Actual start date"
    )
    
    completion_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Actual completion date"
    )

    # Resource allocation
    assigned_to: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        doc="User ID of assigned remediation owner"
    )
    
    estimated_effort_hours: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(6, 2),
        doc="Estimated effort in hours"
    )
    
    actual_effort_hours: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(6, 2),
        doc="Actual effort in hours"
    )

    # Tracking
    progress_percentage: Mapped[int] = mapped_column(
        Integer, default=0,
        doc="Completion percentage (0-100)"
    )
    
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Remediation notes and updates"
    )
    
    extra_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        doc="Additional remediation metadata"
    )

    # Relationships
    vulnerability: Mapped["Vulnerability"] = relationship("Vulnerability")
