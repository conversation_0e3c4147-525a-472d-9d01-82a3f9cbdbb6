"""Work-life balance and wellness monitoring models."""

from datetime import datetime, date
from typing import Optional, List
from uuid import UUID
import enum

from sqlalchemy import String, Boolean, DateTime, Text, Integer, Enum as SQLE<PERSON>, ForeignKey, Float, Date
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import JSONB

from pitas.db.base import Base


class WellnessMetricType(str, enum.Enum):
    """Types of wellness metrics."""
    STRESS_LEVEL = "stress_level"
    WORKLOAD_SATISFACTION = "workload_satisfaction"
    WORK_LIFE_BALANCE = "work_life_balance"
    JOB_SATISFACTION = "job_satisfaction"
    TEAM_SATISFACTION = "team_satisfaction"
    MANAGER_SATISFACTION = "manager_satisfaction"
    CAREER_SATISFACTION = "career_satisfaction"
    BURNOUT_RISK = "burnout_risk"
    ENGAGEMENT_LEVEL = "engagement_level"
    ENERGY_LEVEL = "energy_level"


class AlertSeverity(str, enum.Enum):
    """Severity levels for wellness alerts."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(str, enum.Enum):
    """Status of wellness alerts."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    DISMISSED = "dismissed"


class WellnessCheck(Base):
    """Regular wellness check-ins and assessments."""
    
    __tablename__ = "wellness_checks"
    
    # Basic Information
    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    check_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Overall Scores (1-10 scale)
    overall_wellness_score: Mapped[int] = mapped_column(Integer, nullable=False)
    stress_level: Mapped[int] = mapped_column(Integer, nullable=False)  # 1=low stress, 10=high stress
    energy_level: Mapped[int] = mapped_column(Integer, nullable=False)  # 1=low energy, 10=high energy
    job_satisfaction: Mapped[int] = mapped_column(Integer, nullable=False)
    work_life_balance: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Workload Assessment
    workload_rating: Mapped[int] = mapped_column(Integer, nullable=False)  # 1=too light, 5=perfect, 10=overwhelming
    hours_worked_last_week: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    overtime_hours: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    weekend_work_hours: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Engagement and Motivation
    engagement_level: Mapped[int] = mapped_column(Integer, nullable=False)
    motivation_level: Mapped[int] = mapped_column(Integer, nullable=False)
    career_satisfaction: Mapped[int] = mapped_column(Integer, nullable=False)
    learning_opportunities: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Team and Management
    team_satisfaction: Mapped[int] = mapped_column(Integer, nullable=False)
    manager_support: Mapped[int] = mapped_column(Integer, nullable=False)
    communication_effectiveness: Mapped[int] = mapped_column(Integer, nullable=False)
    recognition_satisfaction: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Burnout Risk Indicators
    burnout_risk_score: Mapped[float] = mapped_column(Float, nullable=False)  # Calculated risk score
    emotional_exhaustion: Mapped[int] = mapped_column(Integer, nullable=False)
    cynicism_level: Mapped[int] = mapped_column(Integer, nullable=False)
    personal_accomplishment: Mapped[int] = mapped_column(Integer, nullable=False)
    
    # Qualitative Feedback
    positive_highlights: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    concerns_challenges: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    improvement_suggestions: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    support_needed: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Preferences and Needs
    preferred_work_schedule: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    workspace_preferences: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    development_interests: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Metadata
    check_type: Mapped[str] = mapped_column(String(50), default="regular", nullable=False)  # regular, triggered, exit
    completion_time_minutes: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="wellness_checks")
    alerts: Mapped[List["WellnessAlert"]] = relationship("WellnessAlert", back_populates="wellness_check", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        """String representation of the wellness check."""
        return f"<WellnessCheck(id={self.id}, user_id={self.user_id}, date={self.check_date})>"


class WellnessAlert(Base):
    """Automated alerts for wellness concerns."""
    
    __tablename__ = "wellness_alerts"
    
    # Basic Information
    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    wellness_check_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("wellness_checks.id"), nullable=True, index=True)
    
    # Alert Details
    alert_type: Mapped[str] = mapped_column(String(100), nullable=False)  # burnout_risk, stress_spike, etc.
    severity: Mapped[AlertSeverity] = mapped_column(SQLEnum(AlertSeverity), nullable=False)
    status: Mapped[AlertStatus] = mapped_column(SQLEnum(AlertStatus), default=AlertStatus.ACTIVE, nullable=False)
    
    # Alert Content
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    recommended_actions: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Timeline
    triggered_date: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    acknowledged_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    resolved_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Response and Follow-up
    acknowledged_by_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True)
    manager_notified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    hr_notified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    follow_up_required: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    follow_up_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Metrics and Thresholds
    trigger_metrics: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Metrics that triggered the alert
    threshold_values: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Threshold values exceeded
    
    # Resolution
    resolution_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    actions_taken: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id])
    wellness_check: Mapped[Optional["WellnessCheck"]] = relationship("WellnessCheck", back_populates="alerts")
    acknowledged_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[acknowledged_by_id])
    
    def __repr__(self) -> str:
        """String representation of the wellness alert."""
        return f"<WellnessAlert(id={self.id}, user_id={self.user_id}, type={self.alert_type})>"


class WorkScheduleType(str, enum.Enum):
    """Types of work schedules."""
    STANDARD = "standard"  # 9-5 or similar
    FLEXIBLE = "flexible"  # Flexible hours within core hours
    COMPRESSED = "compressed"  # 4x10 or similar
    REMOTE = "remote"  # Fully remote
    HYBRID = "hybrid"  # Mix of remote and office
    SHIFT = "shift"  # Specific shifts
    ON_CALL = "on_call"  # On-call rotations


class WorkSchedule(Base):
    """Employee work schedules and preferences."""
    
    __tablename__ = "work_schedules"
    
    # Basic Information
    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False, index=True)
    schedule_name: Mapped[str] = mapped_column(String(100), nullable=False)
    schedule_type: Mapped[WorkScheduleType] = mapped_column(SQLEnum(WorkScheduleType), nullable=False)
    
    # Schedule Details
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    effective_start_date: Mapped[date] = mapped_column(Date, nullable=False)
    effective_end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    
    # Time Configuration
    standard_hours_per_week: Mapped[float] = mapped_column(Float, default=40.0, nullable=False)
    core_hours_start: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)  # HH:MM format
    core_hours_end: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)    # HH:MM format
    timezone: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # Weekly Schedule (JSON with day-specific configurations)
    weekly_schedule: Mapped[dict] = mapped_column(JSONB, nullable=False)
    
    # Flexibility and Preferences
    allows_flexible_start: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    allows_flexible_end: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    allows_remote_work: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    remote_work_days: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Days allowed for remote work
    
    # Break and Time Off
    lunch_break_duration: Mapped[int] = mapped_column(Integer, default=60, nullable=False)  # Minutes
    short_break_frequency: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Minutes between breaks
    
    # Approval and Management
    approved_by_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id"), nullable=True)
    approval_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Notes and Comments
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    employee_comments: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    manager_comments: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="work_schedules")
    approved_by: Mapped[Optional["User"]] = relationship("User", foreign_keys=[approved_by_id])
    
    def __repr__(self) -> str:
        """String representation of the work schedule."""
        return f"<WorkSchedule(id={self.id}, user_id={self.user_id}, type={self.schedule_type})>"


class WellnessResource(Base):
    """Wellness resources and support materials."""
    
    __tablename__ = "wellness_resources"
    
    # Basic Information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    resource_type: Mapped[str] = mapped_column(String(50), nullable=False)  # article, video, tool, contact
    category: Mapped[str] = mapped_column(String(50), nullable=False)  # stress, burnout, mental_health, etc.
    
    # Content
    content_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    content_text: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Accessibility
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_confidential: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    requires_approval: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Targeting
    target_roles: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Specific roles this applies to
    target_conditions: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Conditions for showing
    
    # Usage Tracking
    view_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    last_accessed: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    tags: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    custom_metadata: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)
    
    def __repr__(self) -> str:
        """String representation of the wellness resource."""
        return f"<WellnessResource(id={self.id}, title={self.title}, type={self.resource_type})>"
