"""Base model for all database models."""

from datetime import datetime

from sqlalchemy import Column, DateTime
from sqlalchemy.ext.declarative import declarative_base

# Re-export Base from db.base for convenience
from ..base import Base

__all__ = ["Base"]


class BaseModel(Base):
    """Base model with common fields."""

    __abstract__ = True

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
