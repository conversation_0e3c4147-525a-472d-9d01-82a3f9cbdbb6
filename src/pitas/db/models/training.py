"""Training and competency management database models."""

from datetime import datetime
from enum import Enum
from typing import List, Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    <PERSON><PERSON>an,
    Column,
    DateTime,
    Enum as SQLE<PERSON>,
    Float,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
    JSON,
)
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import relationship

from pitas.db.models.base import BaseModel


class CompetencyLevel(str, Enum):
    """Competency proficiency levels."""
    NOVICE = "novice"
    ADVANCED_BEGINNER = "advanced_beginner"
    COMPETENT = "competent"
    PROFICIENT = "proficient"
    EXPERT = "expert"


class TrainingStatus(str, Enum):
    """Training course status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


class CertificationStatus(str, Enum):
    """Certification status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    ACHIEVED = "achieved"
    EXPIRED = "expired"
    RENEWAL_REQUIRED = "renewal_required"


class CompetencyFramework(BaseModel):
    """NICE Cybersecurity Workforce Framework competencies."""
    
    __tablename__ = "competency_frameworks"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), nullable=False, unique=True)
    description = Column(Text)
    version = Column(String(50), nullable=False)
    work_role_id = Column(String(50), nullable=False)  # NICE work role ID
    specialty_area = Column(String(255), nullable=False)
    category = Column(String(255), nullable=False)
    
    # Relationships
    competencies = relationship("Competency", back_populates="framework")


class Competency(BaseModel):
    """Individual competency definition."""
    
    __tablename__ = "competencies"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    framework_id = Column(PGUUID(as_uuid=True), ForeignKey("competency_frameworks.id"), nullable=False)
    competency_id = Column(String(50), nullable=False)  # NICE competency ID
    name = Column(String(255), nullable=False)
    description = Column(Text)
    knowledge_statements = Column(JSON)  # List of knowledge requirements
    skill_statements = Column(JSON)  # List of skill requirements
    ability_statements = Column(JSON)  # List of ability requirements
    
    # Relationships
    framework = relationship("CompetencyFramework", back_populates="competencies")
    assessments = relationship("SkillAssessment", back_populates="competency")


class SkillAssessment(BaseModel):
    """Skills assessment and gap analysis."""
    
    __tablename__ = "skill_assessments"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    competency_id = Column(PGUUID(as_uuid=True), ForeignKey("competencies.id"), nullable=False)
    current_level = Column(SQLEnum(CompetencyLevel), nullable=False)
    target_level = Column(SQLEnum(CompetencyLevel), nullable=False)
    assessment_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    assessor_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"))
    notes = Column(Text)
    evidence = Column(JSON)  # Supporting evidence for assessment
    
    # Relationships
    competency = relationship("Competency", back_populates="assessments")


class TrainingCourse(BaseModel):
    """Training course definition."""
    
    __tablename__ = "training_courses"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    provider = Column(String(255))  # SANS, internal, etc.
    course_code = Column(String(100))
    duration_hours = Column(Integer)
    difficulty_level = Column(SQLEnum(CompetencyLevel), nullable=False)
    prerequisites = Column(JSON)  # List of prerequisite course IDs
    learning_objectives = Column(JSON)  # List of learning objectives
    competencies_addressed = Column(JSON)  # List of competency IDs
    is_certification_prep = Column(Boolean, default=False)
    certification_id = Column(PGUUID(as_uuid=True), ForeignKey("certifications.id"))
    cost = Column(Float)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    certification = relationship("Certification", back_populates="prep_courses")
    enrollments = relationship("TrainingEnrollment", back_populates="course")


class LearningPath(BaseModel):
    """Personalized learning path."""
    
    __tablename__ = "learning_paths"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    target_role = Column(String(255))
    estimated_duration_weeks = Column(Integer)
    course_sequence = Column(JSON)  # Ordered list of course IDs
    competency_goals = Column(JSON)  # Target competency levels
    is_active = Column(Boolean, default=True)
    completion_percentage = Column(Float, default=0.0)
    
    # Relationships
    enrollments = relationship("TrainingEnrollment", back_populates="learning_path")


class TrainingEnrollment(BaseModel):
    """Training course enrollment and progress tracking."""
    
    __tablename__ = "training_enrollments"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    course_id = Column(PGUUID(as_uuid=True), ForeignKey("training_courses.id"), nullable=False)
    learning_path_id = Column(PGUUID(as_uuid=True), ForeignKey("learning_paths.id"))
    enrollment_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    start_date = Column(DateTime)
    completion_date = Column(DateTime)
    status = Column(SQLEnum(TrainingStatus), nullable=False, default=TrainingStatus.NOT_STARTED)
    progress_percentage = Column(Float, default=0.0)
    assessment_scores = Column(JSON)  # Assessment results
    practical_scores = Column(JSON)  # Hands-on exercise scores
    time_spent_hours = Column(Float, default=0.0)
    
    # Relationships
    course = relationship("TrainingCourse", back_populates="enrollments")
    learning_path = relationship("LearningPath", back_populates="enrollments")


class Certification(BaseModel):
    """Certification tracking and management."""
    
    __tablename__ = "certifications"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(255), nullable=False)
    abbreviation = Column(String(50), nullable=False)
    provider = Column(String(255), nullable=False)
    description = Column(Text)
    level = Column(SQLEnum(CompetencyLevel), nullable=False)
    prerequisites = Column(JSON)  # List of prerequisite certification IDs
    renewal_period_years = Column(Integer)
    cpe_credits_required = Column(Integer)
    exam_cost = Column(Float)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    prep_courses = relationship("TrainingCourse", back_populates="certification")
    achievements = relationship("CertificationAchievement", back_populates="certification")


class CertificationAchievement(BaseModel):
    """Individual certification achievements."""
    
    __tablename__ = "certification_achievements"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    certification_id = Column(PGUUID(as_uuid=True), ForeignKey("certifications.id"), nullable=False)
    status = Column(SQLEnum(CertificationStatus), nullable=False, default=CertificationStatus.NOT_STARTED)
    achievement_date = Column(DateTime)
    expiration_date = Column(DateTime)
    credential_id = Column(String(255))  # External credential ID
    cpe_credits_earned = Column(Integer, default=0)
    renewal_reminder_sent = Column(Boolean, default=False)
    cost_reimbursed = Column(Float)
    
    # Relationships
    certification = relationship("Certification", back_populates="achievements")


class CTFChallenge(BaseModel):
    """Capture-the-Flag challenge definition."""
    
    __tablename__ = "ctf_challenges"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100), nullable=False)  # web, crypto, forensics, etc.
    difficulty = Column(SQLEnum(CompetencyLevel), nullable=False)
    points = Column(Integer, nullable=False)
    flag = Column(String(255), nullable=False)
    hints = Column(JSON)  # List of hints
    files = Column(JSON)  # List of challenge files
    competencies_tested = Column(JSON)  # List of competency IDs
    is_active = Column(Boolean, default=True)
    created_by = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Relationships
    submissions = relationship("CTFSubmission", back_populates="challenge")


class CTFSubmission(BaseModel):
    """CTF challenge submission tracking."""
    
    __tablename__ = "ctf_submissions"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    challenge_id = Column(PGUUID(as_uuid=True), ForeignKey("ctf_challenges.id"), nullable=False)
    submission_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    submitted_flag = Column(String(255), nullable=False)
    is_correct = Column(Boolean, nullable=False)
    points_awarded = Column(Integer, default=0)
    time_to_solve_minutes = Column(Integer)
    
    # Relationships
    challenge = relationship("CTFChallenge", back_populates="submissions")


# Mentorship models moved to mentorship.py to avoid table conflicts
