"""Resource allocation model for Phase 2 team management."""

import enum
from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID

from sqlalchemy import String, Float, Boolean, DateTime, Text, JSON, ForeignKey, Enum
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class AllocationStatus(str, enum.Enum):
    """Resource allocation status."""
    
    PROPOSED = "proposed"
    APPROVED = "approved"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    ON_HOLD = "on_hold"


class AllocationRole(str, enum.Enum):
    """Role of the pentester in the project."""
    
    TEAM_LEAD = "team_lead"
    SENIOR_PENTESTER = "senior_pentester"
    PENTESTER = "pentester"
    JUNIOR_PENTESTER = "junior_pentester"
    SPECIALIST = "specialist"
    CONSULTANT = "consultant"
    REVIEWER = "reviewer"


class ResourceAllocation(Base):
    """Resource allocation model for tracking pentester assignments to projects."""
    
    __tablename__ = "resource_allocation"
    
    # Foreign Keys
    pentester_id: Mapped[UUID] = mapped_column(
        ForeignKey("pentesterprofile.id", ondelete="CASCADE"),
        nullable=False,
        doc="Reference to pentester profile"
    )
    
    project_id: Mapped[UUID] = mapped_column(
        ForeignKey("project.id", ondelete="CASCADE"),
        nullable=False,
        doc="Reference to project"
    )
    
    # Allocation Details
    status: Mapped[AllocationStatus] = mapped_column(
        Enum(AllocationStatus),
        nullable=False,
        default=AllocationStatus.PROPOSED,
        doc="Current allocation status"
    )
    
    role: Mapped[AllocationRole] = mapped_column(
        Enum(AllocationRole),
        nullable=False,
        default=AllocationRole.PENTESTER,
        doc="Role of pentester in this project"
    )
    
    # Time Allocation
    allocated_hours: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Total hours allocated to this project"
    )
    
    hours_per_week: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Expected hours per week on this project"
    )
    
    actual_hours: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        doc="Actual hours worked on this project"
    )
    
    # Timeline
    allocation_start_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="Start date of allocation"
    )
    
    allocation_end_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="End date of allocation"
    )
    
    actual_start_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Actual start date of work"
    )
    
    actual_end_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Actual end date of work"
    )
    
    # Utilization and Performance
    utilization_percentage: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        doc="Percentage of pentester's capacity allocated to this project"
    )
    
    performance_rating: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Performance rating for this allocation (1-5 scale)"
    )
    
    efficiency_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Efficiency score (actual vs estimated hours)"
    )
    
    # Skill Matching
    skill_match_score: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="How well pentester skills match project requirements (0-100)"
    )
    
    primary_skills_used: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        doc="Primary skills utilized in this allocation"
    )
    
    # Approval and Workflow
    approved_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Who approved this allocation"
    )
    
    approved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the allocation was approved"
    )
    
    requested_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Who requested this allocation"
    )
    
    # Constraints and Preferences
    can_work_remotely: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether pentester can work remotely on this project"
    )
    
    requires_security_clearance: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this allocation requires security clearance"
    )
    
    timezone_constraints: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Timezone constraints for this allocation"
    )
    
    # Financial
    hourly_rate: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Hourly rate for this allocation"
    )
    
    total_cost: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        doc="Total cost of this allocation"
    )
    
    # Quality and Feedback
    client_feedback: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Client feedback for this allocation"
    )
    
    internal_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Internal notes about this allocation"
    )
    
    lessons_learned: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        doc="Lessons learned from this allocation"
    )
    
    # Metadata
    allocation_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        doc="Additional allocation metadata"
    )
    
    # Status flags
    is_billable: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        doc="Whether this allocation is billable"
    )
    
    is_primary_assignment: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this is the pentester's primary assignment"
    )
    
    requires_mentoring: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        doc="Whether this allocation requires mentoring"
    )
    
    # Relationships
    pentester: Mapped["PentesterProfile"] = relationship(
        "PentesterProfile",
        back_populates="resource_allocations"
    )
    
    project: Mapped["Project"] = relationship(
        "Project",
        back_populates="resource_allocations"
    )
    
    @property
    def allocation_duration_days(self) -> int:
        """Calculate allocation duration in days."""
        return (self.allocation_end_date - self.allocation_start_date).days
    
    @property
    def is_active(self) -> bool:
        """Check if allocation is currently active."""
        now = datetime.utcnow()
        return (
            self.status == AllocationStatus.ACTIVE and
            self.allocation_start_date <= now <= self.allocation_end_date
        )
    
    @property
    def is_overallocated(self) -> bool:
        """Check if actual hours exceed allocated hours."""
        return self.actual_hours > self.allocated_hours
    
    @property
    def completion_percentage(self) -> float:
        """Calculate completion percentage based on hours."""
        if self.allocated_hours > 0:
            return min((self.actual_hours / self.allocated_hours) * 100, 100.0)
        return 0.0
    
    @property
    def cost_to_date(self) -> Optional[float]:
        """Calculate cost based on actual hours worked."""
        if self.hourly_rate:
            return self.actual_hours * self.hourly_rate
        return None
    
    def __repr__(self) -> str:
        """String representation of the resource allocation."""
        return f"<ResourceAllocation(pentester_id={self.pentester_id}, project_id={self.project_id}, status={self.status})>"
