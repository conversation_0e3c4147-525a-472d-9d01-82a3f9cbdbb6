"""Compliance documentation and audit trail database models."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, Text, DateTime, Boolean, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class ComplianceFramework(str, Enum):
    """Supported compliance frameworks."""
    SOC2_TYPE_II = "soc2_type_ii"
    ISO_27001 = "iso_27001"
    PCI_DSS = "pci_dss"
    NIST_800_53 = "nist_800_53"
    HIPAA = "hipaa"
    GDPR = "gdpr"
    CUSTOM = "custom"


class AuditEventType(str, Enum):
    """Types of audit events."""
    USER_ACTION = "user_action"
    SYSTEM_EVENT = "system_event"
    DATA_ACCESS = "data_access"
    CONFIGURATION_CHANGE = "configuration_change"
    SECURITY_EVENT = "security_event"
    COMPLIANCE_CHECK = "compliance_check"
    WORKFLOW_TRANSITION = "workflow_transition"


class ControlStatus(str, Enum):
    """Control implementation status."""
    NOT_IMPLEMENTED = "not_implemented"
    PARTIALLY_IMPLEMENTED = "partially_implemented"
    IMPLEMENTED = "implemented"
    EFFECTIVE = "effective"
    NEEDS_IMPROVEMENT = "needs_improvement"


class ComplianceMapping(Base):
    """Mapping of system controls to compliance frameworks."""
    
    __tablename__ = "compliance_mappings"
    
    # Framework Information
    framework: Mapped[ComplianceFramework] = mapped_column(
        SQLEnum(ComplianceFramework),
        nullable=False,
        index=True,
        doc="Compliance framework"
    )
    
    control_id: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        index=True,
        doc="Control identifier within framework"
    )
    
    control_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Control name"
    )
    
    control_description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Control description"
    )
    
    # Implementation Details
    implementation_status: Mapped[ControlStatus] = mapped_column(
        SQLEnum(ControlStatus),
        default=ControlStatus.NOT_IMPLEMENTED,
        nullable=False,
        index=True,
        doc="Implementation status"
    )
    
    implementation_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Implementation notes and details"
    )
    
    # Responsibility
    control_owner: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="Control owner user ID"
    )
    
    responsible_team: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Responsible team"
    )
    
    # Testing and Validation
    last_tested_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Last testing timestamp"
    )
    
    last_tested_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who last tested the control"
    )
    
    testing_frequency_days: Mapped[int] = mapped_column(
        default=90,
        doc="Testing frequency in days"
    )
    
    next_test_due: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Next testing due date"
    )
    
    # Evidence and Documentation
    evidence_location: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="Location of control evidence"
    )
    
    documentation_links: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Links to related documentation"
    )
    
    # Risk Assessment
    risk_rating: Mapped[Optional[str]] = mapped_column(
        String(20),
        doc="Risk rating if control fails"
    )
    
    compensating_controls: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Compensating controls if applicable"
    )
    
    # Metadata
    custom_fields: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Custom framework-specific fields"
    )
    
    # Relationships
    owner = relationship("User", foreign_keys=[control_owner])
    tester = relationship("User", foreign_keys=[last_tested_by])
    audit_events = relationship("AuditTrail", back_populates="compliance_mapping")


class AuditTrail(Base):
    """Immutable audit trail for all system activities."""
    
    __tablename__ = "audit_trail"
    
    # Event Information
    event_type: Mapped[AuditEventType] = mapped_column(
        SQLEnum(AuditEventType),
        nullable=False,
        index=True,
        doc="Type of audit event"
    )
    
    event_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Event name or action"
    )
    
    event_description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Detailed event description"
    )
    
    # Actor Information
    user_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        index=True,
        doc="User who performed the action"
    )
    
    session_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        index=True,
        doc="Session identifier"
    )
    
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45),
        doc="IP address of the actor"
    )
    
    user_agent: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="User agent string"
    )
    
    # Resource Information
    resource_type: Mapped[Optional[str]] = mapped_column(
        String(100),
        index=True,
        doc="Type of resource affected"
    )
    
    resource_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        index=True,
        doc="ID of the affected resource"
    )
    
    resource_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Name of the affected resource"
    )
    
    # Change Information
    old_values: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Previous values before change"
    )
    
    new_values: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="New values after change"
    )
    
    # Context Information
    project_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        index=True,
        doc="Associated project ID"
    )
    
    client_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("clients.id"),
        index=True,
        doc="Associated client ID"
    )
    
    compliance_mapping_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("compliance_mappings.id"),
        index=True,
        doc="Associated compliance mapping ID"
    )
    
    # Timing
    event_timestamp: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        index=True,
        doc="Event timestamp"
    )
    
    # Integrity
    checksum: Mapped[Optional[str]] = mapped_column(
        String(64),
        doc="SHA-256 checksum for integrity verification"
    )
    
    signature: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="Digital signature for non-repudiation"
    )
    
    # Metadata
    event_metadata: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional event metadata"
    )

    tags: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Event tags for categorization"
    )
    
    # Relationships
    user = relationship("User")
    project = relationship("Project")
    client = relationship("Client")
    compliance_mapping = relationship("ComplianceMapping", back_populates="audit_events")


class ComplianceReport(Base):
    """Generated compliance reports."""
    
    __tablename__ = "compliance_reports"
    
    # Report Information
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Report title"
    )
    
    framework: Mapped[ComplianceFramework] = mapped_column(
        SQLEnum(ComplianceFramework),
        nullable=False,
        index=True,
        doc="Compliance framework"
    )
    
    report_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Type of report (assessment, gap_analysis, etc.)"
    )
    
    # Scope
    project_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        index=True,
        doc="Associated project ID"
    )
    
    client_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("clients.id"),
        index=True,
        doc="Associated client ID"
    )
    
    scope_description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Report scope description"
    )
    
    # Period
    period_start: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="Report period start date"
    )
    
    period_end: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="Report period end date"
    )
    
    # Content
    executive_summary: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Executive summary"
    )
    
    findings: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Report findings and results"
    )
    
    recommendations: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Recommendations for improvement"
    )
    
    control_results: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Individual control test results"
    )
    
    # Generation
    generated_by: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        doc="User who generated the report"
    )
    
    generated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Report generation timestamp"
    )
    
    # File Information
    file_path: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="Generated report file path"
    )
    
    file_format: Mapped[str] = mapped_column(
        String(20),
        default="pdf",
        doc="Report file format"
    )
    
    # Status
    is_final: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether report is finalized"
    )
    
    approved_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who approved the report"
    )
    
    approved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Report approval timestamp"
    )
    
    # Relationships
    project = relationship("Project")
    client = relationship("Client")
    generator = relationship("User", foreign_keys=[generated_by])
    approver = relationship("User", foreign_keys=[approved_by])


class ComplianceEvidence(Base):
    """Evidence collection for compliance controls."""
    
    __tablename__ = "compliance_evidence"
    
    compliance_mapping_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("compliance_mappings.id"),
        nullable=False,
        index=True,
        doc="Associated compliance mapping ID"
    )
    
    # Evidence Information
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Evidence title"
    )
    
    description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Evidence description"
    )
    
    evidence_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Type of evidence (document, screenshot, log, etc.)"
    )
    
    # File Information
    filename: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Original filename"
    )
    
    file_path: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="File storage path"
    )
    
    file_size: Mapped[Optional[int]] = mapped_column(
        doc="File size in bytes"
    )
    
    content_type: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="MIME content type"
    )
    
    # Collection Information
    collected_by: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        doc="User who collected the evidence"
    )
    
    collected_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Evidence collection timestamp"
    )
    
    # Validation
    validated_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who validated the evidence"
    )
    
    validated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Evidence validation timestamp"
    )
    
    is_valid: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether evidence is valid"
    )
    
    # Metadata
    evidence_metadata: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional evidence metadata"
    )

    tags: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Evidence tags"
    )
    
    # Relationships
    compliance_mapping = relationship("ComplianceMapping")
    collector = relationship("User", foreign_keys=[collected_by])
    validator = relationship("User", foreign_keys=[validated_by])
