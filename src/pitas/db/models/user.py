"""User model for employee management and authentication."""

from datetime import datetime
from typing import Optional, List
from uuid import UUID

from sqlalchemy import String, Boolean, DateTime, Text, Integer, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import JSONB
import enum

from pitas.db.base import Base


class UserRole(str, enum.Enum):
    """User roles in the system."""
    ADMIN = "admin"
    MANAGER = "manager"
    SENIOR_PENTESTER = "senior_pentester"
    PENTESTER = "pentester"
    JUNIOR_PENTESTER = "junior_pentester"
    ANALYST = "analyst"


class CareerTier(str, enum.Enum):
    """Career progression tiers."""
    ENTRY = "entry"          # 0-2 years
    INTERMEDIATE = "intermediate"  # 2-5 years
    SENIOR = "senior"        # 5+ years
    EXPERT = "expert"        # 8+ years


class CareerTrack(str, enum.Enum):
    """Career development tracks."""
    TECHNICAL_SPECIALIST = "technical_specialist"
    TEAM_LEADERSHIP = "team_leadership"
    CLIENT_CONSULTING = "client_consulting"
    RESEARCH_DEVELOPMENT = "research_development"


class User(Base):
    """User model with career development and retention features."""

    __tablename__ = "users"

    # Basic Information
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    username: Mapped[str] = mapped_column(String(100), unique=True, index=True, nullable=False)
    full_name: Mapped[str] = mapped_column(String(255), nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Role and Career Information
    role: Mapped[UserRole] = mapped_column(SQLEnum(UserRole), nullable=False, default=UserRole.JUNIOR_PENTESTER)
    career_tier: Mapped[CareerTier] = mapped_column(SQLEnum(CareerTier), nullable=False, default=CareerTier.ENTRY)
    career_track: Mapped[Optional[CareerTrack]] = mapped_column(SQLEnum(CareerTrack), nullable=True)

    # Employment Details
    hire_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    years_experience: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    department: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    location: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    timezone: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    # Manager Relationship
    manager_id: Mapped[Optional[UUID]] = mapped_column(nullable=True)

    # Professional Development
    professional_dev_budget: Mapped[int] = mapped_column(Integer, default=5000, nullable=False)  # Annual budget in USD
    certifications: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # List of certifications
    skills: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Skills and proficiency levels

    # Work-Life Balance
    preferred_work_hours: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # Flexible schedule preferences
    wellness_score: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-100 wellness score
    last_wellness_check: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Recognition and Engagement
    total_recognition_points: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    last_promotion_date: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Profile and Preferences
    bio: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    preferences: Mapped[Optional[dict]] = mapped_column(JSONB, nullable=True)  # User preferences

    # Relationships - Phase 2 Team Resource Management
    pentester_profile: Mapped[Optional["PentesterProfile"]] = relationship(
        "PentesterProfile",
        back_populates="user",
        uselist=False
    )

    # Relationships - Phase 6 Employee Retention and Career Development
    individual_development_plans: Mapped[List["IndividualDevelopmentPlan"]] = relationship(
        "IndividualDevelopmentPlan",
        foreign_keys="IndividualDevelopmentPlan.user_id",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    recognitions_received: Mapped[List["Recognition"]] = relationship(
        "Recognition",
        foreign_keys="Recognition.recipient_id",
        back_populates="recipient",
        cascade="all, delete-orphan"
    )
    mentorships_as_mentor: Mapped[List["Mentorship"]] = relationship(
        "Mentorship",
        foreign_keys="Mentorship.mentor_id",
        back_populates="mentor",
        cascade="all, delete-orphan"
    )
    mentorships_as_mentee: Mapped[List["Mentorship"]] = relationship(
        "Mentorship",
        foreign_keys="Mentorship.mentee_id",
        back_populates="mentee",
        cascade="all, delete-orphan"
    )
    wellness_checks: Mapped[List["WellnessCheck"]] = relationship(
        "WellnessCheck",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    work_schedules: Mapped[List["WorkSchedule"]] = relationship(
        "WorkSchedule",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    mentor_profile: Mapped[Optional["MentorProfile"]] = relationship(
        "MentorProfile",
        back_populates="user",
        uselist=False,
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(id={self.id}, username={self.username}, role={self.role})>"
