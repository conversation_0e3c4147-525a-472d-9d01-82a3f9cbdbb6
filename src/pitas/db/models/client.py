"""Client and client portal database models."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, Text, DateTime, Boolean, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.db.base import Base


class ClientStatus(str, Enum):
    """Client status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    ARCHIVED = "archived"


class PortalAccessLevel(str, Enum):
    """Portal access levels."""
    READ_ONLY = "read_only"
    STANDARD = "standard"
    ADMIN = "admin"
    FULL_ACCESS = "full_access"


class Client(Base):
    """Client organization model."""
    
    __tablename__ = "clients"
    
    # Basic Information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Client organization name"
    )
    
    display_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Display name for client portal"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Client description"
    )
    
    # Contact Information
    primary_contact_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Primary contact person name"
    )
    
    primary_contact_email: Mapped[Optional[str]] = mapped_column(
        String(255),
        index=True,
        doc="Primary contact email"
    )
    
    primary_contact_phone: Mapped[Optional[str]] = mapped_column(
        String(50),
        doc="Primary contact phone"
    )
    
    # Address Information
    address_line1: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Address line 1"
    )
    
    address_line2: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Address line 2"
    )
    
    city: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="City"
    )
    
    state_province: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="State or province"
    )
    
    postal_code: Mapped[Optional[str]] = mapped_column(
        String(20),
        doc="Postal code"
    )
    
    country: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Country"
    )
    
    # Business Information
    industry: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Industry sector"
    )
    
    company_size: Mapped[Optional[str]] = mapped_column(
        String(50),
        doc="Company size category"
    )
    
    website: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Company website"
    )
    
    # Status and Configuration
    status: Mapped[ClientStatus] = mapped_column(
        SQLEnum(ClientStatus),
        default=ClientStatus.ACTIVE,
        nullable=False,
        index=True,
        doc="Client status"
    )
    
    portal_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether client portal is enabled"
    )
    
    portal_url: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Custom portal URL"
    )
    
    # Compliance and Security
    compliance_frameworks: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Required compliance frameworks"
    )
    
    security_requirements: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Special security requirements"
    )
    
    data_retention_days: Mapped[int] = mapped_column(
        default=2555,  # 7 years
        doc="Data retention period in days"
    )
    
    # Billing and Contract
    contract_start_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Contract start date"
    )
    
    contract_end_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Contract end date"
    )
    
    billing_contact_email: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Billing contact email"
    )
    
    # Metadata
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Internal notes about the client"
    )
    
    custom_fields: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Custom client fields"
    )
    
    # Relationships
    projects = relationship("Project", back_populates="client")
    portal_users = relationship("ClientPortalUser", back_populates="client", cascade="all, delete-orphan")
    documents = relationship("ClientDocument", back_populates="client", cascade="all, delete-orphan")
    communications = relationship("ClientCommunication", back_populates="client", cascade="all, delete-orphan")


class ClientPortalUser(Base):
    """Client portal user accounts."""
    
    __tablename__ = "client_portal_users"
    
    client_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("clients.id"),
        nullable=False,
        index=True,
        doc="Client ID"
    )
    
    # User Information
    email: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        doc="User email address"
    )
    
    first_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="First name"
    )
    
    last_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="Last name"
    )
    
    title: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Job title"
    )
    
    phone: Mapped[Optional[str]] = mapped_column(
        String(50),
        doc="Phone number"
    )
    
    # Access Control
    access_level: Mapped[PortalAccessLevel] = mapped_column(
        SQLEnum(PortalAccessLevel),
        default=PortalAccessLevel.STANDARD,
        nullable=False,
        doc="Portal access level"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        index=True,
        doc="Whether user account is active"
    )
    
    # Authentication
    password_hash: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Hashed password"
    )
    
    last_login_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Last login timestamp"
    )
    
    password_reset_token: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Password reset token"
    )
    
    password_reset_expires: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Password reset token expiration"
    )
    
    # Notifications
    email_notifications: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether to send email notifications"
    )
    
    notification_preferences: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Notification preferences"
    )
    
    # Relationships
    client = relationship("Client", back_populates="portal_users")


class ClientDocument(Base):
    """Documents shared with clients."""
    
    __tablename__ = "client_documents"
    
    client_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("clients.id"),
        nullable=False,
        index=True,
        doc="Client ID"
    )
    
    project_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        index=True,
        doc="Associated project ID"
    )
    
    # Document Information
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Document title"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Document description"
    )
    
    filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Original filename"
    )
    
    file_path: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        doc="File storage path"
    )
    
    file_size: Mapped[int] = mapped_column(
        nullable=False,
        doc="File size in bytes"
    )
    
    content_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="MIME content type"
    )
    
    # Access Control
    is_public: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether document is publicly accessible"
    )
    
    requires_approval: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether document requires approval to access"
    )
    
    approved_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who approved document sharing"
    )
    
    approved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Approval timestamp"
    )
    
    # Metadata
    uploaded_by: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        doc="User who uploaded the document"
    )
    
    uploaded_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Upload timestamp"
    )
    
    version: Mapped[int] = mapped_column(
        default=1,
        doc="Document version"
    )
    
    tags: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Document tags and metadata"
    )
    
    # Relationships
    client = relationship("Client", back_populates="documents")
    project = relationship("Project")
    uploader = relationship("User", foreign_keys=[uploaded_by])
    approver = relationship("User", foreign_keys=[approved_by])


class ClientCommunication(Base):
    """Communication history with clients."""
    
    __tablename__ = "client_communications"
    
    client_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("clients.id"),
        nullable=False,
        index=True,
        doc="Client ID"
    )
    
    project_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        index=True,
        doc="Associated project ID"
    )
    
    # Communication Details
    subject: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Communication subject"
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Communication content"
    )
    
    communication_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Type of communication (email, meeting, call, etc.)"
    )
    
    direction: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        doc="Communication direction (inbound, outbound)"
    )
    
    # Participants
    from_user_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="Internal user who sent the communication"
    )
    
    to_client_user_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("client_portal_users.id"),
        doc="Client user who received the communication"
    )
    
    # Metadata
    sent_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Communication timestamp"
    )
    
    is_internal: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether communication is internal only"
    )
    
    attachments: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Communication attachments"
    )
    
    # Relationships
    client = relationship("Client", back_populates="communications")
    project = relationship("Project")
    from_user = relationship("User")
    to_client_user = relationship("ClientPortalUser")
