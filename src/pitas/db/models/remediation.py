"""Remediation workflow and tracking database models."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, Text, DateTime, Boolean, Integer, ForeignKey, JSON, Enum as SQLEnum, Float
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from pitas.core.escalation import SeverityLevel, EscalationLevel, EscalationStatus
from pitas.core.workflow import WorkflowStatus
from pitas.db.base import Base


class RemediationStatus(str, Enum):
    """Remediation status enumeration."""
    IDENTIFIED = "identified"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    PENDING_VERIFICATION = "pending_verification"
    VERIFIED = "verified"
    CLOSED = "closed"
    REOPENED = "reopened"
    CANCELLED = "cancelled"


class TicketingSystem(str, Enum):
    """Supported ticketing systems."""
    JIRA = "jira"
    SERVICENOW = "servicenow"
    ZENDESK = "zendesk"
    INTERNAL = "internal"


class Remediation(Base):
    """Remediation tracking for vulnerabilities and findings."""
    
    __tablename__ = "remediations"
    
    # Basic Information
    title: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="Remediation title"
    )
    
    description: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Detailed description of the issue"
    )
    
    project_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("projects.id"),
        nullable=False,
        index=True,
        doc="Associated project ID"
    )
    
    vulnerability_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        index=True,
        doc="Associated vulnerability ID"
    )
    
    # Severity and Priority
    severity: Mapped[SeverityLevel] = mapped_column(
        SQLEnum(SeverityLevel),
        nullable=False,
        index=True,
        doc="Severity level"
    )
    
    priority: Mapped[int] = mapped_column(
        Integer,
        default=3,
        doc="Priority level (1=highest, 5=lowest)"
    )
    
    cvss_score: Mapped[Optional[float]] = mapped_column(
        Float,
        doc="CVSS score if applicable"
    )
    
    # Status and Workflow
    status: Mapped[RemediationStatus] = mapped_column(
        SQLEnum(RemediationStatus),
        default=RemediationStatus.IDENTIFIED,
        nullable=False,
        index=True,
        doc="Current remediation status"
    )
    
    # Assignment
    assigned_to: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        index=True,
        doc="Assigned user ID"
    )
    
    assigned_team: Mapped[Optional[str]] = mapped_column(
        String(100),
        doc="Assigned team name"
    )
    
    system_owner: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="System owner contact"
    )
    
    assigned_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Assignment timestamp"
    )
    
    # SLA Tracking
    response_due: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Response due date"
    )
    
    resolution_due: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="Resolution due date"
    )
    
    first_response_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="First response timestamp"
    )
    
    resolved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Resolution timestamp"
    )
    
    sla_breached: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        index=True,
        doc="Whether SLA has been breached"
    )
    
    breach_reason: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Reason for SLA breach"
    )
    
    # External Ticketing
    external_ticket_id: Mapped[Optional[str]] = mapped_column(
        String(100),
        index=True,
        doc="External ticket system ID"
    )
    
    external_ticket_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        doc="External ticket URL"
    )
    
    ticketing_system: Mapped[Optional[TicketingSystem]] = mapped_column(
        SQLEnum(TicketingSystem),
        doc="External ticketing system"
    )
    
    # Remediation Details
    affected_systems: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="List of affected systems"
    )
    
    remediation_steps: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Recommended remediation steps"
    )
    
    business_impact: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Business impact description"
    )
    
    technical_details: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Technical details and metadata"
    )
    
    # Verification
    verification_required: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        doc="Whether verification testing is required"
    )
    
    verified_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who verified the fix"
    )
    
    verified_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Verification timestamp"
    )
    
    verification_notes: Mapped[Optional[str]] = mapped_column(
        Text,
        doc="Verification notes and results"
    )
    
    # Closure
    closed_by: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="User who closed the remediation"
    )
    
    closed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Closure timestamp"
    )
    
    closure_reason: Mapped[Optional[str]] = mapped_column(
        String(255),
        doc="Reason for closure"
    )
    
    # Relationships
    project = relationship("Project", back_populates="remediations")
    assigned_user = relationship("User", foreign_keys=[assigned_to])
    verifier = relationship("User", foreign_keys=[verified_by])
    closer = relationship("User", foreign_keys=[closed_by])
    escalations = relationship("RemediationEscalation", back_populates="remediation", cascade="all, delete-orphan")
    comments = relationship("RemediationComment", back_populates="remediation", cascade="all, delete-orphan")
    attachments = relationship("RemediationAttachment", back_populates="remediation", cascade="all, delete-orphan")


class RemediationEscalation(Base):
    """Escalation events for remediation items."""
    
    __tablename__ = "remediation_escalations"
    
    remediation_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("remediations.id"),
        nullable=False,
        index=True,
        doc="Remediation ID"
    )
    
    level: Mapped[EscalationLevel] = mapped_column(
        SQLEnum(EscalationLevel),
        nullable=False,
        doc="Escalation level"
    )
    
    status: Mapped[EscalationStatus] = mapped_column(
        SQLEnum(EscalationStatus),
        default=EscalationStatus.ACTIVE,
        nullable=False,
        index=True,
        doc="Escalation status"
    )
    
    triggered_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Escalation trigger timestamp"
    )
    
    acknowledged_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Acknowledgment timestamp"
    )
    
    resolved_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        doc="Resolution timestamp"
    )
    
    assignee_id: Mapped[Optional[UUID]] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        doc="Escalation assignee ID"
    )
    
    message: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Escalation message"
    )
    
    escalation_metadata: Mapped[Optional[dict]] = mapped_column(
        JSON,
        doc="Additional escalation metadata"
    )
    
    # Relationships
    remediation = relationship("Remediation", back_populates="escalations")
    assignee = relationship("User")


class RemediationComment(Base):
    """Comments and updates on remediation items."""
    
    __tablename__ = "remediation_comments"
    
    remediation_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("remediations.id"),
        nullable=False,
        index=True,
        doc="Remediation ID"
    )
    
    author_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        doc="Comment author ID"
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        doc="Comment content"
    )
    
    comment_type: Mapped[str] = mapped_column(
        String(50),
        default="comment",
        doc="Type of comment (comment, status_update, etc.)"
    )
    
    is_internal: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        doc="Whether comment is internal only"
    )
    
    # Relationships
    remediation = relationship("Remediation", back_populates="comments")
    author = relationship("User")


class RemediationAttachment(Base):
    """File attachments for remediation items."""
    
    __tablename__ = "remediation_attachments"
    
    remediation_id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("remediations.id"),
        nullable=False,
        index=True,
        doc="Remediation ID"
    )
    
    filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Original filename"
    )
    
    file_path: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        doc="File storage path"
    )
    
    file_size: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        doc="File size in bytes"
    )
    
    content_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        doc="MIME content type"
    )
    
    uploaded_by: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        doc="User who uploaded the file"
    )
    
    uploaded_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        nullable=False,
        doc="Upload timestamp"
    )
    
    # Relationships
    remediation = relationship("Remediation", back_populates="attachments")
    uploader = relationship("User")
