"""InfluxDB time-series database connection and utilities."""

import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

from influxdb_client import InfluxDBClient, Point, QueryApi, WriteApi
from influxdb_client.client.write_api import SYNCHRONOUS
from influxdb_client.rest import ApiException

from pitas.core.config import settings

logger = logging.getLogger(__name__)


class InfluxDBConnection:
    """InfluxDB time-series database connection manager."""
    
    def __init__(self):
        self._client: Optional[InfluxDBClient] = None
        self._write_api: Optional[WriteApi] = None
        self._query_api: Optional[QueryApi] = None
        self._connected = False
    
    async def connect(self) -> None:
        """Establish connection to InfluxDB."""
        try:
            self._client = InfluxDBClient(
                url=settings.influxdb_url,
                token=settings.influxdb_token,
                org=settings.influxdb_org,
                timeout=30000,
            )
            
            # Test connection
            health = self._client.health()
            if health.status == "pass":
                self._write_api = self._client.write_api(write_options=SYNCHRONOUS)
                self._query_api = self._client.query_api()
                self._connected = True
                logger.info("Successfully connected to InfluxDB")
            else:
                raise ConnectionError(f"InfluxDB health check failed: {health.message}")
                
        except Exception as e:
            logger.error(f"Failed to connect to InfluxDB: {e}")
            self._connected = False
            raise
    
    def disconnect(self) -> None:
        """Close InfluxDB connection."""
        if self._client:
            self._client.close()
            self._connected = False
            logger.info("Disconnected from InfluxDB")
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to InfluxDB."""
        return self._connected and self._client is not None
    
    @property
    def write_api(self) -> WriteApi:
        """Get InfluxDB write API."""
        if not self.is_connected or not self._write_api:
            raise RuntimeError("InfluxDB not connected")
        return self._write_api
    
    @property
    def query_api(self) -> QueryApi:
        """Get InfluxDB query API."""
        if not self.is_connected or not self._query_api:
            raise RuntimeError("InfluxDB not connected")
        return self._query_api


# Global InfluxDB connection instance
influxdb_connection = InfluxDBConnection()


class VulnerabilityMetricsService:
    """Service for managing vulnerability metrics in InfluxDB."""
    
    def __init__(self, connection: InfluxDBConnection = influxdb_connection):
        self.connection = connection
        self.bucket = settings.influxdb_bucket
    
    def write_vulnerability_discovery_metric(
        self,
        vulnerability_id: str,
        asset_id: Optional[str] = None,
        severity: Optional[str] = None,
        cvss_score: Optional[float] = None,
        source: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Write vulnerability discovery metric."""
        point = Point("vulnerability_discovery") \
            .tag("vulnerability_id", vulnerability_id) \
            .field("count", 1)
        
        if asset_id:
            point = point.tag("asset_id", asset_id)
        if severity:
            point = point.tag("severity", severity)
        if source:
            point = point.tag("source", source)
        if cvss_score is not None:
            point = point.field("cvss_score", cvss_score)
        if timestamp:
            point = point.time(timestamp)
        
        self.connection.write_api.write(bucket=self.bucket, record=point)
    
    def write_vulnerability_remediation_metric(
        self,
        vulnerability_id: str,
        asset_id: Optional[str] = None,
        remediation_time_hours: Optional[float] = None,
        remediation_type: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Write vulnerability remediation metric."""
        point = Point("vulnerability_remediation") \
            .tag("vulnerability_id", vulnerability_id) \
            .field("count", 1)
        
        if asset_id:
            point = point.tag("asset_id", asset_id)
        if remediation_type:
            point = point.tag("remediation_type", remediation_type)
        if remediation_time_hours is not None:
            point = point.field("remediation_time_hours", remediation_time_hours)
        if timestamp:
            point = point.time(timestamp)
        
        self.connection.write_api.write(bucket=self.bucket, record=point)
    
    def write_vulnerability_density_metric(
        self,
        asset_id: str,
        vulnerability_count: int,
        critical_count: int = 0,
        high_count: int = 0,
        medium_count: int = 0,
        low_count: int = 0,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Write vulnerability density metric for an asset."""
        point = Point("vulnerability_density") \
            .tag("asset_id", asset_id) \
            .field("total_vulnerabilities", vulnerability_count) \
            .field("critical_vulnerabilities", critical_count) \
            .field("high_vulnerabilities", high_count) \
            .field("medium_vulnerabilities", medium_count) \
            .field("low_vulnerabilities", low_count)
        
        if timestamp:
            point = point.time(timestamp)
        
        self.connection.write_api.write(bucket=self.bucket, record=point)
    
    def write_risk_score_metric(
        self,
        vulnerability_id: str,
        asset_id: str,
        risk_score: float,
        business_impact: Optional[float] = None,
        threat_likelihood: Optional[float] = None,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Write risk score metric."""
        point = Point("risk_score") \
            .tag("vulnerability_id", vulnerability_id) \
            .tag("asset_id", asset_id) \
            .field("risk_score", risk_score)
        
        if business_impact is not None:
            point = point.field("business_impact", business_impact)
        if threat_likelihood is not None:
            point = point.field("threat_likelihood", threat_likelihood)
        if timestamp:
            point = point.time(timestamp)
        
        self.connection.write_api.write(bucket=self.bucket, record=point)
    
    def get_vulnerability_discovery_trend(
        self,
        start_time: datetime,
        end_time: datetime,
        window: str = "1d"
    ) -> List[Dict[str, Any]]:
        """Get vulnerability discovery trend over time."""
        query = f'''
        from(bucket: "{self.bucket}")
          |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
          |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
          |> aggregateWindow(every: {window}, fn: sum, createEmpty: false)
          |> yield(name: "sum")
        '''
        
        result = self.connection.query_api.query(query)
        return self._process_query_result(result)
    
    def get_vulnerability_density_by_asset(
        self,
        asset_ids: Optional[List[str]] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """Get vulnerability density metrics by asset."""
        time_filter = ""
        if start_time and end_time:
            time_filter = f'|> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})'
        
        asset_filter = ""
        if asset_ids:
            asset_list = '", "'.join(asset_ids)
            asset_filter = f'|> filter(fn: (r) => contains(value: r.asset_id, set: ["{asset_list}"]))'
        
        query = f'''
        from(bucket: "{self.bucket}")
          {time_filter}
          |> filter(fn: (r) => r._measurement == "vulnerability_density")
          {asset_filter}
          |> last()
        '''
        
        result = self.connection.query_api.query(query)
        return self._process_query_result(result)
    
    def get_average_remediation_time(
        self,
        start_time: datetime,
        end_time: datetime,
        severity: Optional[str] = None
    ) -> float:
        """Get average remediation time for vulnerabilities."""
        severity_filter = ""
        if severity:
            severity_filter = f'|> filter(fn: (r) => r.severity == "{severity}")'
        
        query = f'''
        from(bucket: "{self.bucket}")
          |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
          |> filter(fn: (r) => r._measurement == "vulnerability_remediation")
          |> filter(fn: (r) => r._field == "remediation_time_hours")
          {severity_filter}
          |> mean()
        '''
        
        result = self.connection.query_api.query(query)
        processed = self._process_query_result(result)
        return processed[0].get("_value", 0.0) if processed else 0.0
    
    def get_risk_score_distribution(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """Get risk score distribution over time."""
        query = f'''
        from(bucket: "{self.bucket}")
          |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
          |> filter(fn: (r) => r._measurement == "risk_score")
          |> filter(fn: (r) => r._field == "risk_score")
          |> aggregateWindow(every: 1d, fn: mean, createEmpty: false)
          |> yield(name: "mean")
        '''
        
        result = self.connection.query_api.query(query)
        return self._process_query_result(result)
    
    def _process_query_result(self, result) -> List[Dict[str, Any]]:
        """Process InfluxDB query result into list of dictionaries."""
        processed_data = []
        
        for table in result:
            for record in table.records:
                data = {
                    "time": record.get_time(),
                    "measurement": record.get_measurement(),
                    "field": record.get_field(),
                    "value": record.get_value(),
                }
                
                # Add tags
                for key, value in record.values.items():
                    if key.startswith("_") or key in ["result", "table"]:
                        continue
                    data[key] = value
                
                processed_data.append(data)
        
        return processed_data


# Global vulnerability metrics service instance
vulnerability_metrics_service = VulnerabilityMetricsService()


async def startup_influxdb() -> None:
    """Startup function for InfluxDB connection."""
    await influxdb_connection.connect()


def shutdown_influxdb() -> None:
    """Shutdown function for InfluxDB connection."""
    influxdb_connection.disconnect()
