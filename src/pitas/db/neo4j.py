"""Neo4j graph database connection and utilities."""

import logging
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, AsyncGenerator

from neo4j import AsyncGraphDatabase, AsyncDriver, AsyncSession
from neo4j.exceptions import ServiceUnavailable, AuthError

from pitas.core.config import settings

logger = logging.getLogger(__name__)


class Neo4jConnection:
    """Neo4j database connection manager."""
    
    def __init__(self):
        self._driver: Optional[AsyncDriver] = None
        self._connected = False
    
    async def connect(self) -> None:
        """Establish connection to Neo4j database."""
        try:
            self._driver = AsyncGraphDatabase.driver(
                settings.neo4j_url,
                auth=(settings.neo4j_user, settings.neo4j_password),
                max_connection_lifetime=3600,
                max_connection_pool_size=50,
                connection_acquisition_timeout=60,
            )
            
            # Test connection
            await self._driver.verify_connectivity()
            self._connected = True
            logger.info("Successfully connected to Neo4j database")
            
        except (ServiceUnavailable, AuthError) as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            self._connected = False
            raise
    
    async def disconnect(self) -> None:
        """Close Neo4j database connection."""
        if self._driver:
            await self._driver.close()
            self._connected = False
            logger.info("Disconnected from Neo4j database")
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to Neo4j."""
        return self._connected and self._driver is not None
    
    @asynccontextmanager
    async def session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get Neo4j session context manager."""
        if not self.is_connected:
            await self.connect()
        
        if not self._driver:
            raise RuntimeError("Neo4j driver not initialized")
        
        async with self._driver.session() as session:
            try:
                yield session
            except Exception as e:
                logger.error(f"Neo4j session error: {e}")
                raise
    
    async def execute_query(
        self, 
        query: str, 
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a Cypher query and return results."""
        async with self.session() as session:
            result = await session.run(query, parameters or {})
            records = await result.data()
            return records
    
    async def execute_write_query(
        self, 
        query: str, 
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a write Cypher query in a transaction."""
        async with self.session() as session:
            async with session.begin_transaction() as tx:
                result = await tx.run(query, parameters or {})
                records = await result.data()
                await tx.commit()
                return records


# Global Neo4j connection instance
neo4j_connection = Neo4jConnection()


async def get_neo4j_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get Neo4j session."""
    async with neo4j_connection.session() as session:
        yield session


class VulnerabilityGraphService:
    """Service for managing vulnerability relationships in Neo4j."""
    
    def __init__(self, connection: Neo4jConnection = neo4j_connection):
        self.connection = connection
    
    async def create_vulnerability_node(
        self, 
        vulnerability_id: str,
        cve_id: Optional[str] = None,
        cvss_score: Optional[float] = None,
        severity: Optional[str] = None,
        discovery_date: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Create a vulnerability node in the graph."""
        query = """
        CREATE (v:Vulnerability {
            id: $vulnerability_id,
            cve_id: $cve_id,
            cvss_score: $cvss_score,
            severity: $severity,
            discovery_date: $discovery_date,
            created_at: datetime()
        })
        RETURN v
        """
        
        parameters = {
            "vulnerability_id": vulnerability_id,
            "cve_id": cve_id,
            "cvss_score": cvss_score,
            "severity": severity,
            "discovery_date": discovery_date,
        }
        
        result = await self.connection.execute_write_query(query, parameters)
        return result[0] if result else {}
    
    async def create_asset_node(
        self,
        asset_id: str,
        name: str,
        asset_type: str,
        business_criticality: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Create an asset node in the graph."""
        query = """
        CREATE (a:Asset {
            id: $asset_id,
            name: $name,
            type: $asset_type,
            business_criticality: $business_criticality,
            created_at: datetime()
        })
        RETURN a
        """
        
        parameters = {
            "asset_id": asset_id,
            "name": name,
            "asset_type": asset_type,
            "business_criticality": business_criticality,
        }
        
        result = await self.connection.execute_write_query(query, parameters)
        return result[0] if result else {}
    
    async def create_vulnerability_affects_asset(
        self,
        vulnerability_id: str,
        asset_id: str,
        impact_level: str,
        exploitability_likelihood: Optional[float] = None
    ) -> Dict[str, Any]:
        """Create AFFECTS relationship between vulnerability and asset."""
        query = """
        MATCH (v:Vulnerability {id: $vulnerability_id})
        MATCH (a:Asset {id: $asset_id})
        CREATE (v)-[r:AFFECTS {
            impact_level: $impact_level,
            exploitability_likelihood: $exploitability_likelihood,
            created_at: datetime()
        }]->(a)
        RETURN r
        """
        
        parameters = {
            "vulnerability_id": vulnerability_id,
            "asset_id": asset_id,
            "impact_level": impact_level,
            "exploitability_likelihood": exploitability_likelihood,
        }
        
        result = await self.connection.execute_write_query(query, parameters)
        return result[0] if result else {}
    
    async def find_vulnerability_attack_paths(
        self, 
        start_asset_id: str,
        max_depth: int = 5
    ) -> List[Dict[str, Any]]:
        """Find potential attack paths from a starting asset."""
        query = """
        MATCH path = (start:Asset {id: $start_asset_id})<-[:AFFECTS*1..$max_depth]-(v:Vulnerability)
        WHERE v.cvss_score >= 7.0
        RETURN path, length(path) as depth
        ORDER BY depth ASC, v.cvss_score DESC
        LIMIT 50
        """
        
        parameters = {
            "start_asset_id": start_asset_id,
            "max_depth": max_depth,
        }
        
        return await self.connection.execute_query(query, parameters)
    
    async def find_vulnerability_clusters(
        self, 
        similarity_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """Find clusters of similar vulnerabilities."""
        query = """
        MATCH (v1:Vulnerability), (v2:Vulnerability)
        WHERE v1.id <> v2.id
        AND v1.severity = v2.severity
        AND abs(v1.cvss_score - v2.cvss_score) <= $similarity_threshold
        RETURN v1, v2, abs(v1.cvss_score - v2.cvss_score) as score_diff
        ORDER BY score_diff ASC
        LIMIT 100
        """
        
        parameters = {"similarity_threshold": similarity_threshold}
        return await self.connection.execute_query(query, parameters)
    
    async def get_asset_vulnerability_summary(
        self, 
        asset_id: str
    ) -> Dict[str, Any]:
        """Get vulnerability summary for an asset."""
        query = """
        MATCH (a:Asset {id: $asset_id})<-[r:AFFECTS]-(v:Vulnerability)
        RETURN 
            a.name as asset_name,
            count(v) as total_vulnerabilities,
            avg(v.cvss_score) as avg_cvss_score,
            collect(DISTINCT v.severity) as severities,
            max(v.cvss_score) as max_cvss_score,
            min(v.cvss_score) as min_cvss_score
        """
        
        parameters = {"asset_id": asset_id}
        result = await self.connection.execute_query(query, parameters)
        return result[0] if result else {}


# Global vulnerability graph service instance
vulnerability_graph_service = VulnerabilityGraphService()


async def initialize_neo4j_schema() -> None:
    """Initialize Neo4j schema with constraints and indexes."""
    schema_queries = [
        # Constraints
        "CREATE CONSTRAINT vulnerability_id_unique IF NOT EXISTS FOR (v:Vulnerability) REQUIRE v.id IS UNIQUE",
        "CREATE CONSTRAINT asset_id_unique IF NOT EXISTS FOR (a:Asset) REQUIRE a.id IS UNIQUE",
        
        # Indexes
        "CREATE INDEX vulnerability_cve_id IF NOT EXISTS FOR (v:Vulnerability) ON (v.cve_id)",
        "CREATE INDEX vulnerability_severity IF NOT EXISTS FOR (v:Vulnerability) ON (v.severity)",
        "CREATE INDEX vulnerability_cvss_score IF NOT EXISTS FOR (v:Vulnerability) ON (v.cvss_score)",
        "CREATE INDEX asset_name IF NOT EXISTS FOR (a:Asset) ON (a.name)",
        "CREATE INDEX asset_criticality IF NOT EXISTS FOR (a:Asset) ON (a.business_criticality)",
    ]
    
    for query in schema_queries:
        try:
            await neo4j_connection.execute_write_query(query)
            logger.info(f"Executed schema query: {query}")
        except Exception as e:
            logger.warning(f"Schema query failed (may already exist): {query} - {e}")


async def startup_neo4j() -> None:
    """Startup function for Neo4j connection."""
    await neo4j_connection.connect()
    await initialize_neo4j_schema()


async def shutdown_neo4j() -> None:
    """Shutdown function for Neo4j connection."""
    await neo4j_connection.disconnect()
