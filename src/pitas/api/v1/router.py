"""API v1 router configuration."""

from fastapi import APIRouter

from pitas.api.v1.endpoints import (
    health,
    vulnerabilities,
    assets,
    career,
    training,
    pentesters,
    projects,
    resource_allocation,
    workflow,
    remediation,
    integrations,
    knowledge,
    compliance,
    analytics
)
from pitas.api.v1 import performance

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, tags=["health"])

# Phase 2: Team Resource Management endpoints
api_router.include_router(pentesters.router, prefix="/pentesters", tags=["pentesters"])
api_router.include_router(projects.router, prefix="/projects", tags=["projects"])
api_router.include_router(resource_allocation.router, prefix="/allocations", tags=["resource-allocation"])

# Phase 3: Vulnerability Assessment endpoints
api_router.include_router(vulnerabilities.router, prefix="/vulnerabilities", tags=["vulnerabilities"])
api_router.include_router(assets.router, prefix="/assets", tags=["assets"])

# Phase 4: Project Workflow and Remediation endpoints
api_router.include_router(workflow.router, prefix="/workflow", tags=["workflow"])
api_router.include_router(remediation.router, prefix="/remediations", tags=["remediation"])

# Phase 5 & 6: Training and Career Development endpoints
api_router.include_router(career.router, prefix="/career", tags=["career-development"])
api_router.include_router(training.router, prefix="/training", tags=["training"])

# Phase 7: Integration Layer endpoints
api_router.include_router(integrations.router, prefix="/integrations", tags=["integrations"])
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["knowledge"])
# Phase 8: Compliance and Audit Trail endpoints
api_router.include_router(compliance.router, prefix="/compliance", tags=["compliance"])

# Phase 11: Performance Optimization and Scalability endpoints
api_router.include_router(performance.router, prefix="/performance", tags=["performance"])

# Phase 9: Advanced Analytics and Reporting Engine endpoints
api_router.include_router(analytics.router, prefix="/analytics", tags=["analytics"])

# TODO: Add other endpoint routers as they are implemented
# api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
# api_router.include_router(users.router, prefix="/users", tags=["users"])
# api_router.include_router(teams.router, prefix="/teams", tags=["teams"])
# api_router.include_router(assessments.router, prefix="/assessments", tags=["assessments"])
# api_router.include_router(reports.router, prefix="/reports", tags=["reports"])