"""Performance monitoring and optimization API endpoints."""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import PlainTextResponse
from sqlalchemy.ext.asyncio import AsyncSession
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
import structlog

from pitas.api.deps import get_db, get_admin_user
from pitas.core.performance import performance_optimizer, PerformanceMetrics
from pitas.core.monitoring import performance_monitor, db_performance_monitor
from pitas.core.cache import cache_manager
from pitas.core.query_optimizer import query_optimizer
from pitas.schemas.base import BaseResponse

logger = structlog.get_logger(__name__)

router = APIRouter(prefix="/performance", tags=["performance"])


@router.get("/metrics", response_class=PlainTextResponse)
async def get_prometheus_metrics():
    """Get Prometheus metrics in text format."""
    try:
        return generate_latest()
    except Exception as e:
        logger.error(f"Failed to generate metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate metrics")


@router.get("/health")
async def get_performance_health():
    """Get overall performance health status."""
    try:
        analysis = await performance_optimizer.analyze_performance()
        health_score = performance_optimizer._calculate_health_score(analysis)
        
        return {
            "status": "healthy" if health_score > 80 else "degraded" if health_score > 60 else "unhealthy",
            "health_score": health_score,
            "timestamp": datetime.utcnow().isoformat(),
            "bottlenecks_count": len(analysis.get('bottlenecks', [])),
            "recommendations_count": len(analysis.get('recommendations', []))
        }
    except Exception as e:
        logger.error(f"Failed to get performance health: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance health")


@router.get("/summary")
async def get_performance_summary():
    """Get comprehensive performance summary."""
    try:
        # Get current metrics
        current_metrics = await performance_monitor.get_performance_summary()
        cache_stats = await cache_manager.get_stats()
        query_stats = db_performance_monitor.get_query_stats()
        optimization_stats = await query_optimizer.get_optimization_stats()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "system_metrics": current_metrics,
            "cache_stats": cache_stats,
            "database_stats": query_stats,
            "optimization_stats": optimization_stats,
            "performance_targets": performance_optimizer.optimization_targets
        }
    except Exception as e:
        logger.error(f"Failed to get performance summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance summary")


@router.get("/analysis")
async def get_performance_analysis():
    """Get detailed performance analysis with recommendations."""
    try:
        analysis = await performance_optimizer.analyze_performance()
        return analysis
    except Exception as e:
        logger.error(f"Failed to get performance analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance analysis")


@router.post("/baseline")
async def establish_performance_baseline(
    admin_user: str = Depends(get_admin_user)
):
    """Establish new performance baseline (admin only)."""
    try:
        baseline = await performance_optimizer.establish_baseline()
        
        return {
            "message": "Performance baseline established successfully",
            "baseline": baseline.__dict__,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to establish baseline: {e}")
        raise HTTPException(status_code=500, detail="Failed to establish baseline")


@router.post("/optimize/queries")
async def optimize_vulnerability_queries(
    db: AsyncSession = Depends(get_db),
    admin_user: str = Depends(get_admin_user)
):
    """Optimize vulnerability-related database queries (admin only)."""
    try:
        result = await performance_optimizer.optimize_vulnerability_queries(db)
        
        return {
            "message": "Query optimization completed",
            "optimization_result": {
                "type": result.optimization_type,
                "improvement_percent": result.improvement_percent,
                "recommendations": result.recommendations
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to optimize queries: {e}")
        raise HTTPException(status_code=500, detail="Failed to optimize queries")


@router.post("/optimize/cache")
async def optimize_cache_strategy(
    admin_user: str = Depends(get_admin_user)
):
    """Optimize caching strategy (admin only)."""
    try:
        result = await performance_optimizer.optimize_cache_strategy()
        
        return {
            "message": "Cache optimization completed",
            "optimization_result": {
                "type": result.optimization_type,
                "improvement_percent": result.improvement_percent,
                "recommendations": result.recommendations
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to optimize cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to optimize cache")


@router.get("/cache/stats")
async def get_cache_statistics():
    """Get detailed cache statistics."""
    try:
        stats = await cache_manager.get_stats()
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "cache_stats": stats,
            "recommendations": await _generate_cache_recommendations(stats)
        }
    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cache stats")


@router.delete("/cache/clear")
async def clear_cache(
    pattern: Optional[str] = Query(None, description="Pattern to match keys for deletion"),
    namespace: Optional[str] = Query(None, description="Namespace to clear"),
    admin_user: str = Depends(get_admin_user)
):
    """Clear cache entries (admin only)."""
    try:
        if pattern:
            cleared = await cache_manager.invalidate_pattern(pattern, namespace or "")
            message = f"Cleared {cleared} cache entries matching pattern '{pattern}'"
        else:
            # Clear all cache (dangerous operation)
            cleared = await cache_manager.invalidate_pattern("*", namespace or "")
            message = f"Cleared {cleared} cache entries"
        
        return {
            "message": message,
            "cleared_count": cleared,
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.get("/database/slow-queries")
async def get_slow_queries():
    """Get list of slow database queries."""
    try:
        slow_queries = db_performance_monitor.get_slow_queries()
        query_stats = db_performance_monitor.get_query_stats()
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "slow_queries": slow_queries,
            "statistics": query_stats,
            "threshold_seconds": db_performance_monitor.slow_query_threshold
        }
    except Exception as e:
        logger.error(f"Failed to get slow queries: {e}")
        raise HTTPException(status_code=500, detail="Failed to get slow queries")


@router.post("/database/analyze-query")
async def analyze_database_query(
    query: str,
    params: Optional[Dict[str, Any]] = None,
    db: AsyncSession = Depends(get_db),
    admin_user: str = Depends(get_admin_user)
):
    """Analyze a specific database query (admin only)."""
    try:
        analysis = await query_optimizer.analyze_query(db, query, params)
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "query_analysis": {
                "query_hash": analysis.query_hash,
                "execution_time": analysis.execution_time,
                "rows_examined": analysis.rows_examined,
                "rows_returned": analysis.rows_returned,
                "index_usage": analysis.index_usage,
                "recommendations": analysis.recommendations,
                "complexity_score": analysis.complexity_score,
                "cache_hit": analysis.cache_hit
            }
        }
    except Exception as e:
        logger.error(f"Failed to analyze query: {e}")
        raise HTTPException(status_code=500, detail="Failed to analyze query")


@router.get("/database/index-recommendations")
async def get_index_recommendations(
    table_name: Optional[str] = Query(None, description="Specific table to analyze"),
    db: AsyncSession = Depends(get_db)
):
    """Get database index recommendations."""
    try:
        recommendations = await query_optimizer.get_index_recommendations(db, table_name)
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "table_analyzed": table_name or "all_tables",
            "recommendations": [
                {
                    "table_name": rec.table_name,
                    "columns": rec.columns,
                    "index_type": rec.index_type,
                    "estimated_benefit": rec.estimated_benefit,
                    "reason": rec.reason
                }
                for rec in recommendations
            ],
            "total_recommendations": len(recommendations)
        }
    except Exception as e:
        logger.error(f"Failed to get index recommendations: {e}")
        raise HTTPException(status_code=500, detail="Failed to get index recommendations")


@router.get("/report")
async def get_optimization_report():
    """Get comprehensive performance optimization report."""
    try:
        report = await performance_optimizer.get_optimization_report()
        return report
    except Exception as e:
        logger.error(f"Failed to generate optimization report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate optimization report")


@router.get("/targets")
async def get_performance_targets():
    """Get current performance targets and thresholds."""
    return {
        "timestamp": datetime.utcnow().isoformat(),
        "targets": performance_optimizer.optimization_targets,
        "description": {
            "api_response_time_p95": "95th percentile API response time in seconds",
            "database_query_time_avg": "Average database query time in seconds",
            "cache_hit_rate": "Cache hit rate as decimal (0.8 = 80%)",
            "memory_usage_percent": "Memory usage as decimal (0.8 = 80%)",
            "cpu_usage_percent": "CPU usage as decimal (0.7 = 70%)"
        }
    }


@router.put("/targets")
async def update_performance_targets(
    targets: Dict[str, float],
    admin_user: str = Depends(get_admin_user)
):
    """Update performance targets (admin only)."""
    try:
        # Validate targets
        valid_targets = set(performance_optimizer.optimization_targets.keys())
        invalid_targets = set(targets.keys()) - valid_targets
        
        if invalid_targets:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid targets: {list(invalid_targets)}"
            )
        
        # Update targets
        performance_optimizer.optimization_targets.update(targets)
        
        return {
            "message": "Performance targets updated successfully",
            "updated_targets": targets,
            "current_targets": performance_optimizer.optimization_targets,
            "timestamp": datetime.utcnow().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update targets: {e}")
        raise HTTPException(status_code=500, detail="Failed to update targets")


async def _generate_cache_recommendations(stats: Dict[str, Any]) -> List[str]:
    """Generate cache optimization recommendations based on statistics."""
    recommendations = []
    
    hit_rate = stats.get('hit_rate', 0)
    if hit_rate < 70:
        recommendations.append("Cache hit rate is low - consider warming critical data")
    elif hit_rate > 95:
        recommendations.append("Cache hit rate is very high - consider extending TTL")
    
    memory_usage = stats.get('used_memory', 0)
    if memory_usage > 1024 * 1024 * 1024:  # 1GB
        recommendations.append("High cache memory usage - consider implementing cache eviction policies")
    
    connected_clients = stats.get('connected_clients', 0)
    if connected_clients > 100:
        recommendations.append("High number of cache connections - consider connection pooling")
    
    return recommendations
