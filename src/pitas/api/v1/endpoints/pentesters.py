"""Pentester management endpoints for Phase 2."""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.schemas.base import PaginatedResponse, PaginationParams
from pitas.schemas.pentester import (
    PentesterProfile, PentesterProfileCreate, PentesterProfileUpdate,
    PentesterProfileSummary, PentesterAvailability, PentesterPerformance
)
from pitas.services.pentester import PentesterService
from pitas.core.exceptions import AppException

router = APIRouter()
pentester_service = PentesterService()


@router.get("/", response_model=PaginatedResponse[PentesterProfileSummary])
async def get_pentesters(
    pagination: PaginationParams = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    active_only: bool = Query(True, description="Filter active pentesters only"),
    specialization: Optional[str] = Query(None, description="Filter by specialization"),
    seniority_level: Optional[str] = Query(None, description="Filter by seniority level"),
) -> PaginatedResponse[PentesterProfileSummary]:
    """Get paginated list of pentesters."""
    try:
        # Get pentesters with filters
        if specialization:
            pentesters, total = await pentester_service.get_by_specialization(
                db, specialization, minimum_skill_level=5
            ), 0  # TODO: Implement proper pagination for filtered results
            total = len(pentesters)
        else:
            pentesters, total = await pentester_service.get_multi(
                db, skip=pagination.skip, limit=pagination.limit
            )
        
        # Convert to summary format
        summaries = []
        for pentester in pentesters:
            if active_only and not pentester.is_active:
                continue
            if seniority_level and pentester.seniority_level != seniority_level:
                continue
                
            summary = PentesterProfileSummary(
                id=pentester.id,
                employee_id=pentester.employee_id,
                full_name=pentester.full_name,
                email=pentester.email,
                availability_status=pentester.availability_status,
                current_utilization=pentester.current_utilization,
                seniority_level=pentester.seniority_level,
                primary_specializations=pentester.primary_specializations,
                is_active=pentester.is_active
            )
            summaries.append(summary)
        
        return PaginatedResponse(
            items=summaries,
            total=total,
            skip=pagination.skip,
            limit=pagination.limit
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve pentesters: {str(e)}"
        )


@router.post("/", response_model=PentesterProfile, status_code=status.HTTP_201_CREATED)
async def create_pentester(
    pentester_data: PentesterProfileCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> PentesterProfile:
    """Create a new pentester profile."""
    try:
        # Check if employee ID already exists
        existing = await pentester_service.get_by_employee_id(db, pentester_data.employee_id)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Pentester with employee ID {pentester_data.employee_id} already exists"
            )
        
        # Check if email already exists
        existing_email = await pentester_service.get_by_email(db, pentester_data.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Pentester with email {pentester_data.email} already exists"
            )
        
        pentester = await pentester_service.create(db, obj_in=pentester_data)
        
        # Convert to response model
        return PentesterProfile(
            **pentester.__dict__,
            full_name=pentester.full_name,
            available_hours_remaining=pentester.available_hours_remaining
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create pentester: {str(e)}"
        )


@router.get("/{pentester_id}", response_model=PentesterProfile)
async def get_pentester(
    pentester_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> PentesterProfile:
    """Get a specific pentester by ID."""
    try:
        pentester = await pentester_service.get_with_skills(db, pentester_id)
        if not pentester:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Pentester not found: {pentester_id}"
            )
        
        return PentesterProfile(
            **pentester.__dict__,
            full_name=pentester.full_name,
            available_hours_remaining=pentester.available_hours_remaining
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve pentester: {str(e)}"
        )


@router.put("/{pentester_id}", response_model=PentesterProfile)
async def update_pentester(
    pentester_id: UUID,
    pentester_data: PentesterProfileUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> PentesterProfile:
    """Update a pentester profile."""
    try:
        pentester = await pentester_service.get(db, pentester_id)
        if not pentester:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Pentester not found: {pentester_id}"
            )
        
        updated_pentester = await pentester_service.update(db, db_obj=pentester, obj_in=pentester_data)
        
        return PentesterProfile(
            **updated_pentester.__dict__,
            full_name=updated_pentester.full_name,
            available_hours_remaining=updated_pentester.available_hours_remaining
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update pentester: {str(e)}"
        )


@router.delete("/{pentester_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_pentester(
    pentester_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> None:
    """Delete a pentester profile."""
    try:
        pentester = await pentester_service.get(db, pentester_id)
        if not pentester:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Pentester not found: {pentester_id}"
            )
        
        await pentester_service.remove(db, id=pentester_id)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete pentester: {str(e)}"
        )


@router.get("/available/list", response_model=List[PentesterAvailability])
async def get_available_pentesters(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    max_utilization: float = Query(85.0, ge=0.0, le=100.0, description="Maximum utilization percentage"),
    required_skills: Optional[List[str]] = Query(None, description="Required skills"),
    minimum_seniority: Optional[str] = Query(None, description="Minimum seniority level"),
) -> List[PentesterAvailability]:
    """Get available pentesters based on criteria."""
    try:
        pentesters = await pentester_service.get_available_pentesters(
            db, max_utilization, required_skills, minimum_seniority
        )
        
        return await pentester_service.get_availability_overview(
            db, [p.id for p in pentesters]
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve available pentesters: {str(e)}"
        )


@router.get("/team-leads/list", response_model=List[PentesterProfileSummary])
async def get_team_leads(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    available_only: bool = Query(True, description="Filter available team leads only"),
) -> List[PentesterProfileSummary]:
    """Get team leads."""
    try:
        team_leads = await pentester_service.get_team_leads(db, available_only)
        
        summaries = []
        for lead in team_leads:
            summary = PentesterProfileSummary(
                id=lead.id,
                employee_id=lead.employee_id,
                full_name=lead.full_name,
                email=lead.email,
                availability_status=lead.availability_status,
                current_utilization=lead.current_utilization,
                seniority_level=lead.seniority_level,
                primary_specializations=lead.primary_specializations,
                is_active=lead.is_active
            )
            summaries.append(summary)
        
        return summaries
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve team leads: {str(e)}"
        )


@router.get("/availability/overview", response_model=List[PentesterAvailability])
async def get_availability_overview(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    team_ids: Optional[List[UUID]] = Query(None, description="Filter by team member IDs"),
) -> List[PentesterAvailability]:
    """Get availability overview for pentesters."""
    try:
        return await pentester_service.get_availability_overview(db, team_ids)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve availability overview: {str(e)}"
        )


@router.get("/performance/metrics", response_model=List[PentesterPerformance])
async def get_performance_metrics(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    pentester_id: Optional[UUID] = Query(None, description="Filter by specific pentester"),
) -> List[PentesterPerformance]:
    """Get performance metrics for pentesters."""
    try:
        return await pentester_service.get_performance_metrics(db, pentester_id)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve performance metrics: {str(e)}"
        )


@router.put("/{pentester_id}/utilization", response_model=PentesterProfile)
async def update_utilization(
    pentester_id: UUID,
    new_utilization: float = Query(..., ge=0.0, le=100.0, description="New utilization percentage"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> PentesterProfile:
    """Update pentester utilization."""
    try:
        updated_pentester = await pentester_service.update_utilization(
            db, pentester_id, new_utilization
        )
        
        return PentesterProfile(
            **updated_pentester.__dict__,
            full_name=updated_pentester.full_name,
            available_hours_remaining=updated_pentester.available_hours_remaining
        )
    
    except AppException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update utilization: {str(e)}"
        )


@router.get("/team/capacity", response_model=Dict[str, Any])
async def get_team_capacity(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    team_ids: Optional[List[UUID]] = Query(None, description="Filter by team member IDs"),
) -> Dict[str, Any]:
    """Get team capacity metrics."""
    try:
        return await pentester_service.calculate_team_capacity(db, team_ids)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve team capacity: {str(e)}"
        )


@router.get("/skills/distribution", response_model=Dict[str, Dict[str, int]])
async def get_skill_distribution(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    team_ids: Optional[List[UUID]] = Query(None, description="Filter by team member IDs"),
) -> Dict[str, Dict[str, int]]:
    """Get skill distribution across the team."""
    try:
        return await pentester_service.get_skill_distribution(db, team_ids)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve skill distribution: {str(e)}"
        )
