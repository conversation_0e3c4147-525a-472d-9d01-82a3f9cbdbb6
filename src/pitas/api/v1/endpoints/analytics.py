"""Analytics API endpoints for Phase 9: Advanced Analytics and Reporting Engine."""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.schemas.analytics import (
    AnalyticsModelCreate, AnalyticsModelUpdate, AnalyticsModelResponse,
    ModelTrainingJobCreate, ModelTrainingJobResponse,
    AnalyticsPredictionCreate, AnalyticsPredictionResponse,
    AnalyticsReportCreate, AnalyticsReportResponse,
    AnalyticsAlertCreate, AnalyticsAlertUpdate, AnalyticsAlertResponse,
    AnalyticsDashboardCreate, AnalyticsDashboardUpdate, AnalyticsDashboardResponse,
    PredictiveInsights, AnalyticsSummary, ModelType, AlertStatus
)
from pitas.services.analytics import AnalyticsService
from pitas.core.dashboard import dashboard_engine
from pitas.core.alerting import alerting_engine

router = APIRouter()


# Analytics Models
@router.post("/models", response_model=AnalyticsModelResponse, status_code=status.HTTP_201_CREATED)
async def create_analytics_model(
    model_data: AnalyticsModelCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsModelResponse:
    """Create a new analytics model."""
    service = AnalyticsService(db)
    return await service.create_model(model_data)


@router.get("/models", response_model=List[AnalyticsModelResponse])
async def list_analytics_models(
    model_type: Optional[ModelType] = Query(None, description="Filter by model type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> List[AnalyticsModelResponse]:
    """List analytics models with filtering."""
    service = AnalyticsService(db)
    return await service.list_models(model_type, is_active, page, page_size)


@router.get("/models/{model_id}", response_model=AnalyticsModelResponse)
async def get_analytics_model(
    model_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsModelResponse:
    """Get analytics model by ID."""
    service = AnalyticsService(db)
    model = await service.get_model(model_id)
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analytics model not found"
        )
    
    return model


@router.put("/models/{model_id}", response_model=AnalyticsModelResponse)
async def update_analytics_model(
    model_id: UUID,
    model_data: AnalyticsModelUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsModelResponse:
    """Update analytics model."""
    service = AnalyticsService(db)
    model = await service.update_model(model_id, model_data)
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analytics model not found"
        )
    
    return model


@router.delete("/models/{model_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_analytics_model(
    model_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> None:
    """Delete analytics model."""
    service = AnalyticsService(db)
    success = await service.delete_model(model_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analytics model not found"
        )


# Training Jobs
@router.post("/training-jobs", response_model=ModelTrainingJobResponse, status_code=status.HTTP_201_CREATED)
async def create_training_job(
    job_data: ModelTrainingJobCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ModelTrainingJobResponse:
    """Create a new model training job."""
    service = AnalyticsService(db)
    return await service.create_training_job(job_data)


# Predictions
@router.post("/predictions", response_model=AnalyticsPredictionResponse, status_code=status.HTTP_201_CREATED)
async def create_prediction(
    prediction_data: AnalyticsPredictionCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsPredictionResponse:
    """Create a new prediction."""
    service = AnalyticsService(db)
    return await service.create_prediction(prediction_data)


# Reports
@router.post("/reports", response_model=AnalyticsReportResponse, status_code=status.HTTP_201_CREATED)
async def generate_analytics_report(
    report_data: AnalyticsReportCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsReportResponse:
    """Generate analytics report."""
    service = AnalyticsService(db)
    return await service.generate_report(report_data, UUID(current_user))


# Alerts
@router.post("/alerts", response_model=AnalyticsAlertResponse, status_code=status.HTTP_201_CREATED)
async def create_analytics_alert(
    alert_data: AnalyticsAlertCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsAlertResponse:
    """Create analytics alert."""
    service = AnalyticsService(db)
    return await service.create_alert(alert_data)


@router.put("/alerts/{alert_id}", response_model=AnalyticsAlertResponse)
async def update_analytics_alert(
    alert_id: UUID,
    alert_data: AnalyticsAlertUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsAlertResponse:
    """Update analytics alert."""
    service = AnalyticsService(db)
    alert = await service.update_alert(alert_id, alert_data)
    
    if not alert:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Analytics alert not found"
        )
    
    return alert


# Predictive Insights
@router.get("/insights", response_model=PredictiveInsights)
async def get_predictive_insights(
    start_date: Optional[datetime] = Query(None, description="Analysis start date"),
    end_date: Optional[datetime] = Query(None, description="Analysis end date"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> PredictiveInsights:
    """Get ML-powered predictive insights."""
    service = AnalyticsService(db)
    
    # Default to last 30 days if dates not provided
    if not start_date:
        start_date = datetime.utcnow() - timedelta(days=30)
    if not end_date:
        end_date = datetime.utcnow()
    
    return await service.generate_predictive_insights(start_date, end_date)


# Analytics Summary
@router.get("/summary", response_model=AnalyticsSummary)
async def get_analytics_summary(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsSummary:
    """Get analytics summary for dashboards."""
    service = AnalyticsService(db)
    return await service.get_analytics_summary()


# Vulnerability Prediction
@router.post("/predict/vulnerabilities", response_model=AnalyticsPredictionResponse)
async def predict_vulnerabilities(
    input_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsPredictionResponse:
    """Predict vulnerability discovery and remediation."""
    service = AnalyticsService(db)
    
    prediction_data = AnalyticsPredictionCreate(
        model_id=UUID("00000000-0000-0000-0000-000000000001"),  # Default vulnerability model
        prediction_type="vulnerability_forecast",
        input_data=input_data,
        entity_type="system"
    )
    
    return await service.create_prediction(prediction_data)


# Threat Classification
@router.post("/predict/threats", response_model=AnalyticsPredictionResponse)
async def classify_threats(
    input_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsPredictionResponse:
    """Classify and predict threat evolution."""
    service = AnalyticsService(db)
    
    prediction_data = AnalyticsPredictionCreate(
        model_id=UUID("00000000-0000-0000-0000-000000000002"),  # Default threat model
        prediction_type="threat_classification",
        input_data=input_data,
        entity_type="threat"
    )
    
    return await service.create_prediction(prediction_data)


# Remediation Timeline
@router.post("/predict/remediation", response_model=AnalyticsPredictionResponse)
async def predict_remediation_timeline(
    input_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsPredictionResponse:
    """Predict remediation timeline and resource needs."""
    service = AnalyticsService(db)
    
    prediction_data = AnalyticsPredictionCreate(
        model_id=UUID("00000000-0000-0000-0000-000000000003"),  # Default remediation model
        prediction_type="remediation_timeline",
        input_data=input_data,
        entity_type="vulnerability"
    )
    
    return await service.create_prediction(prediction_data)


# Team Optimization
@router.post("/predict/team-optimization", response_model=AnalyticsPredictionResponse)
async def optimize_team_allocation(
    input_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsPredictionResponse:
    """Optimize team resource allocation."""
    service = AnalyticsService(db)
    
    prediction_data = AnalyticsPredictionCreate(
        model_id=UUID("00000000-0000-0000-0000-000000000004"),  # Default team optimization model
        prediction_type="team_optimization",
        input_data=input_data,
        entity_type="team"
    )
    
    return await service.create_prediction(prediction_data)


# Risk Scoring
@router.post("/predict/risk-score", response_model=AnalyticsPredictionResponse)
async def calculate_risk_score(
    input_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AnalyticsPredictionResponse:
    """Calculate comprehensive risk score."""
    service = AnalyticsService(db)
    
    prediction_data = AnalyticsPredictionCreate(
        model_id=UUID("00000000-0000-0000-0000-000000000005"),  # Default risk scoring model
        prediction_type="risk_scoring",
        input_data=input_data,
        entity_type="asset"
    )
    
    return await service.create_prediction(prediction_data)


# Dashboard Endpoints
@router.get("/dashboards/executive")
async def get_executive_dashboard(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> Dict[str, Any]:
    """Get executive dashboard data."""
    return await dashboard_engine.generate_executive_dashboard(db)


@router.get("/dashboards/technical")
async def get_technical_dashboard(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> Dict[str, Any]:
    """Get technical dashboard data."""
    return await dashboard_engine.generate_technical_dashboard(db)


@router.get("/dashboards/operational")
async def get_operational_dashboard(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> Dict[str, Any]:
    """Get operational dashboard data."""
    return await dashboard_engine.generate_operational_dashboard(db)


# Alerting Endpoints
@router.post("/alerts/evaluate")
async def evaluate_alert_conditions(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> List[AnalyticsAlertResponse]:
    """Evaluate all alerting conditions and generate alerts."""
    return await alerting_engine.evaluate_conditions(db)


@router.get("/alerts/correlations")
async def get_alert_correlations(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> List[Dict[str, Any]]:
    """Get correlated alerts to reduce noise."""
    return await alerting_engine.correlate_alerts(db)
