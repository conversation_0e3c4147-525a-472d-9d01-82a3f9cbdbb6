"""Career development API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.schemas.career import (
    IDP, IDPCreate, IDPUpdate, IDPWithGoals,
    DevelopmentGoal, DevelopmentGoalCreate, DevelopmentGoalUpdate,
    DevelopmentActivity, DevelopmentActivityCreate, DevelopmentActivityUpdate,
    CareerProgressSummary
)
from pitas.services.career import (
    CareerDevelopmentService, DevelopmentGoalService, 
    DevelopmentActivityService, CareerAnalyticsService
)

router = APIRouter()

# Service instances
career_service = CareerDevelopmentService()
goal_service = DevelopmentGoalService()
activity_service = DevelopmentActivityService()
analytics_service = CareerAnalyticsService()


@router.post("/idps", response_model=IDP, status_code=status.HTTP_201_CREATED)
async def create_idp(
    idp_data: IDPCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> IDP:
    """Create a new Individual Development Plan."""
    try:
        idp = await career_service.create_idp(db, idp_data=idp_data, user_id=UUID(current_user_id))
        return IDP.model_validate(idp)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create IDP")


@router.get("/idps", response_model=List[IDP])
async def get_user_idps(
    include_goals: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> List[IDP]:
    """Get all IDPs for the current user."""
    try:
        idps = await career_service.get_user_idps(
            db, 
            user_id=UUID(current_user_id),
            include_goals=include_goals
        )
        return [IDP.model_validate(idp) for idp in idps]
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve IDPs")


@router.get("/idps/active", response_model=Optional[IDPWithGoals])
async def get_active_idp(
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> Optional[IDPWithGoals]:
    """Get the active IDP for the current user."""
    try:
        idp = await career_service.get_active_idp(db, user_id=UUID(current_user_id))
        if idp:
            return IDPWithGoals.model_validate(idp)
        return None
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve active IDP")


@router.get("/idps/{idp_id}", response_model=IDPWithGoals)
async def get_idp(
    idp_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> IDPWithGoals:
    """Get a specific IDP by ID."""
    try:
        idp = await career_service.get(db, idp_id)
        if not idp:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="IDP not found")
        
        # Check if user owns this IDP or is the manager
        if str(idp.user_id) != current_user_id and str(idp.manager_id) != current_user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        return IDPWithGoals.model_validate(idp)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve IDP")


@router.put("/idps/{idp_id}", response_model=IDP)
async def update_idp(
    idp_id: UUID,
    idp_update: IDPUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> IDP:
    """Update an IDP."""
    try:
        idp = await career_service.get(db, idp_id)
        if not idp:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="IDP not found")
        
        # Check if user owns this IDP or is the manager
        if str(idp.user_id) != current_user_id and str(idp.manager_id) != current_user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        updated_idp = await career_service.update(db, db_obj=idp, obj_in=idp_update)
        return IDP.model_validate(updated_idp)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update IDP")


@router.post("/goals", response_model=DevelopmentGoal, status_code=status.HTTP_201_CREATED)
async def create_goal(
    goal_data: DevelopmentGoalCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> DevelopmentGoal:
    """Create a new development goal."""
    try:
        # Verify user owns the IDP
        idp = await career_service.get(db, goal_data.idp_id)
        if not idp:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="IDP not found")
        
        if str(idp.user_id) != current_user_id and str(idp.manager_id) != current_user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        goal = await goal_service.create_goal(db, goal_data=goal_data)
        return DevelopmentGoal.model_validate(goal)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create goal")


@router.get("/idps/{idp_id}/goals", response_model=List[DevelopmentGoal])
async def get_idp_goals(
    idp_id: UUID,
    include_activities: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> List[DevelopmentGoal]:
    """Get all goals for an IDP."""
    try:
        # Verify user owns the IDP
        idp = await career_service.get(db, idp_id)
        if not idp:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="IDP not found")
        
        if str(idp.user_id) != current_user_id and str(idp.manager_id) != current_user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        goals = await goal_service.get_idp_goals(
            db, 
            idp_id=idp_id,
            include_activities=include_activities
        )
        return [DevelopmentGoal.model_validate(goal) for goal in goals]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve goals")


@router.put("/goals/{goal_id}/progress", response_model=DevelopmentGoal)
async def update_goal_progress(
    goal_id: UUID,
    progress: float,
    notes: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> DevelopmentGoal:
    """Update goal progress."""
    try:
        goal = await goal_service.get(db, goal_id)
        if not goal:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Goal not found")
        
        # Verify user owns the goal's IDP
        idp = await career_service.get(db, goal.idp_id)
        if not idp or (str(idp.user_id) != current_user_id and str(idp.manager_id) != current_user_id):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        updated_goal = await goal_service.update_goal_progress(
            db, 
            goal_id=goal_id,
            progress=progress,
            notes=notes
        )
        return DevelopmentGoal.model_validate(updated_goal)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update goal progress")


@router.post("/activities", response_model=DevelopmentActivity, status_code=status.HTTP_201_CREATED)
async def create_activity(
    activity_data: DevelopmentActivityCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> DevelopmentActivity:
    """Create a new development activity."""
    try:
        # Verify user owns the goal's IDP
        goal = await goal_service.get(db, activity_data.goal_id)
        if not goal:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Goal not found")
        
        idp = await career_service.get(db, goal.idp_id)
        if not idp or (str(idp.user_id) != current_user_id and str(idp.manager_id) != current_user_id):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied")
        
        activity = await activity_service.create_activity(db, activity_data=activity_data)
        return DevelopmentActivity.model_validate(activity)
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create activity")


@router.get("/progress-summary", response_model=CareerProgressSummary)
async def get_career_progress_summary(
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id)
) -> CareerProgressSummary:
    """Get career progress summary for the current user."""
    try:
        summary = await analytics_service.get_career_progress_summary(
            db, 
            user_id=UUID(current_user_id)
        )
        return summary
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve career progress summary")
