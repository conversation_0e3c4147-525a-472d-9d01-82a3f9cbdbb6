"""Resource allocation endpoints for Phase 2."""

from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.schemas.base import PaginatedResponse, PaginationParams
from pitas.schemas.resource_allocation import (
    ResourceAllocation, ResourceAllocationCreate, ResourceAllocationUpdate,
    ResourceAllocationSummary, AllocationApproval, AllocationConflict, TeamAllocation
)
from pitas.services.base import BaseService
from pitas.services.resource_optimizer import ResourceOptimizer
from pitas.services.pentester import PentesterService
from pitas.services.project import ProjectService
from pitas.db.models import ResourceAllocation as ResourceAllocationModel
from pitas.core.exceptions import AppException

router = APIRouter()
allocation_service = BaseService(ResourceAllocationModel)
resource_optimizer = ResourceOptimizer()
pentester_service = PentesterService()
project_service = ProjectService()


@router.get("/", response_model=PaginatedResponse[ResourceAllocationSummary])
async def get_allocations(
    pagination: PaginationParams = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    pentester_id: Optional[UUID] = Query(None, description="Filter by pentester ID"),
    project_id: Optional[UUID] = Query(None, description="Filter by project ID"),
    status_filter: Optional[str] = Query(None, description="Filter by allocation status"),
    active_only: bool = Query(True, description="Filter active allocations only"),
) -> PaginatedResponse[ResourceAllocationSummary]:
    """Get paginated list of resource allocations."""
    try:
        # TODO: Implement proper filtering in the service
        allocations, total = await allocation_service.get_multi(
            db, skip=pagination.skip, limit=pagination.limit
        )
        
        # Convert to summary format
        summaries = []
        for allocation in allocations:
            # Skip filtering logic for now - would be implemented in service
            if active_only and not allocation.is_active:
                continue
            if status_filter and allocation.status.value != status_filter:
                continue
            if pentester_id and allocation.pentester_id != pentester_id:
                continue
            if project_id and allocation.project_id != project_id:
                continue
            
            # Get pentester and project names
            pentester = await pentester_service.get(db, allocation.pentester_id)
            project = await project_service.get(db, allocation.project_id)
            
            summary = ResourceAllocationSummary(
                id=allocation.id,
                pentester_id=allocation.pentester_id,
                project_id=allocation.project_id,
                pentester_name=pentester.full_name if pentester else "Unknown",
                project_name=project.name if project else "Unknown",
                status=allocation.status,
                role=allocation.role,
                allocated_hours=allocation.allocated_hours,
                actual_hours=allocation.actual_hours,
                utilization_percentage=allocation.utilization_percentage,
                allocation_start_date=allocation.allocation_start_date,
                allocation_end_date=allocation.allocation_end_date,
                is_active=allocation.is_active,
                completion_percentage=allocation.completion_percentage
            )
            summaries.append(summary)
        
        return PaginatedResponse(
            items=summaries,
            total=total,
            skip=pagination.skip,
            limit=pagination.limit
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve allocations: {str(e)}"
        )


@router.post("/", response_model=ResourceAllocation, status_code=status.HTTP_201_CREATED)
async def create_allocation(
    allocation_data: ResourceAllocationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ResourceAllocation:
    """Create a new resource allocation."""
    try:
        # Validate pentester exists
        pentester = await pentester_service.get(db, allocation_data.pentester_id)
        if not pentester:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Pentester not found: {allocation_data.pentester_id}"
            )
        
        # Validate project exists
        project = await project_service.get(db, allocation_data.project_id)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Project not found: {allocation_data.project_id}"
            )
        
        # Check capacity constraints
        if allocation_data.allocated_hours > pentester.available_hours_remaining:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Insufficient capacity: needs {allocation_data.allocated_hours}h, "
                       f"has {pentester.available_hours_remaining}h available"
            )
        
        allocation = await allocation_service.create(db, obj_in=allocation_data)
        
        # Update pentester utilization
        new_utilization = pentester.current_utilization + allocation_data.utilization_percentage
        await pentester_service.update_utilization(db, pentester.id, new_utilization)
        
        # Convert to response model
        return ResourceAllocation(
            **allocation.__dict__,
            allocation_duration_days=allocation.allocation_duration_days,
            is_active=allocation.is_active,
            is_overallocated=allocation.is_overallocated,
            completion_percentage=allocation.completion_percentage,
            cost_to_date=allocation.cost_to_date
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create allocation: {str(e)}"
        )


@router.get("/{allocation_id}", response_model=ResourceAllocation)
async def get_allocation(
    allocation_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ResourceAllocation:
    """Get a specific resource allocation by ID."""
    try:
        allocation = await allocation_service.get(db, allocation_id)
        if not allocation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Allocation not found: {allocation_id}"
            )
        
        return ResourceAllocation(
            **allocation.__dict__,
            allocation_duration_days=allocation.allocation_duration_days,
            is_active=allocation.is_active,
            is_overallocated=allocation.is_overallocated,
            completion_percentage=allocation.completion_percentage,
            cost_to_date=allocation.cost_to_date
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve allocation: {str(e)}"
        )


@router.put("/{allocation_id}", response_model=ResourceAllocation)
async def update_allocation(
    allocation_id: UUID,
    allocation_data: ResourceAllocationUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ResourceAllocation:
    """Update a resource allocation."""
    try:
        allocation = await allocation_service.get(db, allocation_id)
        if not allocation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Allocation not found: {allocation_id}"
            )
        
        updated_allocation = await allocation_service.update(
            db, db_obj=allocation, obj_in=allocation_data
        )
        
        return ResourceAllocation(
            **updated_allocation.__dict__,
            allocation_duration_days=updated_allocation.allocation_duration_days,
            is_active=updated_allocation.is_active,
            is_overallocated=updated_allocation.is_overallocated,
            completion_percentage=updated_allocation.completion_percentage,
            cost_to_date=updated_allocation.cost_to_date
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update allocation: {str(e)}"
        )


@router.delete("/{allocation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_allocation(
    allocation_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> None:
    """Delete a resource allocation."""
    try:
        allocation = await allocation_service.get(db, allocation_id)
        if not allocation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Allocation not found: {allocation_id}"
            )
        
        # Update pentester utilization before deletion
        pentester = await pentester_service.get(db, allocation.pentester_id)
        if pentester:
            new_utilization = max(0, pentester.current_utilization - allocation.utilization_percentage)
            await pentester_service.update_utilization(db, pentester.id, new_utilization)
        
        await allocation_service.remove(db, id=allocation_id)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete allocation: {str(e)}"
        )


@router.post("/{allocation_id}/approve", response_model=ResourceAllocation)
async def approve_allocation(
    allocation_id: UUID,
    approval_data: AllocationApproval,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ResourceAllocation:
    """Approve or reject a resource allocation."""
    try:
        allocation = await allocation_service.get(db, allocation_id)
        if not allocation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Allocation not found: {allocation_id}"
            )
        
        # Update allocation status
        from datetime import datetime
        update_data = ResourceAllocationUpdate(
            status="approved" if approval_data.approved else "cancelled",
            approved_by=approval_data.approved_by,
            approved_at=datetime.utcnow(),
            internal_notes=approval_data.notes
        )
        
        updated_allocation = await allocation_service.update(
            db, db_obj=allocation, obj_in=update_data
        )
        
        return ResourceAllocation(
            **updated_allocation.__dict__,
            allocation_duration_days=updated_allocation.allocation_duration_days,
            is_active=updated_allocation.is_active,
            is_overallocated=updated_allocation.is_overallocated,
            completion_percentage=updated_allocation.completion_percentage,
            cost_to_date=updated_allocation.cost_to_date
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to approve allocation: {str(e)}"
        )


@router.post("/optimize", response_model=Dict[str, Any])
async def optimize_allocations(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    project_ids: Optional[List[UUID]] = Query(None, description="Specific projects to optimize"),
    team_ids: Optional[List[UUID]] = Query(None, description="Specific team members to consider"),
) -> Dict[str, Any]:
    """Optimize resource allocations using AI-driven algorithms."""
    try:
        # Get projects to optimize
        if project_ids:
            projects = []
            for project_id in project_ids:
                project = await project_service.get(db, project_id)
                if project:
                    projects.append(project)
        else:
            projects = await project_service.get_unallocated_projects(db)
        
        # Get available team members
        if team_ids:
            team_members = []
            for team_id in team_ids:
                pentester = await pentester_service.get_with_skills(db, team_id)
                if pentester:
                    team_members.append(pentester)
        else:
            team_members = await pentester_service.get_available_pentesters(db)
        
        # Run optimization
        allocation_plan = await resource_optimizer.optimize_team_allocation(
            db, projects, team_members
        )
        
        return {
            "optimization_score": allocation_plan.optimization_score,
            "total_utilization": allocation_plan.total_utilization,
            "proposed_allocations": len(allocation_plan.allocations),
            "unallocated_projects": len(allocation_plan.unallocated_projects),
            "conflicts_detected": len(allocation_plan.conflicts),
            "skill_coverage": allocation_plan.skill_coverage,
            "allocations": allocation_plan.allocations,
            "conflicts": [conflict.dict() for conflict in allocation_plan.conflicts]
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to optimize allocations: {str(e)}"
        )


@router.get("/conflicts/detect", response_model=List[AllocationConflict])
async def detect_conflicts(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    pentester_id: Optional[UUID] = Query(None, description="Check conflicts for specific pentester"),
) -> List[AllocationConflict]:
    """Detect allocation conflicts."""
    try:
        # This would be implemented with proper conflict detection logic
        # For now, return empty list as placeholder
        return []
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to detect conflicts: {str(e)}"
        )


@router.get("/team/{project_id}", response_model=TeamAllocation)
async def get_team_allocation(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> TeamAllocation:
    """Get team allocation overview for a project."""
    try:
        project = await project_service.get_with_allocations(db, project_id)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Project not found: {project_id}"
            )
        
        # Build team allocation summary
        team_members = []
        team_lead = None
        total_allocated_hours = 0
        total_actual_hours = 0
        utilizations = []
        
        for allocation in project.resource_allocations:
            if allocation.status in ["approved", "active"]:
                pentester = await pentester_service.get(db, allocation.pentester_id)
                
                summary = ResourceAllocationSummary(
                    id=allocation.id,
                    pentester_id=allocation.pentester_id,
                    project_id=allocation.project_id,
                    pentester_name=pentester.full_name if pentester else "Unknown",
                    project_name=project.name,
                    status=allocation.status,
                    role=allocation.role,
                    allocated_hours=allocation.allocated_hours,
                    actual_hours=allocation.actual_hours,
                    utilization_percentage=allocation.utilization_percentage,
                    allocation_start_date=allocation.allocation_start_date,
                    allocation_end_date=allocation.allocation_end_date,
                    is_active=allocation.is_active,
                    completion_percentage=allocation.completion_percentage
                )
                
                team_members.append(summary)
                total_allocated_hours += allocation.allocated_hours
                total_actual_hours += allocation.actual_hours
                utilizations.append(allocation.utilization_percentage)
                
                if allocation.role.value == "team_lead":
                    team_lead = summary
        
        average_utilization = sum(utilizations) / len(utilizations) if utilizations else 0
        
        # Calculate skill coverage (simplified)
        skill_coverage = {}
        if project.required_skills:
            for skill in project.required_skills:
                skill_coverage[skill] = 1  # Simplified - would check actual team skills
        
        return TeamAllocation(
            project_id=project.id,
            project_name=project.name,
            team_members=team_members,
            total_allocated_hours=total_allocated_hours,
            total_actual_hours=total_actual_hours,
            average_utilization=average_utilization,
            team_lead=team_lead,
            skill_coverage=skill_coverage,
            capacity_status="green" if average_utilization < 85 else "yellow" if average_utilization < 95 else "red"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve team allocation: {str(e)}"
        )
