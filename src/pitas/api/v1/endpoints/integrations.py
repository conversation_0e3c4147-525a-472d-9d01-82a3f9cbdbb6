"""Integration management endpoints for Phase 7."""

from typing import List, Optional
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.db.models.integration import IntegrationType
from pitas.schemas.integration import (
    Integration, IntegrationCreate, IntegrationUpdate,
    IntegrationTestRequest, IntegrationTestResult,
    SyncOperationRequest, SyncOperationStatus,
    IntegrationSyncLog
)
from pitas.schemas.base import PaginationParams
from pitas.services.integration import IntegrationService, SyncService

logger = structlog.get_logger(__name__)
router = APIRouter()

# Initialize services
integration_service = IntegrationService()
sync_service = SyncService()


@router.get("/", response_model=List[Integration])
async def list_integrations(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    integration_type: Optional[IntegrationType] = Query(None, description="Filter by integration type"),
    enabled_only: bool = Query(True, description="Only return enabled integrations"),
    pagination: PaginationParams = Depends(),
) -> List[Integration]:
    """List all integrations.
    
    Args:
        db: Database session
        current_user: Current user ID
        integration_type: Filter by integration type
        enabled_only: Only return enabled integrations
        pagination: Pagination parameters
        
    Returns:
        List of integrations
    """
    try:
        if integration_type:
            integrations = await integration_service.get_integrations_by_type(
                db, integration_type=integration_type, enabled_only=enabled_only
            )
        else:
            integrations, _ = await integration_service.get_multi(
                db, skip=pagination.skip, limit=pagination.limit
            )
        
        logger.info(
            "Integrations listed",
            user_id=current_user,
            count=len(integrations),
            integration_type=integration_type
        )
        
        return integrations
        
    except Exception as e:
        logger.error("Failed to list integrations", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve integrations"
        )


@router.post("/", response_model=Integration, status_code=status.HTTP_201_CREATED)
async def create_integration(
    integration_data: IntegrationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> Integration:
    """Create a new integration.
    
    Args:
        integration_data: Integration creation data
        db: Database session
        current_user: Current user ID
        
    Returns:
        Created integration
    """
    try:
        integration = await integration_service.create_integration(
            db, integration_data=integration_data
        )
        
        logger.info(
            "Integration created",
            integration_id=integration.id,
            integration_type=integration.integration_type,
            user_id=current_user
        )
        
        return integration
        
    except ValueError as e:
        logger.warning("Invalid integration data", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to create integration", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create integration"
        )


@router.get("/{integration_id}", response_model=Integration)
async def get_integration(
    integration_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> Integration:
    """Get an integration by ID.
    
    Args:
        integration_id: Integration ID
        db: Database session
        current_user: Current user ID
        
    Returns:
        Integration details
    """
    integration = await integration_service.get(db, id=integration_id)
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found"
        )
    
    return integration


@router.put("/{integration_id}", response_model=Integration)
async def update_integration(
    integration_id: UUID,
    integration_data: IntegrationUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> Integration:
    """Update an integration.
    
    Args:
        integration_id: Integration ID
        integration_data: Update data
        db: Database session
        current_user: Current user ID
        
    Returns:
        Updated integration
    """
    try:
        integration = await integration_service.update_integration(
            db, integration_id=integration_id, integration_data=integration_data
        )
        
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Integration not found"
            )
        
        logger.info(
            "Integration updated",
            integration_id=integration_id,
            user_id=current_user
        )
        
        return integration
        
    except ValueError as e:
        logger.warning("Invalid integration update data", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to update integration", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update integration"
        )


@router.delete("/{integration_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_integration(
    integration_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
):
    """Delete an integration.
    
    Args:
        integration_id: Integration ID
        db: Database session
        current_user: Current user ID
    """
    integration = await integration_service.get(db, id=integration_id)
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found"
        )
    
    try:
        await db.delete(integration)
        await db.commit()
        
        logger.info(
            "Integration deleted",
            integration_id=integration_id,
            user_id=current_user
        )
        
    except Exception as e:
        logger.error("Failed to delete integration", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete integration"
        )


@router.post("/{integration_id}/test", response_model=IntegrationTestResult)
async def test_integration(
    integration_id: UUID,
    test_request: IntegrationTestRequest,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> IntegrationTestResult:
    """Test integration connectivity.
    
    Args:
        integration_id: Integration ID
        test_request: Test request parameters
        db: Database session
        current_user: Current user ID
        
    Returns:
        Test result
    """
    try:
        result = await integration_service.test_integration(
            db, integration_id=integration_id, test_request=test_request
        )
        
        logger.info(
            "Integration test completed",
            integration_id=integration_id,
            success=result.success,
            user_id=current_user
        )
        
        return result
        
    except Exception as e:
        logger.error("Integration test failed", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test integration"
        )


@router.post("/{integration_id}/sync", response_model=dict)
async def start_sync(
    integration_id: UUID,
    sync_request: SyncOperationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> dict:
    """Start a synchronization operation.
    
    Args:
        integration_id: Integration ID
        sync_request: Sync request parameters
        db: Database session
        current_user: Current user ID
        
    Returns:
        Sync operation details
    """
    try:
        operation_id = await sync_service.start_sync_operation(
            db, integration_id=integration_id, sync_request=sync_request
        )
        
        logger.info(
            "Sync operation started",
            integration_id=integration_id,
            operation_id=operation_id,
            user_id=current_user
        )
        
        return {
            "operation_id": operation_id,
            "message": "Sync operation started successfully"
        }
        
    except ValueError as e:
        logger.warning("Invalid sync request", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to start sync", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start sync operation"
        )


@router.get("/sync/{operation_id}/status", response_model=SyncOperationStatus)
async def get_sync_status(
    operation_id: str,
    current_user: str = Depends(get_current_user_id),
) -> SyncOperationStatus:
    """Get sync operation status.
    
    Args:
        operation_id: Operation ID
        current_user: Current user ID
        
    Returns:
        Sync operation status
    """
    status_info = await sync_service.get_sync_status(operation_id)
    if not status_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sync operation not found"
        )
    
    return status_info


@router.delete("/sync/{operation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def cancel_sync(
    operation_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
):
    """Cancel a sync operation.
    
    Args:
        operation_id: Operation ID
        db: Database session
        current_user: Current user ID
    """
    success = await sync_service.cancel_sync_operation(db, operation_id=operation_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Sync operation not found or cannot be cancelled"
        )
    
    logger.info(
        "Sync operation cancelled",
        operation_id=operation_id,
        user_id=current_user
    )


@router.get("/{integration_id}/sync-logs", response_model=List[IntegrationSyncLog])
async def get_sync_logs(
    integration_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
) -> List[IntegrationSyncLog]:
    """Get sync logs for an integration.
    
    Args:
        integration_id: Integration ID
        db: Database session
        current_user: Current user ID
        limit: Maximum number of logs to return
        offset: Number of logs to skip
        
    Returns:
        List of sync logs
    """
    # Verify integration exists
    integration = await integration_service.get(db, id=integration_id)
    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Integration not found"
        )
    
    try:
        logs = await sync_service.get_sync_logs(
            db, integration_id=integration_id, limit=limit, offset=offset
        )
        
        return logs
        
    except Exception as e:
        logger.error("Failed to get sync logs", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sync logs"
        )


@router.get("/{integration_id}/health", response_model=dict)
async def get_integration_health(
    integration_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> dict:
    """Get integration health metrics.
    
    Args:
        integration_id: Integration ID
        db: Database session
        current_user: Current user ID
        
    Returns:
        Health metrics
    """
    try:
        health_metrics = await integration_service.get_integration_health(
            db, integration_id=integration_id
        )
        
        return health_metrics
        
    except Exception as e:
        logger.error("Failed to get integration health", error=str(e), user_id=current_user)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve integration health"
        )
