"""Project management API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_db, get_current_user
from pitas.db.models.user import User
from pitas.schemas.project import (
    ProjectCreate,
    ProjectUpdate,
    ProjectResponse,
    ProjectSummary,
    ProjectDashboard,
    ProjectListResponse
)
from pitas.services.workflow import WorkflowService

router = APIRouter()
workflow_service = WorkflowService()


@router.post("/", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectResponse:
    """Create a new project.
    
    Args:
        project_data: Project creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created project
    """
    try:
        project = await workflow_service.create(db, obj_in=project_data)
        
        # Create initial phase deliverables
        await workflow_service.create_phase_deliverables(
            db, project.id, project.current_phase
        )
        
        await db.commit()
        return ProjectResponse.model_validate(project)
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create project: {str(e)}"
        )


@router.get("/", response_model=ProjectListResponse)
async def list_projects(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    client_id: Optional[UUID] = Query(None, description="Filter by client ID"),
    project_manager_id: Optional[UUID] = Query(None, description="Filter by project manager"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectListResponse:
    """List projects with pagination and filtering.
    
    Args:
        page: Page number
        per_page: Items per page
        is_active: Filter by active status
        client_id: Filter by client ID
        project_manager_id: Filter by project manager
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Paginated list of projects
    """
    skip = (page - 1) * per_page
    
    # TODO: Add filtering logic based on parameters
    projects, total = await workflow_service.get_multi(db, skip=skip, limit=per_page)
    
    total_pages = (total + per_page - 1) // per_page
    
    project_summaries = [
        ProjectSummary(
            **project.__dict__,
            client_name="Client Name",  # TODO: Load from relationship
            team_size=0  # TODO: Calculate from team assignments
        )
        for project in projects
    ]
    
    return ProjectListResponse(
        projects=project_summaries,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectResponse:
    """Get a specific project by ID.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Project details
    """
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    return ProjectResponse.model_validate(project)


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: UUID,
    project_update: ProjectUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectResponse:
    """Update a project.
    
    Args:
        project_id: Project ID
        project_update: Project update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated project
    """
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        updated_project = await workflow_service.update(db, db_obj=project, obj_in=project_update)
        await db.commit()
        return ProjectResponse.model_validate(updated_project)
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update project: {str(e)}"
        )


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> None:
    """Delete a project (soft delete by archiving).
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
    """
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        # Soft delete by archiving
        project.is_active = False
        project.is_archived = True
        await db.commit()
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete project: {str(e)}"
        )


@router.get("/{project_id}/dashboard", response_model=ProjectDashboard)
async def get_project_dashboard(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectDashboard:
    """Get comprehensive project dashboard data.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Project dashboard data
    """
    project = await workflow_service.get_project_with_workflow_data(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        # Get current phase info
        current_phase_info = await workflow_service.get_phase_requirements(project.current_phase)
        
        # Get progress metrics
        progress_metrics = await workflow_service.get_project_progress_metrics(db, project_id)
        
        # Get timeline
        timeline = await workflow_service.get_project_timeline(db, project_id)
        
        # TODO: Load related data with proper relationships
        team_assignments = []
        recent_deliverables = []
        recent_transitions = []
        upcoming_milestones = []
        
        return ProjectDashboard(
            project=ProjectResponse.model_validate(project),
            current_phase_info=current_phase_info,
            team_assignments=team_assignments,
            recent_deliverables=recent_deliverables,
            recent_transitions=recent_transitions,
            progress_metrics=progress_metrics,
            upcoming_milestones=upcoming_milestones
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load project dashboard: {str(e)}"
        )


@router.get("/{project_id}/metrics")
async def get_project_metrics(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> dict:
    """Get detailed project metrics and analytics.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Project metrics
    """
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        metrics = await workflow_service.get_project_progress_metrics(db, project_id)
        return metrics
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load project metrics: {str(e)}"
        )


@router.post("/{project_id}/archive", status_code=status.HTTP_204_NO_CONTENT)
async def archive_project(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> None:
    """Archive a project.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
    """
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        project.is_archived = True
        project.is_active = False
        await db.commit()
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to archive project: {str(e)}"
        )


@router.post("/{project_id}/restore", status_code=status.HTTP_204_NO_CONTENT)
async def restore_project(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> None:
    """Restore an archived project.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
    """
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        project.is_archived = False
        project.is_active = True
        await db.commit()
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to restore project: {str(e)}"
        )
