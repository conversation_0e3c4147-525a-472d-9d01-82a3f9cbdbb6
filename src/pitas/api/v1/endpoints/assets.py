"""Asset management API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.schemas.vulnerability import (
    AssetCreate, AssetUpdate, AssetResponse, AssetWithVulnerabilities,
    AssetVulnerabilityCreate, AssetVulnerabilityUpdate, AssetVulnerabilityResponse
)
from pitas.services.asset import AssetService

router = APIRouter()


@router.post("/", response_model=AssetResponse, status_code=status.HTTP_201_CREATED)
async def create_asset(
    asset_data: AssetCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AssetResponse:
    """Create a new asset.
    
    Args:
        asset_data: Asset creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        AssetResponse: Created asset
    """
    service = AssetService(db)
    asset = await service.create_asset(asset_data)
    
    return AssetResponse.model_validate(asset)


@router.get("/", response_model=List[AssetResponse])
async def list_assets(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    asset_type: Optional[str] = Query(None, description="Filter by asset type"),
    business_criticality: Optional[str] = Query(None, description="Filter by criticality"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> List[AssetResponse]:
    """List assets with filtering and pagination.
    
    Args:
        page: Page number
        page_size: Items per page
        asset_type: Filter by asset type
        business_criticality: Filter by business criticality
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List[AssetResponse]: List of assets
    """
    service = AssetService(db)
    assets = await service.list_assets(
        page=page,
        page_size=page_size,
        asset_type=asset_type,
        business_criticality=business_criticality,
    )
    
    return [AssetResponse.model_validate(asset) for asset in assets]


@router.get("/{asset_id}", response_model=AssetWithVulnerabilities)
async def get_asset(
    asset_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AssetWithVulnerabilities:
    """Get asset by ID with associated vulnerabilities.
    
    Args:
        asset_id: Asset ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        AssetWithVulnerabilities: Asset with vulnerabilities
        
    Raises:
        HTTPException: If asset not found
    """
    service = AssetService(db)
    asset = await service.get_asset_by_id(asset_id)
    
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    # Get associated vulnerabilities
    vulnerabilities = await service.get_asset_vulnerabilities(asset_id)
    
    return AssetWithVulnerabilities(
        **asset.__dict__,
        vulnerabilities=vulnerabilities,
    )


@router.put("/{asset_id}", response_model=AssetResponse)
async def update_asset(
    asset_id: UUID,
    asset_data: AssetUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AssetResponse:
    """Update asset.
    
    Args:
        asset_id: Asset ID
        asset_data: Asset update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        AssetResponse: Updated asset
        
    Raises:
        HTTPException: If asset not found
    """
    service = AssetService(db)
    asset = await service.update_asset(asset_id, asset_data)
    
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )
    
    return AssetResponse.model_validate(asset)


@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(
    asset_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> None:
    """Delete asset.
    
    Args:
        asset_id: Asset ID
        db: Database session
        current_user: Current authenticated user
        
    Raises:
        HTTPException: If asset not found
    """
    service = AssetService(db)
    success = await service.delete_asset(asset_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset not found"
        )


@router.post("/{asset_id}/vulnerabilities", response_model=AssetVulnerabilityResponse)
async def associate_vulnerability_with_asset(
    asset_id: UUID,
    association_data: AssetVulnerabilityCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AssetVulnerabilityResponse:
    """Associate a vulnerability with an asset.
    
    Args:
        asset_id: Asset ID
        association_data: Association data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        AssetVulnerabilityResponse: Created association
    """
    # Ensure asset_id matches
    association_data.asset_id = asset_id
    
    service = AssetService(db)
    association = await service.create_asset_vulnerability_association(association_data)
    
    return AssetVulnerabilityResponse.model_validate(association)


@router.get("/{asset_id}/vulnerabilities", response_model=List[AssetVulnerabilityResponse])
async def get_asset_vulnerability_associations(
    asset_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> List[AssetVulnerabilityResponse]:
    """Get vulnerability associations for an asset.
    
    Args:
        asset_id: Asset ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List[AssetVulnerabilityResponse]: Asset-vulnerability associations
    """
    service = AssetService(db)
    associations = await service.get_asset_vulnerability_associations(asset_id)
    
    return [AssetVulnerabilityResponse.model_validate(assoc) for assoc in associations]


@router.put("/{asset_id}/vulnerabilities/{vulnerability_id}", response_model=AssetVulnerabilityResponse)
async def update_asset_vulnerability_association(
    asset_id: UUID,
    vulnerability_id: UUID,
    association_data: AssetVulnerabilityUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AssetVulnerabilityResponse:
    """Update asset-vulnerability association.
    
    Args:
        asset_id: Asset ID
        vulnerability_id: Vulnerability ID
        association_data: Association update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        AssetVulnerabilityResponse: Updated association
        
    Raises:
        HTTPException: If association not found
    """
    service = AssetService(db)
    association = await service.update_asset_vulnerability_association(
        asset_id, vulnerability_id, association_data
    )
    
    if not association:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset-vulnerability association not found"
        )
    
    return AssetVulnerabilityResponse.model_validate(association)


@router.delete("/{asset_id}/vulnerabilities/{vulnerability_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_vulnerability_association(
    asset_id: UUID,
    vulnerability_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> None:
    """Delete asset-vulnerability association.
    
    Args:
        asset_id: Asset ID
        vulnerability_id: Vulnerability ID
        db: Database session
        current_user: Current authenticated user
        
    Raises:
        HTTPException: If association not found
    """
    service = AssetService(db)
    success = await service.delete_asset_vulnerability_association(asset_id, vulnerability_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset-vulnerability association not found"
        )
