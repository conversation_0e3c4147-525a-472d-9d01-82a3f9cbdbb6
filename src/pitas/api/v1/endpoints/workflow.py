"""PTES workflow management API endpoints."""

from typing import List, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_db, get_current_user
from pitas.core.workflow import PTESPhase
from pitas.db.models.user import User
from pitas.schemas.project import (
    WorkflowAdvanceRequest,
    WorkflowAdvanceResponse,
    PhaseTransitionResponse,
    ProjectDeliverableCreate,
    ProjectDeliverableUpdate,
    ProjectDeliverableResponse
)
from pitas.services.workflow import WorkflowService

router = APIRouter()
workflow_service = WorkflowService()


@router.post("/{project_id}/advance", response_model=WorkflowAdvanceResponse)
async def advance_project_phase(
    project_id: UUID,
    advance_request: WorkflowAdvanceRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> WorkflowAdvanceResponse:
    """Advance project to the next PTES phase.
    
    Args:
        project_id: Project ID
        advance_request: Phase advance request data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Workflow advance response
    """
    try:
        result = await workflow_service.advance_project_phase(
            db, project_id, advance_request, current_user.id
        )
        
        if result.success:
            await db.commit()
        else:
            await db.rollback()
        
        return result
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to advance project phase: {str(e)}"
        )


@router.get("/{project_id}/phases/{phase}/requirements")
async def get_phase_requirements(
    project_id: UUID,
    phase: PTESPhase,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get requirements for a specific PTES phase.
    
    Args:
        project_id: Project ID
        phase: PTES phase
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Phase requirements
    """
    # Verify project exists
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    requirements = await workflow_service.get_phase_requirements(phase)
    return requirements


@router.post("/{project_id}/phases/{phase}/deliverables", response_model=List[ProjectDeliverableResponse])
async def create_phase_deliverables(
    project_id: UUID,
    phase: PTESPhase,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[ProjectDeliverableResponse]:
    """Create default deliverables for a project phase.
    
    Args:
        project_id: Project ID
        phase: PTES phase
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created deliverables
    """
    # Verify project exists
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        deliverables = await workflow_service.create_phase_deliverables(db, project_id, phase)
        await db.commit()
        
        return [
            ProjectDeliverableResponse.model_validate(deliverable)
            for deliverable in deliverables
        ]
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create phase deliverables: {str(e)}"
        )


@router.post("/{project_id}/deliverables", response_model=ProjectDeliverableResponse)
async def create_deliverable(
    project_id: UUID,
    deliverable_data: ProjectDeliverableCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectDeliverableResponse:
    """Create a custom project deliverable.
    
    Args:
        project_id: Project ID
        deliverable_data: Deliverable creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created deliverable
    """
    # Verify project exists
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Ensure project_id matches
    deliverable_data.project_id = project_id
    
    try:
        from pitas.db.models.project import ProjectDeliverable
        from uuid import uuid4
        
        deliverable = ProjectDeliverable(
            id=uuid4(),
            **deliverable_data.dict()
        )
        
        db.add(deliverable)
        await db.flush()
        await db.refresh(deliverable)
        await db.commit()
        
        return ProjectDeliverableResponse.model_validate(deliverable)
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create deliverable: {str(e)}"
        )


@router.put("/deliverables/{deliverable_id}", response_model=ProjectDeliverableResponse)
async def update_deliverable(
    deliverable_id: UUID,
    deliverable_update: ProjectDeliverableUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> ProjectDeliverableResponse:
    """Update a project deliverable.
    
    Args:
        deliverable_id: Deliverable ID
        deliverable_update: Deliverable update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated deliverable
    """
    try:
        deliverable = await workflow_service.update_deliverable_status(
            db, deliverable_id, deliverable_update, current_user.id
        )
        
        if not deliverable:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deliverable not found"
            )
        
        await db.commit()
        return ProjectDeliverableResponse.model_validate(deliverable)
    
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update deliverable: {str(e)}"
        )


@router.get("/{project_id}/timeline")
async def get_project_timeline(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """Get project timeline with phase transitions and milestones.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Project timeline events
    """
    # Verify project exists
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        timeline = await workflow_service.get_project_timeline(db, project_id)
        return timeline
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load project timeline: {str(e)}"
        )


@router.get("/{project_id}/progress")
async def get_project_progress(
    project_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get detailed project progress metrics.
    
    Args:
        project_id: Project ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Project progress metrics
    """
    # Verify project exists
    project = await workflow_service.get(db, project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    try:
        progress = await workflow_service.get_project_progress_metrics(db, project_id)
        return progress
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load project progress: {str(e)}"
        )


@router.get("/phases", response_model=List[Dict[str, Any]])
async def list_ptes_phases(
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """List all PTES phases with their requirements.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        List of PTES phases and requirements
    """
    phases = []
    for phase in PTESPhase:
        requirements = await workflow_service.get_phase_requirements(phase)
        phases.append({
            "phase": phase,
            "name": phase.replace("_", " ").title(),
            "requirements": requirements
        })
    
    return phases


@router.get("/phases/{phase}")
async def get_phase_details(
    phase: PTESPhase,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get detailed information about a specific PTES phase.
    
    Args:
        phase: PTES phase
        current_user: Current authenticated user
        
    Returns:
        Phase details and requirements
    """
    requirements = await workflow_service.get_phase_requirements(phase)
    
    return {
        "phase": phase,
        "name": phase.replace("_", " ").title(),
        "requirements": requirements,
        "description": f"Detailed information about the {phase} phase of PTES methodology"
    }
