"""Health check endpoints."""

from datetime import datetime
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from pitas.core.config import settings
from pitas.db.session import get_db
from pitas.schemas.base import HealthCheck

router = APIRouter()


@router.get("/health", response_model=HealthCheck)
async def health_check() -> HealthCheck:
    """Basic health check endpoint.

    Returns:
        HealthCheck: Service health status
    """
    return HealthCheck(
        status="healthy",
        timestamp=datetime.utcnow(),
        version=settings.project_version,
        environment=settings.environment,
    )


@router.get("/health/detailed", response_model=dict)
async def detailed_health_check(
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Detailed health check with dependency status.

    Args:
        db: Database session

    Returns:
        dict: Detailed health status
    """
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": settings.project_version,
        "environment": settings.environment,
        "dependencies": {}
    }

    # Check database connectivity
    try:
        await db.execute(text("SELECT 1"))
        health_status["dependencies"]["database"] = {
            "status": "healthy",
            "response_time_ms": 0  # TODO: Measure actual response time
        }
    except Exception as e:
        health_status["status"] = "unhealthy"
        health_status["dependencies"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }

    # TODO: Add checks for other dependencies (Redis, Neo4j, InfluxDB, etc.)

    return health_status


@router.get("/ready")
async def readiness_check(
    db: AsyncSession = Depends(get_db),
) -> dict:
    """Kubernetes readiness probe endpoint.

    Args:
        db: Database session

    Returns:
        dict: Readiness status
    """
    try:
        await db.execute(text("SELECT 1"))
        return {"status": "ready"}
    except Exception:
        return {"status": "not ready"}


@router.get("/live")
async def liveness_check() -> dict:
    """Kubernetes liveness probe endpoint.

    Returns:
        dict: Liveness status
    """
    return {"status": "alive"}