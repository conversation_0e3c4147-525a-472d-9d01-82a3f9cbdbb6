"""Vulnerability management API endpoints."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.db.models.vulnerability import (
    Vulnerability, VulnerabilityStatus, VulnerabilitySeverity
)
from pitas.db.models.asset import (
    Asset, AssetVulnerability
)
from pitas.schemas.vulnerability import (
    VulnerabilityCreate, VulnerabilityUpdate, VulnerabilityResponse,
    VulnerabilityWithAssets, VulnerabilityDashboard, VulnerabilitySearchFilters,
    VulnerabilitySearchResponse
)
from pitas.services.vulnerability import VulnerabilityService
from pitas.db.neo4j import vulnerability_graph_service
from pitas.db.influxdb import vulnerability_metrics_service

router = APIRouter()


@router.post("/", response_model=VulnerabilityResponse, status_code=status.HTTP_201_CREATED)
async def create_vulnerability(
    vulnerability_data: VulnerabilityCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> VulnerabilityResponse:
    """Create a new vulnerability.
    
    Args:
        vulnerability_data: Vulnerability creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        VulnerabilityResponse: Created vulnerability
    """
    service = VulnerabilityService(db)
    vulnerability = await service.create_vulnerability(vulnerability_data)
    
    # Create graph node for vulnerability correlation
    try:
        await vulnerability_graph_service.create_vulnerability_node(
            vulnerability_id=str(vulnerability.id),
            cve_id=vulnerability.cve_id,
            cvss_score=float(vulnerability.cvss_score) if vulnerability.cvss_score else None,
            severity=vulnerability.severity.value,
            discovery_date=vulnerability.discovery_date.isoformat(),
        )
    except Exception as e:
        # Log error but don't fail the request
        print(f"Failed to create graph node: {e}")
    
    # Record discovery metric
    try:
        vulnerability_metrics_service.write_vulnerability_discovery_metric(
            vulnerability_id=str(vulnerability.id),
            severity=vulnerability.severity.value,
            cvss_score=float(vulnerability.cvss_score) if vulnerability.cvss_score else None,
            source=vulnerability.source,
            timestamp=vulnerability.discovery_date,
        )
    except Exception as e:
        print(f"Failed to record discovery metric: {e}")
    
    return VulnerabilityResponse.model_validate(vulnerability)


@router.get("/", response_model=VulnerabilitySearchResponse)
async def list_vulnerabilities(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    severity: Optional[List[VulnerabilitySeverity]] = Query(None),
    status: Optional[List[VulnerabilityStatus]] = Query(None),
    cvss_score_min: Optional[float] = Query(None, ge=0.0, le=10.0),
    cvss_score_max: Optional[float] = Query(None, ge=0.0, le=10.0),
    discovery_date_from: Optional[datetime] = Query(None),
    discovery_date_to: Optional[datetime] = Query(None),
    tags: Optional[List[str]] = Query(None),
    source: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> VulnerabilitySearchResponse:
    """List vulnerabilities with filtering and pagination.
    
    Args:
        page: Page number
        page_size: Items per page
        severity: Filter by severity levels
        status: Filter by status values
        cvss_score_min: Minimum CVSS score
        cvss_score_max: Maximum CVSS score
        discovery_date_from: Filter from discovery date
        discovery_date_to: Filter to discovery date
        tags: Filter by tags
        source: Filter by source
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        VulnerabilitySearchResponse: Paginated vulnerability list
    """
    service = VulnerabilityService(db)
    
    filters = VulnerabilitySearchFilters(
        severity=severity,
        status=status,
        cvss_score_min=cvss_score_min,
        cvss_score_max=cvss_score_max,
        discovery_date_from=discovery_date_from,
        discovery_date_to=discovery_date_to,
        tags=tags,
        source=source,
    )
    
    vulnerabilities, total_count = await service.search_vulnerabilities(
        filters=filters,
        page=page,
        page_size=page_size,
    )
    
    return VulnerabilitySearchResponse(
        vulnerabilities=[VulnerabilityResponse.model_validate(v) for v in vulnerabilities],
        total_count=total_count,
        page=page,
        page_size=page_size,
        filters_applied=filters,
    )


@router.get("/{vulnerability_id}", response_model=VulnerabilityWithAssets)
async def get_vulnerability(
    vulnerability_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> VulnerabilityWithAssets:
    """Get vulnerability by ID with associated assets.
    
    Args:
        vulnerability_id: Vulnerability ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        VulnerabilityWithAssets: Vulnerability with assets
        
    Raises:
        HTTPException: If vulnerability not found
    """
    service = VulnerabilityService(db)
    vulnerability = await service.get_vulnerability_by_id(vulnerability_id)
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    # Get associated assets
    assets = await service.get_vulnerability_assets(vulnerability_id)
    
    return VulnerabilityWithAssets(
        **vulnerability.__dict__,
        assets=assets,
    )


@router.put("/{vulnerability_id}", response_model=VulnerabilityResponse)
async def update_vulnerability(
    vulnerability_id: UUID,
    vulnerability_data: VulnerabilityUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> VulnerabilityResponse:
    """Update vulnerability.
    
    Args:
        vulnerability_id: Vulnerability ID
        vulnerability_data: Vulnerability update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        VulnerabilityResponse: Updated vulnerability
        
    Raises:
        HTTPException: If vulnerability not found
    """
    service = VulnerabilityService(db)
    vulnerability = await service.update_vulnerability(vulnerability_id, vulnerability_data)
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    # Record remediation metric if status changed to remediated
    if (vulnerability_data.status == VulnerabilityStatus.REMEDIATED and 
        vulnerability_data.remediation_date):
        try:
            # Calculate remediation time
            remediation_time = (
                vulnerability_data.remediation_date - vulnerability.discovery_date
            ).total_seconds() / 3600  # Convert to hours
            
            vulnerability_metrics_service.write_vulnerability_remediation_metric(
                vulnerability_id=str(vulnerability.id),
                remediation_time_hours=remediation_time,
                timestamp=vulnerability_data.remediation_date,
            )
        except Exception as e:
            print(f"Failed to record remediation metric: {e}")
    
    return VulnerabilityResponse.model_validate(vulnerability)


@router.delete("/{vulnerability_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_vulnerability(
    vulnerability_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> None:
    """Delete vulnerability.
    
    Args:
        vulnerability_id: Vulnerability ID
        db: Database session
        current_user: Current authenticated user
        
    Raises:
        HTTPException: If vulnerability not found
    """
    service = VulnerabilityService(db)
    success = await service.delete_vulnerability(vulnerability_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )


@router.get("/dashboard/summary", response_model=VulnerabilityDashboard)
async def get_vulnerability_dashboard(
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> VulnerabilityDashboard:
    """Get vulnerability dashboard summary.
    
    Args:
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        VulnerabilityDashboard: Dashboard summary data
    """
    service = VulnerabilityService(db)
    dashboard_data = await service.get_dashboard_summary()
    
    return VulnerabilityDashboard(**dashboard_data)


@router.get("/{vulnerability_id}/attack-paths")
async def get_vulnerability_attack_paths(
    vulnerability_id: UUID,
    max_depth: int = Query(5, ge=1, le=10, description="Maximum path depth"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
):
    """Get potential attack paths for a vulnerability.
    
    Args:
        vulnerability_id: Vulnerability ID
        max_depth: Maximum path depth to explore
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List of attack paths
    """
    # First verify vulnerability exists
    service = VulnerabilityService(db)
    vulnerability = await service.get_vulnerability_by_id(vulnerability_id)
    
    if not vulnerability:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Vulnerability not found"
        )
    
    # Get attack paths from graph database
    try:
        # Get assets affected by this vulnerability
        assets = await service.get_vulnerability_assets(vulnerability_id)
        
        attack_paths = []
        for asset in assets:
            paths = await vulnerability_graph_service.find_vulnerability_attack_paths(
                start_asset_id=str(asset.id),
                max_depth=max_depth
            )
            attack_paths.extend(paths)
        
        return {"vulnerability_id": vulnerability_id, "attack_paths": attack_paths}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve attack paths: {str(e)}"
        )
