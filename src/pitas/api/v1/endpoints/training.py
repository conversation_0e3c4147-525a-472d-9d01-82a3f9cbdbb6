"""Training and competency management API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id, get_db
from pitas.db.models.training import CompetencyLevel, TrainingStatus, CertificationStatus
from pitas.schemas.training import (
    # Competency schemas
    CompetencyFramework,
    CompetencyFrameworkCreate,
    CompetencyFrameworkUpdate,
    Competency,
    CompetencyCreate,
    CompetencyUpdate,
    SkillAssessment,
    SkillAssessmentCreate,
    SkillAssessmentUpdate,
    SkillGapAnalysis,
    
    # Training schemas
    TrainingCourse,
    TrainingCourseCreate,
    TrainingCourseUpdate,
    LearningPath,
    LearningPathCreate,
    LearningPathUpdate,
    TrainingEnrollment,
    TrainingEnrollmentCreate,
    TrainingEnrollmentUpdate,
    LearningProgress,
    
    # Certification schemas
    Certification,
    CertificationCreate,
    CertificationUpdate,
    CertificationAchievement,
    CertificationAchievementCreate,
    CertificationAchievementUpdate,
    CertificationPathway,
    
    # CTF schemas
    CTFChallenge,
    CTFChallengeCreate,
    CTFChallengeUpdate,
    CTFSubmission,
    CTFSubmissionCreate,
    CTFLeaderboard,
    
    # Mentorship schemas
    MentorshipPair,
    MentorshipPairCreate,
    MentorshipPairUpdate,
    MentorshipSession,
    MentorshipSessionCreate,
    MentorshipSessionUpdate,
)
from pitas.services.training import (
    CompetencyService,
    TrainingService,
    CertificationService,
    CTFService,
    MentorshipService,
)

router = APIRouter()


# Competency Framework Endpoints
@router.post("/frameworks", response_model=CompetencyFramework, status_code=status.HTTP_201_CREATED)
async def create_competency_framework(
    framework_data: CompetencyFrameworkCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CompetencyFramework:
    """Create a new competency framework."""
    service = CompetencyService(db)
    return await service.create_framework(framework_data)


@router.get("/frameworks", response_model=List[CompetencyFramework])
async def list_competency_frameworks(
    category: Optional[str] = Query(None, description="Filter by category"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[CompetencyFramework]:
    """List competency frameworks."""
    service = CompetencyService(db)
    if category:
        return await service.get_frameworks_by_category(category)
    return await service.get_all()


@router.get("/frameworks/{framework_id}", response_model=CompetencyFramework)
async def get_competency_framework(
    framework_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CompetencyFramework:
    """Get a specific competency framework."""
    service = CompetencyService(db)
    framework = await service.get(framework_id)
    if not framework:
        raise HTTPException(status_code=404, detail="Competency framework not found")
    return framework


@router.put("/frameworks/{framework_id}", response_model=CompetencyFramework)
async def update_competency_framework(
    framework_id: UUID,
    framework_data: CompetencyFrameworkUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CompetencyFramework:
    """Update a competency framework."""
    service = CompetencyService(db)
    framework = await service.update(framework_id, framework_data)
    if not framework:
        raise HTTPException(status_code=404, detail="Competency framework not found")
    return framework


@router.delete("/frameworks/{framework_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_competency_framework(
    framework_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> None:
    """Delete a competency framework."""
    service = CompetencyService(db)
    success = await service.delete(framework_id)
    if not success:
        raise HTTPException(status_code=404, detail="Competency framework not found")


# Competency Endpoints
@router.post("/competencies", response_model=Competency, status_code=status.HTTP_201_CREATED)
async def create_competency(
    competency_data: CompetencyCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> Competency:
    """Create a new competency."""
    service = CompetencyService(db)
    return await service.create_competency(competency_data)


# Skill Assessment Endpoints
@router.post("/assessments", response_model=SkillAssessment, status_code=status.HTTP_201_CREATED)
async def create_skill_assessment(
    assessment_data: SkillAssessmentCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> SkillAssessment:
    """Create a new skill assessment."""
    service = CompetencyService(db)
    return await service.create_skill_assessment(assessment_data)


@router.get("/assessments/users/{user_id}", response_model=List[SkillAssessment])
async def get_user_assessments(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[SkillAssessment]:
    """Get all skill assessments for a user."""
    service = CompetencyService(db)
    return await service.get_user_assessments(user_id)


@router.get("/assessments/users/{user_id}/gap-analysis", response_model=SkillGapAnalysis)
async def get_skill_gap_analysis(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> SkillGapAnalysis:
    """Get skill gap analysis for a user."""
    service = CompetencyService(db)
    return await service.assess_skill_gaps(user_id)


# Training Course Endpoints
@router.post("/courses", response_model=TrainingCourse, status_code=status.HTTP_201_CREATED)
async def create_training_course(
    course_data: TrainingCourseCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> TrainingCourse:
    """Create a new training course."""
    service = TrainingService(db)
    return await service.create_course(course_data)


@router.get("/courses", response_model=List[TrainingCourse])
async def list_training_courses(
    provider: Optional[str] = Query(None, description="Filter by provider"),
    difficulty: Optional[CompetencyLevel] = Query(None, description="Filter by difficulty"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[TrainingCourse]:
    """List training courses."""
    service = TrainingService(db)
    if provider:
        return await service.get_courses_by_provider(provider)
    return await service.get_all()


@router.get("/courses/{course_id}", response_model=TrainingCourse)
async def get_training_course(
    course_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> TrainingCourse:
    """Get a specific training course."""
    service = TrainingService(db)
    course = await service.get(course_id)
    if not course:
        raise HTTPException(status_code=404, detail="Training course not found")
    return course


@router.put("/courses/{course_id}", response_model=TrainingCourse)
async def update_training_course(
    course_id: UUID,
    course_data: TrainingCourseUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> TrainingCourse:
    """Update a training course."""
    service = TrainingService(db)
    course = await service.update(course_id, course_data)
    if not course:
        raise HTTPException(status_code=404, detail="Training course not found")
    return course


@router.delete("/courses/{course_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_training_course(
    course_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> None:
    """Delete a training course."""
    service = TrainingService(db)
    success = await service.delete(course_id)
    if not success:
        raise HTTPException(status_code=404, detail="Training course not found")


# Training Enrollment Endpoints
@router.post("/enrollments", response_model=TrainingEnrollment, status_code=status.HTTP_201_CREATED)
async def enroll_in_course(
    enrollment_data: TrainingEnrollmentCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> TrainingEnrollment:
    """Enroll a user in a training course."""
    service = TrainingService(db)
    return await service.enroll_user(enrollment_data)


@router.put("/enrollments/{enrollment_id}", response_model=TrainingEnrollment)
async def update_training_progress(
    enrollment_id: UUID,
    progress_data: TrainingEnrollmentUpdate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> TrainingEnrollment:
    """Update training progress for an enrollment."""
    service = TrainingService(db)
    try:
        return await service.update_progress(enrollment_id, progress_data)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/enrollments/users/{user_id}/courses/{course_id}/progress", response_model=LearningProgress)
async def get_learning_progress(
    user_id: UUID,
    course_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> LearningProgress:
    """Get detailed learning progress for a user and course."""
    service = TrainingService(db)
    try:
        return await service.get_learning_progress(user_id, course_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


# Learning Path Endpoints
@router.post("/learning-paths", response_model=LearningPath, status_code=status.HTTP_201_CREATED)
async def create_learning_path(
    path_data: LearningPathCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> LearningPath:
    """Create a personalized learning path."""
    service = TrainingService(db)
    return await service.create_learning_path(path_data)


@router.get("/learning-paths/users/{user_id}", response_model=List[LearningPath])
async def get_user_learning_paths(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[LearningPath]:
    """Get all learning paths for a user."""
    service = TrainingService(db)
    return await service.get_user_learning_paths(user_id)


# Certification Endpoints
@router.post("/certifications", response_model=Certification, status_code=status.HTTP_201_CREATED)
async def create_certification(
    cert_data: CertificationCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> Certification:
    """Create a new certification."""
    service = CertificationService(db)
    return await service.create_certification(cert_data)


@router.get("/certifications", response_model=List[Certification])
async def list_certifications(
    level: Optional[CompetencyLevel] = Query(None, description="Filter by level"),
    provider: Optional[str] = Query(None, description="Filter by provider"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[Certification]:
    """List certifications."""
    service = CertificationService(db)
    return await service.get_all()


@router.get("/certifications/{cert_id}", response_model=Certification)
async def get_certification(
    cert_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> Certification:
    """Get a specific certification."""
    service = CertificationService(db)
    cert = await service.get(cert_id)
    if not cert:
        raise HTTPException(status_code=404, detail="Certification not found")
    return cert


@router.post("/certifications/achievements", response_model=CertificationAchievement, status_code=status.HTTP_201_CREATED)
async def record_certification_achievement(
    achievement_data: CertificationAchievementCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CertificationAchievement:
    """Record a certification achievement."""
    service = CertificationService(db)
    return await service.record_achievement(achievement_data)


@router.get("/certifications/achievements/users/{user_id}", response_model=List[CertificationAchievement])
async def get_user_certifications(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[CertificationAchievement]:
    """Get all certifications for a user."""
    service = CertificationService(db)
    return await service.get_user_certifications(user_id)


@router.get("/certifications/expiring", response_model=List[CertificationAchievement])
async def get_expiring_certifications(
    days_ahead: int = Query(90, description="Days ahead to check for expiration"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[CertificationAchievement]:
    """Get certifications expiring within specified days."""
    service = CertificationService(db)
    return await service.get_expiring_certifications(days_ahead)


@router.get("/certifications/pathways/users/{user_id}", response_model=CertificationPathway)
async def get_certification_pathway(
    user_id: UUID,
    target_level: CompetencyLevel = Query(..., description="Target competency level"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CertificationPathway:
    """Generate a certification pathway for a user."""
    service = CertificationService(db)
    return await service.generate_certification_pathway(user_id, target_level)


# CTF Endpoints
@router.post("/ctf/challenges", response_model=CTFChallenge, status_code=status.HTTP_201_CREATED)
async def create_ctf_challenge(
    challenge_data: CTFChallengeCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CTFChallenge:
    """Create a new CTF challenge."""
    service = CTFService(db)
    return await service.create_challenge(challenge_data)


@router.get("/ctf/challenges", response_model=List[CTFChallenge])
async def list_ctf_challenges(
    category: Optional[str] = Query(None, description="Filter by category"),
    difficulty: Optional[CompetencyLevel] = Query(None, description="Filter by difficulty"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[CTFChallenge]:
    """List CTF challenges."""
    service = CTFService(db)
    if category:
        return await service.get_challenges_by_category(category)
    return await service.get_all()


@router.get("/ctf/challenges/{challenge_id}", response_model=CTFChallenge)
async def get_ctf_challenge(
    challenge_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CTFChallenge:
    """Get a specific CTF challenge."""
    service = CTFService(db)
    challenge = await service.get(challenge_id)
    if not challenge:
        raise HTTPException(status_code=404, detail="CTF challenge not found")
    return challenge


@router.post("/ctf/submissions", response_model=CTFSubmission, status_code=status.HTTP_201_CREATED)
async def submit_ctf_flag(
    submission_data: CTFSubmissionCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> CTFSubmission:
    """Submit a flag for a CTF challenge."""
    service = CTFService(db)
    try:
        return await service.submit_flag(submission_data)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/ctf/leaderboard", response_model=List[CTFLeaderboard])
async def get_ctf_leaderboard(
    limit: int = Query(10, description="Number of top entries to return"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[CTFLeaderboard]:
    """Get CTF leaderboard."""
    service = CTFService(db)
    return await service.get_leaderboard(limit)


@router.get("/ctf/submissions/users/{user_id}", response_model=List[CTFSubmission])
async def get_user_ctf_submissions(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[CTFSubmission]:
    """Get all CTF submissions for a user."""
    service = CTFService(db)
    return await service.get_user_submissions(user_id)


# Mentorship Endpoints
@router.post("/mentorship/pairs", response_model=MentorshipPair, status_code=status.HTTP_201_CREATED)
async def create_mentorship_pair(
    pair_data: MentorshipPairCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> MentorshipPair:
    """Create a new mentorship pair."""
    service = MentorshipService(db)
    try:
        return await service.create_mentorship_pair(pair_data)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/mentorship/pairs/users/{user_id}", response_model=List[MentorshipPair])
async def get_user_mentorships(
    user_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> List[MentorshipPair]:
    """Get all mentorships for a user (as mentor or mentee)."""
    service = MentorshipService(db)
    return await service.get_user_mentorships(user_id)


@router.post("/mentorship/sessions", response_model=MentorshipSession, status_code=status.HTTP_201_CREATED)
async def create_mentorship_session(
    session_data: MentorshipSessionCreate,
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> MentorshipSession:
    """Create a new mentorship session."""
    service = MentorshipService(db)
    return await service.create_session(session_data)


@router.put("/mentorship/pairs/{pair_id}/end", response_model=MentorshipPair)
async def end_mentorship(
    pair_id: UUID,
    satisfaction_rating: Optional[float] = Query(None, description="Satisfaction rating (1-5)"),
    db: AsyncSession = Depends(get_db),
    current_user_id: str = Depends(get_current_user_id),
) -> MentorshipPair:
    """End a mentorship relationship."""
    service = MentorshipService(db)
    try:
        return await service.end_mentorship(pair_id, satisfaction_rating)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
