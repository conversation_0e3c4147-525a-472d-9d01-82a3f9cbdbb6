"""Security framework integration API endpoints."""

from typing import Any, Dict, List, Optional

import structlog
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from pitas.api.deps import get_current_user
from pitas.integrations.orchestrator import security_orchestrator, EnrichedVulnerability
from pitas.integrations.mitre import MITRETechnique
from pitas.integrations.cvss import CVSSScore
from pitas.integrations.nist import NISTAssessmentResult, NISTFunction

logger = structlog.get_logger(__name__)

router = APIRouter()


class VulnerabilityInput(BaseModel):
    """Input model for vulnerability analysis."""
    
    id: str = Field(..., description="Vulnerability identifier")
    title: str = Field(..., description="Vulnerability title")
    description: str = Field(..., description="Vulnerability description")
    severity: str = Field(..., description="Original severity rating")
    cve_id: Optional[str] = Field(None, description="CVE identifier if available")
    affected_systems: List[str] = Field(default_factory=list, description="Affected systems")
    discovery_date: Optional[str] = Field(None, description="Discovery date")


class FrameworkStatsResponse(BaseModel):
    """Response model for framework statistics."""
    
    mitre_techniques_count: int = Field(..., description="Number of MITRE techniques")
    cvss_version: str = Field(..., description="CVSS version in use")
    nist_categories_count: int = Field(..., description="Number of NIST categories")
    nist_subcategories_count: int = Field(..., description="Number of NIST subcategories")
    correlation_cache_size: int = Field(..., description="Correlation cache size")
    last_updated: str = Field(..., description="Last update timestamp")


@router.post("/vulnerabilities/analyze", response_model=EnrichedVulnerability)
async def analyze_vulnerability(
    vulnerability: VulnerabilityInput,
    current_user: Any = Depends(get_current_user)
) -> EnrichedVulnerability:
    """Analyze vulnerability using multi-framework security integration.
    
    This endpoint correlates vulnerability data across:
    - MITRE ATT&CK framework for technique mapping
    - CVSS 4.0 for comprehensive scoring
    - NIST CSF 2.0 for control recommendations
    
    Args:
        vulnerability: Vulnerability data to analyze
        current_user: Current authenticated user
        
    Returns:
        EnrichedVulnerability: Vulnerability enriched with framework data
        
    Raises:
        HTTPException: If analysis fails
    """
    try:
        logger.info(
            "Starting vulnerability analysis",
            vulnerability_id=vulnerability.id,
            user_id=getattr(current_user, 'id', 'unknown')
        )
        
        # Convert input to dict for processing
        vulnerability_data = vulnerability.dict()
        
        # Perform multi-framework analysis
        enriched_vulnerability = await security_orchestrator.correlate_security_data(
            vulnerability_data
        )
        
        logger.info(
            "Completed vulnerability analysis",
            vulnerability_id=vulnerability.id,
            risk_score=enriched_vulnerability.risk_score,
            mitre_techniques=len(enriched_vulnerability.mitre_techniques),
            nist_controls=len(enriched_vulnerability.nist_controls)
        )
        
        return enriched_vulnerability
        
    except Exception as e:
        logger.error(
            "Failed to analyze vulnerability",
            vulnerability_id=vulnerability.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"Vulnerability analysis failed: {str(e)}"
        )


@router.get("/mitre/techniques", response_model=List[MITRETechnique])
async def get_mitre_techniques(
    search: Optional[str] = Query(None, description="Search query for techniques"),
    limit: int = Query(10, ge=1, le=100, description="Maximum results to return"),
    current_user: Any = Depends(get_current_user)
) -> List[MITReTechnique]:
    """Get MITRE ATT&CK techniques.
    
    Args:
        search: Optional search query
        limit: Maximum number of results
        current_user: Current authenticated user
        
    Returns:
        List[MITRETechnique]: MITRE techniques
    """
    try:
        if search:
            techniques = await security_orchestrator.mitre_client.search_techniques(
                query=search,
                limit=limit
            )
        else:
            all_techniques = await security_orchestrator.mitre_client.get_techniques()
            techniques = list(all_techniques.values())[:limit]
        
        logger.info(
            "Retrieved MITRE techniques",
            count=len(techniques),
            search_query=search
        )
        
        return techniques
        
    except Exception as e:
        logger.error(
            "Failed to retrieve MITRE techniques",
            error=str(e),
            search_query=search
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve MITRE techniques: {str(e)}"
        )


@router.get("/mitre/techniques/{technique_id}", response_model=MITRETechnique)
async def get_mitre_technique(
    technique_id: str,
    current_user: Any = Depends(get_current_user)
) -> MITReTechnique:
    """Get specific MITRE ATT&CK technique by ID.
    
    Args:
        technique_id: MITRE technique ID (e.g., T1055)
        current_user: Current authenticated user
        
    Returns:
        MITReTechnique: MITRE technique details
        
    Raises:
        HTTPException: If technique not found
    """
    try:
        technique = await security_orchestrator.mitre_client.get_technique_by_id(technique_id)
        
        if not technique:
            raise HTTPException(
                status_code=404,
                detail=f"MITRE technique {technique_id} not found"
            )
        
        logger.info(
            "Retrieved MITRE technique",
            technique_id=technique_id,
            technique_name=technique.name
        )
        
        return technique
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to retrieve MITRE technique",
            technique_id=technique_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve MITRE technique: {str(e)}"
        )


@router.post("/cvss/calculate", response_model=CVSSScore)
async def calculate_cvss_score(
    vulnerability: VulnerabilityInput,
    current_user: Any = Depends(get_current_user)
) -> CVSSScore:
    """Calculate CVSS 4.0 score for vulnerability.
    
    Args:
        vulnerability: Vulnerability data
        current_user: Current authenticated user
        
    Returns:
        CVSSScore: CVSS 4.0 score details
    """
    try:
        vulnerability_data = vulnerability.dict()
        
        cvss_score = security_orchestrator.cvss_calculator.calculate_score_with_context(
            vulnerability_data
        )
        
        logger.info(
            "Calculated CVSS score",
            vulnerability_id=vulnerability.id,
            base_score=cvss_score.base_score,
            base_severity=cvss_score.base_severity
        )
        
        return cvss_score
        
    except Exception as e:
        logger.error(
            "Failed to calculate CVSS score",
            vulnerability_id=vulnerability.id,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"CVSS calculation failed: {str(e)}"
        )


@router.post("/nist/assess/{function}", response_model=NISTAssessmentResult)
async def assess_nist_function(
    function: NISTFunction,
    assessment_data: Dict[str, Any],
    current_user: Any = Depends(get_current_user)
) -> NISTAssessmentResult:
    """Assess NIST CSF function maturity.
    
    Args:
        function: NIST CSF function to assess
        assessment_data: Assessment input data
        current_user: Current authenticated user
        
    Returns:
        NISTAssessmentResult: Assessment result
    """
    try:
        assessment_result = security_orchestrator.nist_assessor.assess_function_maturity(
            function=function,
            assessment_data=assessment_data
        )
        
        logger.info(
            "Completed NIST function assessment",
            function=function.value,
            current_tier=assessment_result.current_tier.value,
            maturity_score=assessment_result.maturity_score
        )
        
        return assessment_result
        
    except Exception as e:
        logger.error(
            "Failed to assess NIST function",
            function=function.value,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"NIST assessment failed: {str(e)}"
        )


@router.get("/nist/compliance-report")
async def generate_compliance_report(
    organization_name: str = Query(..., description="Organization name"),
    current_user: Any = Depends(get_current_user)
) -> Dict[str, Any]:
    """Generate comprehensive NIST CSF compliance report.
    
    Args:
        organization_name: Name of the organization
        current_user: Current authenticated user
        
    Returns:
        Dict[str, Any]: Compliance report
    """
    try:
        organization_data = {"name": organization_name}
        
        compliance_report = security_orchestrator.nist_assessor.generate_compliance_report(
            organization_data
        )
        
        logger.info(
            "Generated NIST compliance report",
            organization=organization_name,
            overall_maturity=compliance_report["overall_maturity"]
        )
        
        return compliance_report
        
    except Exception as e:
        logger.error(
            "Failed to generate compliance report",
            organization=organization_name,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"Compliance report generation failed: {str(e)}"
        )


@router.get("/statistics", response_model=FrameworkStatsResponse)
async def get_framework_statistics(
    current_user: Any = Depends(get_current_user)
) -> FrameworkStatsResponse:
    """Get security framework integration statistics.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        FrameworkStatsResponse: Framework statistics
    """
    try:
        stats = await security_orchestrator.get_framework_statistics()
        
        logger.info(
            "Retrieved framework statistics",
            mitre_techniques=stats["mitre_techniques_count"],
            nist_categories=stats["nist_categories_count"]
        )
        
        return FrameworkStatsResponse(**stats)
        
    except Exception as e:
        logger.error(
            "Failed to retrieve framework statistics",
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve statistics: {str(e)}"
        )


@router.post("/frameworks/refresh")
async def refresh_framework_data(
    force: bool = Query(False, description="Force refresh even if cache is valid"),
    current_user: Any = Depends(get_current_user)
) -> Dict[str, str]:
    """Refresh security framework data from external sources.
    
    Args:
        force: Force refresh even if cache is valid
        current_user: Current authenticated user
        
    Returns:
        Dict[str, str]: Refresh status
    """
    try:
        # Refresh MITRE ATT&CK data
        await security_orchestrator.mitre_client.get_techniques(force_refresh=force)
        
        logger.info(
            "Refreshed security framework data",
            force_refresh=force,
            user_id=getattr(current_user, 'id', 'unknown')
        )
        
        return {
            "status": "success",
            "message": "Security framework data refreshed successfully"
        }
        
    except Exception as e:
        logger.error(
            "Failed to refresh framework data",
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail=f"Framework data refresh failed: {str(e)}"
        )
