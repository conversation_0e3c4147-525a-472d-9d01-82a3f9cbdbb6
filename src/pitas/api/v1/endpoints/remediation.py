"""Remediation workflow API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_db, get_current_user
from pitas.core.escalation import SeverityLevel
from pitas.db.models.user import User
from pitas.db.models.remediation import RemediationStatus, TicketingSystem
from pitas.schemas.remediation import (
    RemediationCreate,
    RemediationUpdate,
    RemediationResponse,
    RemediationSummary,
    RemediationDashboard,
    RemediationListResponse,
    RemediationMetrics,
    RemediationAssignRequest,
    RemediationVerifyRequest,
    RemediationCloseRequest,
    RemediationCommentCreate,
    RemediationCommentResponse,
    TicketingIntegrationRequest,
    TicketingIntegrationResponse,
    EscalationAcknowledgeRequest
)
from pitas.services.remediation import RemediationService
from pitas.utils.ticketing import Ticketing<PERSON>anager, TicketData

router = APIRouter()
remediation_service = RemediationService()
ticketing_manager = TicketingManager()


@router.post("/", response_model=RemediationResponse, status_code=status.HTTP_201_CREATED)
async def create_remediation(
    remediation_data: RemediationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationResponse:
    """Create a new remediation.
    
    Args:
        remediation_data: Remediation creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created remediation
    """
    try:
        remediation = await remediation_service.create_remediation(
            db, remediation_data, current_user.id
        )
        await db.commit()
        return RemediationResponse.model_validate(remediation)
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create remediation: {str(e)}"
        )


@router.get("/", response_model=RemediationListResponse)
async def list_remediations(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    project_id: Optional[UUID] = Query(None, description="Filter by project ID"),
    severity: Optional[SeverityLevel] = Query(None, description="Filter by severity"),
    status: Optional[RemediationStatus] = Query(None, description="Filter by status"),
    assigned_to: Optional[UUID] = Query(None, description="Filter by assignee"),
    sla_breached: Optional[bool] = Query(None, description="Filter by SLA breach status"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationListResponse:
    """List remediations with pagination and filtering.
    
    Args:
        page: Page number
        per_page: Items per page
        project_id: Filter by project ID
        severity: Filter by severity
        status: Filter by status
        assigned_to: Filter by assignee
        sla_breached: Filter by SLA breach status
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Paginated list of remediations
    """
    skip = (page - 1) * per_page
    
    # TODO: Add filtering logic based on parameters
    remediations, total = await remediation_service.get_multi(db, skip=skip, limit=per_page)
    
    total_pages = (total + per_page - 1) // per_page
    
    remediation_summaries = [
        RemediationSummary(
            **remediation.__dict__,
            escalation_count=len(remediation.escalations) if hasattr(remediation, 'escalations') else 0
        )
        for remediation in remediations
    ]
    
    return RemediationListResponse(
        remediations=remediation_summaries,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )


@router.get("/{remediation_id}", response_model=RemediationResponse)
async def get_remediation(
    remediation_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationResponse:
    """Get a specific remediation by ID.
    
    Args:
        remediation_id: Remediation ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Remediation details
    """
    remediation = await remediation_service.get(db, remediation_id)
    if not remediation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Remediation not found"
        )
    
    return RemediationResponse.model_validate(remediation)


@router.put("/{remediation_id}", response_model=RemediationResponse)
async def update_remediation(
    remediation_id: UUID,
    remediation_update: RemediationUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationResponse:
    """Update a remediation.
    
    Args:
        remediation_id: Remediation ID
        remediation_update: Remediation update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated remediation
    """
    remediation = await remediation_service.get(db, remediation_id)
    if not remediation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Remediation not found"
        )
    
    try:
        updated_remediation = await remediation_service.update(
            db, db_obj=remediation, obj_in=remediation_update
        )
        await db.commit()
        return RemediationResponse.model_validate(updated_remediation)
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update remediation: {str(e)}"
        )


@router.post("/{remediation_id}/assign", response_model=RemediationResponse)
async def assign_remediation(
    remediation_id: UUID,
    assign_request: RemediationAssignRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationResponse:
    """Assign a remediation to a user or team.
    
    Args:
        remediation_id: Remediation ID
        assign_request: Assignment request data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated remediation
    """
    try:
        remediation = await remediation_service.assign_remediation(
            db, remediation_id, assign_request, current_user.id
        )
        
        if not remediation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Remediation not found"
            )
        
        await db.commit()
        return RemediationResponse.model_validate(remediation)
    
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to assign remediation: {str(e)}"
        )


@router.post("/{remediation_id}/verify", response_model=RemediationResponse)
async def verify_remediation(
    remediation_id: UUID,
    verify_request: RemediationVerifyRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationResponse:
    """Verify a remediation fix.
    
    Args:
        remediation_id: Remediation ID
        verify_request: Verification request data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated remediation
    """
    try:
        remediation = await remediation_service.verify_remediation(
            db, remediation_id, verify_request, current_user.id
        )
        
        if not remediation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Remediation not found"
            )
        
        await db.commit()
        return RemediationResponse.model_validate(remediation)
    
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to verify remediation: {str(e)}"
        )


@router.post("/{remediation_id}/close", response_model=RemediationResponse)
async def close_remediation(
    remediation_id: UUID,
    close_request: RemediationCloseRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationResponse:
    """Close a remediation.
    
    Args:
        remediation_id: Remediation ID
        close_request: Close request data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Updated remediation
    """
    try:
        remediation = await remediation_service.close_remediation(
            db, remediation_id, close_request, current_user.id
        )
        
        if not remediation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Remediation not found"
            )
        
        await db.commit()
        return RemediationResponse.model_validate(remediation)
    
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to close remediation: {str(e)}"
        )


@router.post("/{remediation_id}/comments", response_model=RemediationCommentResponse)
async def add_comment(
    remediation_id: UUID,
    comment_data: RemediationCommentCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationCommentResponse:
    """Add a comment to a remediation.
    
    Args:
        remediation_id: Remediation ID
        comment_data: Comment data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Created comment
    """
    # Verify remediation exists
    remediation = await remediation_service.get(db, remediation_id)
    if not remediation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Remediation not found"
        )
    
    try:
        from pitas.db.models.remediation import RemediationComment
        from uuid import uuid4
        
        comment = RemediationComment(
            id=uuid4(),
            remediation_id=remediation_id,
            author_id=current_user.id,
            content=comment_data.content,
            comment_type=comment_data.comment_type,
            is_internal=comment_data.is_internal
        )
        
        db.add(comment)
        await db.flush()
        await db.refresh(comment)
        await db.commit()
        
        return RemediationCommentResponse.model_validate(comment)
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to add comment: {str(e)}"
        )


@router.post("/{remediation_id}/tickets", response_model=TicketingIntegrationResponse)
async def create_external_ticket(
    remediation_id: UUID,
    ticket_request: TicketingIntegrationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> TicketingIntegrationResponse:
    """Create an external ticket for a remediation.
    
    Args:
        remediation_id: Remediation ID
        ticket_request: Ticket creation request
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Ticket creation response
    """
    remediation = await remediation_service.get(db, remediation_id)
    if not remediation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Remediation not found"
        )
    
    try:
        # Prepare ticket data
        ticket_data = TicketData(
            title=remediation.title,
            description=remediation.description,
            priority=str(remediation.priority),
            severity=remediation.severity,
            assignee=ticket_request.assignee,
            labels=ticket_request.labels or [],
            custom_fields=ticket_request.custom_fields or {}
        )
        
        # Create external ticket
        response = await ticketing_manager.create_ticket(
            ticket_request.ticketing_system,
            ticket_data
        )
        
        # Update remediation with ticket info if successful
        if response.success:
            remediation.external_ticket_id = response.external_ticket_id
            remediation.external_ticket_url = response.external_ticket_url
            remediation.ticketing_system = ticket_request.ticketing_system
            await db.commit()
        
        return response
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create external ticket: {str(e)}"
        )


@router.get("/metrics", response_model=RemediationMetrics)
async def get_remediation_metrics(
    project_id: Optional[UUID] = Query(None, description="Filter by project ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RemediationMetrics:
    """Get remediation metrics and statistics.
    
    Args:
        project_id: Optional project ID filter
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        Remediation metrics
    """
    try:
        metrics = await remediation_service.get_remediation_metrics(db, project_id)
        return RemediationMetrics(**metrics)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load remediation metrics: {str(e)}"
        )


@router.post("/check-sla-breaches")
async def check_sla_breaches(
    project_id: Optional[UUID] = Query(None, description="Filter by project ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> dict:
    """Check for SLA breaches and trigger escalations.
    
    Args:
        project_id: Optional project ID filter
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        SLA breach check results
    """
    try:
        breached_remediations = await remediation_service.check_sla_breaches(db, project_id)
        await db.commit()
        
        return {
            "checked_at": "2024-01-01T00:00:00Z",  # TODO: Use actual timestamp
            "breached_count": len(breached_remediations),
            "breached_remediations": [
                {
                    "id": str(r.id),
                    "title": r.title,
                    "severity": r.severity,
                    "breach_reason": r.breach_reason
                }
                for r in breached_remediations
            ]
        }
    
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check SLA breaches: {str(e)}"
        )
