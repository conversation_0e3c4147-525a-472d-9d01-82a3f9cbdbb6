"""Compliance and audit trail API endpoints for Phase 8."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.api.deps import get_current_user_id
from pitas.db.session import get_db
from pitas.db.models.compliance import ComplianceFramework, AuditEventType, ControlStatus
from pitas.schemas.compliance import (
    ComplianceMappingCreate, ComplianceMappingUpdate, ComplianceMappingResponse,
    AuditTrailCreate, AuditTrailResponse, AuditTrailSearchFilters, AuditTrailSearchResponse,
    ComplianceReportCreate, ComplianceReportResponse,
    ComplianceEvidenceCreate, ComplianceEvidenceResponse,
    ComplianceDashboard, ControlTestRequest
)
from pitas.services.compliance import ComplianceService, AuditTrailService
from pitas.core.exceptions import AppException

router = APIRouter()


@router.get("/dashboard", response_model=ComplianceDashboard)
async def get_compliance_dashboard(
    framework: Optional[ComplianceFramework] = Query(None, description="Filter by framework"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ComplianceDashboard:
    """Get compliance dashboard summary.
    
    Args:
        framework: Optional framework filter
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ComplianceDashboard: Dashboard summary data
    """
    service = ComplianceService()
    dashboard_data = await service.get_compliance_dashboard(db, framework)
    
    return ComplianceDashboard(**dashboard_data)


@router.get("/controls", response_model=List[ComplianceMappingResponse])
async def list_compliance_controls(
    framework: Optional[ComplianceFramework] = Query(None, description="Filter by framework"),
    status: Optional[ControlStatus] = Query(None, description="Filter by status"),
    overdue_only: bool = Query(False, description="Show only overdue controls"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> List[ComplianceMappingResponse]:
    """List compliance controls with filtering.
    
    Args:
        framework: Optional framework filter
        status: Optional status filter
        overdue_only: Show only overdue controls
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        List[ComplianceMappingResponse]: List of compliance controls
    """
    service = ComplianceService()
    
    if overdue_only:
        controls = await service.get_overdue_controls(db, framework)
    elif framework:
        controls = await service.get_by_framework(db, framework, status)
    else:
        # Get all controls with optional status filter
        controls = await service.get_all(db)
        if status:
            controls = [c for c in controls if c.implementation_status == status]
    
    return [ComplianceMappingResponse.model_validate(control) for control in controls]


@router.post("/controls", response_model=ComplianceMappingResponse, status_code=status.HTTP_201_CREATED)
async def create_compliance_control(
    control_data: ComplianceMappingCreate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ComplianceMappingResponse:
    """Create a new compliance control.
    
    Args:
        control_data: Control creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ComplianceMappingResponse: Created control
    """
    service = ComplianceService()
    
    # Check if control already exists
    existing_control = await service.get_control_by_id(
        db, control_data.framework, control_data.control_id
    )
    if existing_control:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Control {control_data.control_id} already exists for {control_data.framework.value}"
        )
    
    control = await service.create(db, control_data)
    
    # Create audit trail entry
    audit_service = AuditTrailService()
    await audit_service.create_audit_entry(
        db=db,
        event_type=AuditEventType.CONFIGURATION_CHANGE,
        event_name="compliance_control_created",
        event_description=f"Created compliance control {control_data.control_id} for {control_data.framework.value}",
        user_id=UUID(current_user),
        resource_type="compliance_mapping",
        resource_id=control.id,
        new_values=control_data.dict()
    )
    
    return ComplianceMappingResponse.model_validate(control)


@router.get("/controls/{control_id}", response_model=ComplianceMappingResponse)
async def get_compliance_control(
    control_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ComplianceMappingResponse:
    """Get compliance control by ID.
    
    Args:
        control_id: Control ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ComplianceMappingResponse: Control details
        
    Raises:
        HTTPException: If control not found
    """
    service = ComplianceService()
    control = await service.get(db, control_id)
    
    if not control:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Control not found"
        )
    
    return ComplianceMappingResponse.model_validate(control)


@router.put("/controls/{control_id}", response_model=ComplianceMappingResponse)
async def update_compliance_control(
    control_id: UUID,
    control_data: ComplianceMappingUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ComplianceMappingResponse:
    """Update compliance control.
    
    Args:
        control_id: Control ID
        control_data: Control update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ComplianceMappingResponse: Updated control
        
    Raises:
        HTTPException: If control not found
    """
    service = ComplianceService()
    
    # Get existing control for audit trail
    existing_control = await service.get(db, control_id)
    if not existing_control:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Control not found"
        )
    
    old_values = {
        "implementation_status": existing_control.implementation_status.value,
        "implementation_notes": existing_control.implementation_notes,
        "control_owner": str(existing_control.control_owner) if existing_control.control_owner else None,
        "responsible_team": existing_control.responsible_team
    }
    
    control = await service.update(db, control_id, control_data)
    
    # Create audit trail entry
    audit_service = AuditTrailService()
    await audit_service.create_audit_entry(
        db=db,
        event_type=AuditEventType.CONFIGURATION_CHANGE,
        event_name="compliance_control_updated",
        event_description=f"Updated compliance control {existing_control.control_id}",
        user_id=UUID(current_user),
        resource_type="compliance_mapping",
        resource_id=control.id,
        old_values=old_values,
        new_values=control_data.dict(exclude_unset=True)
    )
    
    return ComplianceMappingResponse.model_validate(control)


@router.post("/controls/{control_id}/test", response_model=ComplianceMappingResponse)
async def test_compliance_control(
    control_id: UUID,
    test_request: ControlTestRequest,
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> ComplianceMappingResponse:
    """Record control testing results.
    
    Args:
        control_id: Control ID
        test_request: Test results and configuration
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ComplianceMappingResponse: Updated control
        
    Raises:
        HTTPException: If control not found
    """
    service = ComplianceService()
    
    try:
        control = await service.update_control_test(
            db=db,
            control_id=control_id,
            tested_by=UUID(current_user),
            test_results=test_request.test_results,
            next_test_days=test_request.next_test_days
        )
        
        # Create audit trail entry
        audit_service = AuditTrailService()
        await audit_service.create_audit_entry(
            db=db,
            event_type=AuditEventType.COMPLIANCE_CHECK,
            event_name="control_tested",
            event_description=f"Control {control.control_id} tested - {'PASSED' if test_request.test_results.get('passed') else 'FAILED'}",
            user_id=UUID(current_user),
            resource_type="compliance_mapping",
            resource_id=control.id,
            new_values=test_request.test_results
        )
        
        return ComplianceMappingResponse.model_validate(control)
        
    except AppException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/audit-trail", response_model=AuditTrailSearchResponse)
async def search_audit_trail(
    user_id: Optional[UUID] = Query(None, description="Filter by user ID"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    resource_id: Optional[UUID] = Query(None, description="Filter by resource ID"),
    event_type: Optional[AuditEventType] = Query(None, description="Filter by event type"),
    start_date: Optional[datetime] = Query(None, description="Filter from date"),
    end_date: Optional[datetime] = Query(None, description="Filter to date"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    db: AsyncSession = Depends(get_db),
    current_user: str = Depends(get_current_user_id),
) -> AuditTrailSearchResponse:
    """Search audit trail entries.
    
    Args:
        user_id: Optional user filter
        resource_type: Optional resource type filter
        resource_id: Optional resource ID filter
        event_type: Optional event type filter
        start_date: Optional start date filter
        end_date: Optional end date filter
        limit: Maximum results
        offset: Results offset
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        AuditTrailSearchResponse: Search results
    """
    audit_service = AuditTrailService()
    
    entries = await audit_service.get_audit_trail(
        db=db,
        user_id=user_id,
        resource_type=resource_type,
        resource_id=resource_id,
        event_type=event_type,
        start_date=start_date,
        end_date=end_date,
        limit=limit,
        offset=offset
    )
    
    # Get total count for pagination (simplified for now)
    total_count = len(entries)
    
    filters = AuditTrailSearchFilters(
        user_id=user_id,
        resource_type=resource_type,
        resource_id=resource_id,
        event_type=event_type,
        start_date=start_date,
        end_date=end_date,
        limit=limit,
        offset=offset
    )
    
    return AuditTrailSearchResponse(
        entries=[AuditTrailResponse.model_validate(entry) for entry in entries],
        total_count=total_count,
        filters_applied=filters
    )
