# Kubernetes configuration for Phase 11: Performance Optimization and Scalability
apiVersion: v1
kind: Namespace
metadata:
  name: pitas-performance
  labels:
    name: pitas-performance
    phase: "11"

---
# Redis Cluster for High-Performance Caching
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: pitas-performance
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: client
        - containerPort: 16379
          name: gossip
        command:
        - redis-server
        args:
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        - --protected-mode
        - "no"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi

---
# Redis Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: pitas-performance
data:
  redis.conf: |
    bind 0.0.0.0
    port 6379
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 5000
    appendonly yes
    appendfsync everysec
    maxmemory 1gb
    maxmemory-policy allkeys-lru
    save 900 1
    save 300 10
    save 60 10000

---
# Redis Cluster Service
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster
  namespace: pitas-performance
spec:
  clusterIP: None
  selector:
    app: redis-cluster
  ports:
  - port: 6379
    targetPort: 6379
    name: client
  - port: 16379
    targetPort: 16379
    name: gossip

---
# PITAS Application Deployment with Auto-scaling
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pitas-app
  namespace: pitas-performance
  labels:
    app: pitas-app
    phase: "11"
spec:
  replicas: 3
  selector:
    matchLabels:
      app: pitas-app
  template:
    metadata:
      labels:
        app: pitas-app
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/api/v1/performance/metrics"
    spec:
      containers:
      - name: pitas
        image: pitas:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: REDIS_URL
          value: "redis://redis-cluster:6379/0"
        - name: REDIS_CLUSTER_NODES
          value: "redis-cluster-0.redis-cluster:6379,redis-cluster-1.redis-cluster:6379,redis-cluster-2.redis-cluster:6379"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: pitas-secrets
              key: database-url
        - name: PROMETHEUS_METRICS_PORT
          value: "9090"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: app-config
          mountPath: /app/config
      volumes:
      - name: app-config
        configMap:
          name: pitas-config

---
# Horizontal Pod Autoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: pitas-hpa
  namespace: pitas-performance
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: pitas-app
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: pitas_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60

---
# Service for PITAS Application
apiVersion: v1
kind: Service
metadata:
  name: pitas-service
  namespace: pitas-performance
  labels:
    app: pitas-app
spec:
  selector:
    app: pitas-app
  ports:
  - port: 80
    targetPort: 8000
    name: http
  type: ClusterIP

---
# Ingress for Load Balancing
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pitas-ingress
  namespace: pitas-performance
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - pitas.example.com
    secretName: pitas-tls
  rules:
  - host: pitas.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pitas-service
            port:
              number: 80

---
# Prometheus ServiceMonitor for Metrics Collection
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: pitas-metrics
  namespace: pitas-performance
  labels:
    app: pitas-app
spec:
  selector:
    matchLabels:
      app: pitas-app
  endpoints:
  - port: http
    path: /api/v1/performance/metrics
    interval: 30s
    scrapeTimeout: 10s

---
# Network Policy for Security
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: pitas-network-policy
  namespace: pitas-performance
spec:
  podSelector:
    matchLabels:
      app: pitas-app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: pitas-performance
    ports:
    - protocol: TCP
      port: 6379
  - to: []
    ports:
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
