version: '3.8'

services:
  # Neo4j Graph Database for vulnerability correlation
  neo4j:
    image: neo4j:5.14-community
    container_name: pitas-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/pitas_neo4j
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - pitas_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "pitas_neo4j", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # InfluxDB Time Series Database for vulnerability metrics
  influxdb:
    image: influxdb:2.7
    container_name: pitas-influxdb
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=pitas_influx_admin
      - DOCKER_INFLUXDB_INIT_ORG=pitas
      - DOCKER_INFLUXDB_INIT_BUCKET=vulnerability-metrics
      - DOCKER_INFLUXDB_INIT_RETENTION=90d
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=pitas-dev-token
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - pitas_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL (existing from previous phases)
  postgres:
    image: postgres:15-alpine
    container_name: pitas-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=pitas_db
      - POSTGRES_USER=pitas
      - POSTGRES_PASSWORD=pitas_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - pitas_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pitas -d pitas_db"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis (existing from previous phases)
  redis:
    image: redis:7-alpine
    container_name: pitas-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass pitas_redis
    volumes:
      - redis_data:/data
    networks:
      - pitas_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "pitas_redis", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  influxdb_data:
    driver: local
  influxdb_config:
    driver: local

networks:
  pitas_net:
    driver: bridge
    name: pitas_net
