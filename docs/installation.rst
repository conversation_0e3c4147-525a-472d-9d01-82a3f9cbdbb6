Installation Guide
==================

This guide will walk you through setting up the PITAS Training System on your local development environment or production server.

📋 Prerequisites
-----------------

Before installing the PITAS Training System, ensure you have the following prerequisites:

**System Requirements:**
  * Python 3.11 or higher
  * PostgreSQL 13 or higher
  * Redis 6 or higher (for caching and sessions)
  * Git (for version control)

**Recommended Tools:**
  * `<PERSON> <https://nixos.org/download.html>`_ package manager (for reproducible environments)
  * `direnv <https://direnv.net/>`_ (for automatic environment activation)
  * Docker and Docker Compose (for containerized services)

**Hardware Requirements:**
  * Minimum: 4GB RAM, 2 CPU cores, 20GB disk space
  * Recommended: 8GB RAM, 4 CPU cores, 50GB disk space

🚀 Quick Start with Nix (Recommended)
--------------------------------------

The fastest way to get started is using the Nix shell environment:

1. **Clone the Repository**

   .. code-block:: bash

      git clone https://github.com/forkrul/pitas.git
      cd pitas

2. **Enter Nix Shell**

   .. code-block:: bash

      nix-shell

   This automatically sets up:
   - Python environment with all dependencies
   - CLI tools and utilities
   - Environment variables
   - Pre-commit hooks

3. **Initialize the Environment**

   .. code-block:: bash

      make setup

4. **Start Services**

   .. code-block:: bash

      docker-compose up -d

5. **Initialize Database**

   .. code-block:: bash

      make db-init

6. **Run the Application**

   .. code-block:: bash

      make run

The application will be available at http://localhost:8000

🐳 Docker Installation
-----------------------

For a containerized setup:

1. **Clone and Build**

   .. code-block:: bash

      git clone https://github.com/forkrul/pitas.git
      cd pitas
      make docker-build

2. **Start All Services**

   .. code-block:: bash

      make docker-up

3. **Initialize Database**

   .. code-block:: bash

      docker-compose exec api alembic upgrade head

🔧 Manual Installation
-----------------------

If you prefer a manual setup without Nix:

1. **Clone Repository**

   .. code-block:: bash

      git clone https://github.com/forkrul/pitas.git
      cd pitas

2. **Create Virtual Environment**

   .. code-block:: bash

      python -m venv .venv
      source .venv/bin/activate  # On Windows: .venv\Scripts\activate

3. **Install Dependencies**

   .. code-block:: bash

      pip install -e ".[dev,docs,security]"

4. **Set Up Environment**

   .. code-block:: bash

      cp .env.example .env
      # Edit .env with your configuration

5. **Install Pre-commit Hooks**

   .. code-block:: bash

      pre-commit install

6. **Set Up Database**

   Create a PostgreSQL database and update your .env file:

   .. code-block:: bash

      createdb pitas_db
      alembic upgrade head

7. **Run the Application**

   .. code-block:: bash

      uvicorn pitas.main:app --reload

🗄️ Database Setup
------------------

**PostgreSQL Configuration:**

1. **Create Database and User**

   .. code-block:: sql

      CREATE DATABASE pitas_db;
      CREATE USER pitas_user WITH PASSWORD 'your_password';
      GRANT ALL PRIVILEGES ON DATABASE pitas_db TO pitas_user;

2. **Update Environment Variables**

   .. code-block:: bash

      # .env file
      DATABASE_URL=postgresql://pitas_user:your_password@localhost:5432/pitas_db
      POSTGRES_SERVER=localhost
      POSTGRES_USER=pitas_user
      POSTGRES_PASSWORD=your_password
      POSTGRES_DB=pitas_db

3. **Run Migrations**

   .. code-block:: bash

      alembic upgrade head

⚙️ Configuration
-----------------

**Environment Variables:**

The system uses environment variables for configuration. Key variables include:

.. code-block:: bash

   # Database
   DATABASE_URL=postgresql://user:pass@localhost:5432/pitas_db
   
   # Security
   SECRET_KEY=your-secret-key-here
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # Redis
   REDIS_URL=redis://localhost:6379/0
   
   # Application
   ENVIRONMENT=development
   DEBUG=true
   API_V1_STR=/api/v1

**Configuration Files:**

* ``.env`` - Environment variables
* ``alembic.ini`` - Database migration configuration
* ``pyproject.toml`` - Python project configuration
* ``docker-compose.yml`` - Development services

🧪 Verify Installation
-----------------------

1. **Check API Health**

   .. code-block:: bash

      curl http://localhost:8000/api/v1/health

2. **Access API Documentation**

   Open http://localhost:8000/api/v1/docs in your browser

3. **Run Tests**

   .. code-block:: bash

      make test

4. **Check Database Connection**

   .. code-block:: bash

      make db-migrate  # Should create a new migration if needed

🔍 Troubleshooting
-------------------

**Common Issues:**

1. **Database Connection Error**
   
   * Verify PostgreSQL is running
   * Check database credentials in .env
   * Ensure database exists and user has permissions

2. **Port Already in Use**
   
   * Change the port in the run command: ``uvicorn pitas.main:app --port 8001``
   * Or kill the process using the port

3. **Import Errors**
   
   * Ensure virtual environment is activated
   * Reinstall dependencies: ``pip install -e ".[dev]"``

4. **Migration Errors**
   
   * Check database connection
   * Verify alembic.ini configuration
   * Reset migrations if needed: ``make db-reset``

**Getting Help:**

* Check the `GitHub Issues <https://github.com/forkrul/pitas/issues>`_
* Review the troubleshooting section in the README
* Join our community discussions

🚀 Next Steps
--------------

After successful installation:

1. **Read the Quick Start Guide** - :doc:`quickstart`
2. **Explore the API** - :doc:`api/endpoints`
3. **Set Up Your First Training Course** - :doc:`guides/training-courses`
4. **Configure Competency Frameworks** - :doc:`guides/competency-management`

🔒 Production Deployment
------------------------

For production deployment, see:

* :doc:`deployment/configuration` - Production configuration
* :doc:`deployment/database` - Database setup and optimization
* :doc:`deployment/monitoring` - Monitoring and alerting setup

The installation is now complete! You're ready to start using the PITAS Training System.
