Career Development Services
===========================

Overview
--------

The career development services provide comprehensive business logic for managing Individual Development Plans (IDPs), development goals, activities, and career progression analytics. These services implement automated workflows, progress calculations, and integration with other PITAS components.

Service Architecture
--------------------

The career development services follow a layered architecture:

.. code-block:: text

   API Layer
   ├── CareerDevelopmentService
   ├── DevelopmentGoalService
   ├── DevelopmentActivityService
   └── CareerAnalyticsService
       │
   Database Layer
   ├── IndividualDevelopmentPlan
   ├── DevelopmentGoal
   └── DevelopmentActivity

All services inherit from ``BaseService`` and implement async/await patterns for optimal performance.

CareerDevelopmentService
------------------------

Core IDP Management
~~~~~~~~~~~~~~~~~~~

The ``CareerDevelopmentService`` handles Individual Development Plan lifecycle management.

**Class Definition:**

.. code-block:: python

   from pitas.services.base import BaseService
   from pitas.db.models.career import IndividualDevelopmentPlan
   from pitas.schemas.career import IDPCreate, IDPUpdate

   class CareerDevelopmentService(BaseService[IndividualDevelopmentPlan, IDPCreate, IDPUpdate]):
       """Service for managing Individual Development Plans."""

       def __init__(self):
           super().__init__(IndividualDevelopmentPlan)

**Key Methods:**

.. automethod:: pitas.services.career.CareerDevelopmentService.create_idp
   :noindex:

.. code-block:: python

   async def create_idp(
       self,
       db: AsyncSession,
       *,
       idp_data: IDPCreate,
       user_id: UUID
   ) -> IndividualDevelopmentPlan:
       """Create a new Individual Development Plan.
       
       Args:
           db: Database session
           idp_data: IDP creation data
           user_id: User ID for the IDP
           
       Returns:
           Created IDP instance
           
       Raises:
           ValueError: If user not found or validation fails
       """

**Usage Example:**

.. code-block:: python

   from pitas.services.career import CareerDevelopmentService
   from pitas.schemas.career import IDPCreate

   service = CareerDevelopmentService()
   
   idp_data = IDPCreate(
       title="2025 Career Development Plan",
       description="Focus on advancing to Senior Penetration Tester",
       start_date=datetime(2025, 1, 1),
       end_date=datetime(2025, 12, 31),
       current_tier="intermediate",
       target_tier="senior",
       career_track="technical_specialist"
   )
   
   idp = await service.create_idp(db, idp_data=idp_data, user_id=user_id)

IDP Retrieval and Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pitas.services.career.CareerDevelopmentService.get_user_idps
   :noindex:

.. code-block:: python

   async def get_user_idps(
       self,
       db: AsyncSession,
       user_id: UUID,
       *,
       status: Optional[IDPStatus] = None,
       include_goals: bool = False
   ) -> List[IndividualDevelopmentPlan]:
       """Get all IDPs for a user with optional filtering."""

.. automethod:: pitas.services.career.CareerDevelopmentService.get_active_idp
   :noindex:

.. code-block:: python

   async def get_active_idp(
       self,
       db: AsyncSession,
       user_id: UUID
   ) -> Optional[IndividualDevelopmentPlan]:
       """Get the active IDP for a user."""

Progress Tracking
~~~~~~~~~~~~~~~~~

.. automethod:: pitas.services.career.CareerDevelopmentService.update_progress
   :noindex:

.. code-block:: python

   async def update_progress(
       self,
       db: AsyncSession,
       idp_id: UUID,
       progress: float
   ) -> IndividualDevelopmentPlan:
       """Update IDP progress with validation."""

DevelopmentGoalService
----------------------

Goal Lifecycle Management
~~~~~~~~~~~~~~~~~~~~~~~~~

The ``DevelopmentGoalService`` manages development goals within IDPs.

**Key Methods:**

.. automethod:: pitas.services.career.DevelopmentGoalService.create_goal
   :noindex:

.. code-block:: python

   async def create_goal(
       self,
       db: AsyncSession,
       *,
       goal_data: DevelopmentGoalCreate
   ) -> DevelopmentGoal:
       """Create a new development goal with IDP validation."""

**Usage Example:**

.. code-block:: python

   from pitas.services.career import DevelopmentGoalService
   from pitas.schemas.career import DevelopmentGoalCreate

   service = DevelopmentGoalService()
   
   goal_data = DevelopmentGoalCreate(
       idp_id=idp_id,
       title="Obtain OSCP Certification",
       description="Complete Offensive Security Certified Professional",
       priority="high",
       target_date=datetime(2025, 8, 31),
       success_criteria="Pass OSCP exam with score >= 70%",
       estimated_cost=1500.0
   )
   
   goal = await service.create_goal(db, goal_data=goal_data)

Goal Progress Management
~~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pitas.services.career.DevelopmentGoalService.update_goal_progress
   :noindex:

.. code-block:: python

   async def update_goal_progress(
       self,
       db: AsyncSession,
       goal_id: UUID,
       progress: float,
       notes: Optional[str] = None
   ) -> DevelopmentGoal:
       """Update goal progress with automatic IDP progress recalculation."""

**Automated Progress Calculation:**

The service automatically recalculates IDP progress when goal progress is updated:

.. code-block:: python

   async def _update_idp_progress(self, db: AsyncSession, idp_id: UUID) -> None:
       """Update IDP progress based on goal completion."""
       goals = await self.get_idp_goals(db, idp_id)
       
       if not goals:
           return
       
       total_progress = sum(goal.progress for goal in goals)
       overall_progress = total_progress / len(goals)
       
       # Update IDP with calculated progress
       await self.career_service.update_progress(db, idp_id, overall_progress)

Goal Retrieval and Filtering
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pitas.services.career.DevelopmentGoalService.get_idp_goals
   :noindex:

.. code-block:: python

   async def get_idp_goals(
       self,
       db: AsyncSession,
       idp_id: UUID,
       *,
       status: Optional[GoalStatus] = None,
       include_activities: bool = False
   ) -> List[DevelopmentGoal]:
       """Get goals for an IDP with optional filtering and activity inclusion."""

DevelopmentActivityService
--------------------------

Activity Management
~~~~~~~~~~~~~~~~~~~

The ``DevelopmentActivityService`` handles specific learning and development activities.

**Key Methods:**

.. automethod:: pitas.services.career.DevelopmentActivityService.create_activity
   :noindex:

.. code-block:: python

   async def create_activity(
       self,
       db: AsyncSession,
       *,
       activity_data: DevelopmentActivityCreate
   ) -> DevelopmentActivity:
       """Create a new development activity with goal validation."""

**Usage Example:**

.. code-block:: python

   from pitas.services.career import DevelopmentActivityService
   from pitas.schemas.career import DevelopmentActivityCreate

   service = DevelopmentActivityService()
   
   activity_data = DevelopmentActivityCreate(
       goal_id=goal_id,
       title="Complete PWK Course",
       description="Penetration Testing with Kali Linux course",
       activity_type="training",
       planned_start_date=datetime(2025, 6, 1),
       planned_end_date=datetime(2025, 7, 31),
       estimated_hours=120.0,
       estimated_cost=800.0,
       provider="Offensive Security"
   )
   
   activity = await service.create_activity(db, activity_data=activity_data)

Activity Completion
~~~~~~~~~~~~~~~~~~~

.. automethod:: pitas.services.career.DevelopmentActivityService.complete_activity
   :noindex:

.. code-block:: python

   async def complete_activity(
       self,
       db: AsyncSession,
       activity_id: UUID,
       *,
       completion_notes: Optional[str] = None,
       outcome: Optional[str] = None,
       certificate_url: Optional[str] = None,
       actual_hours: Optional[float] = None,
       actual_cost: Optional[float] = None
   ) -> DevelopmentActivity:
       """Mark activity as completed with outcome tracking."""

**Completion Workflow:**

.. code-block:: python

   # Complete an activity with full tracking
   completed_activity = await service.complete_activity(
       db,
       activity_id,
       completion_notes="Successfully completed all lab exercises",
       outcome="Gained hands-on penetration testing experience",
       certificate_url="https://certificates.offensive-security.com/...",
       actual_hours=135.0,
       actual_cost=800.0
   )

Activity Retrieval
~~~~~~~~~~~~~~~~~~

.. automethod:: pitas.services.career.DevelopmentActivityService.get_goal_activities
   :noindex:

.. code-block:: python

   async def get_goal_activities(
       self,
       db: AsyncSession,
       goal_id: UUID,
       *,
       status: Optional[ActivityStatus] = None
   ) -> List[DevelopmentActivity]:
       """Get activities for a goal with optional status filtering."""

CareerAnalyticsService
----------------------

Comprehensive Analytics
~~~~~~~~~~~~~~~~~~~~~~~

The ``CareerAnalyticsService`` provides detailed analytics and reporting capabilities.

**Key Methods:**

.. automethod:: pitas.services.career.CareerAnalyticsService.get_career_progress_summary
   :noindex:

.. code-block:: python

   async def get_career_progress_summary(
       self,
       db: AsyncSession,
       user_id: UUID
   ) -> CareerProgressSummary:
       """Get comprehensive career progress summary for a user."""

**Usage Example:**

.. code-block:: python

   from pitas.services.career import CareerAnalyticsService

   analytics_service = CareerAnalyticsService()
   
   summary = await analytics_service.get_career_progress_summary(db, user_id)
   
   print(f"Current Tier: {summary.current_tier}")
   print(f"Target Tier: {summary.target_tier}")
   print(f"Overall Progress: {summary.overall_progress:.1%}")
   print(f"Active IDPs: {summary.active_idps}")
   print(f"Completed Goals: {summary.completed_goals}/{summary.total_goals}")

**Summary Data Structure:**

.. code-block:: python

   class CareerProgressSummary(BaseSchema):
       user_id: UUID
       current_tier: str
       target_tier: str
       career_track: Optional[str]
       active_idps: int
       completed_idps: int
       total_goals: int
       completed_goals: int
       in_progress_goals: int
       overall_progress: float
       last_review_date: Optional[datetime]
       next_review_date: Optional[datetime]
       development_budget_used: float
       development_budget_remaining: float

Advanced Analytics
~~~~~~~~~~~~~~~~~~

**Trend Analysis:**

.. code-block:: python

   async def get_progress_trends(
       self,
       db: AsyncSession,
       user_id: UUID,
       period_months: int = 12
   ) -> Dict[str, Any]:
       """Get progress trends over time."""
       
       # Implementation includes:
       # - Monthly progress tracking
       # - Goal completion rates
       # - Activity completion trends
       # - Budget utilization patterns
       # - Skill development progression

**Predictive Analytics:**

.. code-block:: python

   async def predict_completion_dates(
       self,
       db: AsyncSession,
       user_id: UUID
   ) -> Dict[str, datetime]:
       """Predict completion dates for active goals and IDPs."""
       
       # Implementation includes:
       # - Historical progress analysis
       # - Current velocity calculation
       # - Resource availability consideration
       # - Risk factor assessment

Error Handling and Validation
-----------------------------

Service-Level Validation
~~~~~~~~~~~~~~~~~~~~~~~~

All services implement comprehensive validation:

.. code-block:: python

   class CareerDevelopmentService(BaseService):
       async def create_idp(self, db: AsyncSession, *, idp_data: IDPCreate, user_id: UUID):
           # Verify user exists
           user = await self._get_user(db, user_id)
           if not user:
               raise ValueError(f"User with ID {user_id} not found")
           
           # Validate date ranges
           if idp_data.end_date <= idp_data.start_date:
               raise ValueError("End date must be after start date")
           
           # Check for overlapping active IDPs
           active_idp = await self.get_active_idp(db, user_id)
           if active_idp and idp_data.status == IDPStatus.ACTIVE:
               raise ValueError("User already has an active IDP")

Exception Handling
~~~~~~~~~~~~~~~~~~

**Custom Exceptions:**

.. code-block:: python

   class CareerDevelopmentError(Exception):
       """Base exception for career development operations."""
       pass

   class IDPNotFoundError(CareerDevelopmentError):
       """Raised when IDP is not found."""
       pass

   class GoalValidationError(CareerDevelopmentError):
       """Raised when goal validation fails."""
       pass

   class ActivityCompletionError(CareerDevelopmentError):
       """Raised when activity completion fails."""
       pass

**Error Handling Pattern:**

.. code-block:: python

   try:
       idp = await service.create_idp(db, idp_data=idp_data, user_id=user_id)
   except ValueError as e:
       logger.error(f"IDP creation failed: {e}")
       raise HTTPException(status_code=400, detail=str(e))
   except Exception as e:
       logger.error(f"Unexpected error creating IDP: {e}")
       raise HTTPException(status_code=500, detail="Internal server error")

Integration with Other Services
-------------------------------

Training System Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Career development services integrate with the training system:

.. code-block:: python

   async def link_training_to_goal(
       self,
       db: AsyncSession,
       goal_id: UUID,
       training_course_id: UUID
   ) -> DevelopmentActivity:
       """Create activity linking training course to development goal."""
       
       # Get training course details
       training_course = await self.training_service.get_course(db, training_course_id)
       
       # Create linked activity
       activity_data = DevelopmentActivityCreate(
           goal_id=goal_id,
           title=f"Complete {training_course.title}",
           description=training_course.description,
           activity_type=ActivityType.TRAINING,
           estimated_hours=training_course.duration_hours,
           estimated_cost=training_course.cost,
           provider=training_course.provider
       )
       
       return await self.create_activity(db, activity_data=activity_data)

Recognition System Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Integration with the recognition system for achievement tracking:

.. code-block:: python

   async def create_recognition_for_goal_completion(
       self,
       db: AsyncSession,
       goal: DevelopmentGoal
   ) -> None:
       """Create recognition when significant goals are completed."""
       
       if goal.status == GoalStatus.COMPLETED and goal.priority == GoalPriority.HIGH:
           recognition_data = RecognitionCreate(
               title=f"Goal Achievement: {goal.title}",
               description=f"Successfully completed development goal: {goal.description}",
               recognition_type=RecognitionType.ACHIEVEMENT,
               recipient_id=goal.idp.user_id,
               points_awarded=self._calculate_goal_points(goal),
               achievement_category=AchievementCategory.CONTINUOUS_LEARNING
           )
           
           await self.recognition_service.create_recognition(
               db, recognition_data=recognition_data, auto_approve=True
           )

Performance Optimization
------------------------

Caching Strategies
~~~~~~~~~~~~~~~~~~

**Redis Caching:**

.. code-block:: python

   from pitas.core.cache import cache_manager

   class CareerDevelopmentService(BaseService):
       @cache_manager.cached(expire=3600)  # 1 hour cache
       async def get_career_progress_summary(
           self,
           db: AsyncSession,
           user_id: UUID
       ) -> CareerProgressSummary:
           """Cached career progress summary."""
           return await self._calculate_progress_summary(db, user_id)

**Cache Invalidation:**

.. code-block:: python

   async def update_goal_progress(self, db: AsyncSession, goal_id: UUID, progress: float):
       goal = await self.update_progress(db, goal_id, progress)
       
       # Invalidate related caches
       await cache_manager.delete(f"career_summary:{goal.idp.user_id}")
       await cache_manager.delete(f"idp_progress:{goal.idp_id}")
       
       return goal

Database Query Optimization
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Efficient Queries:**

.. code-block:: python

   async def get_user_idps_with_goals(
       self,
       db: AsyncSession,
       user_id: UUID
   ) -> List[IndividualDevelopmentPlan]:
       """Optimized query with eager loading."""
       
       query = select(IndividualDevelopmentPlan).where(
           IndividualDevelopmentPlan.user_id == user_id
       ).options(
           selectinload(IndividualDevelopmentPlan.goals).selectinload(
               DevelopmentGoal.activities
           )
       ).order_by(IndividualDevelopmentPlan.created_at.desc())
       
       result = await db.execute(query)
       return list(result.scalars().all())

Testing and Quality Assurance
-----------------------------

Unit Testing
~~~~~~~~~~~~

**Service Testing Example:**

.. code-block:: python

   import pytest
   from pitas.services.career import CareerDevelopmentService
   from pitas.schemas.career import IDPCreate

   @pytest.mark.asyncio
   async def test_create_idp(db_session, test_user):
       service = CareerDevelopmentService()
       
       idp_data = IDPCreate(
           title="Test IDP",
           description="Test description",
           start_date=datetime(2025, 1, 1),
           end_date=datetime(2025, 12, 31),
           current_tier="entry",
           target_tier="intermediate"
       )
       
       idp = await service.create_idp(db_session, idp_data=idp_data, user_id=test_user.id)
       
       assert idp.title == "Test IDP"
       assert idp.user_id == test_user.id
       assert idp.overall_progress == 0.0

Integration Testing
~~~~~~~~~~~~~~~~~~~

**End-to-End Testing:**

.. code-block:: python

   @pytest.mark.asyncio
   async def test_complete_goal_workflow(db_session, test_user):
       # Create IDP
       idp = await career_service.create_idp(db_session, idp_data=idp_data, user_id=test_user.id)
       
       # Create goal
       goal = await goal_service.create_goal(db_session, goal_data=goal_data)
       
       # Create activity
       activity = await activity_service.create_activity(db_session, activity_data=activity_data)
       
       # Complete activity
       completed_activity = await activity_service.complete_activity(
           db_session, activity.id, completion_notes="Test completion"
       )
       
       # Update goal progress
       updated_goal = await goal_service.update_goal_progress(
           db_session, goal.id, 1.0, "Goal completed"
       )
       
       # Verify IDP progress updated
       updated_idp = await career_service.get(db_session, idp.id)
       assert updated_idp.overall_progress > 0.0

For API usage examples, see:
- :doc:`../api/career_endpoints`

For database schema details, see:
- :doc:`../database/career_models`
