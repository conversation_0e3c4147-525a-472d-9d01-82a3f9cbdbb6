Analytics API Reference
=======================

This document provides comprehensive API reference for the Advanced Analytics and Reporting Engine.

Authentication
--------------

All analytics API endpoints require authentication using JWT tokens:

.. code-block:: http

   Authorization: Bearer <jwt_token>

Base URL
--------

All analytics endpoints are prefixed with:

.. code-block:: text

   /api/v1/analytics

Model Management API
-------------------

Create Analytics Model
~~~~~~~~~~~~~~~~~~~~~

Create a new machine learning model.

.. code-block:: http

   POST /api/v1/analytics/models
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "name": "Vulnerability Predictor v2.0",
       "model_type": "vulnerability_prediction",
       "version": "2.0.0",
       "description": "Enhanced vulnerability prediction model",
       "hyperparameters": {
           "n_estimators": 100,
           "max_depth": 10,
           "random_state": 42
       },
       "feature_columns": [
           "asset_type",
           "severity_history",
           "patch_frequency"
       ],
       "target_column": "vulnerability_count",
       "is_active": false
   }

**Response (201 Created):**

.. code-block:: json

   {
       "id": "550e8400-e29b-41d4-a716-446655440000",
       "name": "Vulnerability Predictor v2.0",
       "model_type": "vulnerability_prediction",
       "version": "2.0.0",
       "description": "Enhanced vulnerability prediction model",
       "training_data_size": null,
       "accuracy_score": null,
       "precision_score": null,
       "recall_score": null,
       "f1_score": null,
       "is_active": false,
       "is_trained": false,
       "last_trained_at": null,
       "created_at": "2024-01-15T10:30:00Z",
       "updated_at": "2024-01-15T10:30:00Z"
   }

List Analytics Models
~~~~~~~~~~~~~~~~~~~

Retrieve a list of analytics models with optional filtering.

.. code-block:: http

   GET /api/v1/analytics/models?model_type=vulnerability_prediction&is_active=true&page=1&page_size=50

**Query Parameters:**

* ``model_type`` (optional): Filter by model type
* ``is_active`` (optional): Filter by active status
* ``page`` (optional): Page number (default: 1)
* ``page_size`` (optional): Items per page (default: 50, max: 100)

**Response (200 OK):**

.. code-block:: json

   [
       {
           "id": "550e8400-e29b-41d4-a716-446655440000",
           "name": "Vulnerability Predictor v2.0",
           "model_type": "vulnerability_prediction",
           "version": "2.0.0",
           "accuracy_score": 0.87,
           "is_active": true,
           "is_trained": true,
           "last_trained_at": "2024-01-15T12:00:00Z",
           "created_at": "2024-01-15T10:30:00Z",
           "updated_at": "2024-01-15T12:00:00Z"
       }
   ]

Get Analytics Model
~~~~~~~~~~~~~~~~~

Retrieve a specific analytics model by ID.

.. code-block:: http

   GET /api/v1/analytics/models/{model_id}

**Response (200 OK):**

.. code-block:: json

   {
       "id": "550e8400-e29b-41d4-a716-446655440000",
       "name": "Vulnerability Predictor v2.0",
       "model_type": "vulnerability_prediction",
       "version": "2.0.0",
       "description": "Enhanced vulnerability prediction model",
       "training_data_size": 10000,
       "training_duration_seconds": 1800.5,
       "accuracy_score": 0.87,
       "precision_score": 0.85,
       "recall_score": 0.89,
       "f1_score": 0.87,
       "hyperparameters": {
           "n_estimators": 100,
           "max_depth": 10,
           "random_state": 42
       },
       "feature_columns": [
           "asset_type",
           "severity_history",
           "patch_frequency"
       ],
       "target_column": "vulnerability_count",
       "is_active": true,
       "is_trained": true,
       "last_trained_at": "2024-01-15T12:00:00Z",
       "last_prediction_at": "2024-01-15T14:30:00Z",
       "model_path": "/models/vuln_predictor_v2.pkl",
       "created_at": "2024-01-15T10:30:00Z",
       "updated_at": "2024-01-15T12:00:00Z"
   }

Update Analytics Model
~~~~~~~~~~~~~~~~~~~~

Update an existing analytics model.

.. code-block:: http

   PUT /api/v1/analytics/models/{model_id}
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "name": "Vulnerability Predictor v2.1",
       "description": "Updated vulnerability prediction model",
       "is_active": true
   }

**Response (200 OK):**

.. code-block:: json

   {
       "id": "550e8400-e29b-41d4-a716-446655440000",
       "name": "Vulnerability Predictor v2.1",
       "description": "Updated vulnerability prediction model",
       "is_active": true,
       "updated_at": "2024-01-15T15:00:00Z"
   }

Delete Analytics Model
~~~~~~~~~~~~~~~~~~~~

Delete an analytics model.

.. code-block:: http

   DELETE /api/v1/analytics/models/{model_id}

**Response (204 No Content)**

Training Jobs API
----------------

Create Training Job
~~~~~~~~~~~~~~~~~~

Start a new model training job.

.. code-block:: http

   POST /api/v1/analytics/training-jobs
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "model_id": "550e8400-e29b-41d4-a716-446655440000",
       "job_name": "Vulnerability Predictor Training",
       "training_config": {
           "validation_split": 0.2,
           "epochs": 100,
           "batch_size": 32
       },
       "dataset_size": 10000,
       "validation_split": 0.2
   }

**Response (201 Created):**

.. code-block:: json

   {
       "id": "660e8400-e29b-41d4-a716-446655440001",
       "model_id": "550e8400-e29b-41d4-a716-446655440000",
       "job_name": "Vulnerability Predictor Training",
       "status": "pending",
       "training_config": {
           "validation_split": 0.2,
           "epochs": 100,
           "batch_size": 32
       },
       "dataset_size": 10000,
       "validation_split": 0.2,
       "started_at": null,
       "completed_at": null,
       "created_at": "2024-01-15T16:00:00Z",
       "updated_at": "2024-01-15T16:00:00Z"
   }

Predictions API
--------------

Create Prediction
~~~~~~~~~~~~~~~~

Make a prediction using a trained model.

.. code-block:: http

   POST /api/v1/analytics/predictions
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "model_id": "550e8400-e29b-41d4-a716-446655440000",
       "prediction_type": "vulnerability_forecast",
       "input_data": {
           "asset_type": "web_application",
           "severity_history": [2, 1, 3, 0, 1],
           "patch_frequency": 0.8,
           "system_age": 365,
           "exposure_score": 7.5
       },
       "entity_type": "asset",
       "entity_id": "770e8400-e29b-41d4-a716-446655440002"
   }

**Response (201 Created):**

.. code-block:: json

   {
       "id": "880e8400-e29b-41d4-a716-446655440003",
       "model_id": "550e8400-e29b-41d4-a716-446655440000",
       "prediction_type": "vulnerability_forecast",
       "input_data": {
           "asset_type": "web_application",
           "severity_history": [2, 1, 3, 0, 1],
           "patch_frequency": 0.8,
           "system_age": 365,
           "exposure_score": 7.5
       },
       "prediction_result": {
           "predicted_vulnerabilities": 3,
           "confidence_interval": [2, 5],
           "risk_level": "medium"
       },
       "confidence_score": 0.78,
       "entity_type": "asset",
       "entity_id": "770e8400-e29b-41d4-a716-446655440002",
       "feature_importance": {
           "exposure_score": 0.35,
           "system_age": 0.25,
           "severity_history": 0.20,
           "patch_frequency": 0.15,
           "asset_type": 0.05
       },
       "created_at": "2024-01-15T17:00:00Z",
       "updated_at": "2024-01-15T17:00:00Z"
   }

Specialized Prediction Endpoints
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Vulnerability Prediction:**

.. code-block:: http

   POST /api/v1/analytics/predict/vulnerabilities

**Threat Classification:**

.. code-block:: http

   POST /api/v1/analytics/predict/threats

**Remediation Timeline:**

.. code-block:: http

   POST /api/v1/analytics/predict/remediation

**Team Optimization:**

.. code-block:: http

   POST /api/v1/analytics/predict/team-optimization

**Risk Scoring:**

.. code-block:: http

   POST /api/v1/analytics/predict/risk-score

Reports API
----------

Generate Report
~~~~~~~~~~~~~~

Generate an analytics report.

.. code-block:: http

   POST /api/v1/analytics/reports
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "report_name": "Q4 Security Executive Summary",
       "report_type": "executive",
       "report_format": "pdf",
       "data_period_start": "2024-10-01T00:00:00Z",
       "data_period_end": "2024-12-31T23:59:59Z",
       "recipients": [
           "<EMAIL>",
           "<EMAIL>"
       ],
       "report_config": {
           "include_trends": true,
           "include_predictions": true,
           "include_recommendations": true
       }
   }

**Response (201 Created):**

.. code-block:: json

   {
       "id": "990e8400-e29b-41d4-a716-446655440004",
       "report_name": "Q4 Security Executive Summary",
       "report_type": "executive",
       "report_format": "pdf",
       "generated_by": "aa0e8400-e29b-41d4-a716-446655440005",
       "generation_time_seconds": 45.2,
       "data_period_start": "2024-10-01T00:00:00Z",
       "data_period_end": "2024-12-31T23:59:59Z",
       "report_summary": "Comprehensive security analytics report showing positive trends",
       "key_findings": [
           "Vulnerability resolution improved by 25%",
           "Team efficiency increased by 15%"
       ],
       "recommendations": [
           "Continue current practices",
           "Invest in automation"
       ],
       "recipients": [
           "<EMAIL>",
           "<EMAIL>"
       ],
       "distribution_status": "pending",
       "file_path": "/reports/q4_executive_summary.pdf",
       "file_size_bytes": 2048576,
       "created_at": "2024-01-15T18:00:00Z",
       "updated_at": "2024-01-15T18:00:00Z"
   }

Alerts API
----------

Create Alert
~~~~~~~~~~~

Create a new analytics alert.

.. code-block:: http

   POST /api/v1/analytics/alerts
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "alert_type": "security_score_low",
       "severity": "high",
       "title": "Security Score Below Threshold",
       "description": "Overall security score has dropped below 8.0",
       "trigger_condition": {
           "metric": "security_score",
           "operator": "less_than",
           "threshold": 8.0
       },
       "threshold_value": 8.0,
       "actual_value": 7.5,
       "entity_type": "system"
   }

**Response (201 Created):**

.. code-block:: json

   {
       "id": "bb0e8400-e29b-41d4-a716-446655440006",
       "alert_type": "security_score_low",
       "severity": "high",
       "title": "Security Score Below Threshold",
       "description": "Overall security score has dropped below 8.0",
       "trigger_condition": {
           "metric": "security_score",
           "operator": "less_than",
           "threshold": 8.0
       },
       "threshold_value": 8.0,
       "actual_value": 7.5,
       "entity_type": "system",
       "status": "active",
       "escalation_level": 0,
       "recommended_actions": [
           "Investigate security metrics",
           "Review recent changes"
       ],
       "created_at": "2024-01-15T19:00:00Z",
       "updated_at": "2024-01-15T19:00:00Z"
   }

Update Alert
~~~~~~~~~~~

Update an existing alert (typically to acknowledge or resolve).

.. code-block:: http

   PUT /api/v1/analytics/alerts/{alert_id}
   Content-Type: application/json

**Request Body:**

.. code-block:: json

   {
       "status": "acknowledged",
       "acknowledged_by": "cc0e8400-e29b-41d4-a716-446655440007",
       "actions_taken": [
           "Investigated root cause",
           "Applied security patches"
       ]
   }

**Response (200 OK):**

.. code-block:: json

   {
       "id": "bb0e8400-e29b-41d4-a716-446655440006",
       "status": "acknowledged",
       "acknowledged_by": "cc0e8400-e29b-41d4-a716-446655440007",
       "acknowledged_at": "2024-01-15T19:30:00Z",
       "actions_taken": [
           "Investigated root cause",
           "Applied security patches"
       ],
       "updated_at": "2024-01-15T19:30:00Z"
   }

Evaluate Alert Conditions
~~~~~~~~~~~~~~~~~~~~~~~~

Trigger evaluation of all alert conditions.

.. code-block:: http

   POST /api/v1/analytics/alerts/evaluate

**Response (200 OK):**

.. code-block:: json

   [
       {
           "id": "dd0e8400-e29b-41d4-a716-446655440008",
           "alert_type": "vulnerability_spike",
           "severity": "medium",
           "title": "Vulnerability Discovery Spike",
           "status": "active",
           "created_at": "2024-01-15T20:00:00Z"
       }
   ]

Get Alert Correlations
~~~~~~~~~~~~~~~~~~~~~

Retrieve correlated alerts to reduce noise.

.. code-block:: http

   GET /api/v1/analytics/alerts/correlations

**Response (200 OK):**

.. code-block:: json

   [
       {
           "correlation_id": "corr_vuln_spike_1642276800",
           "alert_count": 3,
           "alert_type": "vulnerability_spike",
           "entity_type": "system",
           "severity": "high",
           "first_occurrence": "2024-01-15T19:00:00Z",
           "last_occurrence": "2024-01-15T20:00:00Z",
           "alert_ids": [
               "dd0e8400-e29b-41d4-a716-446655440008",
               "ee0e8400-e29b-41d4-a716-446655440009",
               "ff0e8400-e29b-41d4-a716-44665544000a"
           ]
       }
   ]

Dashboards API
--------------

Executive Dashboard
~~~~~~~~~~~~~~~~~

Get executive-level dashboard data.

.. code-block:: http

   GET /api/v1/analytics/dashboards/executive

**Response (200 OK):**

.. code-block:: json

   {
       "dashboard_type": "executive",
       "generated_at": "2024-01-15T21:00:00Z",
       "widgets": {
           "security_overview": {
               "widget_type": "security_overview",
               "data": {
                   "total_vulnerabilities": 156,
                   "vulnerabilities_by_severity": {
                       "critical": 3,
                       "high": 12,
                       "medium": 28,
                       "low": 113
                   },
                   "total_assets": 245,
                   "security_score": 8.7,
                   "trend": "improving"
               }
           },
           "risk_metrics": {
               "widget_type": "risk_metrics",
               "data": {
                   "overall_risk_score": 3.2,
                   "risk_level": "Low",
                   "risk_trend": "decreasing"
               }
           }
       },
       "kpis": {
           "security_score": 8.7,
           "compliance_score": 9.2,
           "risk_level": "Low",
           "team_efficiency": "85%",
           "budget_utilization": "78%"
       },
       "alerts": [
           {
               "id": "bb0e8400-e29b-41d4-a716-446655440006",
               "title": "Security Score Below Threshold",
               "severity": "high",
               "created_at": "2024-01-15T19:00:00Z"
           }
       ],
       "recommendations": [
           "Continue investment in automation tools",
           "Expand cloud security capabilities"
       ]
   }

Technical Dashboard
~~~~~~~~~~~~~~~~~

Get technical dashboard data.

.. code-block:: http

   GET /api/v1/analytics/dashboards/technical

**Response (200 OK):**

.. code-block:: json

   {
       "dashboard_type": "technical",
       "generated_at": "2024-01-15T21:00:00Z",
       "widgets": {
           "vulnerability_trends": {
               "widget_type": "vulnerability_trends",
               "data": {
                   "trend_direction": "decreasing",
                   "discovery_rate": "8.7 per day",
                   "resolution_rate": "14.1 per day"
               },
               "chart_data": {
                   "timeline": [
                       {"date": "2024-01-01", "discovered": 15, "resolved": 12},
                       {"date": "2024-01-02", "discovered": 8, "resolved": 18}
                   ]
               }
           },
           "threat_intelligence": {
               "widget_type": "threat_intelligence",
               "data": {
                   "threat_level": "Medium",
                   "active_campaigns": 3,
                   "new_iocs": 47
               }
           }
       },
       "metrics": {
           "vulnerabilities_discovered": 89,
           "vulnerabilities_resolved": 76,
           "mean_time_to_resolution": "4.2 days",
           "false_positive_rate": "12%"
       }
   }

Operational Dashboard
~~~~~~~~~~~~~~~~~~~

Get operational dashboard data.

.. code-block:: http

   GET /api/v1/analytics/dashboards/operational

**Response (200 OK):**

.. code-block:: json

   {
       "dashboard_type": "operational",
       "generated_at": "2024-01-15T21:00:00Z",
       "widgets": {
           "team_performance": {
               "widget_type": "team_performance",
               "data": {
                   "team_size": 12,
                   "utilization_rate": 0.78,
                   "productivity_score": 8.4
               }
           },
           "project_status": {
               "widget_type": "project_status",
               "data": {
                   "active_projects": 8,
                   "on_time_delivery": 0.87
               }
           }
       },
       "operational_kpis": {
           "team_utilization": "78%",
           "project_delivery": "87%",
           "training_completion": "94%"
       }
   }

Insights API
-----------

Get Predictive Insights
~~~~~~~~~~~~~~~~~~~~~~

Retrieve ML-powered predictive insights.

.. code-block:: http

   GET /api/v1/analytics/insights?start_date=2024-01-01&end_date=2024-01-31

**Query Parameters:**

* ``start_date`` (optional): Analysis start date (ISO 8601)
* ``end_date`` (optional): Analysis end date (ISO 8601)

**Response (200 OK):**

.. code-block:: json

   {
       "vulnerability_forecast": {
           "predicted_new_vulnerabilities": {
               "next_week": 15,
               "next_month": 60,
               "next_quarter": 180
           },
           "severity_distribution": {
               "critical": 0.05,
               "high": 0.15,
               "medium": 0.35,
               "low": 0.45
           },
           "confidence": 0.87
       },
       "threat_evolution": {
           "emerging_threats": [
               {
                   "threat_type": "AI-powered attacks",
                   "probability": 0.75,
                   "impact": "high",
                   "timeline": "6 months"
               }
           ],
           "confidence": 0.82
       },
       "remediation_estimates": {
           "current_backlog": {
               "total_vulnerabilities": 245,
               "estimated_completion": "6 weeks"
           },
           "confidence": 0.91
       },
       "resource_optimization": {
           "efficiency_gains": {
               "potential_improvement": "25%",
               "time_savings": "8 hours/week per team member"
           },
           "confidence": 0.85
       },
       "confidence_scores": {
           "vulnerability_forecast": 0.87,
           "threat_evolution": 0.82,
           "remediation_estimates": 0.91,
           "resource_optimization": 0.85
       },
       "risk_factors": [
           "Increasing vulnerability discovery rate",
           "Limited remediation resources"
       ],
       "recommendations": [
           "Increase security team capacity by 30%",
           "Implement automated vulnerability scanning"
       ],
       "generated_at": "2024-01-15T22:00:00Z",
       "valid_until": "2024-01-16T22:00:00Z"
   }

Get Analytics Summary
~~~~~~~~~~~~~~~~~~~

Get high-level analytics summary for dashboards.

.. code-block:: http

   GET /api/v1/analytics/summary

**Response (200 OK):**

.. code-block:: json

   {
       "total_models": 6,
       "active_models": 4,
       "total_predictions": 1247,
       "average_accuracy": 0.85,
       "recent_alerts": [
           {
               "id": "bb0e8400-e29b-41d4-a716-446655440006",
               "title": "Security Score Below Threshold",
               "severity": "high",
               "created_at": "2024-01-15T19:00:00Z"
           }
       ],
       "top_insights": [
           "Vulnerability discovery rate decreasing",
           "Team efficiency improving",
           "Cloud security posture strengthening"
       ],
       "performance_metrics": {
           "model_accuracy": 0.85,
           "prediction_latency": 0.15,
           "alert_response_time": 2.3
       },
       "last_updated": "2024-01-15T22:00:00Z"
   }

Error Responses
--------------

The API uses standard HTTP status codes and returns error details in JSON format:

**400 Bad Request:**

.. code-block:: json

   {
       "error": "validation_error",
       "message": "Invalid request parameters",
       "details": {
           "field": "model_type",
           "issue": "Invalid model type specified"
       }
   }

**401 Unauthorized:**

.. code-block:: json

   {
       "error": "unauthorized",
       "message": "Authentication required"
   }

**403 Forbidden:**

.. code-block:: json

   {
       "error": "forbidden",
       "message": "Insufficient permissions for this operation"
   }

**404 Not Found:**

.. code-block:: json

   {
       "error": "not_found",
       "message": "Analytics model not found"
   }

**422 Unprocessable Entity:**

.. code-block:: json

   {
       "error": "validation_error",
       "message": "Request validation failed",
       "details": [
           {
               "field": "model_type",
               "message": "Invalid model type"
           }
       ]
   }

**500 Internal Server Error:**

.. code-block:: json

   {
       "error": "internal_error",
       "message": "An internal server error occurred",
       "request_id": "req_123456789"
   }

Rate Limiting
------------

The analytics API implements rate limiting to ensure fair usage:

* **Standard endpoints**: 1000 requests per hour per user
* **Prediction endpoints**: 100 requests per hour per user
* **Report generation**: 10 requests per hour per user
* **Training jobs**: 5 requests per hour per user

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1642280400

When rate limits are exceeded, a 429 status code is returned:

.. code-block:: json

   {
       "error": "rate_limit_exceeded",
       "message": "Rate limit exceeded. Try again later.",
       "retry_after": 3600
   }
