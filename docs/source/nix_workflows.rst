================
Nix Workflows
================

Overview
========

PITAS integrates Nix with Make-based workflows to provide standardized, reproducible development processes. All development tasks are executed within the Nix environment to ensure consistency across team members and environments.

.. mermaid::

   graph TB
       A[Developer] --> B[nix-shell]
       B --> C[PITAS Environment]
       C --> D[Make Commands]
       
       D --> E[Development]
       D --> F[Testing]
       D --> G[Quality Assurance]
       D --> H[Documentation]
       D --> I[Deployment]
       
       E --> E1[make setup]
       E --> E2[make install]
       E --> E3[make run]
       
       F --> F1[make test]
       F --> F2[make test-fast]
       F --> F3[make test-integration]
       
       G --> G1[make lint]
       G --> G2[make format]
       G --> G3[make security]
       
       H --> H1[make docs]
       H --> H2[make docs-serve]
       
       I --> I1[make build]
       I --> I2[make deploy]

Development Workflows
=====================

Environment Setup
-----------------

Initial project setup workflow:

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Nix as Nix Shell
       participant Make as Makefile
       participant Pip as pip
       participant Git as Git Hooks
       
       Dev->>Nix: nix-shell
       Nix->>Dev: Environment ready
       Dev->>Make: make setup
       Make->>Pip: Install Python packages
       Make->>Git: Setup pre-commit hooks
       Git->>Dev: Hooks installed
       Pip->>Dev: Dependencies ready

Commands:

.. code-block:: bash

   # Enter Nix environment
   nix-shell
   
   # Setup development environment
   make setup
   
   # Install Python dependencies
   make install
   make install-dev

Daily Development
-----------------

Typical daily development workflow:

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Nix as Nix Shell
       participant Make as Makefile
       participant Tools as Dev Tools
       
       Dev->>Nix: nix-shell
       Dev->>Make: make run
       Make->>Tools: Start application
       
       loop Development Cycle
           Dev->>Make: make test-fast
           Make->>Tools: Run quick tests
           Dev->>Make: make lint
           Make->>Tools: Check code quality
           Dev->>Make: make format
           Make->>Tools: Format code
       end

Commands:

.. code-block:: bash

   # Start development server
   make run
   
   # Run quick tests during development
   make test-fast
   
   # Check code quality
   make lint
   
   # Format code
   make format

Testing Workflows
=================

Test Execution Pipeline
-----------------------

Comprehensive testing workflow:

.. mermaid::

   graph TD
       A[make test] --> B[Unit Tests]
       A --> C[Integration Tests]
       A --> D[Security Tests]
       
       B --> E[pytest]
       C --> F[API Tests]
       C --> G[Database Tests]
       D --> H[Bandit Scan]
       D --> I[Semgrep Analysis]
       
       E --> J[Test Results]
       F --> J
       G --> J
       H --> K[Security Report]
       I --> K
       
       J --> L[Coverage Report]
       K --> L

Test Commands
-------------

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Command
     - Purpose
   * - ``make test``
     - Run complete test suite
   * - ``make test-fast``
     - Run quick unit tests only
   * - ``make test-integration``
     - Run integration tests
   * - ``make test-security``
     - Run security scans
   * - ``make test-coverage``
     - Generate coverage reports

Test Environment Setup
----------------------

.. code-block:: bash

   # Setup test environment
   nix-shell --run "make setup"
   
   # Run tests with coverage
   nix-shell --run "make test-coverage"
   
   # View coverage report
   nix-shell --run "make coverage-report"

Code Quality Workflows
======================

Quality Assurance Pipeline
---------------------------

.. mermaid::

   graph LR
       A[Code Changes] --> B[Pre-commit Hooks]
       B --> C[Linting]
       C --> D[Formatting]
       D --> E[Type Checking]
       E --> F[Security Scanning]
       F --> G[Commit]
       
       C --> C1[Ruff]
       D --> D1[Black]
       E --> E1[MyPy]
       F --> F1[Bandit]
       F --> F2[Semgrep]

Quality Commands
----------------

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Command
     - Purpose
   * - ``make lint``
     - Run all linting checks
   * - ``make format``
     - Format code with Black and Ruff
   * - ``make type-check``
     - Run MyPy type checking
   * - ``make security``
     - Run security scans
   * - ``make pre-commit``
     - Setup pre-commit hooks

Pre-commit Integration
----------------------

.. code-block:: bash

   # Install pre-commit hooks
   nix-shell --run "make pre-commit"
   
   # Run pre-commit on all files
   nix-shell --run "pre-commit run --all-files"
   
   # Update pre-commit hooks
   nix-shell --run "pre-commit autoupdate"

Documentation Workflows
=======================

Documentation Generation
-------------------------

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Nix as Nix Shell
       participant Sphinx as Sphinx
       participant Mermaid as Mermaid
       participant Browser as Browser
       
       Dev->>Nix: nix-shell
       Dev->>Sphinx: make docs
       Sphinx->>Mermaid: Render diagrams
       Mermaid->>Sphinx: Diagrams ready
       Sphinx->>Dev: Documentation built
       Dev->>Browser: make docs-serve
       Browser->>Dev: Documentation served

Documentation Commands
----------------------

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Command
     - Purpose
   * - ``make docs``
     - Build Sphinx documentation
   * - ``make docs-serve``
     - Serve documentation locally
   * - ``make docs-clean``
     - Clean documentation build
   * - ``make docs-watch``
     - Watch and rebuild docs

Documentation Workflow
----------------------

.. code-block:: bash

   # Build documentation
   nix-shell --run "make docs"
   
   # Serve documentation locally
   nix-shell --run "make docs-serve"
   
   # Open browser to http://localhost:8080

Database Workflows
==================

Database Management
-------------------

.. mermaid::

   graph TD
       A[Database Operations] --> B[Initialization]
       A --> C[Migrations]
       A --> D[Seeding]
       A --> E[Backup/Restore]
       
       B --> B1[make db-init]
       C --> C1[make db-migrate]
       C --> C2[make db-upgrade]
       C --> C3[make db-downgrade]
       D --> D1[make db-seed]
       E --> E1[make db-backup]
       E --> E2[make db-restore]

Database Commands
-----------------

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Command
     - Purpose
   * - ``make db-init``
     - Initialize database schema
   * - ``make db-migrate``
     - Create new migration
   * - ``make db-upgrade``
     - Apply migrations
   * - ``make db-downgrade``
     - Rollback migrations
   * - ``make db-reset``
     - Reset database completely

Database Workflow
-----------------

.. code-block:: bash

   # Initialize database
   nix-shell --run "make db-init"
   
   # Create migration
   nix-shell --run "make db-migrate"
   
   # Apply migrations
   nix-shell --run "make db-upgrade"

Deployment Workflows
====================

Build and Deploy Pipeline
--------------------------

.. mermaid::

   graph TD
       A[Source Code] --> B[Nix Build]
       B --> C[Tests Pass?]
       C -->|Yes| D[Security Scan]
       C -->|No| E[Fix Issues]
       E --> A
       D --> F[Build Container]
       F --> G[Deploy to Staging]
       G --> H[Integration Tests]
       H --> I[Deploy to Production]

Deployment Commands
-------------------

.. list-table::
   :header-rows: 1
   :widths: 30 70

   * - Command
     - Purpose
   * - ``make build``
     - Build application
   * - ``make build-container``
     - Build Docker container
   * - ``make deploy-staging``
     - Deploy to staging
   * - ``make deploy-prod``
     - Deploy to production
   * - ``make rollback``
     - Rollback deployment

Container Workflows
===================

Container Development
---------------------

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Nix as Nix Shell
       participant Docker as Docker
       participant Compose as Docker Compose
       
       Dev->>Nix: nix-shell
       Dev->>Docker: make build-container
       Docker->>Dev: Container built
       Dev->>Compose: make up
       Compose->>Dev: Services started
       Dev->>Compose: make logs
       Compose->>Dev: Service logs

Container Commands
------------------

.. code-block:: bash

   # Build container
   nix-shell --run "make build-container"
   
   # Start services
   nix-shell --run "make up"
   
   # View logs
   nix-shell --run "make logs"
   
   # Stop services
   nix-shell --run "make down"

CI/CD Integration
=================

GitHub Actions Workflow
------------------------

.. code-block:: yaml

   name: CI/CD Pipeline
   
   on: [push, pull_request]
   
   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v3
         - uses: cachix/install-nix-action@v20
         - name: Setup environment
           run: nix-shell --run "make setup"
         - name: Run tests
           run: nix-shell --run "make test"
         - name: Security scan
           run: nix-shell --run "make security"
         - name: Build docs
           run: nix-shell --run "make docs"

Workflow Integration
--------------------

.. mermaid::

   graph LR
       A[Git Push] --> B[GitHub Actions]
       B --> C[Nix Environment]
       C --> D[Run Tests]
       D --> E[Security Scan]
       E --> F[Build Docs]
       F --> G[Deploy]

Custom Workflows
================

Creating Custom Workflows
--------------------------

Add custom commands to the Makefile:

.. code-block:: makefile

   custom-task: ## Custom development task
   	@echo "$(BLUE)Running custom task...$(RESET)"
   	@nix-shell --run "python scripts/custom_task.py"

Workflow Templates
------------------

Common workflow patterns:

.. code-block:: bash

   # Feature development workflow
   nix-shell --run "make setup && make test-fast && make lint"
   
   # Release preparation workflow
   nix-shell --run "make test && make security && make docs && make build"
   
   # Debugging workflow
   nix-shell --run "make db-reset && make test-integration && make logs"

Best Practices
==============

Workflow Guidelines
-------------------

1. **Always use nix-shell**: Execute all commands within the Nix environment
2. **Test before commit**: Run tests and quality checks before committing
3. **Use Make commands**: Prefer standardized Make commands over direct tool usage
4. **Document workflows**: Keep workflow documentation up-to-date
5. **Automate repetitive tasks**: Create Make targets for common task sequences

Performance Optimization
-------------------------

.. code-block:: bash

   # Use binary caches for faster setup
   nix-shell --option substituters "https://cache.nixos.org/"
   
   # Parallel test execution
   nix-shell --run "make test -j4"
   
   # Incremental builds
   nix-shell --run "make build --incremental"

Troubleshooting Workflows
--------------------------

Common workflow issues and solutions:

.. list-table::
   :header-rows: 1
   :widths: 40 60

   * - Issue
     - Solution
   * - Command not found
     - Ensure you're in nix-shell
   * - Tests failing
     - Run ``make setup`` to reinstall dependencies
   * - Slow builds
     - Configure binary caches
   * - Environment issues
     - Run ``python test_shell_nix_simple.py``
