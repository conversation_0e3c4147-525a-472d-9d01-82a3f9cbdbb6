Configuration Guide
===================

Overview
--------

PITAS provides comprehensive configuration options for customizing the application behavior, security settings, integrations, and performance parameters. Configuration is managed through environment variables, configuration files, and database settings.

Environment Variables
---------------------

Core Application Settings
~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Core Configuration
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``DEBUG``
     - ``false``
     - Enable debug mode for development
   * - ``ENVIRONMENT``
     - ``production``
     - Application environment (development/staging/production)
   * - ``LOG_LEVEL``
     - ``INFO``
     - Logging level (DEBUG/INFO/WARNING/ERROR/CRITICAL)
   * - ``SECRET_KEY``
     - *Required*
     - Secret key for cryptographic operations
   * - ``PROJECT_NAME``
     - ``PITAS``
     - Application name for branding
   * - ``PROJECT_VERSION``
     - ``0.6.0``
     - Current application version
   * - ``API_V1_STR``
     - ``/api/v1``
     - API version prefix

Database Configuration
~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Database Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``DATABASE_URL``
     - *Required*
     - PostgreSQL connection string
   * - ``DATABASE_POOL_SIZE``
     - ``20``
     - Connection pool size
   * - ``DATABASE_MAX_OVERFLOW``
     - ``30``
     - Maximum overflow connections
   * - ``DATABASE_POOL_TIMEOUT``
     - ``30``
     - Connection timeout in seconds
   * - ``DATABASE_POOL_RECYCLE``
     - ``3600``
     - Connection recycle time in seconds

**Example Database URL:**

.. code-block:: bash

   DATABASE_URL=postgresql://username:password@localhost:5432/pitas

Redis Configuration
~~~~~~~~~~~~~~~~~~

.. list-table:: Redis Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``REDIS_URL``
     - ``redis://localhost:6379/0``
     - Redis connection string
   * - ``REDIS_POOL_SIZE``
     - ``10``
     - Redis connection pool size
   * - ``REDIS_TIMEOUT``
     - ``5``
     - Redis operation timeout
   * - ``CACHE_TTL``
     - ``3600``
     - Default cache TTL in seconds

Security Configuration
~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Security Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``JWT_SECRET_KEY``
     - *Required*
     - JWT token signing key
   * - ``JWT_ALGORITHM``
     - ``HS256``
     - JWT signing algorithm
   * - ``ACCESS_TOKEN_EXPIRE_MINUTES``
     - ``30``
     - Access token expiration time
   * - ``REFRESH_TOKEN_EXPIRE_DAYS``
     - ``7``
     - Refresh token expiration time
   * - ``PASSWORD_MIN_LENGTH``
     - ``8``
     - Minimum password length
   * - ``PASSWORD_REQUIRE_UPPERCASE``
     - ``true``
     - Require uppercase in passwords
   * - ``PASSWORD_REQUIRE_LOWERCASE``
     - ``true``
     - Require lowercase in passwords
   * - ``PASSWORD_REQUIRE_NUMBERS``
     - ``true``
     - Require numbers in passwords
   * - ``PASSWORD_REQUIRE_SYMBOLS``
     - ``false``
     - Require symbols in passwords

CORS and Host Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: CORS Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``ALLOWED_HOSTS``
     - ``["*"]``
     - Allowed host headers (JSON array)
   * - ``CORS_ORIGINS``
     - ``["*"]``
     - Allowed CORS origins (JSON array)
   * - ``CORS_CREDENTIALS``
     - ``true``
     - Allow credentials in CORS requests
   * - ``CORS_METHODS``
     - ``["*"]``
     - Allowed HTTP methods (JSON array)
   * - ``CORS_HEADERS``
     - ``["*"]``
     - Allowed headers (JSON array)

**Example CORS Configuration:**

.. code-block:: bash

   ALLOWED_HOSTS=["api.pitas.com", "pitas.com"]
   CORS_ORIGINS=["https://pitas.com", "https://app.pitas.com"]
   CORS_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]

Email Configuration
~~~~~~~~~~~~~~~~~~

.. list-table:: Email Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``SMTP_HOST``
     - ``localhost``
     - SMTP server hostname
   * - ``SMTP_PORT``
     - ``587``
     - SMTP server port
   * - ``SMTP_USER``
     - ``""``
     - SMTP username
   * - ``SMTP_PASSWORD``
     - ``""``
     - SMTP password
   * - ``SMTP_TLS``
     - ``true``
     - Use TLS encryption
   * - ``SMTP_SSL``
     - ``false``
     - Use SSL encryption
   * - ``EMAIL_FROM``
     - ``<EMAIL>``
     - Default sender email
   * - ``EMAIL_FROM_NAME``
     - ``PITAS System``
     - Default sender name

File Storage Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: File Storage Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``STORAGE_BACKEND``
     - ``local``
     - Storage backend (local/s3/gcs)
   * - ``UPLOAD_MAX_SIZE``
     - ``10485760``
     - Maximum upload size in bytes (10MB)
   * - ``UPLOAD_ALLOWED_TYPES``
     - ``["pdf", "doc", "docx", "jpg", "png"]``
     - Allowed file types (JSON array)
   * - ``LOCAL_STORAGE_PATH``
     - ``./uploads``
     - Local storage directory
   * - ``AWS_ACCESS_KEY_ID``
     - ``""``
     - AWS access key for S3
   * - ``AWS_SECRET_ACCESS_KEY``
     - ``""``
     - AWS secret key for S3
   * - ``AWS_S3_BUCKET``
     - ``""``
     - S3 bucket name
   * - ``AWS_REGION``
     - ``us-east-1``
     - AWS region

Monitoring and Observability
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Monitoring Settings
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``SENTRY_DSN``
     - ``""``
     - Sentry error tracking DSN
   * - ``SENTRY_ENVIRONMENT``
     - ``${ENVIRONMENT}``
     - Sentry environment tag
   * - ``METRICS_ENABLED``
     - ``true``
     - Enable Prometheus metrics
   * - ``METRICS_PATH``
     - ``/metrics``
     - Metrics endpoint path
   * - ``HEALTH_CHECK_PATH``
     - ``/health``
     - Health check endpoint path
   * - ``REQUEST_TIMEOUT``
     - ``30``
     - Request timeout in seconds

Phase 6 Specific Configuration
-----------------------------

Career Development Settings
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Career Development
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``DEFAULT_DEVELOPMENT_BUDGET``
     - ``5000``
     - Default annual development budget (USD)
   * - ``IDP_REVIEW_FREQUENCY_MONTHS``
     - ``3``
     - IDP review frequency in months
   * - ``GOAL_PROGRESS_THRESHOLD``
     - ``0.8``
     - Progress threshold for goal completion
   * - ``CAREER_TIER_PROGRESSION_MONTHS``
     - ``24``
     - Minimum months between tier progressions
   * - ``AUTO_PROGRESS_CALCULATION``
     - ``true``
     - Enable automatic progress calculation

Recognition System Settings
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Recognition Configuration
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``PEER_NOMINATION_VOTING_DAYS``
     - ``14``
     - Voting period for peer nominations
   * - ``PEER_NOMINATION_MIN_VOTES``
     - ``3``
     - Minimum votes required for approval
   * - ``PEER_NOMINATION_APPROVAL_THRESHOLD``
     - ``0.6``
     - Approval threshold (percentage of positive votes)
   * - ``RECOGNITION_POINTS_MULTIPLIER``
     - ``1.0``
     - Global points multiplier
   * - ``AUTO_RECOGNITION_ENABLED``
     - ``true``
     - Enable automatic recognition for achievements

Wellness Monitoring Settings
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Wellness Configuration
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``WELLNESS_CHECK_FREQUENCY_DAYS``
     - ``90``
     - Wellness check frequency in days
   * - ``BURNOUT_RISK_THRESHOLD_HIGH``
     - ``0.7``
     - High burnout risk threshold
   * - ``BURNOUT_RISK_THRESHOLD_CRITICAL``
     - ``0.8``
     - Critical burnout risk threshold
   * - ``WELLNESS_ALERT_ENABLED``
     - ``true``
     - Enable automatic wellness alerts
   * - ``WELLNESS_ALERT_ESCALATION_HOURS``
     - ``24``
     - Hours before alert escalation

Mentorship System Settings
~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Mentorship Configuration
   :widths: 30 20 50
   :header-rows: 1

   * - Variable
     - Default
     - Description
   * - ``MENTORSHIP_MATCHING_ALGORITHM``
     - ``weighted``
     - Matching algorithm (simple/weighted/ml)
   * - ``MENTOR_MAX_MENTEES_DEFAULT``
     - ``3``
     - Default maximum mentees per mentor
   * - ``MENTORSHIP_SESSION_REMINDER_HOURS``
     - ``24``
     - Hours before session reminder
   * - ``MENTORSHIP_EFFECTIVENESS_THRESHOLD``
     - ``4.0``
     - Minimum effectiveness rating (1-5)
   * - ``AUTO_MENTORSHIP_MATCHING``
     - ``false``
     - Enable automatic mentorship matching

Configuration Files
-------------------

Application Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

**config/app.yaml:**

.. code-block:: yaml

   app:
     name: "PITAS"
     version: "0.6.0"
     description: "Pentesting Team Management System"
     
   api:
     title: "PITAS API"
     version: "v1"
     prefix: "/api/v1"
     docs_url: "/docs"
     redoc_url: "/redoc"
     
   security:
     jwt:
       algorithm: "HS256"
       access_token_expire_minutes: 30
       refresh_token_expire_days: 7
     
     password:
       min_length: 8
       require_uppercase: true
       require_lowercase: true
       require_numbers: true
       require_symbols: false
     
     rate_limiting:
       enabled: true
       requests_per_minute: 100
       burst_size: 200

Database Configuration
~~~~~~~~~~~~~~~~~~~~~

**config/database.yaml:**

.. code-block:: yaml

   database:
     pool:
       size: 20
       max_overflow: 30
       timeout: 30
       recycle: 3600
     
     migrations:
       auto_upgrade: false
       backup_before_upgrade: true
     
     monitoring:
       slow_query_threshold: 1000  # milliseconds
       log_queries: false
       
   redis:
     pool:
       size: 10
       timeout: 5
     
     cache:
       default_ttl: 3600
       key_prefix: "pitas:"

Feature Flags Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

**config/features.yaml:**

.. code-block:: yaml

   features:
     career_development:
       enabled: true
       auto_progress_calculation: true
       goal_recommendations: true
       
     recognition_system:
       enabled: true
       peer_nominations: true
       auto_recognition: true
       
     wellness_monitoring:
       enabled: true
       burnout_detection: true
       auto_alerts: true
       
     mentorship_system:
       enabled: true
       auto_matching: false
       session_reminders: true
       
     analytics:
       enabled: true
       real_time_dashboards: true
       predictive_models: true

Logging Configuration
~~~~~~~~~~~~~~~~~~~~

**config/logging.yaml:**

.. code-block:: yaml

   version: 1
   disable_existing_loggers: false
   
   formatters:
     standard:
       format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
     detailed:
       format: "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s"
     json:
       format: '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}'
   
   handlers:
     console:
       class: logging.StreamHandler
       level: INFO
       formatter: standard
       stream: ext://sys.stdout
     
     file:
       class: logging.handlers.RotatingFileHandler
       level: INFO
       formatter: detailed
       filename: logs/pitas.log
       maxBytes: 10485760  # 10MB
       backupCount: 5
     
     error_file:
       class: logging.handlers.RotatingFileHandler
       level: ERROR
       formatter: detailed
       filename: logs/error.log
       maxBytes: 10485760
       backupCount: 5
   
   loggers:
     pitas:
       level: INFO
       handlers: [console, file, error_file]
       propagate: false
     
     sqlalchemy.engine:
       level: WARNING
       handlers: [console]
       propagate: false
     
     uvicorn:
       level: INFO
       handlers: [console]
       propagate: false
   
   root:
     level: INFO
     handlers: [console]

Environment-Specific Configuration
----------------------------------

Development Environment
~~~~~~~~~~~~~~~~~~~~~~

**config/development.env:**

.. code-block:: bash

   # Development-specific settings
   DEBUG=true
   LOG_LEVEL=DEBUG
   
   # Database
   DATABASE_URL=postgresql://pitas_dev:password@localhost:5432/pitas_dev
   
   # Security (less strict for development)
   JWT_SECRET_KEY=dev-secret-key
   ACCESS_TOKEN_EXPIRE_MINUTES=480  # 8 hours for development
   
   # CORS (allow all for development)
   CORS_ORIGINS=["*"]
   ALLOWED_HOSTS=["*"]
   
   # Email (use console backend for development)
   EMAIL_BACKEND=console
   
   # Features (enable all for testing)
   CAREER_DEVELOPMENT_ENABLED=true
   RECOGNITION_SYSTEM_ENABLED=true
   WELLNESS_MONITORING_ENABLED=true
   MENTORSHIP_SYSTEM_ENABLED=true

Staging Environment
~~~~~~~~~~~~~~~~~~

**config/staging.env:**

.. code-block:: bash

   # Staging environment settings
   DEBUG=false
   ENVIRONMENT=staging
   LOG_LEVEL=INFO
   
   # Database
   DATABASE_URL=**********************************************************/pitas_staging
   
   # Security
   JWT_SECRET_KEY=staging-secret-key
   ACCESS_TOKEN_EXPIRE_MINUTES=60
   
   # CORS (restrict to staging domains)
   CORS_ORIGINS=["https://staging.pitas.com"]
   ALLOWED_HOSTS=["staging.pitas.com", "api-staging.pitas.com"]
   
   # Email
   SMTP_HOST=smtp.staging.com
   EMAIL_FROM=<EMAIL>
   
   # Monitoring
   SENTRY_DSN=https://your-staging-sentry-dsn
   SENTRY_ENVIRONMENT=staging

Production Environment
~~~~~~~~~~~~~~~~~~~~~

**config/production.env:**

.. code-block:: bash

   # Production environment settings
   DEBUG=false
   ENVIRONMENT=production
   LOG_LEVEL=WARNING
   
   # Database (use environment variables for sensitive data)
   DATABASE_URL=${PRODUCTION_DATABASE_URL}
   DATABASE_POOL_SIZE=50
   DATABASE_MAX_OVERFLOW=100
   
   # Security
   JWT_SECRET_KEY=${PRODUCTION_JWT_SECRET}
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # CORS (strict production settings)
   CORS_ORIGINS=["https://pitas.com", "https://app.pitas.com"]
   ALLOWED_HOSTS=["pitas.com", "api.pitas.com"]
   
   # Email
   SMTP_HOST=${PRODUCTION_SMTP_HOST}
   SMTP_USER=${PRODUCTION_SMTP_USER}
   SMTP_PASSWORD=${PRODUCTION_SMTP_PASSWORD}
   EMAIL_FROM=<EMAIL>
   
   # Monitoring
   SENTRY_DSN=${PRODUCTION_SENTRY_DSN}
   SENTRY_ENVIRONMENT=production
   METRICS_ENABLED=true

Configuration Management
------------------------

Environment Variable Precedence
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Configuration is loaded in the following order (later sources override earlier ones):

1. Default values in code
2. Configuration files (YAML)
3. Environment-specific files (.env)
4. Environment variables
5. Command-line arguments (if applicable)

Configuration Validation
~~~~~~~~~~~~~~~~~~~~~~~~

**Validation Example:**

.. code-block:: python

   from pydantic import BaseSettings, validator
   
   class Settings(BaseSettings):
       database_url: str
       jwt_secret_key: str
       access_token_expire_minutes: int = 30
       
       @validator('database_url')
       def validate_database_url(cls, v):
           if not v.startswith('postgresql://'):
               raise ValueError('Database URL must be PostgreSQL')
           return v
       
       @validator('jwt_secret_key')
       def validate_jwt_secret(cls, v):
           if len(v) < 32:
               raise ValueError('JWT secret key must be at least 32 characters')
           return v
       
       class Config:
           env_file = '.env'
           case_sensitive = False

Configuration Hot Reloading
~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Feature Flags Hot Reloading:**

.. code-block:: python

   import asyncio
   from watchdog.observers import Observer
   from watchdog.events import FileSystemEventHandler
   
   class ConfigReloadHandler(FileSystemEventHandler):
       def __init__(self, config_manager):
           self.config_manager = config_manager
       
       def on_modified(self, event):
           if event.src_path.endswith('features.yaml'):
               asyncio.create_task(self.config_manager.reload_features())

Security Considerations
----------------------

Sensitive Data Management
~~~~~~~~~~~~~~~~~~~~~~~~

**Environment Variables for Secrets:**

.. code-block:: bash

   # Use environment variables for sensitive data
   export DATABASE_PASSWORD="$(cat /run/secrets/db_password)"
   export JWT_SECRET_KEY="$(cat /run/secrets/jwt_secret)"
   export SMTP_PASSWORD="$(cat /run/secrets/smtp_password)"

**Docker Secrets:**

.. code-block:: yaml

   # docker-compose.yml
   services:
     app:
       secrets:
         - db_password
         - jwt_secret
       environment:
         - DATABASE_PASSWORD_FILE=/run/secrets/db_password
         - JWT_SECRET_KEY_FILE=/run/secrets/jwt_secret
   
   secrets:
     db_password:
       file: ./secrets/db_password.txt
     jwt_secret:
       file: ./secrets/jwt_secret.txt

Configuration Encryption
~~~~~~~~~~~~~~~~~~~~~~~~

**Encrypted Configuration Files:**

.. code-block:: python

   from cryptography.fernet import Fernet
   import yaml
   
   class EncryptedConfig:
       def __init__(self, key: bytes):
           self.cipher = Fernet(key)
       
       def encrypt_config(self, config_dict: dict) -> str:
           config_yaml = yaml.dump(config_dict)
           encrypted = self.cipher.encrypt(config_yaml.encode())
           return encrypted.decode()
       
       def decrypt_config(self, encrypted_config: str) -> dict:
           decrypted = self.cipher.decrypt(encrypted_config.encode())
           return yaml.safe_load(decrypted.decode())

For more configuration examples, see:
- :doc:`installation`
- :doc:`deployment/overview`
- :doc:`development/contributing`
