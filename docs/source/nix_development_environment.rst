===============================
Nix Development Environment
===============================

Overview
========

PITAS uses <PERSON>'s ``shell.nix`` for comprehensive dependency management, providing reproducible development environments across all team members. This approach ensures consistent tooling, exact dependency versions, and isolated development environments that don't interfere with system packages.

.. mermaid::

   graph TB
       A[Developer Machine] --> B[nix-shell]
       B --> C[PITAS Development Environment]
       C --> D[Python 3.11+]
       C --> E[Development Tools]
       C --> F[Code Quality Tools]
       C --> G[Security Tools]
       C --> H[Database Tools]
       C --> I[Container Tools]
       C --> J[Cloud Tools]
       C --> K[Monitoring Tools]
       
       E --> E1[Git, GitHub CLI]
       F --> F1[Ruff, <PERSON>, MyPy]
       G --> G1[Bandit, Semgrep]
       H --> H1[PostgreSQL, Redis]
       I --> I1[<PERSON><PERSON>, Kubernet<PERSON>]
       J --> J1[AWS, GCP, Azure]
       K --> K1[Prometheus, Grafana]

Why Nix?
=========

**Reproducible Environments**
   Nix ensures that every developer has exactly the same development environment, eliminating "works on my machine" issues.

**Declarative Dependencies**
   All dependencies are declared in ``shell.nix``, making it easy to see what tools are available and update them consistently.

**Isolated Environments**
   Nix environments don't interfere with system packages, preventing conflicts and ensuring clean development setups.

**Cross-Platform Compatibility**
   Works consistently across Linux, macOS, and Windows (with WSL), providing the same experience for all team members.

**Zero Configuration**
   New developers can get a fully functional development environment with a single ``nix-shell`` command.

Quick Start
===========

Prerequisites
-------------

Install Nix package manager:

.. code-block:: bash

   # Install Nix (single-user installation)
   curl -L https://nixos.org/nix/install | sh
   
   # Source the Nix environment
   source ~/.nix-profile/etc/profile.d/nix.sh

Entering the Development Environment
------------------------------------

.. code-block:: bash

   # Navigate to the project root
   cd /path/to/pitas
   
   # Enter the Nix shell environment
   nix-shell
   
   # You'll see the PITAS development environment banner
   # All dependencies are now available

Environment Architecture
========================

The PITAS Nix environment is structured in layers:

.. mermaid::

   graph TD
       A[shell.nix] --> B[Base System]
       B --> C[Python Environment]
       B --> D[Development Tools]
       B --> E[Infrastructure Tools]
       
       C --> C1[Python 3.11+]
       C --> C2[pip, setuptools]
       C --> C3[Virtual Environment]
       
       D --> D1[Code Quality]
       D --> D2[Security Scanning]
       D --> D3[Documentation]
       D --> D4[Testing Framework]
       
       E --> E1[Database Systems]
       E --> E2[Container Platform]
       E --> E3[Cloud Providers]
       E --> E4[Monitoring Stack]

Available Tools
===============

Core Development Tools
-----------------------

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tool
     - Version
     - Purpose
   * - Python
     - 3.11+
     - Primary development language
   * - pip
     - Latest
     - Python package manager
   * - Git
     - Latest
     - Version control system
   * - GitHub CLI
     - Latest
     - GitHub integration and automation

Code Quality Tools
------------------

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tool
     - Version
     - Purpose
   * - Ruff
     - Latest
     - Fast Python linter and formatter
   * - Black
     - Latest
     - Python code formatter
   * - MyPy
     - Latest
     - Static type checker
   * - Pre-commit
     - Latest
     - Git hook framework

Security Tools
--------------

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tool
     - Version
     - Purpose
   * - Bandit
     - Latest
     - Python security scanner
   * - Semgrep
     - Latest
     - Static analysis for security

Database Tools
--------------

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tool
     - Version
     - Purpose
   * - PostgreSQL
     - 15+
     - Primary database system
   * - Redis
     - Latest
     - Caching and session storage

Documentation Tools
-------------------

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tool
     - Version
     - Purpose
   * - Sphinx
     - Latest
     - Documentation generator
   * - Pandoc
     - Latest
     - Document converter

Container & Orchestration
-------------------------

.. list-table::
   :header-rows: 1
   :widths: 20 30 50

   * - Tool
     - Version
     - Purpose
   * - Docker
     - Latest
     - Containerization platform
   * - Docker Compose
     - Latest
     - Multi-container orchestration
   * - Kubectl
     - Latest
     - Kubernetes CLI
   * - Helm
     - Latest
     - Kubernetes package manager

Environment Variables
=====================

The shell.nix environment automatically configures:

.. code-block:: bash

   # Python environment
   PYTHONPATH="./src"
   PITAS_ENV="development"
   
   # Database connections
   DATABASE_URL="postgresql://pitas:pitas@localhost:5432/pitas"
   REDIS_URL="redis://localhost:6379/0"
   
   # Development settings
   DEBUG="true"
   LOG_LEVEL="DEBUG"

Environment Validation
======================

Use the provided test script to verify your environment:

.. code-block:: bash

   # Run comprehensive environment test
   python test_shell_nix_simple.py
   
   # Quick environment check
   nix-shell --run "echo 'Testing basic commands:' && python --version && pip --version && ruff --version"

Expected output:

.. code-block:: text

   🔍 Testing Shell.nix Environment
   ==================================================
   In Nix Shell: Yes
   ✅ Python: 3.11.11
   ✅ python: Python 3.11.11
   ✅ pip: pip 24.0
   ✅ git: git version 2.47.2
   
   🎯 Test Results:
     Tools: 3/3 working
     Imports: 2/3 working
     Overall: ✅ PASS

Development Workflow
====================

The Nix environment integrates seamlessly with the project's Make-based workflow:

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Nix as Nix Shell
       participant Make as Makefile
       participant Tools as Development Tools
       
       Dev->>Nix: nix-shell
       Nix->>Dev: Environment ready
       Dev->>Make: make setup
       Make->>Tools: Install dependencies
       Tools->>Dev: Setup complete
       
       Dev->>Make: make test
       Make->>Nix: Execute in shell
       Nix->>Tools: Run pytest
       Tools->>Dev: Test results
       
       Dev->>Make: make lint
       Make->>Nix: Execute in shell
       Nix->>Tools: Run ruff
       Tools->>Dev: Lint results

Best Practices
==============

Always Use Shell.nix
---------------------

.. code-block:: bash

   # Good: Use shell.nix for all development
   nix-shell --run "make test"
   
   # Avoid: Direct tool usage outside Nix
   pytest tests/

Keep Dependencies in Shell.nix
-------------------------------

- Add new CLI tools to ``shell.nix``
- Use ``pyproject.toml`` for Python packages only
- Document any new dependencies

Test Environment Regularly
---------------------------

.. code-block:: bash

   # Run environment tests weekly
   python test_shell_nix_simple.py

Use Make Commands
-----------------

.. code-block:: bash

   # Good: Use standardized commands
   make test
   make lint
   make docs
   
   # Avoid: Direct tool invocation
   pytest
   ruff check
   sphinx-build

Troubleshooting
===============

Common Issues
-------------

**Nix not found**

.. code-block:: bash

   # Install Nix
   curl -L https://nixos.org/nix/install | sh
   source ~/.nix-profile/etc/profile.d/nix.sh

**Shell.nix not loading**

.. code-block:: bash

   # Ensure you're in the project root
   cd /path/to/pitas
   ls -la shell.nix  # Should exist
   
   # Try entering shell explicitly
   nix-shell shell.nix

**Dependencies not available**

.. code-block:: bash

   # Check if you're in the Nix shell
   echo $IN_NIX_SHELL  # Should output "1"
   
   # Verify environment
   nix-shell --run "which python"

Performance Tips
----------------

**Use Nix binary cache**

.. code-block:: bash

   # Add to ~/.config/nix/nix.conf
   substituters = https://cache.nixos.org/ https://nix-community.cachix.org
   trusted-public-keys = cache.nixos.org-1:6NCHdD59X431o0gWypbMrAURkbJ16ZPMQFGspcDShjY= nix-community.cachix.org-1:mB9FSh9qf2dCimDSUo8Zy7bkq5CX+/rkCWyvRCYg3Fs=

**Use direnv for automatic environment loading**

.. code-block:: bash

   # Install direnv
   nix-env -iA nixpkgs.direnv
   
   # Create .envrc in project root
   echo "use nix" > .envrc
   direnv allow
