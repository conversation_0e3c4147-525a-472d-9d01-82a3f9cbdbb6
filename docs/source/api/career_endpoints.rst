Career Development API Endpoints
=================================

Overview
--------

The Career Development API provides comprehensive endpoints for managing Individual Development Plans (IDPs), development goals, activities, and career progression tracking. All endpoints require authentication and follow RESTful design principles.

Base URL
--------

All career development endpoints are prefixed with:

.. code-block:: text

   /api/v1/career/

Authentication
--------------

All endpoints require a valid JWT token in the Authorization header:

.. code-block:: http

   Authorization: Bearer <your-jwt-token>

Individual Development Plans (IDPs)
-----------------------------------

Create IDP
~~~~~~~~~~

Create a new Individual Development Plan for the authenticated user.

.. code-block:: http

   POST /api/v1/career/idps

**Request Body:**

.. code-block:: json

   {
     "title": "2025 Career Development Plan",
     "description": "Focus on advancing to Senior Penetration Tester role",
     "status": "draft",
     "start_date": "2025-01-01T00:00:00Z",
     "end_date": "2025-12-31T23:59:59Z",
     "current_tier": "intermediate",
     "target_tier": "senior",
     "career_track": "technical_specialist",
     "manager_id": "123e4567-e89b-12d3-a456-426614174000",
     "metadata": {
       "focus_areas": ["advanced_penetration_testing", "team_leadership"],
       "budget_allocated": 5000
     }
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "987fcdeb-51a2-43d1-9c4f-123456789abc",
     "title": "2025 Career Development Plan",
     "description": "Focus on advancing to Senior Penetration Tester role",
     "status": "draft",
     "user_id": "456e7890-e12b-34d5-a678-901234567def",
     "manager_id": "123e4567-e89b-12d3-a456-426614174000",
     "start_date": "2025-01-01T00:00:00Z",
     "end_date": "2025-12-31T23:59:59Z",
     "current_tier": "intermediate",
     "target_tier": "senior",
     "career_track": "technical_specialist",
     "overall_progress": 0.0,
     "created_at": "2025-06-16T10:00:00Z",
     "updated_at": "2025-06-16T10:00:00Z"
   }

Get User IDPs
~~~~~~~~~~~~~

Retrieve all IDPs for the authenticated user.

.. code-block:: http

   GET /api/v1/career/idps?include_goals=true

**Query Parameters:**

- ``include_goals`` (boolean, optional): Include associated goals in response

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "987fcdeb-51a2-43d1-9c4f-123456789abc",
       "title": "2025 Career Development Plan",
       "status": "active",
       "overall_progress": 0.35,
       "goals": [
         {
           "id": "goal-123",
           "title": "Obtain OSCP Certification",
           "progress": 0.6,
           "target_date": "2025-08-31T23:59:59Z"
         }
       ]
     }
   ]

Get Active IDP
~~~~~~~~~~~~~~

Retrieve the currently active IDP for the authenticated user.

.. code-block:: http

   GET /api/v1/career/idps/active

**Response (200 OK):**

.. code-block:: json

   {
     "id": "987fcdeb-51a2-43d1-9c4f-123456789abc",
     "title": "2025 Career Development Plan",
     "status": "active",
     "overall_progress": 0.35,
     "goals": [
       {
         "id": "goal-123",
         "title": "Obtain OSCP Certification",
         "status": "in_progress",
         "progress": 0.6,
         "activities": [
           {
             "id": "activity-456",
             "title": "Complete PWK Course",
             "status": "completed",
             "completion_percentage": 100.0
           }
         ]
       }
     ]
   }

Update IDP
~~~~~~~~~~

Update an existing IDP.

.. code-block:: http

   PUT /api/v1/career/idps/{idp_id}

**Request Body:**

.. code-block:: json

   {
     "status": "active",
     "last_review_notes": "Good progress on certification goals",
     "next_review_date": "2025-09-30T00:00:00Z"
   }

Development Goals
-----------------

Create Goal
~~~~~~~~~~~

Create a new development goal for an IDP.

.. code-block:: http

   POST /api/v1/career/goals

**Request Body:**

.. code-block:: json

   {
     "idp_id": "987fcdeb-51a2-43d1-9c4f-123456789abc",
     "title": "Obtain OSCP Certification",
     "description": "Complete Offensive Security Certified Professional certification",
     "priority": "high",
     "target_date": "2025-08-31T23:59:59Z",
     "success_criteria": "Pass OSCP exam with score >= 70%",
     "estimated_cost": 1500.0,
     "target_skills": {
       "penetration_testing": "advanced",
       "exploit_development": "intermediate",
       "report_writing": "advanced"
     }
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "goal-123",
     "idp_id": "987fcdeb-51a2-43d1-9c4f-123456789abc",
     "title": "Obtain OSCP Certification",
     "description": "Complete Offensive Security Certified Professional certification",
     "status": "not_started",
     "priority": "high",
     "progress": 0.0,
     "target_date": "2025-08-31T23:59:59Z",
     "success_criteria": "Pass OSCP exam with score >= 70%",
     "estimated_cost": 1500.0,
     "created_at": "2025-06-16T10:00:00Z"
   }

Get IDP Goals
~~~~~~~~~~~~~

Retrieve all goals for a specific IDP.

.. code-block:: http

   GET /api/v1/career/idps/{idp_id}/goals?include_activities=true

**Response (200 OK):**

.. code-block:: json

   [
     {
       "id": "goal-123",
       "title": "Obtain OSCP Certification",
       "status": "in_progress",
       "priority": "high",
       "progress": 0.6,
       "target_date": "2025-08-31T23:59:59Z",
       "activities": [
         {
           "id": "activity-456",
           "title": "Complete PWK Course",
           "status": "completed",
           "completion_percentage": 100.0
         }
       ]
     }
   ]

Update Goal Progress
~~~~~~~~~~~~~~~~~~~~

Update the progress of a specific goal.

.. code-block:: http

   PUT /api/v1/career/goals/{goal_id}/progress

**Request Body:**

.. code-block:: json

   {
     "progress": 0.75,
     "notes": "Completed lab exercises, preparing for exam"
   }

**Response (200 OK):**

.. code-block:: json

   {
     "id": "goal-123",
     "title": "Obtain OSCP Certification",
     "status": "in_progress",
     "progress": 0.75,
     "last_update_notes": "Completed lab exercises, preparing for exam",
     "updated_at": "2025-06-16T15:30:00Z"
   }

Development Activities
----------------------

Create Activity
~~~~~~~~~~~~~~~

Create a new development activity for a goal.

.. code-block:: http

   POST /api/v1/career/activities

**Request Body:**

.. code-block:: json

   {
     "goal_id": "goal-123",
     "title": "Complete PWK Course",
     "description": "Complete Penetration Testing with Kali Linux course",
     "activity_type": "training",
     "planned_start_date": "2025-06-01T00:00:00Z",
     "planned_end_date": "2025-07-31T23:59:59Z",
     "estimated_hours": 120.0,
     "estimated_cost": 800.0,
     "provider": "Offensive Security",
     "url": "https://www.offensive-security.com/pwk-oscp/"
   }

**Response (201 Created):**

.. code-block:: json

   {
     "id": "activity-456",
     "goal_id": "goal-123",
     "title": "Complete PWK Course",
     "description": "Complete Penetration Testing with Kali Linux course",
     "activity_type": "training",
     "status": "planned",
     "planned_start_date": "2025-06-01T00:00:00Z",
     "planned_end_date": "2025-07-31T23:59:59Z",
     "estimated_hours": 120.0,
     "estimated_cost": 800.0,
     "completion_percentage": 0.0,
     "created_at": "2025-06-16T10:00:00Z"
   }

Career Progress Summary
-----------------------

Get Progress Summary
~~~~~~~~~~~~~~~~~~~~

Get comprehensive career progress summary for the authenticated user.

.. code-block:: http

   GET /api/v1/career/progress-summary

**Response (200 OK):**

.. code-block:: json

   {
     "user_id": "456e7890-e12b-34d5-a678-901234567def",
     "current_tier": "intermediate",
     "target_tier": "senior",
     "career_track": "technical_specialist",
     "active_idps": 1,
     "completed_idps": 2,
     "total_goals": 8,
     "completed_goals": 5,
     "in_progress_goals": 3,
     "overall_progress": 0.65,
     "last_review_date": "2025-03-31T00:00:00Z",
     "next_review_date": "2025-06-30T00:00:00Z",
     "development_budget_used": 3200.0,
     "development_budget_remaining": 1800.0
   }

Error Responses
---------------

The API returns standard HTTP status codes and error messages:

**400 Bad Request:**

.. code-block:: json

   {
     "detail": "Invalid request data",
     "errors": [
       {
         "field": "target_date",
         "message": "Target date must be in the future"
       }
     ]
   }

**401 Unauthorized:**

.. code-block:: json

   {
     "detail": "Authentication required"
   }

**403 Forbidden:**

.. code-block:: json

   {
     "detail": "Access denied. You can only access your own career development data."
   }

**404 Not Found:**

.. code-block:: json

   {
     "detail": "IDP not found"
   }

**422 Validation Error:**

.. code-block:: json

   {
     "detail": [
       {
         "loc": ["body", "title"],
         "msg": "field required",
         "type": "value_error.missing"
       }
     ]
   }

Rate Limiting
-------------

API endpoints are rate-limited to prevent abuse:

- **Standard endpoints**: 100 requests per minute per user
- **Analytics endpoints**: 20 requests per minute per user
- **Bulk operations**: 10 requests per minute per user

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1640995200

Pagination
----------

List endpoints support pagination using query parameters:

.. code-block:: http

   GET /api/v1/career/idps?page=1&per_page=20

**Response includes pagination metadata:**

.. code-block:: json

   {
     "items": [...],
     "total": 45,
     "page": 1,
     "per_page": 20,
     "pages": 3,
     "has_next": true,
     "has_prev": false
   }

Filtering and Sorting
--------------------

Many endpoints support filtering and sorting:

.. code-block:: http

   GET /api/v1/career/goals?status=in_progress&sort=priority&order=desc

**Common filter parameters:**
- ``status``: Filter by status
- ``priority``: Filter by priority level
- ``created_after``: Filter by creation date
- ``updated_after``: Filter by update date

**Common sort parameters:**
- ``sort``: Field to sort by
- ``order``: Sort order (asc/desc)

WebSocket Support
-----------------

Real-time updates are available via WebSocket connections:

.. code-block:: javascript

   const ws = new WebSocket('wss://api.pitas.com/ws/career');
   
   ws.onmessage = function(event) {
     const update = JSON.parse(event.data);
     console.log('Career update:', update);
   };

**Update types:**
- ``goal_progress_updated``
- ``activity_completed``
- ``idp_status_changed``
- ``review_scheduled``

For more detailed API documentation, see the interactive OpenAPI documentation at ``/docs`` when running the application.
