API Overview
============

Introduction
-----------

The PITAS API provides comprehensive access to all platform functionality through a RESTful interface. Built with FastAPI, it offers automatic OpenAPI documentation, request/response validation, and high-performance async operations.

Base Information
---------------

**Base URL**: ``https://api.pitas.com/api/v1``

**API Version**: v1

**Documentation**: ``https://api.pitas.com/docs``

**OpenAPI Spec**: ``https://api.pitas.com/openapi.json``

Authentication
-------------

All API endpoints require authentication using JWT (JSON Web Tokens).

**Authentication Flow**:

1. **Login**: ``POST /auth/login``
2. **Receive Token**: Get access and refresh tokens
3. **Use Token**: Include in Authorization header
4. **Refresh**: Use refresh token when access token expires

**Example Authentication**:

.. code-block:: http

   POST /api/v1/auth/login
   Content-Type: application/json

   {
     "username": "<EMAIL>",
     "password": "your_password"
   }

**Response**:

.. code-block:: json

   {
     "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     "token_type": "bearer",
     "expires_in": 1800
   }

**Using the Token**:

.. code-block:: http

   GET /api/v1/users/me
   Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

API Structure
------------

The API is organized into logical modules:

Core Modules
~~~~~~~~~~~

**Authentication & Users**
- ``/auth/*`` - Authentication and token management
- ``/users/*`` - User management and profiles

**Phase 5: Training & Competency**
- ``/training/frameworks/*`` - NICE framework and competencies
- ``/training/courses/*`` - Training course management
- ``/training/certifications/*`` - Certification tracking
- ``/training/ctf/*`` - CTF platform and challenges
- ``/training/mentorship/*`` - Training mentorship

**Phase 6: Career Development**
- ``/career/idps/*`` - Individual Development Plans
- ``/career/goals/*`` - Development goals
- ``/career/activities/*`` - Learning activities
- ``/career/progress/*`` - Progress tracking and analytics

**Recognition & Rewards**
- ``/recognition/recognitions/*`` - Recognition management
- ``/recognition/nominations/*`` - Peer nominations
- ``/recognition/rewards/*`` - Reward catalog and redemption

**Wellness & Work-Life Balance**
- ``/wellness/checks/*`` - Wellness assessments
- ``/wellness/alerts/*`` - Wellness alerts and interventions
- ``/wellness/schedules/*`` - Work schedule management

**Mentorship (Phase 6)**
- ``/mentorship/relationships/*`` - Mentorship relationships
- ``/mentorship/sessions/*`` - Session management
- ``/mentorship/profiles/*`` - Mentor profiles
- ``/mentorship/requests/*`` - Mentorship requests

Request/Response Format
----------------------

Content Types
~~~~~~~~~~~~

**Request Content-Type**: ``application/json``

**Response Content-Type**: ``application/json``

**File Uploads**: ``multipart/form-data``

Standard Response Format
~~~~~~~~~~~~~~~~~~~~~~~

**Success Response**:

.. code-block:: json

   {
     "id": "123e4567-e89b-12d3-a456-************",
     "title": "Example Resource",
     "created_at": "2025-06-16T10:00:00Z",
     "updated_at": "2025-06-16T10:00:00Z"
   }

**Error Response**:

.. code-block:: json

   {
     "detail": "Error description",
     "error_code": "VALIDATION_ERROR",
     "errors": [
       {
         "field": "email",
         "message": "Invalid email format"
       }
     ]
   }

**Paginated Response**:

.. code-block:: json

   {
     "items": [...],
     "total": 150,
     "page": 1,
     "per_page": 20,
     "pages": 8,
     "has_next": true,
     "has_prev": false
   }

HTTP Status Codes
-----------------

Standard HTTP status codes are used throughout the API:

**Success Codes**:
- ``200 OK`` - Successful GET, PUT, PATCH
- ``201 Created`` - Successful POST
- ``204 No Content`` - Successful DELETE

**Client Error Codes**:
- ``400 Bad Request`` - Invalid request data
- ``401 Unauthorized`` - Authentication required
- ``403 Forbidden`` - Insufficient permissions
- ``404 Not Found`` - Resource not found
- ``409 Conflict`` - Resource conflict
- ``422 Unprocessable Entity`` - Validation error

**Server Error Codes**:
- ``500 Internal Server Error`` - Server error
- ``502 Bad Gateway`` - Upstream error
- ``503 Service Unavailable`` - Service temporarily unavailable

Pagination
----------

List endpoints support pagination using query parameters:

**Parameters**:
- ``page`` (integer): Page number (1-based)
- ``per_page`` (integer): Items per page (max 100)

**Example**:

.. code-block:: http

   GET /api/v1/career/idps?page=2&per_page=20

**Response Headers**:

.. code-block:: http

   X-Total-Count: 150
   X-Page: 2
   X-Per-Page: 20
   X-Total-Pages: 8

Filtering and Sorting
--------------------

Many endpoints support filtering and sorting:

**Common Filters**:
- ``status`` - Filter by status
- ``created_after`` - Filter by creation date
- ``updated_after`` - Filter by update date
- ``search`` - Text search

**Sorting**:
- ``sort`` - Field to sort by
- ``order`` - Sort order (asc/desc)

**Example**:

.. code-block:: http

   GET /api/v1/career/goals?status=in_progress&sort=priority&order=desc

Field Selection
--------------

Reduce response size by selecting specific fields:

**Parameter**: ``fields`` (comma-separated list)

**Example**:

.. code-block:: http

   GET /api/v1/users?fields=id,email,full_name

**Response**:

.. code-block:: json

   [
     {
       "id": "123e4567-e89b-12d3-a456-************",
       "email": "<EMAIL>",
       "full_name": "John Doe"
     }
   ]

Rate Limiting
------------

API endpoints are rate-limited to ensure fair usage:

**Limits**:
- **Standard endpoints**: 100 requests per minute
- **Analytics endpoints**: 20 requests per minute
- **File upload endpoints**: 10 requests per minute

**Headers**:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: 1640995200

**Rate Limit Exceeded**:

.. code-block:: json

   {
     "detail": "Rate limit exceeded",
     "error_code": "RATE_LIMIT_EXCEEDED",
     "retry_after": 60
   }

Webhooks
--------

PITAS supports webhooks for real-time event notifications:

**Supported Events**:
- ``user.created`` - New user registration
- ``idp.completed`` - IDP completion
- ``goal.achieved`` - Goal achievement
- ``recognition.created`` - New recognition
- ``wellness.alert`` - Wellness alert triggered
- ``mentorship.matched`` - Mentorship match created

**Webhook Configuration**:

.. code-block:: json

   {
     "url": "https://your-app.com/webhooks/pitas",
     "events": ["goal.achieved", "recognition.created"],
     "secret": "your-webhook-secret"
   }

**Webhook Payload**:

.. code-block:: json

   {
     "event": "goal.achieved",
     "timestamp": "2025-06-16T10:00:00Z",
     "data": {
       "goal_id": "123e4567-e89b-12d3-a456-************",
       "user_id": "456e7890-e12b-34d5-a678-901234567def",
       "title": "OSCP Certification Completed"
     }
   }

WebSocket Support
----------------

Real-time updates are available via WebSocket connections:

**Connection**: ``wss://api.pitas.com/ws``

**Authentication**: Include JWT token in connection headers

**Channels**:
- ``career.progress`` - Career development updates
- ``recognition.notifications`` - Recognition notifications
- ``wellness.alerts`` - Wellness alerts
- ``mentorship.updates`` - Mentorship updates

**Example**:

.. code-block:: javascript

   const ws = new WebSocket('wss://api.pitas.com/ws/career.progress', [], {
     headers: {
       'Authorization': 'Bearer your-jwt-token'
     }
   });

   ws.onmessage = function(event) {
     const update = JSON.parse(event.data);
     console.log('Career update:', update);
   };

API Versioning
-------------

**Current Version**: v1

**Version Header**: ``Accept: application/vnd.pitas.v1+json``

**Deprecation Policy**:
- 6 months notice for breaking changes
- Backward compatibility within major versions
- Clear migration guides for version upgrades

**Version Discovery**:

.. code-block:: http

   GET /api/versions

.. code-block:: json

   {
     "current": "v1",
     "supported": ["v1"],
     "deprecated": [],
     "sunset_dates": {}
   }

Error Handling
-------------

Comprehensive error handling with detailed error information:

**Validation Errors**:

.. code-block:: json

   {
     "detail": "Validation failed",
     "error_code": "VALIDATION_ERROR",
     "errors": [
       {
         "field": "email",
         "message": "Invalid email format",
         "code": "INVALID_FORMAT"
       },
       {
         "field": "password",
         "message": "Password too short",
         "code": "MIN_LENGTH"
       }
     ]
   }

**Business Logic Errors**:

.. code-block:: json

   {
     "detail": "Cannot delete active IDP",
     "error_code": "BUSINESS_RULE_VIOLATION",
     "context": {
       "idp_id": "123e4567-e89b-12d3-a456-************",
       "status": "active"
     }
   }

SDK and Client Libraries
-----------------------

Official client libraries are available:

**Python SDK**:

.. code-block:: bash

   pip install pitas-python-sdk

.. code-block:: python

   from pitas import PitasClient

   client = PitasClient(
       base_url="https://api.pitas.com",
       api_key="your-api-key"
   )

   # Create an IDP
   idp = client.career.create_idp({
       "title": "2025 Development Plan",
       "start_date": "2025-01-01",
       "end_date": "2025-12-31"
   })

**JavaScript SDK**:

.. code-block:: bash

   npm install @pitas/javascript-sdk

.. code-block:: javascript

   import { PitasClient } from '@pitas/javascript-sdk';

   const client = new PitasClient({
     baseUrl: 'https://api.pitas.com',
     apiKey: 'your-api-key'
   });

   // Create a recognition
   const recognition = await client.recognition.create({
     title: 'Great Work!',
     recipient_id: 'user-id',
     points_awarded: 100
   });

Testing and Development
----------------------

**Sandbox Environment**: ``https://sandbox-api.pitas.com``

**Test Data**: Pre-populated test data available in sandbox

**API Explorer**: Interactive API testing at ``/docs``

**Postman Collection**: Available for download

**Mock Server**: Available for development and testing

For detailed endpoint documentation, see:

- :doc:`career_endpoints` - Career development API
- :doc:`training_endpoints` - Training and competency API
- :doc:`recognition_endpoints` - Recognition and rewards API
- :doc:`wellness_endpoints` - Wellness monitoring API
- :doc:`mentorship_endpoints` - Mentorship system API

For authentication details, see:
- :doc:`authentication` - Authentication and authorization guide
