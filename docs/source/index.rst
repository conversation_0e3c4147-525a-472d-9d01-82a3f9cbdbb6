PITAS - Pentesting Team Management System
=========================================

**PITAS** (Pentesting Team Management System) is a comprehensive platform designed to address the critical cybersecurity talent shortage through evidence-based retention strategies, career development programs, and advanced training delivery systems.

.. image:: https://img.shields.io/badge/version-0.6.0-blue.svg
   :target: https://github.com/forkrul/pitas
   :alt: Version

.. image:: https://img.shields.io/badge/python-3.11+-blue.svg
   :target: https://python.org
   :alt: Python Version

.. image:: https://img.shields.io/badge/framework-FastAPI-green.svg
   :target: https://fastapi.tiangolo.com
   :alt: FastAPI

.. image:: https://img.shields.io/badge/database-PostgreSQL-blue.svg
   :target: https://postgresql.org
   :alt: PostgreSQL

Overview
--------

PITAS combines two major implementations:

**Phase 5: Training Delivery and Competency Management**
   - NICE Cybersecurity Workforce Framework integration
   - Competency-based learning with skills assessment
   - Training course management and certification tracking
   - CTF platform for practical skills development
   - Mentorship program coordination

**Phase 6: Employee Retention and Career Development**
   - Four-tier career advancement structure
   - Individual Development Plans (IDPs) with goal tracking
   - Recognition and rewards system with peer nominations
   - Work-life balance monitoring and burnout prevention
   - Advanced mentorship matching and session tracking

**Advanced Analytics and Reporting Engine**
   - ML-powered predictive analytics with 6 specialized models
   - Multi-stakeholder reporting (Executive, Technical, Compliance, Client)
   - Real-time dashboards with KPIs and performance metrics
   - Intelligent alerting with correlation and escalation
   - Comprehensive business intelligence and data visualization

Key Features
------------

🎯 **Career Progression Framework**
   Four-tier advancement structure (Entry → Intermediate → Senior → Expert) with clear progression pathways across multiple career tracks.

📚 **Comprehensive Training System**
   Integration with NICE framework, personalized learning paths, and practical skills assessment through CTF challenges.

🏆 **Recognition & Rewards**
   Peer nomination system with automated point calculations, achievement tracking, and reward fulfillment workflows.

💡 **Wellness Monitoring**
   Burnout risk assessment, stress level tracking, and automated alert generation for early intervention.

👥 **Advanced Mentorship**
   Intelligent mentor-mentee matching, session tracking, and effectiveness measurement with comprehensive analytics.

📊 **Advanced Analytics & ML**
   Machine learning-powered predictive insights, multi-format reporting, real-time dashboards, and intelligent alerting for data-driven decision making.

Quick Start
-----------

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/pitas.git
   cd pitas

   # Install dependencies
   pip install -r requirements.txt

   # Set up environment variables
   cp .env.example .env

   # Run database migrations
   alembic upgrade head

   # Start the development server
   uvicorn src.pitas.main:app --reload

Documentation Structure
-----------------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   installation
   configuration
   quickstart

.. toctree::
   :maxdepth: 2
   :caption: Phase 5: Training & Competency

   training/overview
   training/competency_framework
   training/courses
   training/certifications
   training/ctf_platform
   training/mentorship

.. toctree::
   :maxdepth: 2
   :caption: Phase 6: Career Development

   career/overview
   career/progression_framework
   career/development_plans
   career/recognition_system
   career/wellness_monitoring
   career/mentorship_matching

.. toctree::
   :maxdepth: 2
   :caption: Advanced Analytics Engine

   advanced_analytics
   analytics_diagrams
   analytics_api
   nix_development_environment
   nix_dependency_management
   nix_workflows

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/overview
   api/authentication
   api/training_endpoints
   api/career_endpoints
   api/recognition_endpoints
   api/wellness_endpoints
   api/mentorship_endpoints

.. toctree::
   :maxdepth: 2
   :caption: Database Schema

   database/overview
   database/user_models
   database/training_models
   database/career_models
   database/recognition_models
   database/wellness_models
   database/mentorship_models

.. toctree::
   :maxdepth: 2
   :caption: Business Logic

   services/overview
   services/training_services
   services/career_services
   services/recognition_services
   services/wellness_services
   services/mentorship_services

.. toctree::
   :maxdepth: 2
   :caption: Analytics & Reporting

   analytics/overview
   analytics/career_analytics
   analytics/training_analytics
   analytics/wellness_analytics
   analytics/retention_metrics

.. toctree::
   :maxdepth: 2
   :caption: Deployment & Operations

   deployment/overview
   deployment/docker
   deployment/kubernetes
   deployment/monitoring
   deployment/security

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/contributing
   development/testing
   development/code_style
   development/architecture

Success Metrics
---------------

PITAS is designed to achieve measurable improvements in cybersecurity team management:

- **>90% annual retention rate** through comprehensive career development
- **>95% IDP completion rate** with structured goal tracking
- **25% faster career progression** via clear advancement pathways
- **>4.0/5.0 work-life balance satisfaction** through wellness monitoring
- **3:1 leadership pipeline strength** with mentorship programs

Technical Architecture
----------------------

Built with modern, scalable technologies:

- **Backend**: FastAPI with async/await patterns
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT-based with role-based access control
- **API**: RESTful design with comprehensive OpenAPI documentation
- **Validation**: Pydantic v2 schemas with comprehensive field validation
- **Testing**: pytest with >95% code coverage
- **Deployment**: Docker containers with Kubernetes orchestration

Support & Community
-------------------

- **Documentation**: https://pitas.readthedocs.io
- **Issues**: https://github.com/forkrul/pitas/issues
- **Discussions**: https://github.com/forkrul/pitas/discussions
- **Contributing**: See :doc:`development/contributing`

License
-------

This project is licensed under the MIT License - see the LICENSE file for details.

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
