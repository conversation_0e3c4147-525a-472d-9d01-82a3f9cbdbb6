Career Development Database Models
==================================

Overview
--------

The career development database models support the comprehensive career progression framework, Individual Development Plans (IDPs), goal tracking, and activity management. These models are designed for scalability, performance, and data integrity.

Model Relationships
-------------------

The career development models form a hierarchical structure:

.. code-block:: text

   User
   └── IndividualDevelopmentPlan (1:N)
       └── DevelopmentGoal (1:N)
           └── DevelopmentActivity (1:N)

Core Models
-----------

IndividualDevelopmentPlan
~~~~~~~~~~~~~~~~~~~~~~~~~

The central model for managing career development plans.

**Table Name:** ``individual_development_plans``

**Fields:**

.. list-table::
   :widths: 20 20 15 45
   :header-rows: 1

   * - Field
     - Type
     - Constraints
     - Description
   * - id
     - UUID
     - Primary Key
     - Unique identifier
   * - user_id
     - UUID
     - Foreign Key, Not Null
     - Reference to User
   * - manager_id
     - UUID
     - Foreign Key, Nullable
     - Reference to manager User
   * - title
     - String(255)
     - Not Null
     - IDP title
   * - description
     - Text
     - Nullable
     - Detailed description
   * - status
     - Enum
     - Not Null, Default: DRAFT
     - Current status (DRAFT, ACTIVE, COMPLETED, CANCELLED)
   * - start_date
     - DateTime
     - Not Null
     - Plan start date
   * - end_date
     - DateTime
     - Not Null
     - Plan end date
   * - current_tier
     - String(50)
     - Not Null
     - Current career tier
   * - target_tier
     - String(50)
     - Not Null
     - Target career tier
   * - career_track
     - String(50)
     - Nullable
     - Career development track
   * - overall_progress
     - Float
     - Default: 0.0, Range: 0.0-1.0
     - Overall completion progress
   * - review_date
     - DateTime
     - Nullable
     - Last review date
   * - last_review_notes
     - Text
     - Nullable
     - Notes from last review
   * - next_review_date
     - DateTime
     - Nullable
     - Scheduled next review
   * - metadata
     - JSONB
     - Nullable
     - Additional flexible data
   * - created_at
     - DateTime
     - Auto-generated
     - Creation timestamp
   * - updated_at
     - DateTime
     - Auto-updated
     - Last update timestamp

**Indexes:**

.. code-block:: sql

   CREATE INDEX idx_idp_user_id ON individual_development_plans(user_id);
   CREATE INDEX idx_idp_manager_id ON individual_development_plans(manager_id);
   CREATE INDEX idx_idp_status ON individual_development_plans(status);
   CREATE INDEX idx_idp_dates ON individual_development_plans(start_date, end_date);

**Relationships:**

- ``user``: Many-to-One with User
- ``manager``: Many-to-One with User (manager)
- ``goals``: One-to-Many with DevelopmentGoal

DevelopmentGoal
~~~~~~~~~~~~~~~

Individual goals within an IDP.

**Table Name:** ``development_goals``

**Fields:**

.. list-table::
   :widths: 20 20 15 45
   :header-rows: 1

   * - Field
     - Type
     - Constraints
     - Description
   * - id
     - UUID
     - Primary Key
     - Unique identifier
   * - idp_id
     - UUID
     - Foreign Key, Not Null
     - Reference to IndividualDevelopmentPlan
   * - title
     - String(255)
     - Not Null
     - Goal title
   * - description
     - Text
     - Nullable
     - Detailed description
   * - status
     - Enum
     - Not Null, Default: NOT_STARTED
     - Goal status
   * - priority
     - Enum
     - Not Null, Default: MEDIUM
     - Priority level (LOW, MEDIUM, HIGH, CRITICAL)
   * - target_date
     - DateTime
     - Nullable
     - Target completion date
   * - completed_date
     - DateTime
     - Nullable
     - Actual completion date
   * - progress
     - Float
     - Default: 0.0, Range: 0.0-1.0
     - Goal completion progress
   * - success_criteria
     - Text
     - Nullable
     - Success measurement criteria
   * - measurement_method
     - Text
     - Nullable
     - How to measure progress
   * - required_resources
     - JSONB
     - Nullable
     - Required resources and support
   * - estimated_cost
     - Decimal(10,2)
     - Nullable
     - Estimated cost in USD
   * - assigned_mentor
     - UUID
     - Foreign Key, Nullable
     - Assigned mentor User ID
   * - target_skills
     - JSONB
     - Nullable
     - Skills to develop
   * - current_skill_level
     - JSONB
     - Nullable
     - Current skill assessments
   * - target_skill_level
     - JSONB
     - Nullable
     - Target skill levels
   * - notes
     - Text
     - Nullable
     - General notes
   * - last_update_notes
     - Text
     - Nullable
     - Last update notes

**Enums:**

.. code-block:: python

   class GoalStatus(str, enum.Enum):
       NOT_STARTED = "not_started"
       IN_PROGRESS = "in_progress"
       COMPLETED = "completed"
       ON_HOLD = "on_hold"
       CANCELLED = "cancelled"

   class GoalPriority(str, enum.Enum):
       LOW = "low"
       MEDIUM = "medium"
       HIGH = "high"
       CRITICAL = "critical"

**Indexes:**

.. code-block:: sql

   CREATE INDEX idx_goal_idp_id ON development_goals(idp_id);
   CREATE INDEX idx_goal_status ON development_goals(status);
   CREATE INDEX idx_goal_priority ON development_goals(priority);
   CREATE INDEX idx_goal_target_date ON development_goals(target_date);

DevelopmentActivity
~~~~~~~~~~~~~~~~~~~

Specific activities to achieve development goals.

**Table Name:** ``development_activities``

**Fields:**

.. list-table::
   :widths: 20 20 15 45
   :header-rows: 1

   * - Field
     - Type
     - Constraints
     - Description
   * - id
     - UUID
     - Primary Key
     - Unique identifier
   * - goal_id
     - UUID
     - Foreign Key, Not Null
     - Reference to DevelopmentGoal
   * - title
     - String(255)
     - Not Null
     - Activity title
   * - description
     - Text
     - Nullable
     - Detailed description
   * - activity_type
     - Enum
     - Not Null
     - Type of activity
   * - status
     - Enum
     - Not Null, Default: PLANNED
     - Activity status
   * - planned_start_date
     - DateTime
     - Nullable
     - Planned start date
   * - planned_end_date
     - DateTime
     - Nullable
     - Planned end date
   * - actual_start_date
     - DateTime
     - Nullable
     - Actual start date
   * - actual_end_date
     - DateTime
     - Nullable
     - Actual end date
   * - estimated_hours
     - Float
     - Nullable
     - Estimated time investment
   * - actual_hours
     - Float
     - Nullable
     - Actual time spent
   * - estimated_cost
     - Decimal(10,2)
     - Nullable
     - Estimated cost
   * - actual_cost
     - Decimal(10,2)
     - Nullable
     - Actual cost
   * - provider
     - String(255)
     - Nullable
     - Training provider or organization
   * - url
     - String(500)
     - Nullable
     - Related URL or link
   * - location
     - String(255)
     - Nullable
     - Physical or virtual location
   * - completion_percentage
     - Float
     - Default: 0.0, Range: 0.0-100.0
     - Completion percentage
   * - completion_notes
     - Text
     - Nullable
     - Completion notes
   * - outcome
     - Text
     - Nullable
     - Activity outcome
   * - certificate_url
     - String(500)
     - Nullable
     - Certificate or proof URL
   * - metadata
     - JSONB
     - Nullable
     - Additional flexible data

**Enums:**

.. code-block:: python

   class ActivityType(str, enum.Enum):
       TRAINING = "training"
       CERTIFICATION = "certification"
       CONFERENCE = "conference"
       WORKSHOP = "workshop"
       MENTORING = "mentoring"
       PROJECT = "project"
       READING = "reading"
       RESEARCH = "research"
       SHADOWING = "shadowing"
       VOLUNTEERING = "volunteering"

   class ActivityStatus(str, enum.Enum):
       PLANNED = "planned"
       IN_PROGRESS = "in_progress"
       COMPLETED = "completed"
       CANCELLED = "cancelled"
       ON_HOLD = "on_hold"

**Indexes:**

.. code-block:: sql

   CREATE INDEX idx_activity_goal_id ON development_activities(goal_id);
   CREATE INDEX idx_activity_status ON development_activities(status);
   CREATE INDEX idx_activity_type ON development_activities(activity_type);
   CREATE INDEX idx_activity_dates ON development_activities(planned_start_date, planned_end_date);

Data Integrity and Constraints
------------------------------

Foreign Key Constraints
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: sql

   -- IDP to User relationships
   ALTER TABLE individual_development_plans 
   ADD CONSTRAINT fk_idp_user 
   FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

   ALTER TABLE individual_development_plans 
   ADD CONSTRAINT fk_idp_manager 
   FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

   -- Goal to IDP relationship
   ALTER TABLE development_goals 
   ADD CONSTRAINT fk_goal_idp 
   FOREIGN KEY (idp_id) REFERENCES individual_development_plans(id) ON DELETE CASCADE;

   -- Activity to Goal relationship
   ALTER TABLE development_activities 
   ADD CONSTRAINT fk_activity_goal 
   FOREIGN KEY (goal_id) REFERENCES development_goals(id) ON DELETE CASCADE;

Check Constraints
~~~~~~~~~~~~~~~~~

.. code-block:: sql

   -- Progress constraints
   ALTER TABLE individual_development_plans 
   ADD CONSTRAINT chk_idp_progress 
   CHECK (overall_progress >= 0.0 AND overall_progress <= 1.0);

   ALTER TABLE development_goals 
   ADD CONSTRAINT chk_goal_progress 
   CHECK (progress >= 0.0 AND progress <= 1.0);

   -- Date constraints
   ALTER TABLE individual_development_plans 
   ADD CONSTRAINT chk_idp_dates 
   CHECK (end_date > start_date);

   ALTER TABLE development_goals 
   ADD CONSTRAINT chk_goal_dates 
   CHECK (completed_date IS NULL OR completed_date >= created_at);

   -- Cost constraints
   ALTER TABLE development_goals 
   ADD CONSTRAINT chk_goal_cost 
   CHECK (estimated_cost IS NULL OR estimated_cost >= 0);

   ALTER TABLE development_activities 
   ADD CONSTRAINT chk_activity_cost 
   CHECK (estimated_cost IS NULL OR estimated_cost >= 0);

Performance Optimization
------------------------

Indexing Strategy
~~~~~~~~~~~~~~~~~

**Primary Indexes:**
- Primary key indexes (automatic)
- Foreign key indexes for join performance
- Status indexes for filtering
- Date range indexes for temporal queries

**Composite Indexes:**

.. code-block:: sql

   -- User and status combination for efficient filtering
   CREATE INDEX idx_idp_user_status ON individual_development_plans(user_id, status);
   
   -- Goal priority and status for dashboard queries
   CREATE INDEX idx_goal_priority_status ON development_goals(priority, status);
   
   -- Activity type and status for reporting
   CREATE INDEX idx_activity_type_status ON development_activities(activity_type, status);

**JSONB Indexes:**

.. code-block:: sql

   -- GIN indexes for JSONB fields
   CREATE INDEX idx_idp_metadata_gin ON individual_development_plans USING GIN (metadata);
   CREATE INDEX idx_goal_skills_gin ON development_goals USING GIN (target_skills);
   CREATE INDEX idx_activity_metadata_gin ON development_activities USING GIN (metadata);

Query Optimization
~~~~~~~~~~~~~~~~~~

**Common Query Patterns:**

.. code-block:: sql

   -- Get active IDPs with goals for a user
   SELECT idp.*, goal.*
   FROM individual_development_plans idp
   LEFT JOIN development_goals goal ON idp.id = goal.idp_id
   WHERE idp.user_id = $1 AND idp.status = 'active'
   ORDER BY idp.created_at DESC, goal.priority DESC;

   -- Calculate overall progress for an IDP
   SELECT idp.id, 
          AVG(goal.progress) as calculated_progress
   FROM individual_development_plans idp
   JOIN development_goals goal ON idp.id = goal.idp_id
   WHERE idp.id = $1
   GROUP BY idp.id;

   -- Get activities by type and status
   SELECT a.*
   FROM development_activities a
   JOIN development_goals g ON a.goal_id = g.id
   JOIN individual_development_plans idp ON g.idp_id = idp.id
   WHERE idp.user_id = $1 
     AND a.activity_type = $2 
     AND a.status = $3
   ORDER BY a.planned_start_date;

Data Migration and Versioning
-----------------------------

Alembic Migrations
~~~~~~~~~~~~~~~~~~

**Initial Migration:**

.. code-block:: python

   """Create career development tables

   Revision ID: 001_career_development
   Revises: 000_initial
   Create Date: 2025-06-16 10:00:00.000000

   """
   from alembic import op
   import sqlalchemy as sa
   from sqlalchemy.dialects import postgresql

   def upgrade():
       # Create individual_development_plans table
       op.create_table('individual_development_plans',
           sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
           sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
           sa.Column('manager_id', postgresql.UUID(as_uuid=True), nullable=True),
           sa.Column('title', sa.String(length=255), nullable=False),
           sa.Column('description', sa.Text(), nullable=True),
           # ... additional columns
           sa.PrimaryKeyConstraint('id'),
           sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
           sa.ForeignKeyConstraint(['manager_id'], ['users.id'], ondelete='SET NULL')
       )

**Schema Evolution:**

.. code-block:: python

   """Add metadata fields to career models

   Revision ID: 002_career_metadata
   Revises: 001_career_development
   Create Date: 2025-06-16 11:00:00.000000

   """
   def upgrade():
       # Add JSONB metadata columns
       op.add_column('individual_development_plans', 
                     sa.Column('metadata', postgresql.JSONB(), nullable=True))
       op.add_column('development_goals', 
                     sa.Column('target_skills', postgresql.JSONB(), nullable=True))

Backup and Recovery
~~~~~~~~~~~~~~~~~~~

**Backup Strategy:**
- Daily full backups of career development tables
- Point-in-time recovery capability
- Cross-region backup replication
- Automated backup verification

**Recovery Procedures:**
- Table-level recovery for data corruption
- Point-in-time recovery for accidental deletions
- Cross-region failover for disaster recovery
- Data validation after recovery

Monitoring and Maintenance
--------------------------

Performance Monitoring
~~~~~~~~~~~~~~~~~~~~~~

**Key Metrics:**
- Query execution times
- Index usage statistics
- Table size and growth rates
- Lock contention and deadlocks
- Connection pool utilization

**Monitoring Queries:**

.. code-block:: sql

   -- Check index usage
   SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
   FROM pg_stat_user_indexes
   WHERE schemaname = 'public' 
     AND tablename LIKE '%development%'
   ORDER BY idx_scan DESC;

   -- Monitor table sizes
   SELECT tablename, 
          pg_size_pretty(pg_total_relation_size(tablename::regclass)) as size
   FROM pg_tables 
   WHERE schemaname = 'public' 
     AND tablename LIKE '%development%';

Maintenance Tasks
~~~~~~~~~~~~~~~~~

**Regular Maintenance:**
- Weekly VACUUM and ANALYZE operations
- Monthly index rebuilding for fragmented indexes
- Quarterly statistics updates
- Annual table reorganization if needed

**Automated Maintenance:**

.. code-block:: sql

   -- Automated cleanup of old completed activities
   DELETE FROM development_activities 
   WHERE status = 'completed' 
     AND actual_end_date < NOW() - INTERVAL '2 years';

   -- Archive old IDPs
   INSERT INTO archived_individual_development_plans 
   SELECT * FROM individual_development_plans 
   WHERE status = 'completed' 
     AND end_date < NOW() - INTERVAL '1 year';

For implementation details, see:
- :doc:`../services/career_services`
- :doc:`../api/career_endpoints`
