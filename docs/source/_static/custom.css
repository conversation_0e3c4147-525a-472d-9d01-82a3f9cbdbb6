/* Custom CSS for PITAS Documentation */

/* Brand colors */
:root {
    --pitas-primary: #2980B9;
    --pitas-secondary: #3498DB;
    --pitas-accent: #E74C3C;
    --pitas-success: #27AE60;
    --pitas-warning: #F39C12;
    --pitas-dark: #2C3E50;
    --pitas-light: #ECF0F1;
}

/* Header customization */
.wy-nav-top {
    background-color: var(--pitas-primary) !important;
}

.wy-nav-top a {
    color: white !important;
}

/* Sidebar customization */
.wy-nav-side {
    background: linear-gradient(180deg, var(--pitas-dark) 0%, #34495e 100%);
}

.wy-menu-vertical a {
    color: #bdc3c7 !important;
}

.wy-menu-vertical a:hover {
    background-color: var(--pitas-primary) !important;
    color: white !important;
}

.wy-menu-vertical li.current a {
    background-color: var(--pitas-secondary) !important;
    color: white !important;
    border-right: 3px solid var(--pitas-accent);
}

.wy-menu-vertical li.current > a {
    background-color: var(--pitas-primary) !important;
}

/* Content area */
.wy-nav-content {
    background-color: #fafafa;
}

/* Code blocks */
.highlight {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 1em 0;
}

.highlight pre {
    background-color: transparent !important;
    border: none !important;
    padding: 1em !important;
}

/* Inline code */
code.literal {
    background-color: #f1f3f4 !important;
    color: var(--pitas-dark) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-size: 0.9em !important;
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal !important;
}

.wy-table thead th {
    background-color: var(--pitas-primary) !important;
    color: white !important;
}

.wy-table-striped tr:nth-child(2n-1) td {
    background-color: #f8f9fa !important;
}

/* Admonitions */
.admonition {
    border-radius: 6px !important;
    border-left: 4px solid var(--pitas-primary) !important;
}

.admonition.note {
    border-left-color: var(--pitas-secondary) !important;
}

.admonition.warning {
    border-left-color: var(--pitas-warning) !important;
}

.admonition.danger {
    border-left-color: var(--pitas-accent) !important;
}

.admonition.tip {
    border-left-color: var(--pitas-success) !important;
}

.admonition-title {
    background-color: rgba(52, 152, 219, 0.1) !important;
    color: var(--pitas-dark) !important;
    font-weight: 600 !important;
}

/* Links */
a {
    color: var(--pitas-primary) !important;
}

a:hover {
    color: var(--pitas-secondary) !important;
}

a:visited {
    color: #8e44ad !important;
}

/* Buttons */
.btn {
    border-radius: 6px !important;
    font-weight: 500 !important;
}

.btn-primary {
    background-color: var(--pitas-primary) !important;
    border-color: var(--pitas-primary) !important;
}

.btn-primary:hover {
    background-color: var(--pitas-secondary) !important;
    border-color: var(--pitas-secondary) !important;
}

/* Search box */
.wy-side-nav-search {
    background-color: var(--pitas-dark) !important;
}

.wy-side-nav-search input[type="text"] {
    border-radius: 6px !important;
    border: 1px solid #bdc3c7 !important;
}

/* Version selector */
.wy-side-nav-search .version {
    color: #bdc3c7 !important;
}

/* Footer */
.rst-footer-buttons {
    margin-top: 2em !important;
}

.btn-neutral {
    background-color: var(--pitas-light) !important;
    color: var(--pitas-dark) !important;
    border: 1px solid #bdc3c7 !important;
}

.btn-neutral:hover {
    background-color: #d5dbdb !important;
}

/* Custom classes for documentation */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5em;
    margin: 2em 0;
}

.feature-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5em;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.feature-card h3 {
    color: var(--pitas-primary);
    margin-top: 0;
    border-bottom: 2px solid var(--pitas-light);
    padding-bottom: 0.5em;
}

.metric-badge {
    display: inline-block;
    background-color: var(--pitas-success);
    color: white;
    padding: 0.25em 0.75em;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    margin: 0.25em;
}

.metric-badge.warning {
    background-color: var(--pitas-warning);
}

.metric-badge.danger {
    background-color: var(--pitas-accent);
}

.api-endpoint {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1em;
    margin: 1em 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.api-method {
    display: inline-block;
    padding: 0.25em 0.5em;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.8em;
    margin-right: 0.5em;
}

.api-method.get {
    background-color: #d4edda;
    color: #155724;
}

.api-method.post {
    background-color: #cce5ff;
    color: #004085;
}

.api-method.put {
    background-color: #fff3cd;
    color: #856404;
}

.api-method.delete {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive design */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
        max-width: none !important;
    }
}
