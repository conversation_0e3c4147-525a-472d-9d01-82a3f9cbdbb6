============================
Nix Dependency Management
============================

Overview
========

PITAS uses <PERSON> for comprehensive dependency management, providing a declarative approach to managing development tools, CLI utilities, and system dependencies. This ensures reproducible builds and consistent development environments across all team members.

.. mermaid::

   graph LR
       A[shell.nix] --> B[Nix Package Manager]
       B --> C[Binary Cache]
       B --> D[Package Derivations]
       
       C --> E[Pre-built Packages]
       D --> F[Source Builds]
       
       E --> G[Development Environment]
       F --> G
       
       G --> H[40+ CLI Tools]
       G --> I[Python Ecosystem]
       G --> J[Infrastructure Tools]

Dependency Categories
=====================

Development Tools
-----------------

Core development utilities for daily programming tasks:

.. code-block:: nix

   # Core development tools
   python311
   python311Packages.pip
   python311Packages.setuptools
   python311Packages.wheel
   git
   gh  # GitHub CLI

Code Quality & Linting
-----------------------

Tools for maintaining code quality and consistency:

.. code-block:: nix

   # Code quality tools
   ruff           # Fast Python linter/formatter
   black          # Python code formatter
   mypy           # Static type checker
   pre-commit     # Git hook framework

Security & Analysis
-------------------

Security scanning and static analysis tools:

.. code-block:: nix

   # Security tools
   bandit         # Python security scanner
   semgrep        # Static analysis for security

Database Systems
----------------

Database servers and client tools:

.. code-block:: nix

   # Database tools
   postgresql_15  # PostgreSQL database server
   redis          # Redis cache and session store

Documentation
-------------

Documentation generation and processing tools:

.. code-block:: nix

   # Documentation tools
   sphinx         # Documentation generator
   pandoc         # Universal document converter

Container & Orchestration
--------------------------

Container management and orchestration tools:

.. code-block:: nix

   # Container tools
   docker         # Container platform
   docker-compose # Multi-container orchestration
   kubectl        # Kubernetes CLI
   kubernetes-helm # Kubernetes package manager

Monitoring & Observability
---------------------------

Monitoring, metrics, and observability tools:

.. code-block:: nix

   # Monitoring tools
   prometheus     # Metrics collection
   grafana        # Metrics visualization

Cloud Providers
---------------

CLI tools for major cloud platforms:

.. code-block:: nix

   # Cloud tools
   awscli2        # Amazon Web Services CLI
   google-cloud-sdk # Google Cloud Platform CLI
   azure-cli      # Microsoft Azure CLI

Infrastructure as Code
-----------------------

Infrastructure management and automation tools:

.. code-block:: nix

   # Infrastructure tools
   terraform      # Infrastructure as Code
   ansible        # Configuration management

HTTP & API Tools
----------------

Tools for HTTP requests and API testing:

.. code-block:: nix

   # HTTP/API tools
   curl           # HTTP client
   httpie         # User-friendly HTTP client
   jq             # JSON processor

Utility Tools
-------------

General-purpose utilities for enhanced productivity:

.. code-block:: nix

   # Utility tools
   tree           # Directory structure visualization
   fd             # Fast file finder (find alternative)
   ripgrep        # Fast text search (grep alternative)
   bat            # Enhanced file viewer (cat alternative)

Shell.nix Structure
===================

The ``shell.nix`` file is organized for maintainability:

.. code-block:: nix

   { pkgs ? import <nixpkgs> {} }:

   pkgs.mkShell {
     name = "pitas-dev-environment";
     
     buildInputs = with pkgs; [
       # Core development tools
       python311
       python311Packages.pip
       git
       gh
       
       # Code quality tools
       ruff
       black
       mypy
       pre-commit
       
       # Security tools
       bandit
       semgrep
       
       # ... additional tools organized by category
     ];
     
     shellHook = ''
       echo "🚀 PITAS Development Environment"
       echo "Python: $(python --version)"
       echo "Available tools: ruff, black, mypy, git, docker, kubectl, terraform"
       
       # Set environment variables
       export PYTHONPATH="./src"
       export PITAS_ENV="development"
     '';
   }

Dependency Management Workflow
==============================

Adding New Dependencies
-----------------------

.. mermaid::

   sequenceDiagram
       participant Dev as Developer
       participant Shell as shell.nix
       participant Nix as Nix Package Manager
       participant Cache as Binary Cache
       participant Env as Environment
       
       Dev->>Shell: Add new package
       Dev->>Nix: nix-shell
       Nix->>Cache: Check for binary
       alt Binary available
           Cache->>Env: Download binary
       else Build required
           Nix->>Env: Build from source
       end
       Env->>Dev: Tool available

To add a new dependency:

1. **Find the package**: Search for packages at https://search.nixos.org/packages

.. code-block:: bash

   # Search for a package
   nix search nixpkgs nodejs

2. **Add to shell.nix**: Add the package to the buildInputs list

.. code-block:: nix

   buildInputs = with pkgs; [
     # Existing packages...
     nodejs  # Add new package
   ];

3. **Test the environment**: Enter the shell and verify the tool is available

.. code-block:: bash

   nix-shell
   which nodejs
   nodejs --version

Updating Dependencies
---------------------

.. code-block:: bash

   # Update Nix channels
   nix-channel --update
   
   # Rebuild environment with updated packages
   nix-shell --run "echo 'Environment updated'"

Version Pinning
---------------

For reproducible builds, you can pin specific versions:

.. code-block:: nix

   { pkgs ? import (fetchTarball {
       url = "https://github.com/NixOS/nixpkgs/archive/nixos-23.11.tar.gz";
       sha256 = "1234567890abcdef...";
     }) {}
   }:

Package Categories
==================

System Dependencies
-------------------

Packages that provide system-level functionality:

.. list-table::
   :header-rows: 1
   :widths: 25 25 50

   * - Package
     - Category
     - Purpose
   * - postgresql_15
     - Database
     - PostgreSQL database server
   * - redis
     - Database
     - Redis cache and session store
   * - docker
     - Container
     - Container platform
   * - kubectl
     - Container
     - Kubernetes CLI

Development Dependencies
------------------------

Packages specifically for development workflows:

.. list-table::
   :header-rows: 1
   :widths: 25 25 50

   * - Package
     - Category
     - Purpose
   * - ruff
     - Code Quality
     - Fast Python linter/formatter
   * - black
     - Code Quality
     - Python code formatter
   * - mypy
     - Code Quality
     - Static type checker
   * - pre-commit
     - Code Quality
     - Git hook framework

Infrastructure Dependencies
---------------------------

Packages for infrastructure management:

.. list-table::
   :header-rows: 1
   :widths: 25 25 50

   * - Package
     - Category
     - Purpose
   * - terraform
     - IaC
     - Infrastructure as Code
   * - ansible
     - Config Mgmt
     - Configuration management
   * - awscli2
     - Cloud
     - AWS CLI
   * - google-cloud-sdk
     - Cloud
     - Google Cloud CLI

Dependency Resolution
=====================

Nix uses a functional approach to dependency resolution:

.. mermaid::

   graph TD
       A[shell.nix] --> B[Package Expressions]
       B --> C[Dependency Graph]
       C --> D[Build Plan]
       D --> E[Binary Cache Check]
       E --> F{Cache Hit?}
       F -->|Yes| G[Download Binary]
       F -->|No| H[Build from Source]
       G --> I[Environment]
       H --> I

Conflict Resolution
-------------------

Nix prevents dependency conflicts by:

1. **Isolation**: Each package has its own directory
2. **Hashing**: Package paths include content hashes
3. **Composition**: Dependencies are composed, not merged

.. code-block:: text

   /nix/store/abc123-python-3.11.1/
   /nix/store/def456-nodejs-18.17.0/
   /nix/store/ghi789-postgresql-15.4/

Environment Composition
-----------------------

The final environment is composed from all dependencies:

.. code-block:: bash

   # PATH includes all package binaries
   /nix/store/abc123-python-3.11.1/bin
   /nix/store/def456-nodejs-18.17.0/bin
   /nix/store/ghi789-postgresql-15.4/bin

Binary Caching
==============

Nix uses binary caches to avoid rebuilding packages:

.. mermaid::

   graph LR
       A[Developer] --> B[nix-shell]
       B --> C{Package in Cache?}
       C -->|Yes| D[Download Binary]
       C -->|No| E[Build from Source]
       D --> F[Environment Ready]
       E --> G[Store in Cache]
       G --> F

Cache Configuration
-------------------

Configure binary caches in ``~/.config/nix/nix.conf``:

.. code-block:: text

   substituters = https://cache.nixos.org/ https://nix-community.cachix.org
   trusted-public-keys = cache.nixos.org-1:6NCHdD59X431o0gWypbMrAURkbJ16ZPMQFGspcDShjY=

Custom Packages
===============

For packages not in nixpkgs, you can create custom derivations:

.. code-block:: nix

   let
     customTool = pkgs.stdenv.mkDerivation {
       pname = "custom-tool";
       version = "1.0.0";
       src = fetchurl {
         url = "https://example.com/tool.tar.gz";
         sha256 = "...";
       };
       buildPhase = "make";
       installPhase = "make install PREFIX=$out";
     };
   in
   pkgs.mkShell {
     buildInputs = [ customTool ];
   }

Maintenance
===========

Regular Maintenance Tasks
-------------------------

1. **Update channels monthly**:

.. code-block:: bash

   nix-channel --update

2. **Garbage collect unused packages**:

.. code-block:: bash

   nix-collect-garbage -d

3. **Verify environment integrity**:

.. code-block:: bash

   python test_shell_nix_simple.py

4. **Update documentation** when adding new dependencies

Monitoring Dependencies
-----------------------

Track dependency usage and updates:

.. code-block:: bash

   # List all packages in environment
   nix-shell --run "nix-store -q --references \$NIX_STORE"
   
   # Check package versions
   nix-shell --run "python --version && ruff --version && terraform --version"

Best Practices
==============

1. **Organize by category**: Group related packages together in shell.nix
2. **Document new additions**: Add comments explaining why packages are needed
3. **Test after changes**: Always test the environment after modifications
4. **Use binary caches**: Configure caches to speed up environment setup
5. **Pin versions for production**: Use specific nixpkgs commits for reproducibility
6. **Regular updates**: Keep dependencies updated but test thoroughly
