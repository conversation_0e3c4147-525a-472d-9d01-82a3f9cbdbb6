Installation Guide
==================

System Requirements
-------------------

**Minimum Requirements:**
- Python 3.11 or higher
- PostgreSQL 14 or higher
- Redis 6.0 or higher (for caching and sessions)
- 4 GB RAM
- 20 GB disk space

**Recommended Requirements:**
- Python 3.12
- PostgreSQL 15
- Redis 7.0
- 8 GB RAM
- 50 GB disk space
- Docker and Docker Compose

**Supported Operating Systems:**
- Ubuntu 20.04 LTS or higher
- CentOS 8 or higher
- macOS 12 or higher
- Windows 10/11 with WSL2

Development Environment Setup
-----------------------------

Local Development with Docker
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**1. Clone the Repository**

.. code-block:: bash

   git clone https://github.com/forkrul/pitas.git
   cd pitas

**2. Environment Configuration**

.. code-block:: bash

   # Copy environment template
   cp .env.example .env
   
   # Edit environment variables
   nano .env

**3. Start Services with Docker Compose**

.. code-block:: bash

   # Start all services
   docker-compose up -d
   
   # View logs
   docker-compose logs -f app

**4. Initialize Database**

.. code-block:: bash

   # Run database migrations
   docker-compose exec app alembic upgrade head
   
   # Create initial admin user
   docker-compose exec app python scripts/create_admin.py

**5. Access the Application**

- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health
- Admin Interface: http://localhost:8000/admin

Local Development without Docker
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**1. Python Environment Setup**

.. code-block:: bash

   # Create virtual environment
   python -m venv venv
   
   # Activate virtual environment
   source venv/bin/activate  # Linux/macOS
   # or
   venv\Scripts\activate     # Windows
   
   # Upgrade pip
   pip install --upgrade pip

**2. Install Dependencies**

.. code-block:: bash

   # Install Python dependencies
   pip install -r requirements.txt
   
   # Install development dependencies
   pip install -r requirements-dev.txt

**3. Database Setup**

.. code-block:: bash

   # Install PostgreSQL (Ubuntu/Debian)
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   
   # Create database and user
   sudo -u postgres psql
   CREATE DATABASE pitas;
   CREATE USER pitas_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE pitas TO pitas_user;
   \q

**4. Redis Setup**

.. code-block:: bash

   # Install Redis (Ubuntu/Debian)
   sudo apt install redis-server
   
   # Start Redis service
   sudo systemctl start redis-server
   sudo systemctl enable redis-server

**5. Environment Configuration**

.. code-block:: bash

   # Create .env file
   cat > .env << EOF
   # Database Configuration
   DATABASE_URL=postgresql://pitas_user:your_password@localhost/pitas
   
   # Redis Configuration
   REDIS_URL=redis://localhost:6379/0
   
   # Security
   SECRET_KEY=your-secret-key-here
   JWT_SECRET_KEY=your-jwt-secret-key-here
   
   # Application Settings
   DEBUG=true
   ENVIRONMENT=development
   LOG_LEVEL=INFO
   
   # Email Configuration (optional for development)
   SMTP_HOST=localhost
   SMTP_PORT=587
   SMTP_USER=
   SMTP_PASSWORD=
   EOF

**6. Database Migration**

.. code-block:: bash

   # Run database migrations
   alembic upgrade head
   
   # Create initial data
   python scripts/create_admin.py
   python scripts/load_sample_data.py

**7. Start Development Server**

.. code-block:: bash

   # Start the application
   uvicorn src.pitas.main:app --reload --host 0.0.0.0 --port 8000

Production Deployment
---------------------

Docker Production Deployment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**1. Production Environment File**

.. code-block:: bash

   # Create production .env file
   cat > .env.prod << EOF
   # Database Configuration
   DATABASE_URL=***********************************************/pitas
   
   # Redis Configuration
   REDIS_URL=redis://redis:6379/0
   
   # Security
   SECRET_KEY=your-very-secure-secret-key
   JWT_SECRET_KEY=your-very-secure-jwt-key
   JWT_ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # Application Settings
   DEBUG=false
   ENVIRONMENT=production
   LOG_LEVEL=WARNING
   
   # CORS Settings
   ALLOWED_HOSTS=["your-domain.com", "api.your-domain.com"]
   CORS_ORIGINS=["https://your-domain.com"]
   
   # Email Configuration
   SMTP_HOST=smtp.your-provider.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-email-password
   SMTP_TLS=true
   
   # Monitoring
   SENTRY_DSN=your-sentry-dsn
   EOF

**2. Production Docker Compose**

.. code-block:: yaml

   # docker-compose.prod.yml
   version: '3.8'
   
   services:
     app:
       build:
         context: .
         dockerfile: Dockerfile.prod
       ports:
         - "8000:8000"
       environment:
         - DATABASE_URL=${DATABASE_URL}
         - REDIS_URL=${REDIS_URL}
         - SECRET_KEY=${SECRET_KEY}
       depends_on:
         - db
         - redis
       restart: unless-stopped
       volumes:
         - ./logs:/app/logs
   
     db:
       image: postgres:15
       environment:
         - POSTGRES_DB=pitas
         - POSTGRES_USER=pitas_user
         - POSTGRES_PASSWORD=${DB_PASSWORD}
       volumes:
         - postgres_data:/var/lib/postgresql/data
         - ./backups:/backups
       restart: unless-stopped
   
     redis:
       image: redis:7-alpine
       restart: unless-stopped
       volumes:
         - redis_data:/data
   
     nginx:
       image: nginx:alpine
       ports:
         - "80:80"
         - "443:443"
       volumes:
         - ./nginx.conf:/etc/nginx/nginx.conf
         - ./ssl:/etc/nginx/ssl
       depends_on:
         - app
       restart: unless-stopped
   
   volumes:
     postgres_data:
     redis_data:

**3. Deploy to Production**

.. code-block:: bash

   # Deploy with production configuration
   docker-compose -f docker-compose.prod.yml up -d
   
   # Run database migrations
   docker-compose -f docker-compose.prod.yml exec app alembic upgrade head
   
   # Create admin user
   docker-compose -f docker-compose.prod.yml exec app python scripts/create_admin.py

Kubernetes Deployment
~~~~~~~~~~~~~~~~~~~~~

**1. Kubernetes Manifests**

.. code-block:: yaml

   # k8s/namespace.yaml
   apiVersion: v1
   kind: Namespace
   metadata:
     name: pitas
   
   ---
   # k8s/configmap.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: pitas-config
     namespace: pitas
   data:
     ENVIRONMENT: "production"
     LOG_LEVEL: "INFO"
     DEBUG: "false"
   
   ---
   # k8s/secret.yaml
   apiVersion: v1
   kind: Secret
   metadata:
     name: pitas-secrets
     namespace: pitas
   type: Opaque
   data:
     DATABASE_URL: <base64-encoded-database-url>
     SECRET_KEY: <base64-encoded-secret-key>
     JWT_SECRET_KEY: <base64-encoded-jwt-key>

**2. Application Deployment**

.. code-block:: yaml

   # k8s/deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: pitas-app
     namespace: pitas
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: pitas-app
     template:
       metadata:
         labels:
           app: pitas-app
       spec:
         containers:
         - name: pitas
           image: pitas:latest
           ports:
           - containerPort: 8000
           envFrom:
           - configMapRef:
               name: pitas-config
           - secretRef:
               name: pitas-secrets
           resources:
             requests:
               memory: "512Mi"
               cpu: "250m"
             limits:
               memory: "1Gi"
               cpu: "500m"
           livenessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 5
             periodSeconds: 5

**3. Deploy to Kubernetes**

.. code-block:: bash

   # Apply Kubernetes manifests
   kubectl apply -f k8s/
   
   # Check deployment status
   kubectl get pods -n pitas
   
   # Run database migrations
   kubectl exec -n pitas deployment/pitas-app -- alembic upgrade head

Database Setup and Migration
----------------------------

Initial Database Setup
~~~~~~~~~~~~~~~~~~~~~~

**1. PostgreSQL Configuration**

.. code-block:: sql

   -- Create database and user
   CREATE DATABASE pitas;
   CREATE USER pitas_user WITH PASSWORD 'secure_password';
   
   -- Grant privileges
   GRANT ALL PRIVILEGES ON DATABASE pitas TO pitas_user;
   
   -- Enable required extensions
   \c pitas
   CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
   CREATE EXTENSION IF NOT EXISTS "pg_trgm";

**2. Database Migration**

.. code-block:: bash

   # Initialize Alembic (if not already done)
   alembic init alembic
   
   # Generate initial migration
   alembic revision --autogenerate -m "Initial migration"
   
   # Apply migrations
   alembic upgrade head
   
   # Check migration status
   alembic current

**3. Sample Data Loading**

.. code-block:: bash

   # Load sample data for development
   python scripts/load_sample_data.py
   
   # Load NICE framework data
   python scripts/load_nice_framework.py
   
   # Create test users
   python scripts/create_test_users.py

Environment Configuration
-------------------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

**Core Configuration:**

.. code-block:: bash

   # Application
   DEBUG=false
   ENVIRONMENT=production
   LOG_LEVEL=INFO
   SECRET_KEY=your-secret-key
   
   # Database
   DATABASE_URL=postgresql://user:pass@host:port/db
   DATABASE_POOL_SIZE=20
   DATABASE_MAX_OVERFLOW=30
   
   # Redis
   REDIS_URL=redis://host:port/db
   REDIS_POOL_SIZE=10
   
   # Security
   JWT_SECRET_KEY=your-jwt-secret
   JWT_ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # CORS
   ALLOWED_HOSTS=["your-domain.com"]
   CORS_ORIGINS=["https://your-domain.com"]

**Optional Configuration:**

.. code-block:: bash

   # Email
   SMTP_HOST=smtp.example.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=password
   SMTP_TLS=true
   
   # Monitoring
   SENTRY_DSN=your-sentry-dsn
   
   # File Storage
   AWS_ACCESS_KEY_ID=your-access-key
   AWS_SECRET_ACCESS_KEY=your-secret-key
   AWS_S3_BUCKET=your-bucket-name
   AWS_REGION=us-east-1

SSL/TLS Configuration
~~~~~~~~~~~~~~~~~~~~

**Nginx SSL Configuration:**

.. code-block:: nginx

   server {
       listen 443 ssl http2;
       server_name api.your-domain.com;
       
       ssl_certificate /etc/nginx/ssl/cert.pem;
       ssl_certificate_key /etc/nginx/ssl/key.pem;
       
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
       ssl_prefer_server_ciphers off;
       
       location / {
           proxy_pass http://app:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }

Monitoring and Logging
----------------------

Application Monitoring
~~~~~~~~~~~~~~~~~~~~~~

**Health Checks:**

.. code-block:: bash

   # Application health
   curl http://localhost:8000/health
   
   # Database health
   curl http://localhost:8000/health/db
   
   # Redis health
   curl http://localhost:8000/health/redis

**Logging Configuration:**

.. code-block:: python

   # logging.conf
   [loggers]
   keys=root,pitas
   
   [handlers]
   keys=consoleHandler,fileHandler
   
   [formatters]
   keys=simpleFormatter,detailedFormatter
   
   [logger_root]
   level=INFO
   handlers=consoleHandler
   
   [logger_pitas]
   level=INFO
   handlers=consoleHandler,fileHandler
   qualname=pitas
   propagate=0

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Database Connection Issues:**

.. code-block:: bash

   # Check database connectivity
   psql -h localhost -U pitas_user -d pitas -c "SELECT 1;"
   
   # Check database logs
   sudo tail -f /var/log/postgresql/postgresql-15-main.log

**Redis Connection Issues:**

.. code-block:: bash

   # Test Redis connection
   redis-cli ping
   
   # Check Redis logs
   sudo tail -f /var/log/redis/redis-server.log

**Application Startup Issues:**

.. code-block:: bash

   # Check application logs
   docker-compose logs app
   
   # Debug mode startup
   DEBUG=true uvicorn src.pitas.main:app --reload

**Migration Issues:**

.. code-block:: bash

   # Check migration status
   alembic current
   
   # Show migration history
   alembic history
   
   # Rollback migration
   alembic downgrade -1

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Database Optimization:**

.. code-block:: sql

   -- Check slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC 
   LIMIT 10;
   
   -- Analyze table statistics
   ANALYZE;
   
   -- Reindex if needed
   REINDEX DATABASE pitas;

**Application Optimization:**

.. code-block:: bash

   # Profile application performance
   python -m cProfile -o profile.stats src/pitas/main.py
   
   # Monitor memory usage
   docker stats pitas_app

For additional help:
- Check the FAQ: :doc:`faq`
- Review logs: :doc:`deployment/monitoring`
- Contact support: <EMAIL>
