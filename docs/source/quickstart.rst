Quick Start Guide
==================

Get PITAS up and running in 10 minutes with this comprehensive quick start guide.

Prerequisites
-------------

Before you begin, ensure you have:

- **Docker and Docker Compose** (recommended for quick setup)
- **Python 3.11+** (for local development)
- **PostgreSQL 14+** (if not using Docker)
- **Redis 6.0+** (if not using Docker)
- **Git** for cloning the repository

Quick Setup with Docker
-----------------------

The fastest way to get PITAS running is with Docker Compose.

Step 1: Clone and Setup
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/pitas.git
   cd pitas

   # Copy environment template
   cp .env.example .env

   # Edit environment variables (optional for quick start)
   nano .env

Step 2: Start Services
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Start all services in the background
   docker-compose up -d

   # Check service status
   docker-compose ps

   # View logs (optional)
   docker-compose logs -f app

Step 3: Initialize Database
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Run database migrations
   docker-compose exec app alembic upgrade head

   # Create initial admin user
   docker-compose exec app python scripts/create_admin.py

   # Load sample data (optional)
   docker-compose exec app python scripts/load_sample_data.py

Step 4: Access the Application
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Open your browser and navigate to:

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Admin Panel**: http://localhost:8000/admin

**Default Admin Credentials:**
- Username: ``<EMAIL>``
- Password: ``admin123`` (change immediately!)

First Steps
-----------

Create Your First User
~~~~~~~~~~~~~~~~~~~~~~

Using the API documentation at http://localhost:8000/docs:

1. **Authenticate as Admin**:
   - Go to ``POST /api/v1/auth/login``
   - Use admin credentials to get an access token

2. **Create a New User**:
   - Go to ``POST /api/v1/users/``
   - Click "Authorize" and enter your token
   - Create a user with the following example:

.. code-block:: json

   {
     "email": "<EMAIL>",
     "username": "john.doe",
     "full_name": "John Doe",
     "password": "SecurePass123",
     "role": "pentester",
     "career_tier": "intermediate",
     "career_track": "technical_specialist",
     "hire_date": "2025-01-15T00:00:00Z",
     "years_experience": 3,
     "department": "Cybersecurity",
     "location": "Remote"
   }

Create an Individual Development Plan
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Login as the New User**:
   - Use ``POST /api/v1/auth/login`` with the new user's credentials

2. **Create an IDP**:
   - Go to ``POST /api/v1/career/idps``
   - Create an IDP with this example:

.. code-block:: json

   {
     "title": "2025 Career Development Plan",
     "description": "Focus on advancing to Senior Penetration Tester role",
     "start_date": "2025-01-01T00:00:00Z",
     "end_date": "2025-12-31T23:59:59Z",
     "current_tier": "intermediate",
     "target_tier": "senior",
     "career_track": "technical_specialist",
     "manager_id": null
   }

Add Development Goals
~~~~~~~~~~~~~~~~~~~~

1. **Create a Goal**:
   - Go to ``POST /api/v1/career/goals``
   - Add a goal to your IDP:

.. code-block:: json

   {
     "idp_id": "your-idp-id-here",
     "title": "Obtain OSCP Certification",
     "description": "Complete Offensive Security Certified Professional certification",
     "priority": "high",
     "target_date": "2025-08-31T23:59:59Z",
     "success_criteria": "Pass OSCP exam with score >= 70%",
     "estimated_cost": 1500.0,
     "target_skills": {
       "penetration_testing": "advanced",
       "exploit_development": "intermediate",
       "report_writing": "advanced"
     }
   }

Add Learning Activities
~~~~~~~~~~~~~~~~~~~~~~

1. **Create an Activity**:
   - Go to ``POST /api/v1/career/activities``
   - Add an activity to your goal:

.. code-block:: json

   {
     "goal_id": "your-goal-id-here",
     "title": "Complete PWK Course",
     "description": "Complete Penetration Testing with Kali Linux course",
     "activity_type": "training",
     "planned_start_date": "2025-06-01T00:00:00Z",
     "planned_end_date": "2025-07-31T23:59:59Z",
     "estimated_hours": 120.0,
     "estimated_cost": 800.0,
     "provider": "Offensive Security",
     "url": "https://www.offensive-security.com/pwk-oscp/"
   }

Explore Phase 6 Features
------------------------

Recognition System
~~~~~~~~~~~~~~~~~

1. **Create a Recognition**:
   - Go to ``POST /api/v1/recognition/recognitions``
   - Recognize a colleague's achievement:

.. code-block:: json

   {
     "title": "Excellent Penetration Testing Report",
     "description": "John delivered an outstanding penetration testing report with clear recommendations",
     "recognition_type": "peer_nomination",
     "recipient_id": "colleague-user-id",
     "points_awarded": 100,
     "achievement_category": "technical_excellence",
     "is_public": true
   }

Wellness Check
~~~~~~~~~~~~~

1. **Submit a Wellness Check**:
   - Go to ``POST /api/v1/wellness/checks``
   - Complete a wellness assessment:

.. code-block:: json

   {
     "check_date": "2025-06-16T00:00:00Z",
     "overall_wellness_score": 8,
     "stress_level": 4,
     "energy_level": 7,
     "job_satisfaction": 8,
     "work_life_balance": 7,
     "workload_rating": 6,
     "hours_worked_last_week": 42,
     "engagement_level": 8,
     "motivation_level": 7,
     "positive_highlights": "Great team collaboration this week",
     "concerns_challenges": "Slightly heavy workload but manageable"
   }

Mentorship Request
~~~~~~~~~~~~~~~~~

1. **Create a Mentorship Request**:
   - Go to ``POST /api/v1/mentorship/requests``
   - Request a mentor:

.. code-block:: json

   {
     "request_type": "career_development",
     "priority": "medium",
     "desired_expertise": ["penetration_testing", "career_advancement"],
     "goals_and_objectives": "Seeking guidance on career progression to senior role",
     "preferred_mentor_level": "senior",
     "preferred_meeting_frequency": "bi_weekly",
     "requested_start_date": "2025-07-01T00:00:00Z",
     "preferred_duration": "6_months",
     "background_info": "3 years experience in penetration testing, looking to advance"
   }

Explore Analytics
~~~~~~~~~~~~~~~~

1. **View Career Progress**:
   - Go to ``GET /api/v1/career/progress-summary``
   - See your comprehensive career development summary

2. **Check Recognition Stats**:
   - Go to ``GET /api/v1/recognition/stats``
   - View your recognition statistics and trends

3. **Monitor Wellness Metrics**:
   - Go to ``GET /api/v1/wellness/metrics``
   - Review your wellness trends and insights

Common Use Cases
---------------

Manager Workflow
~~~~~~~~~~~~~~~

As a manager, you can:

1. **Review Team IDPs**:
   - Access team members' development plans
   - Approve goals and provide feedback
   - Track team development progress

2. **Monitor Team Wellness**:
   - Review wellness alerts for your team
   - Respond to burnout risk indicators
   - Support work-life balance initiatives

3. **Facilitate Recognition**:
   - Approve peer nominations
   - Create manager recognitions
   - Track team recognition metrics

Employee Workflow
~~~~~~~~~~~~~~~~

As an employee, you can:

1. **Manage Career Development**:
   - Create and update your IDP
   - Set and track development goals
   - Log learning activities and progress

2. **Participate in Recognition**:
   - Nominate colleagues for achievements
   - Vote on peer nominations
   - Redeem recognition points for rewards

3. **Monitor Personal Wellness**:
   - Complete regular wellness checks
   - Track work-life balance metrics
   - Access wellness resources and support

HR Workflow
~~~~~~~~~~

As an HR professional, you can:

1. **Oversee Career Development**:
   - Monitor organization-wide IDP completion
   - Analyze career progression trends
   - Support succession planning

2. **Manage Recognition Programs**:
   - Configure recognition categories and points
   - Monitor program effectiveness
   - Generate recognition reports

3. **Support Employee Wellness**:
   - Monitor organizational wellness metrics
   - Respond to wellness alerts
   - Analyze retention risk factors

Troubleshooting
--------------

Common Issues
~~~~~~~~~~~~

**Services Won't Start**:

.. code-block:: bash

   # Check Docker status
   docker --version
   docker-compose --version

   # Check for port conflicts
   netstat -tulpn | grep :8000

   # Restart services
   docker-compose down
   docker-compose up -d

**Database Connection Errors**:

.. code-block:: bash

   # Check database container
   docker-compose logs db

   # Reset database
   docker-compose down -v
   docker-compose up -d
   docker-compose exec app alembic upgrade head

**Authentication Issues**:

.. code-block:: bash

   # Recreate admin user
   docker-compose exec app python scripts/create_admin.py --force

   # Check JWT configuration
   docker-compose exec app python -c "from pitas.core.config import settings; print(settings.jwt_secret_key)"

**API Documentation Not Loading**:

.. code-block:: bash

   # Check application logs
   docker-compose logs app

   # Verify application is running
   curl http://localhost:8000/health

Getting Help
-----------

**Documentation**:
- Full documentation: :doc:`index`
- API reference: :doc:`api/overview`
- Configuration guide: :doc:`configuration`

**Community**:
- GitHub Issues: https://github.com/forkrul/pitas/issues
- Discussions: https://github.com/forkrul/pitas/discussions
- Contributing: :doc:`development/contributing`

**Support**:
- Email: <EMAIL>
- Documentation: https://pitas.readthedocs.io

Next Steps
----------

Now that you have PITAS running:

1. **Explore the Documentation**:
   - Read about :doc:`career/overview` for Phase 6 features
   - Learn about :doc:`training/overview` for Phase 5 features
   - Review :doc:`api/overview` for API integration

2. **Customize Your Installation**:
   - Configure :doc:`configuration` for your environment
   - Set up :doc:`deployment/monitoring` for production
   - Implement :doc:`deployment/security` best practices

3. **Integrate with Your Systems**:
   - Connect to your HR information system
   - Set up email notifications
   - Configure single sign-on (SSO)

4. **Train Your Team**:
   - Conduct user training sessions
   - Create organization-specific workflows
   - Establish governance and policies

Welcome to PITAS! 🎉
