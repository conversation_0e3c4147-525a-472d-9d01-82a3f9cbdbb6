Competency Management Guide
============================

This guide covers how to use the PITAS Training System's competency management features, including NICE framework integration, skills assessment, and gap analysis.

🎯 Overview
-----------

The competency management system provides:

* **NICE Framework Integration** - Comprehensive cybersecurity competency tracking
* **Skills Assessment** - Evidence-based competency evaluation
* **Gap Analysis** - Personalized skill development recommendations
* **Career Pathway Mapping** - Progression tracking from entry to expert levels

📋 NICE Framework Setup
-----------------------

**Creating a Competency Framework**

First, set up a competency framework aligned with NICE standards:

.. code-block:: http

   POST /api/v1/training/frameworks
   Content-Type: application/json
   Authorization: Bearer <jwt_token>

   {
     "name": "NICE Cybersecurity Framework - Penetration Testing",
     "description": "Competency framework for penetration testing specialists",
     "version": "1.0",
     "work_role_id": "SP-TES-001",
     "specialty_area": "Vulnerability Assessment and Management",
     "category": "Securely Provision"
   }

**Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": "123e4567-e89b-12d3-a456-************",
       "name": "NICE Cybersecurity Framework - Penetration Testing",
       "work_role_id": "SP-TES-001",
       "specialty_area": "Vulnerability Assessment and Management",
       "category": "Securely Provision",
       "created_at": "2025-06-16T10:30:00Z"
     }
   }

**Adding Competencies to Framework**

Define specific competencies within the framework:

.. code-block:: http

   POST /api/v1/training/competencies
   Content-Type: application/json

   {
     "framework_id": "123e4567-e89b-12d3-a456-************",
     "competency_id": "K0001",
     "name": "Knowledge of computer networking concepts and protocols",
     "description": "Understanding of networking fundamentals for security testing",
     "knowledge_statements": [
       "TCP/IP protocol suite",
       "OSI model layers",
       "Routing and switching concepts",
       "Network security protocols"
     ],
     "skill_statements": [
       "Network troubleshooting",
       "Protocol analysis",
       "Network scanning techniques"
     ],
     "ability_statements": [
       "Analyze network traffic patterns",
       "Identify network vulnerabilities",
       "Design secure network architectures"
     ]
   }

🔍 Skills Assessment Process
----------------------------

**Conducting Skills Assessments**

The assessment process involves evaluating current competency levels:

.. mermaid::

   flowchart TD
       START[Start Assessment] --> IDENTIFY[Identify Competencies]
       IDENTIFY --> CURRENT[Assess Current Level]
       CURRENT --> TARGET[Set Target Level]
       TARGET --> EVIDENCE[Gather Evidence]
       EVIDENCE --> VALIDATE[Validate Assessment]
       VALIDATE --> RECORD[Record Results]
       RECORD --> ANALYZE[Generate Gap Analysis]

**Creating a Skills Assessment**

.. code-block:: http

   POST /api/v1/training/assessments
   Content-Type: application/json

   {
     "user_id": "user-uuid-here",
     "competency_id": "competency-uuid-here",
     "current_level": "advanced_beginner",
     "target_level": "proficient",
     "assessor_id": "assessor-uuid-here",
     "notes": "Demonstrated strong theoretical knowledge but needs practical experience",
     "evidence": {
       "certifications": ["Network+", "Security+"],
       "projects": ["Network security audit", "Vulnerability assessment"],
       "training_completed": ["SANS SEC401"],
       "practical_demonstrations": {
         "network_scanning": "completed",
         "vulnerability_identification": "in_progress"
       }
     }
   }

**Competency Levels Explained**

.. list-table:: NICE Competency Levels
   :header-rows: 1
   :widths: 20 80

   * - Level
     - Description
   * - **Novice**
     - Basic understanding, requires supervision
   * - **Advanced Beginner**
     - Some experience, can perform routine tasks
   * - **Competent**
     - Solid experience, can handle most situations
   * - **Proficient**
     - Deep understanding, can mentor others
   * - **Expert**
     - Recognized authority, drives innovation

📊 Gap Analysis and Recommendations
-----------------------------------

**Generating Skills Gap Analysis**

The system automatically analyzes skill gaps and provides recommendations:

.. code-block:: http

   GET /api/v1/training/assessments/users/{user_id}/gap-analysis

**Sample Gap Analysis Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "user_id": "user-uuid-here",
       "current_competencies": {
         "K0001": "advanced_beginner",
         "K0002": "novice",
         "S0001": "competent"
       },
       "target_competencies": {
         "K0001": "proficient",
         "K0002": "competent", 
         "S0001": "proficient"
       },
       "skill_gaps": {
         "K0001": "competent",
         "K0002": "advanced_beginner",
         "S0001": "advanced_beginner"
       },
       "recommended_training": [
         "course-uuid-1",
         "course-uuid-2"
       ],
       "estimated_completion_weeks": 12
     }
   }

**Understanding Gap Analysis Results**

The gap analysis provides:

* **Current vs Target Comparison** - Visual representation of skill progression needs
* **Priority Ranking** - Which competencies need immediate attention
* **Training Recommendations** - Specific courses to address gaps
* **Timeline Estimation** - Realistic completion timeframes

🎯 Career Pathway Planning
--------------------------

**Role-Based Competency Mapping**

Different cybersecurity roles require different competency profiles:

.. code-block:: text

   Penetration Tester Pathway:
   ├── Entry Level (6-12 months)
   │   ├── Network Fundamentals (K0001) → Advanced Beginner
   │   ├── Security Principles (K0002) → Competent
   │   └── Basic Tools (S0001) → Advanced Beginner
   │
   ├── Intermediate Level (12-24 months)
   │   ├── Advanced Networking (K0001) → Proficient
   │   ├── Vulnerability Assessment (K0003) → Competent
   │   └── Exploitation Techniques (S0002) → Competent
   │
   └── Senior Level (24+ months)
       ├── Advanced Exploitation (S0002) → Proficient
       ├── Report Writing (A0001) → Proficient
       └── Team Leadership (A0002) → Competent

**Creating Learning Paths Based on Gaps**

The system can automatically generate personalized learning paths:

.. code-block:: http

   POST /api/v1/training/learning-paths
   Content-Type: application/json

   {
     "user_id": "user-uuid-here",
     "name": "Penetration Tester Development Path",
     "description": "Customized path based on current skill gaps",
     "target_role": "Senior Penetration Tester",
     "estimated_duration_weeks": 52,
     "course_sequence": [
       "networking-fundamentals-uuid",
       "vulnerability-assessment-uuid",
       "exploitation-techniques-uuid",
       "advanced-penetration-testing-uuid"
     ],
     "competency_goals": {
       "K0001": "proficient",
       "K0002": "competent",
       "S0001": "proficient",
       "S0002": "competent"
     }
   }

📈 Progress Tracking
--------------------

**Monitoring Competency Development**

Track progress over time with regular reassessments:

.. mermaid::

   gantt
       title Competency Development Timeline
       dateFormat  YYYY-MM-DD
       section Network Knowledge
       Initial Assessment    :done, assess1, 2025-01-01, 1d
       Training Period      :active, train1, 2025-01-02, 90d
       Reassessment         :milestone, reassess1, 2025-04-01, 1d
       
       section Security Skills
       Initial Assessment    :done, assess2, 2025-02-01, 1d
       Training Period      :train2, 2025-02-02, 120d
       Reassessment         :milestone, reassess2, 2025-06-01, 1d

**Competency Validation Methods**

1. **Formal Assessments** - Structured evaluations with scoring
2. **Practical Demonstrations** - Hands-on skill validation
3. **Peer Reviews** - Colleague feedback and validation
4. **Project Portfolios** - Evidence-based competency proof
5. **Certification Achievements** - Third-party validation

🔄 Continuous Improvement
-------------------------

**Regular Reassessment Schedule**

.. list-table:: Recommended Assessment Frequency
   :header-rows: 1
   :widths: 30 70

   * - Competency Level
     - Reassessment Frequency
   * - **Novice**
     - Every 3 months
   * - **Advanced Beginner**
     - Every 6 months
   * - **Competent**
     - Every 12 months
   * - **Proficient**
     - Every 18 months
   * - **Expert**
     - Every 24 months

**Updating Competency Frameworks**

Keep frameworks current with industry changes:

.. code-block:: http

   PUT /api/v1/training/frameworks/{framework_id}
   Content-Type: application/json

   {
     "version": "1.1",
     "description": "Updated framework with cloud security competencies"
   }

🎯 Best Practices
-----------------

**For Assessors:**

1. **Use Multiple Evidence Sources** - Don't rely on single assessments
2. **Document Thoroughly** - Provide clear justification for ratings
3. **Be Consistent** - Apply standards uniformly across assessments
4. **Focus on Practical Application** - Emphasize real-world skills
5. **Provide Constructive Feedback** - Help individuals understand gaps

**For Individuals:**

1. **Self-Assessment First** - Honestly evaluate your current level
2. **Gather Evidence** - Document your skills and achievements
3. **Seek Feedback** - Get input from colleagues and supervisors
4. **Create Development Plans** - Use gap analysis for targeted learning
5. **Track Progress** - Regular reassessment and adjustment

**For Organizations:**

1. **Align with Business Goals** - Ensure competencies support objectives
2. **Regular Framework Updates** - Keep pace with industry evolution
3. **Invest in Assessor Training** - Ensure quality and consistency
4. **Integrate with Performance Management** - Link to career development
5. **Measure ROI** - Track competency development impact

This competency management system provides a comprehensive foundation for developing and maintaining cybersecurity expertise aligned with industry standards and organizational needs.
