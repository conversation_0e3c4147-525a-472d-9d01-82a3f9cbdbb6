Training Courses Guide
======================

This comprehensive guide covers how to manage training courses, create learning paths, and track progress in the PITAS Training System.

🎓 Course Management Overview
-----------------------------

The training course system provides:

* **Course Catalog Management** - Comprehensive course database
* **Provider Integration** - Support for SANS, internal, and external training
* **Learning Path Creation** - Personalized learning sequences
* **Progress Tracking** - Real-time monitoring and analytics
* **Certification Alignment** - Course-to-certification mapping

📚 Creating Training Courses
----------------------------

**Basic Course Creation**

Create a new training course with comprehensive metadata:

.. code-block:: http

   POST /api/v1/training/courses
   Content-Type: application/json
   Authorization: Bearer <jwt_token>

   {
     "title": "Advanced Penetration Testing with Metasploit",
     "description": "Comprehensive course covering advanced penetration testing techniques using Metasploit framework",
     "provider": "SANS",
     "course_code": "SEC660",
     "duration_hours": 40,
     "difficulty_level": "proficient",
     "prerequisites": [
       "basic-networking-course-uuid",
       "security-fundamentals-course-uuid"
     ],
     "learning_objectives": [
       "Master advanced Metasploit techniques",
       "Understand post-exploitation methodologies",
       "Develop custom exploit modules",
       "Perform advanced persistence techniques"
     ],
     "competencies_addressed": [
       "competency-exploitation-uuid",
       "competency-post-exploitation-uuid",
       "competency-tool-development-uuid"
     ],
     "is_certification_prep": true,
     "certification_id": "oscp-certification-uuid",
     "cost": 7500.00,
     "is_active": true
   }

**Course Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": "course-uuid-here",
       "title": "Advanced Penetration Testing with Metasploit",
       "provider": "SANS",
       "course_code": "SEC660",
       "difficulty_level": "proficient",
       "duration_hours": 40,
       "cost": 7500.00,
       "created_at": "2025-06-16T10:30:00Z",
       "updated_at": "2025-06-16T10:30:00Z"
     }
   }

**Course Difficulty Levels**

.. list-table:: Training Difficulty Levels
   :header-rows: 1
   :widths: 20 80

   * - Level
     - Description
   * - **Novice**
     - Entry-level courses for beginners
   * - **Advanced Beginner**
     - Courses for those with basic knowledge
   * - **Competent**
     - Intermediate courses for experienced practitioners
   * - **Proficient**
     - Advanced courses for senior professionals
   * - **Expert**
     - Specialized courses for subject matter experts

🎯 Course Enrollment Process
----------------------------

**Enrollment Workflow**

.. mermaid::

   flowchart TD
       START[User Requests Enrollment] --> CHECK_PREREQ[Check Prerequisites]
       CHECK_PREREQ --> PREREQ_OK{Prerequisites Met?}
       PREREQ_OK -->|No| SUGGEST[Suggest Prerequisite Courses]
       PREREQ_OK -->|Yes| CHECK_APPROVAL[Check Approval Required]
       CHECK_APPROVAL --> APPROVAL_REQ{Approval Required?}
       APPROVAL_REQ -->|Yes| MANAGER_APPROVAL[Manager Approval]
       APPROVAL_REQ -->|No| ENROLL[Enroll User]
       MANAGER_APPROVAL --> APPROVED{Approved?}
       APPROVED -->|Yes| ENROLL
       APPROVED -->|No| REJECT[Reject Enrollment]
       ENROLL --> NOTIFY[Send Confirmation]
       SUGGEST --> END[End]
       REJECT --> END
       NOTIFY --> END

**Enrolling a User**

.. code-block:: http

   POST /api/v1/training/enrollments
   Content-Type: application/json

   {
     "user_id": "user-uuid-here",
     "course_id": "course-uuid-here",
     "learning_path_id": "path-uuid-here",
     "status": "not_started"
   }

**Enrollment Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": "enrollment-uuid-here",
       "user_id": "user-uuid-here",
       "course_id": "course-uuid-here",
       "enrollment_date": "2025-06-16T10:30:00Z",
       "status": "not_started",
       "progress_percentage": 0.0
     }
   }

📊 Progress Tracking
--------------------

**Updating Training Progress**

Track detailed progress including assessments and practical exercises:

.. code-block:: http

   PUT /api/v1/training/enrollments/{enrollment_id}
   Content-Type: application/json

   {
     "start_date": "2025-06-16T09:00:00Z",
     "status": "in_progress",
     "progress_percentage": 65.0,
     "assessment_scores": {
       "module_1_quiz": 85.0,
       "module_2_quiz": 92.0,
       "midterm_exam": 78.0
     },
     "practical_scores": {
       "lab_1_exploitation": 90.0,
       "lab_2_post_exploitation": 85.0,
       "capstone_project": 88.0
     },
     "time_spent_hours": 26.5
   }

**Progress Tracking Features**

* **Percentage Completion** - Overall course progress
* **Assessment Scores** - Quiz and exam results
* **Practical Scores** - Hands-on exercise performance
* **Time Tracking** - Actual time spent learning
* **Milestone Achievements** - Key learning checkpoints

**Getting Detailed Progress**

.. code-block:: http

   GET /api/v1/training/enrollments/users/{user_id}/courses/{course_id}/progress

**Progress Response:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "user_id": "user-uuid-here",
       "course_id": "course-uuid-here",
       "completion_percentage": 65.0,
       "assessment_scores": {
         "module_1_quiz": 85.0,
         "module_2_quiz": 92.0,
         "midterm_exam": 78.0
       },
       "practical_demonstrations": {
         "network_scanning": true,
         "vulnerability_exploitation": true,
         "post_exploitation": false
       },
       "time_spent_hours": 26.5,
       "projected_completion_date": "2025-07-15T17:00:00Z"
     }
   }

🛤️ Learning Paths
------------------

**Creating Personalized Learning Paths**

Learning paths provide structured sequences of courses tailored to individual goals:

.. code-block:: http

   POST /api/v1/training/learning-paths
   Content-Type: application/json

   {
     "user_id": "user-uuid-here",
     "name": "Penetration Tester Career Path",
     "description": "Comprehensive path from beginner to advanced penetration tester",
     "target_role": "Senior Penetration Tester",
     "estimated_duration_weeks": 52,
     "course_sequence": [
       "networking-fundamentals-uuid",
       "security-fundamentals-uuid",
       "ethical-hacking-basics-uuid",
       "advanced-penetration-testing-uuid",
       "web-application-security-uuid",
       "wireless-security-uuid"
     ],
     "competency_goals": {
       "network_security": "proficient",
       "vulnerability_assessment": "proficient",
       "exploitation_techniques": "competent",
       "report_writing": "competent"
     }
   }

**Learning Path Visualization**

.. mermaid::

   graph TD
       START[Start Learning Path] --> COURSE1[Networking Fundamentals]
       COURSE1 --> ASSESS1[Skills Assessment]
       ASSESS1 --> COURSE2[Security Fundamentals]
       COURSE2 --> ASSESS2[Skills Assessment]
       ASSESS2 --> COURSE3[Ethical Hacking Basics]
       COURSE3 --> ASSESS3[Skills Assessment]
       ASSESS3 --> COURSE4[Advanced Penetration Testing]
       COURSE4 --> ASSESS4[Skills Assessment]
       ASSESS4 --> COURSE5[Web Application Security]
       COURSE5 --> ASSESS5[Skills Assessment]
       ASSESS5 --> COURSE6[Wireless Security]
       COURSE6 --> FINAL[Final Assessment]
       FINAL --> COMPLETE[Path Complete]

**Adaptive Learning Paths**

The system can automatically adjust learning paths based on:

* **Performance Data** - Modify based on assessment results
* **Learning Speed** - Adjust timeline based on actual progress
* **Interest Areas** - Include elective courses based on preferences
* **Career Changes** - Pivot paths when goals change

📈 Analytics and Reporting
--------------------------

**Course Performance Analytics**

Track comprehensive metrics for continuous improvement:

.. code-block:: json

   {
     "course_analytics": {
       "enrollment_stats": {
         "total_enrollments": 156,
         "active_enrollments": 45,
         "completed_enrollments": 98,
         "dropout_rate": 8.3
       },
       "performance_metrics": {
         "average_completion_time_hours": 38.5,
         "average_assessment_score": 84.2,
         "pass_rate": 92.3,
         "satisfaction_rating": 4.6
       },
       "competency_impact": {
         "average_skill_improvement": 1.8,
         "competency_achievement_rate": 89.1
       }
     }
   }

**Individual Progress Reports**

Generate detailed reports for learners and managers:

.. code-block:: text

   Training Progress Report - John Doe
   ===================================
   
   Current Learning Path: Penetration Tester Career Path
   Progress: 65% Complete (3 of 6 courses finished)
   
   Completed Courses:
   ✓ Networking Fundamentals (Score: 92%)
   ✓ Security Fundamentals (Score: 88%)
   ✓ Ethical Hacking Basics (Score: 85%)
   
   Current Course:
   → Advanced Penetration Testing (65% complete)
     - Assessment Average: 84%
     - Practical Labs: 4 of 6 completed
     - Estimated Completion: July 15, 2025
   
   Upcoming Courses:
   • Web Application Security
   • Wireless Security
   
   Competency Progress:
   • Network Security: Advanced Beginner → Competent ✓
   • Vulnerability Assessment: Novice → Advanced Beginner ✓
   • Exploitation Techniques: Novice → Advanced Beginner (In Progress)

🎯 Best Practices
-----------------

**For Course Creators:**

1. **Clear Learning Objectives** - Define specific, measurable outcomes
2. **Appropriate Prerequisites** - Ensure learners have necessary foundation
3. **Hands-on Components** - Include practical exercises and labs
4. **Regular Assessments** - Check understanding throughout the course
5. **Real-world Scenarios** - Use relevant, current examples

**For Learners:**

1. **Set Clear Goals** - Understand why you're taking the course
2. **Manage Time Effectively** - Create a realistic study schedule
3. **Engage Actively** - Participate in discussions and exercises
4. **Seek Help When Needed** - Don't struggle alone
5. **Apply Knowledge** - Practice skills in real environments

**For Managers:**

1. **Align with Business Needs** - Ensure training supports objectives
2. **Provide Time and Resources** - Enable successful completion
3. **Track Progress Regularly** - Monitor and support team members
4. **Measure Impact** - Assess training effectiveness
5. **Recognize Achievements** - Celebrate completions and improvements

🔄 Integration with External Providers
--------------------------------------

**SANS Training Integration**

.. code-block:: python

   # Example integration configuration
   SANS_CONFIG = {
       "api_endpoint": "https://api.sans.org/training",
       "api_key": "your-sans-api-key",
       "sync_frequency": "daily",
       "auto_enroll": True,
       "progress_sync": True
   }

**Supported Training Providers**

* **SANS Institute** - Cybersecurity training and certifications
* **Offensive Security** - Penetration testing courses
* **EC-Council** - Ethical hacking and security certifications
* **CompTIA** - IT fundamentals and security basics
* **Internal Training** - Custom organizational content

This training course system provides a comprehensive platform for managing all aspects of cybersecurity education, from individual course enrollment to complex learning path orchestration.
