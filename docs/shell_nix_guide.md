# Shell.nix Dependency Management Guide

This guide explains how PITAS uses `shell.nix` for comprehensive dependency management, ensuring reproducible development environments across all team members.

## Overview

PITAS uses <PERSON>'s `shell.nix` for managing CLI dependencies and development tools, providing:

- **Reproducible environments** across different machines
- **Declarative dependency management** with exact versions
- **Isolated development environments** that don't interfere with system packages
- **Cross-platform compatibility** (Linux, macOS, Windows with WSL)

## Quick Start

### Prerequisites

Install Nix package manager:

```bash
# Install Nix (single-user installation)
curl -L https://nixos.org/nix/install | sh

# Source the Nix environment
source ~/.nix-profile/etc/profile.d/nix.sh
```

### Entering the Development Environment

```bash
# Navigate to the project root
cd /path/to/pitas

# Enter the Nix shell environment
nix-shell

# You'll see the PITAS development environment banner
# All dependencies are now available
```

## Available Tools and Dependencies

The `shell.nix` environment provides:

### Core Development Tools
- **Python 3.11+** - Primary development language
- **pip** - Python package manager
- **Git** - Version control
- **GitHub CLI (gh)** - GitHub integration

### Code Quality Tools
- **Ruff** - Fast Python linter and formatter
- **Black** - Python code formatter
- **MyPy** - Static type checker
- **Pre-commit** - Git hook framework

### Security Tools
- **Bandit** - Python security scanner
- **Semgrep** - Static analysis for security

### Database Tools
- **PostgreSQL** - Primary database
- **Redis** - Caching and session storage

### Documentation Tools
- **Sphinx** - Documentation generator
- **Pandoc** - Document converter

### Container & Orchestration
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **Kubectl** - Kubernetes CLI
- **Helm** - Kubernetes package manager

### Monitoring Tools
- **Prometheus** - Metrics collection
- **Grafana** - Metrics visualization

### API & HTTP Tools
- **cURL** - HTTP client
- **HTTPie** - User-friendly HTTP client
- **jq** - JSON processor

### Cloud Tools
- **AWS CLI** - Amazon Web Services
- **Google Cloud SDK** - Google Cloud Platform
- **Azure CLI** - Microsoft Azure

### Infrastructure Tools
- **Terraform** - Infrastructure as Code
- **Ansible** - Configuration management

### Utility Tools
- **tree** - Directory structure visualization
- **fd** - Fast file finder
- **ripgrep (rg)** - Fast text search
- **bat** - Enhanced file viewer

## Using Make Commands with Shell.nix

All Makefile commands are configured to use shell.nix:

### Development Commands

```bash
# Set up development environment
make setup

# Run tests
make test
make test-fast
make test-integration
make test-security

# Code quality
make lint
make format

# Security checks
make security

# Documentation
make docs
make docs-serve

# Database operations
make db-init
make db-migrate
make db-upgrade
make db-downgrade
make db-reset

# Application
make run
make run-prod

# Package management
make install
make install-dev

# Pre-commit hooks
make pre-commit
```

### Fallback Commands

If you need to use traditional virtual environments:

```bash
# Use venv instead of shell.nix
make setup-fallback
make test-fallback
make lint-fallback
make security-fallback
make run-fallback
make install-fallback
make pre-commit-fallback
```

## Environment Variables

The shell.nix environment sets up:

```bash
# Python environment
PYTHONPATH="./src"
PITAS_ENV="development"

# Database connections
DATABASE_URL="postgresql://pitas:pitas@localhost:5432/pitas"
REDIS_URL="redis://localhost:6379/0"

# Development settings
DEBUG="true"
LOG_LEVEL="DEBUG"
```

## Testing Shell.nix Environment

Use the provided test script to verify your environment:

```bash
# Run comprehensive environment test
nix-shell --run "./scripts/test_shell_nix.sh"

# Quick environment check
nix-shell --run "echo 'Testing basic commands:' && python --version && pip --version && ruff --version"
```

## Troubleshooting

### Common Issues

**1. Nix not found**
```bash
# Install Nix
curl -L https://nixos.org/nix/install | sh
source ~/.nix-profile/etc/profile.d/nix.sh
```

**2. Shell.nix not loading**
```bash
# Ensure you're in the project root
cd /path/to/pitas
ls -la shell.nix  # Should exist

# Try entering shell explicitly
nix-shell shell.nix
```

**3. Dependencies not available**
```bash
# Check if you're in the Nix shell
echo $IN_NIX_SHELL  # Should output "1"

# Verify environment
nix-shell --run "which python"
```

**4. Permission issues**
```bash
# Ensure proper permissions
chmod +x scripts/test_shell_nix.sh
```

### Performance Tips

**1. Use Nix binary cache**
```bash
# Add to ~/.config/nix/nix.conf
substituters = https://cache.nixos.org/ https://nix-community.cachix.org
trusted-public-keys = cache.nixos.org-1:6NCHdD59X431o0gWypbMrAURkbJ16ZPMQFGspcDShjY= nix-community.cachix.org-1:mB9FSh9qf2dCimDSUo8Zy7bkq5CX+/rkCWyvRCYg3Fs=
```

**2. Use direnv for automatic environment loading**
```bash
# Install direnv
nix-env -iA nixpkgs.direnv

# Create .envrc in project root
echo "use nix" > .envrc
direnv allow
```

## Integration with IDEs

### VS Code

Add to `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": ".venv/bin/python",
    "python.terminal.activateEnvironment": false,
    "terminal.integrated.shellArgs.linux": ["-c", "nix-shell --run zsh"]
}
```

### PyCharm

1. Configure Python interpreter to use `.venv/bin/python`
2. Set terminal to use `nix-shell`
3. Configure run configurations to use Nix environment

## Best Practices

### 1. Always Use Shell.nix

```bash
# Good: Use shell.nix for all development
nix-shell --run "make test"

# Avoid: Direct tool usage outside Nix
pytest tests/
```

### 2. Keep Dependencies in Shell.nix

- Add new CLI tools to `shell.nix`
- Use `pyproject.toml` for Python packages only
- Document any new dependencies

### 3. Test Environment Regularly

```bash
# Run environment tests weekly
nix-shell --run "./scripts/test_shell_nix.sh"
```

### 4. Use Make Commands

```bash
# Good: Use standardized commands
make test
make lint
make docs

# Avoid: Direct tool invocation
pytest
ruff check
sphinx-build
```

## Updating Dependencies

### Adding New Tools

Edit `shell.nix`:

```nix
buildInputs = with pkgs; [
  # Existing tools...
  
  # Add new tool
  newtool
];
```

### Updating Nix Packages

```bash
# Update Nix channels
nix-channel --update

# Rebuild environment
nix-shell --run "echo 'Environment updated'"
```

## Continuous Integration

The shell.nix environment is used in CI/CD:

```yaml
# .github/workflows/test.yml
- name: Setup Nix
  uses: cachix/install-nix-action@v20

- name: Run tests
  run: nix-shell --run "make test"
```

## Conclusion

Shell.nix provides a robust, reproducible development environment for PITAS. By using Nix for dependency management, we ensure:

- **Consistency** across development, testing, and production
- **Reproducibility** for all team members
- **Isolation** from system-level package conflicts
- **Declarative** dependency specification

Always use `nix-shell` and the provided Make commands for development work to maintain environment consistency.
