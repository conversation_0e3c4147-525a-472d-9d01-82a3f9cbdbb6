/* Custom CSS for PITAS Documentation */

/* Brand colors */
:root {
    --pitas-primary: #2980B9;
    --pitas-secondary: #3498DB;
    --pitas-accent: #E74C3C;
    --pitas-success: #27AE60;
    --pitas-warning: #F39C12;
    --pitas-dark: #2C3E50;
    --pitas-light: #ECF0F1;
}

/* Header customization */
.wy-nav-top {
    background-color: var(--pitas-primary) !important;
}

.wy-nav-top a {
    color: white !important;
}

/* Sidebar customization */
.wy-nav-side {
    background: linear-gradient(180deg, var(--pitas-dark) 0%, #34495e 100%);
}

.wy-menu-vertical a {
    color: #bdc3c7 !important;
}

.wy-menu-vertical a:hover {
    background-color: var(--pitas-primary) !important;
    color: white !important;
}

.wy-menu-vertical li.current a {
    background-color: var(--pitas-secondary) !important;
    color: white !important;
}

/* Content area improvements */
.rst-content {
    max-width: none;
}

/* Code blocks */
.rst-content pre.literal-block,
.rst-content div[class^='highlight'] {
    border: 1px solid #e1e4e5;
    border-radius: 4px;
    margin: 1em 0;
}

.rst-content .highlight {
    background: #f8f9fa;
}

/* API documentation styling */
.rst-content dl.class,
.rst-content dl.function,
.rst-content dl.method {
    border: 1px solid #e1e4e5;
    border-radius: 4px;
    padding: 1em;
    margin: 1em 0;
    background: #f8f9fa;
}

.rst-content dl.class > dt,
.rst-content dl.function > dt,
.rst-content dl.method > dt {
    background: var(--pitas-primary);
    color: white;
    padding: 0.5em 1em;
    margin: -1em -1em 1em -1em;
    border-radius: 3px 3px 0 0;
    font-weight: bold;
}

/* HTTP method badges */
.http-method {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: bold;
    color: white;
    margin-right: 0.5em;
}

.http-get { background-color: var(--pitas-success); }
.http-post { background-color: var(--pitas-primary); }
.http-put { background-color: var(--pitas-warning); }
.http-delete { background-color: var(--pitas-accent); }

/* Status code styling */
.http-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
    font-weight: bold;
    margin-right: 0.5em;
}

.http-200 { background-color: var(--pitas-success); color: white; }
.http-201 { background-color: var(--pitas-success); color: white; }
.http-400 { background-color: var(--pitas-warning); color: white; }
.http-401 { background-color: var(--pitas-accent); color: white; }
.http-404 { background-color: var(--pitas-accent); color: white; }
.http-422 { background-color: var(--pitas-accent); color: white; }

/* Admonition styling */
.rst-content .admonition {
    border-radius: 4px;
    border-left: 4px solid;
    padding: 1em;
    margin: 1em 0;
}

.rst-content .admonition.note {
    border-left-color: var(--pitas-primary);
    background-color: #e8f4fd;
}

.rst-content .admonition.warning {
    border-left-color: var(--pitas-warning);
    background-color: #fef9e7;
}

.rst-content .admonition.danger {
    border-left-color: var(--pitas-accent);
    background-color: #fdf2f2;
}

.rst-content .admonition.tip {
    border-left-color: var(--pitas-success);
    background-color: #f0f9ff;
}

/* Table styling */
.rst-content table.docutils {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin: 1em 0;
}

.rst-content table.docutils th {
    background-color: var(--pitas-primary);
    color: white;
    padding: 0.75em;
    text-align: left;
    font-weight: bold;
}

.rst-content table.docutils td {
    padding: 0.75em;
    border-bottom: 1px solid #e1e4e5;
}

.rst-content table.docutils tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Phase 3 specific styling */
.phase3-feature {
    border: 2px solid var(--pitas-secondary);
    border-radius: 8px;
    padding: 1.5em;
    margin: 1em 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.phase3-feature h3 {
    color: var(--pitas-primary);
    margin-top: 0;
}

/* Vulnerability severity badges */
.severity-critical {
    background-color: #dc3545;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.severity-high {
    background-color: #fd7e14;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.severity-medium {
    background-color: #ffc107;
    color: #212529;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.severity-low {
    background-color: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

/* Code syntax highlighting improvements */
.rst-content .highlight-python .hll { background-color: #ffffcc }
.rst-content .highlight-python .c { color: #408080; font-style: italic }
.rst-content .highlight-python .k { color: #008000; font-weight: bold }
.rst-content .highlight-python .o { color: #666666 }
.rst-content .highlight-python .cm { color: #408080; font-style: italic }
.rst-content .highlight-python .cp { color: #BC7A00 }
.rst-content .highlight-python .c1 { color: #408080; font-style: italic }
.rst-content .highlight-python .cs { color: #408080; font-style: italic }
.rst-content .highlight-python .gd { color: #A00000 }
.rst-content .highlight-python .ge { font-style: italic }
.rst-content .highlight-python .gr { color: #FF0000 }
.rst-content .highlight-python .gh { color: #000080; font-weight: bold }
.rst-content .highlight-python .gi { color: #00A000 }
.rst-content .highlight-python .go { color: #888888 }
.rst-content .highlight-python .gp { color: #000080; font-weight: bold }
.rst-content .highlight-python .gs { font-weight: bold }
.rst-content .highlight-python .gu { color: #800080; font-weight: bold }
.rst-content .highlight-python .gt { color: #0044DD }
.rst-content .highlight-python .kc { color: #008000; font-weight: bold }
.rst-content .highlight-python .kd { color: #008000; font-weight: bold }
.rst-content .highlight-python .kn { color: #008000; font-weight: bold }
.rst-content .highlight-python .kp { color: #008000 }
.rst-content .highlight-python .kr { color: #008000; font-weight: bold }
.rst-content .highlight-python .kt { color: #B00040 }
.rst-content .highlight-python .m { color: #666666 }
.rst-content .highlight-python .s { color: #BA2121 }
.rst-content .highlight-python .na { color: #7D9029 }
.rst-content .highlight-python .nb { color: #008000 }
.rst-content .highlight-python .nc { color: #0000FF; font-weight: bold }
.rst-content .highlight-python .no { color: #880000 }
.rst-content .highlight-python .nd { color: #AA22FF }
.rst-content .highlight-python .ni { color: #999999; font-weight: bold }
.rst-content .highlight-python .ne { color: #D2413A; font-weight: bold }
.rst-content .highlight-python .nf { color: #0000FF }
.rst-content .highlight-python .nl { color: #A0A000 }
.rst-content .highlight-python .nn { color: #0000FF; font-weight: bold }
.rst-content .highlight-python .nt { color: #008000; font-weight: bold }
.rst-content .highlight-python .nv { color: #19177C }
.rst-content .highlight-python .ow { color: #AA22FF; font-weight: bold }
.rst-content .highlight-python .w { color: #bbbbbb }
.rst-content .highlight-python .mb { color: #666666 }
.rst-content .highlight-python .mf { color: #666666 }
.rst-content .highlight-python .mh { color: #666666 }
.rst-content .highlight-python .mi { color: #666666 }
.rst-content .highlight-python .mo { color: #666666 }
.rst-content .highlight-python .sb { color: #BA2121 }
.rst-content .highlight-python .sc { color: #BA2121 }
.rst-content .highlight-python .sd { color: #BA2121; font-style: italic }
.rst-content .highlight-python .s2 { color: #BA2121 }
.rst-content .highlight-python .se { color: #BB6622; font-weight: bold }
.rst-content .highlight-python .sh { color: #BA2121 }
.rst-content .highlight-python .si { color: #BB6688; font-weight: bold }
.rst-content .highlight-python .sx { color: #008000 }
.rst-content .highlight-python .sr { color: #BB6688 }
.rst-content .highlight-python .s1 { color: #BA2121 }
.rst-content .highlight-python .ss { color: #19177C }
.rst-content .highlight-python .bp { color: #008000 }
.rst-content .highlight-python .vc { color: #19177C }
.rst-content .highlight-python .vg { color: #19177C }
.rst-content .highlight-python .vi { color: #19177C }
.rst-content .highlight-python .il { color: #666666 }

/* Responsive design improvements */
@media screen and (max-width: 768px) {
    .rst-content table.docutils {
        font-size: 0.9em;
    }
    
    .phase3-feature {
        padding: 1em;
        margin: 0.5em 0;
    }
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .rst-content {
        margin-left: 0 !important;
    }
}
