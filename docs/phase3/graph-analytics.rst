Graph-Based Vulnerability Analytics
===================================

Phase 3 leverages Neo4j graph database technology to provide advanced vulnerability correlation, attack path analysis, and relationship modeling for comprehensive security analytics.

Graph Database Architecture
----------------------------

Neo4j Integration
~~~~~~~~~~~~~~~~~

.. autoclass:: pitas.db.neo4j.Neo4jConnection
   :members:
   :undoc-members:
   :show-inheritance:

**Connection Management:**

.. code-block:: python

    # Async connection with proper lifecycle management
    async with neo4j_connection.session() as session:
        result = await session.run(
            "MATCH (v:Vulnerability)-[:AFFECTS]->(a:Asset) RETURN v, a"
        )
        records = await result.data()

**Configuration:**

.. code-block:: python

    # Neo4j connection settings
    NEO4J_URL = "bolt://localhost:7687"
    NEO4J_USER = "neo4j"
    NEO4J_PASSWORD = "pitas_neo4j"
    
    # Connection pool settings
    MAX_CONNECTION_LIFETIME = 3600
    MAX_CONNECTION_POOL_SIZE = 50
    CONNECTION_ACQUISITION_TIMEOUT = 60

Graph Schema Design
~~~~~~~~~~~~~~~~~~~

**Node Types:**

.. code-block:: cypher

    // Vulnerability nodes
    CREATE (v:Vulnerability {
        id: $vulnerability_id,
        cve_id: $cve_id,
        cvss_score: $cvss_score,
        severity: $severity,
        discovery_date: $discovery_date,
        created_at: datetime()
    })

    // Asset nodes
    CREATE (a:Asset {
        id: $asset_id,
        name: $name,
        type: $asset_type,
        business_criticality: $business_criticality,
        created_at: datetime()
    })

    // Technique nodes (MITRE ATT&CK)
    CREATE (t:Technique {
        mitre_id: $technique_id,
        name: $technique_name,
        tactic: $tactic,
        platform: $platform
    })

    // Mitigation nodes
    CREATE (m:Mitigation {
        control_id: $control_id,
        name: $control_name,
        effectiveness: $effectiveness_rating,
        type: $mitigation_type
    })

**Relationship Types:**

.. code-block:: cypher

    // Vulnerability affects asset
    CREATE (v:Vulnerability)-[:AFFECTS {
        impact_level: $impact,
        exploitability_likelihood: $likelihood,
        discovered_date: datetime()
    }]->(a:Asset)

    // Technique exploits vulnerability
    CREATE (t:Technique)-[:EXPLOITS {
        likelihood: $probability,
        complexity: $complexity
    }]->(v:Vulnerability)

    // Mitigation controls vulnerability
    CREATE (m:Mitigation)-[:MITIGATES {
        coverage: $percentage,
        effectiveness: $rating
    }]->(v:Vulnerability)

    // Asset dependencies
    CREATE (a1:Asset)-[:DEPENDS_ON {
        dependency_type: $type,
        criticality: $level
    }]->(a2:Asset)

Vulnerability Graph Service
---------------------------

Core Graph Operations
~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: pitas.db.neo4j.VulnerabilityGraphService
   :members:
   :undoc-members:
   :show-inheritance:

**Node Creation:**

.. automethod:: pitas.db.neo4j.VulnerabilityGraphService.create_vulnerability_node

.. automethod:: pitas.db.neo4j.VulnerabilityGraphService.create_asset_node

**Relationship Management:**

.. automethod:: pitas.db.neo4j.VulnerabilityGraphService.create_vulnerability_affects_asset

Attack Path Analysis
~~~~~~~~~~~~~~~~~~~~

Advanced attack path discovery using graph traversal algorithms:

.. code-block:: python

    async def find_attack_paths(
        self,
        start_asset_id: str,
        target_asset_id: str,
        max_depth: int = 5
    ) -> List[Dict[str, Any]]:
        """Find potential attack paths between assets."""
        
        query = """
        MATCH path = shortestPath(
            (start:Asset {id: $start_asset_id})-[*1..$max_depth]->
            (target:Asset {id: $target_asset_id})
        )
        WHERE ALL(rel IN relationships(path) 
                  WHERE type(rel) IN ['AFFECTS', 'DEPENDS_ON', 'CONNECTS_TO'])
        RETURN path,
               length(path) as path_length,
               [node IN nodes(path) | node.name] as asset_path,
               [rel IN relationships(path) | type(rel)] as relationship_types
        ORDER BY path_length ASC
        LIMIT 10
        """
        
        parameters = {
            "start_asset_id": start_asset_id,
            "target_asset_id": target_asset_id,
            "max_depth": max_depth
        }
        
        return await self.connection.execute_query(query, parameters)

**Multi-Hop Vulnerability Chaining:**

.. code-block:: cypher

    // Find vulnerability chains that could lead to privilege escalation
    MATCH path = (start:Asset)-[:AFFECTS*1..5]-(v:Vulnerability)
    WHERE start.business_criticality = 'low'
      AND v.severity IN ['critical', 'high']
      AND ANY(node IN nodes(path) 
              WHERE node:Asset AND node.business_criticality = 'critical')
    RETURN path,
           [vuln IN nodes(path) WHERE vuln:Vulnerability | vuln.cve_id] as cve_chain,
           length(path) as chain_length
    ORDER BY chain_length ASC

Vulnerability Clustering
~~~~~~~~~~~~~~~~~~~~~~~~

Machine learning-powered vulnerability clustering using graph algorithms:

.. code-block:: python

    async def cluster_similar_vulnerabilities(
        self,
        similarity_threshold: float = 0.8,
        clustering_algorithm: str = "louvain"
    ) -> List[Dict[str, Any]]:
        """Cluster vulnerabilities based on similarity metrics."""
        
        # Create similarity relationships
        similarity_query = """
        MATCH (v1:Vulnerability), (v2:Vulnerability)
        WHERE v1.id <> v2.id
          AND v1.severity = v2.severity
          AND abs(v1.cvss_score - v2.cvss_score) <= $threshold
        
        // Calculate Jaccard similarity for affected assets
        WITH v1, v2,
             [(v1)-[:AFFECTS]->(a) | a.id] as assets1,
             [(v2)-[:AFFECTS]->(a) | a.id] as assets2
        
        WITH v1, v2, assets1, assets2,
             size([a IN assets1 WHERE a IN assets2]) as intersection,
             size(assets1 + [a IN assets2 WHERE NOT a IN assets1]) as union
        
        WHERE union > 0 AND (intersection * 1.0 / union) >= $threshold
        
        CREATE (v1)-[:SIMILAR_TO {
            similarity_score: intersection * 1.0 / union,
            created_at: datetime()
        }]->(v2)
        """
        
        await self.connection.execute_write_query(
            similarity_query, 
            {"threshold": similarity_threshold}
        )
        
        # Apply clustering algorithm
        clustering_query = f"""
        CALL gds.{clustering_algorithm}.stream('vulnerability-similarity-graph')
        YIELD nodeId, communityId
        MATCH (v:Vulnerability) WHERE id(v) = nodeId
        RETURN communityId as cluster_id,
               collect(v.id) as vulnerability_ids,
               collect(v.cve_id) as cve_ids,
               size(collect(v)) as cluster_size
        ORDER BY cluster_size DESC
        """
        
        return await self.connection.execute_query(clustering_query)

Network Topology Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    class NetworkTopologyAnalyzer:
        """Analyze network topology for security insights."""
        
        async def analyze_network_segmentation(self) -> Dict[str, Any]:
            """Analyze network segmentation effectiveness."""
            
            query = """
            // Find assets in different network segments
            MATCH (a1:Asset)-[r:CONNECTS_TO]-(a2:Asset)
            WHERE a1.network_segment <> a2.network_segment
            
            WITH a1.network_segment as segment1,
                 a2.network_segment as segment2,
                 count(r) as connections
            
            RETURN segment1, segment2, connections
            ORDER BY connections DESC
            """
            
            cross_segment_connections = await self.execute_query(query)
            
            # Analyze vulnerability exposure across segments
            exposure_query = """
            MATCH (v:Vulnerability)-[:AFFECTS]->(a:Asset)
            WITH a.network_segment as segment,
                 v.severity as severity,
                 count(v) as vuln_count
            
            RETURN segment,
                   collect({severity: severity, count: vuln_count}) as vulnerabilities
            ORDER BY segment
            """
            
            segment_exposure = await self.execute_query(exposure_query)
            
            return {
                "cross_segment_connections": cross_segment_connections,
                "segment_vulnerability_exposure": segment_exposure
            }

Graph Analytics Algorithms
--------------------------

Centrality Analysis
~~~~~~~~~~~~~~~~~~~

Identify critical assets and vulnerabilities using centrality measures:

.. code-block:: cypher

    // PageRank centrality for asset importance
    CALL gds.pageRank.stream('asset-vulnerability-graph')
    YIELD nodeId, score
    MATCH (a:Asset) WHERE id(a) = nodeId
    RETURN a.name as asset_name,
           a.business_criticality as criticality,
           score as importance_score
    ORDER BY score DESC
    LIMIT 20

    // Betweenness centrality for attack path bottlenecks
    CALL gds.betweenness.stream('asset-vulnerability-graph')
    YIELD nodeId, score
    MATCH (n) WHERE id(n) = nodeId
    RETURN labels(n)[0] as node_type,
           CASE 
             WHEN n:Asset THEN n.name
             WHEN n:Vulnerability THEN n.cve_id
           END as identifier,
           score as bottleneck_score
    ORDER BY score DESC

Community Detection
~~~~~~~~~~~~~~~~~~~

.. code-block:: cypher

    // Louvain community detection for vulnerability clusters
    CALL gds.louvain.stream('vulnerability-correlation-graph')
    YIELD nodeId, communityId
    MATCH (v:Vulnerability) WHERE id(v) = nodeId
    
    WITH communityId,
         collect(v.cve_id) as vulnerabilities,
         avg(v.cvss_score) as avg_cvss,
         collect(DISTINCT v.severity) as severities
    
    RETURN communityId as cluster_id,
           vulnerabilities,
           avg_cvss,
           severities,
           size(vulnerabilities) as cluster_size
    ORDER BY cluster_size DESC

Shortest Path Analysis
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    async def find_critical_attack_vectors(
        self,
        target_criticality: str = "critical"
    ) -> List[Dict[str, Any]]:
        """Find shortest attack paths to critical assets."""
        
        query = """
        MATCH (target:Asset {business_criticality: $target_criticality})
        MATCH (entry:Asset)
        WHERE entry.network_exposure = 'external'
          AND entry <> target
        
        MATCH path = shortestPath(
            (entry)-[*1..6]-(target)
        )
        WHERE ALL(rel IN relationships(path) 
                  WHERE type(rel) IN ['AFFECTS', 'DEPENDS_ON'])
        
        WITH path, entry, target,
             [node IN nodes(path) WHERE node:Vulnerability] as vulns,
             length(path) as path_length
        
        RETURN entry.name as entry_point,
               target.name as target_asset,
               [v IN vulns | v.cve_id] as vulnerability_chain,
               path_length,
               avg([v IN vulns | v.cvss_score]) as avg_severity
        ORDER BY path_length ASC, avg_severity DESC
        LIMIT 10
        """
        
        return await self.connection.execute_query(
            query, 
            {"target_criticality": target_criticality}
        )

Graph Visualization
-------------------

D3.js Integration
~~~~~~~~~~~~~~~~~

.. code-block:: javascript

    // Graph visualization using D3.js
    class VulnerabilityGraphVisualizer {
        constructor(containerId) {
            this.container = d3.select(`#${containerId}`);
            this.width = 1200;
            this.height = 800;
            this.setupSVG();
        }
        
        async loadGraphData(filters = {}) {
            const response = await fetch('/api/v1/analytics/graph-data', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(filters)
            });
            
            const data = await response.json();
            this.renderGraph(data);
        }
        
        renderGraph(data) {
            // Create force simulation
            const simulation = d3.forceSimulation(data.nodes)
                .force("link", d3.forceLink(data.links).id(d => d.id))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(this.width / 2, this.height / 2));
            
            // Render nodes and links
            this.renderNodes(data.nodes);
            this.renderLinks(data.links);
            
            // Update positions on simulation tick
            simulation.on("tick", () => this.updatePositions());
        }
    }

Interactive Features
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    @router.get("/analytics/graph-data")
    async def get_graph_visualization_data(
        filters: GraphFilters = Depends(),
        db: AsyncSession = Depends(get_db)
    ):
        """Get graph data for visualization."""
        
        # Build Cypher query based on filters
        query_parts = ["MATCH (v:Vulnerability)-[r:AFFECTS]->(a:Asset)"]
        
        if filters.severity:
            query_parts.append(f"WHERE v.severity IN {filters.severity}")
        
        if filters.asset_criticality:
            query_parts.append(f"AND a.business_criticality IN {filters.asset_criticality}")
        
        query_parts.extend([
            "RETURN v, r, a",
            f"LIMIT {filters.limit or 100}"
        ])
        
        query = " ".join(query_parts)
        results = await vulnerability_graph_service.execute_query(query)
        
        # Transform to D3.js format
        nodes = []
        links = []
        
        for record in results:
            vuln = record['v']
            asset = record['a']
            relationship = record['r']
            
            # Add nodes
            nodes.append({
                'id': vuln['id'],
                'type': 'vulnerability',
                'label': vuln['cve_id'],
                'severity': vuln['severity'],
                'cvss_score': vuln['cvss_score']
            })
            
            nodes.append({
                'id': asset['id'],
                'type': 'asset',
                'label': asset['name'],
                'criticality': asset['business_criticality']
            })
            
            # Add link
            links.append({
                'source': vuln['id'],
                'target': asset['id'],
                'type': 'affects',
                'impact_level': relationship['impact_level']
            })
        
        return {
            'nodes': nodes,
            'links': links,
            'metadata': {
                'total_vulnerabilities': len([n for n in nodes if n['type'] == 'vulnerability']),
                'total_assets': len([n for n in nodes if n['type'] == 'asset']),
                'total_relationships': len(links)
            }
        }

Performance Optimization
------------------------

Query Optimization
~~~~~~~~~~~~~~~~~~

.. code-block:: cypher

    // Optimized query with proper indexing
    CREATE INDEX vulnerability_severity_idx FOR (v:Vulnerability) ON (v.severity);
    CREATE INDEX asset_criticality_idx FOR (a:Asset) ON (a.business_criticality);
    CREATE INDEX affects_impact_idx FOR ()-[r:AFFECTS]-() ON (r.impact_level);

    // Use query hints for complex traversals
    MATCH (start:Asset {id: $asset_id})
    USING INDEX start:Asset(id)
    MATCH path = (start)-[:AFFECTS*1..3]-(v:Vulnerability)
    WHERE v.severity = 'critical'
    RETURN path

Caching Strategy
~~~~~~~~~~~~~~~~

.. code-block:: python

    class GraphQueryCache:
        """Cache frequently accessed graph query results."""
        
        def __init__(self, redis_client):
            self.redis = redis_client
            self.default_ttl = 300  # 5 minutes
        
        async def get_cached_result(self, query_hash: str):
            """Get cached query result."""
            cached = await self.redis.get(f"graph_query:{query_hash}")
            return json.loads(cached) if cached else None
        
        async def cache_result(
            self, 
            query_hash: str, 
            result: Any, 
            ttl: int = None
        ):
            """Cache query result."""
            await self.redis.setex(
                f"graph_query:{query_hash}",
                ttl or self.default_ttl,
                json.dumps(result, default=str)
            )

Monitoring and Metrics
----------------------

Graph Database Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    class Neo4jMonitor:
        """Monitor Neo4j performance and health."""
        
        async def collect_metrics(self):
            """Collect Neo4j performance metrics."""
            
            # Query performance metrics
            query = """
            CALL dbms.queryJmx('org.neo4j:instance=kernel#0,name=Transactions')
            YIELD attributes
            RETURN attributes.NumberOfOpenTransactions as open_transactions,
                   attributes.NumberOfCommittedTransactions as committed_transactions
            """
            
            tx_metrics = await self.execute_query(query)
            
            # Memory usage
            memory_query = """
            CALL dbms.queryJmx('java.lang:type=Memory')
            YIELD attributes
            RETURN attributes.HeapMemoryUsage.used as heap_used,
                   attributes.HeapMemoryUsage.max as heap_max
            """
            
            memory_metrics = await self.execute_query(memory_query)
            
            return {
                'transactions': tx_metrics,
                'memory': memory_metrics,
                'timestamp': datetime.utcnow()
            }
