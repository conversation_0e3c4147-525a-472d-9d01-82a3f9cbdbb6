Vulnerability Data Models
==========================

Phase 3 introduces comprehensive data models for vulnerability management, risk assessment, and analytics. These models provide the foundation for advanced vulnerability tracking and correlation.

Core Vulnerability Models
--------------------------

Vulnerability Model
~~~~~~~~~~~~~~~~~~~

The primary model for tracking security vulnerabilities with comprehensive metadata and lifecycle management.

.. autoclass:: pitas.db.models.vulnerability.Vulnerability
   :members:
   :undoc-members:
   :show-inheritance:

**Key Features:**

* **CVE Integration** - Automatic CVE identifier validation and correlation
* **CVSS Scoring** - Support for CVSS 3.1 and 4.0 scoring systems
* **Lifecycle Tracking** - Complete vulnerability state management
* **Business Impact** - Correlation with business processes and criticality
* **Metadata Storage** - Flexible JSON storage for scanner-specific data

**Severity Levels:**

.. autoclass:: pitas.db.models.vulnerability.VulnerabilitySeverity
   :members:
   :undoc-members:

**Status Workflow:**

.. autoclass:: pitas.db.models.vulnerability.VulnerabilityStatus
   :members:
   :undoc-members:

Asset Model
~~~~~~~~~~~

Comprehensive asset inventory with business context and compliance requirements.

.. autoclass:: pitas.db.models.vulnerability.Asset
   :members:
   :undoc-members:
   :show-inheritance:

**Business Criticality:**

.. autoclass:: pitas.db.models.vulnerability.AssetCriticality
   :members:
   :undoc-members:

**Key Capabilities:**

* **Business Process Mapping** - Link assets to critical business functions
* **Compliance Tracking** - Associate regulatory requirements with assets
* **Network Context** - IP address and hostname management
* **Metadata Flexibility** - Custom attributes for asset classification

AssetVulnerability Association
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Many-to-many relationship model connecting assets with vulnerabilities, including impact assessment and remediation context.

.. autoclass:: pitas.db.models.vulnerability.AssetVulnerability
   :members:
   :undoc-members:
   :show-inheritance:

**Impact Assessment Fields:**

* **Impact Level** - Qualitative assessment of vulnerability impact on specific asset
* **Exploitability Likelihood** - Quantitative score (0.0-10.0) for exploitation probability
* **Remediation Priority** - Numerical ranking for remediation ordering
* **Remediation Effort** - Estimated effort level (low, medium, high)

VulnerabilityMetric Model
~~~~~~~~~~~~~~~~~~~~~~~~~

Time-series data model for tracking vulnerability metrics and analytics.

.. autoclass:: pitas.db.models.vulnerability.VulnerabilityMetric
   :members:
   :undoc-members:
   :show-inheritance:

**Metric Types:**

* **discovery_rate** - Rate of new vulnerability discovery
* **remediation_time** - Time from discovery to remediation
* **vulnerability_density** - Number of vulnerabilities per asset
* **risk_score** - Calculated risk scores over time
* **compliance_score** - Compliance posture metrics

Risk Assessment Models
----------------------

RiskAssessment Model
~~~~~~~~~~~~~~~~~~~~

Implementation of Risk-Based Vulnerability Management (RBVM) methodology with TruRisk scoring.

.. autoclass:: pitas.db.models.risk_assessment.RiskAssessment
   :members:
   :undoc-members:
   :show-inheritance:

**Risk Levels:**

.. autoclass:: pitas.db.models.risk_assessment.RiskLevel
   :members:
   :undoc-members:

**RBVM Components:**

* **Risk Score Calculation** - Composite score based on multiple factors
* **Business Impact Assessment** - Financial and operational impact modeling
* **Threat Likelihood** - Probability of successful exploitation
* **Environmental Factors** - Network segmentation and compensating controls
* **Confidence Scoring** - Assessment reliability and data quality metrics

ThreatIntelligence Model
~~~~~~~~~~~~~~~~~~~~~~~~

Contextual threat intelligence data for enhanced risk assessment.

.. autoclass:: pitas.db.models.risk_assessment.ThreatIntelligence
   :members:
   :undoc-members:
   :show-inheritance:

**Threat Actor Types:**

.. autoclass:: pitas.db.models.risk_assessment.ThreatActorType
   :members:
   :undoc-members:

**Intelligence Sources:**

* **Commercial Feeds** - Subscription-based threat intelligence
* **Open Source** - Community and government sources
* **Internal Research** - Organization-specific threat analysis
* **Partner Sharing** - Industry collaboration and sharing
* **Honeypot Data** - Internal threat detection systems

RemediationPlan Model
~~~~~~~~~~~~~~~~~~~~~

Comprehensive remediation planning and tracking with timeline management.

.. autoclass:: pitas.db.models.risk_assessment.RemediationPlan
   :members:
   :undoc-members:
   :show-inheritance:

**Remediation Types:**

* **patch** - Software patches and updates
* **configuration** - Configuration changes and hardening
* **mitigation** - Compensating controls and workarounds
* **replacement** - System or component replacement
* **decommission** - Asset retirement or removal

**Timeline Management:**

* **Planned Dates** - Scheduled start and completion dates
* **Actual Dates** - Real execution timeline tracking
* **Effort Estimation** - Resource allocation and planning
* **Progress Tracking** - Percentage completion monitoring

Database Schema Relationships
------------------------------

Entity Relationship Diagram
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: text

    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   Vulnerability │    │ AssetVulnerability│    │      Asset      │
    │                 │    │                 │    │                 │
    │ • id (PK)       │◄──►│ • vulnerability_id│◄──►│ • id (PK)       │
    │ • cve_id        │    │ • asset_id      │    │ • name          │
    │ • title         │    │ • impact_level  │    │ • asset_type    │
    │ • cvss_score    │    │ • priority      │    │ • criticality   │
    │ • severity      │    └─────────────────┘    │ • ip_address    │
    │ • status        │                           └─────────────────┘
    └─────────────────┘                                    │
             │                                             │
             │                                             │
             ▼                                             ▼
    ┌─────────────────┐                           ┌─────────────────┐
    │ RiskAssessment  │                           │VulnerabilityMetric│
    │                 │                           │                 │
    │ • vulnerability_id│                          │ • metric_type   │
    │ • asset_id      │                           │ • timestamp     │
    │ • risk_score    │                           │ • value         │
    │ • risk_level    │                           │ • asset_id      │
    │ • methodology   │                           │ • vuln_id       │
    └─────────────────┘                           └─────────────────┘
             │
             │
             ▼
    ┌─────────────────┐    ┌─────────────────┐
    │ThreatIntelligence│    │ RemediationPlan │
    │                 │    │                 │
    │ • vulnerability_id│    │ • vulnerability_id│
    │ • source        │    │ • priority      │
    │ • actor_type    │    │ • status        │
    │ • iocs          │    │ • timeline      │
    │ • ttps          │    │ • effort        │
    └─────────────────┘    └─────────────────┘

Indexing Strategy
~~~~~~~~~~~~~~~~~

**Performance Indexes:**

.. code-block:: sql

    -- Vulnerability indexes
    CREATE INDEX idx_vulnerability_cve ON vulnerabilities(cve_id);
    CREATE INDEX idx_vulnerability_severity ON vulnerabilities(severity);
    CREATE INDEX idx_vulnerability_status ON vulnerabilities(status);
    CREATE INDEX idx_vulnerability_discovery_date ON vulnerabilities(discovery_date);
    CREATE INDEX idx_vulnerability_cvss_score ON vulnerabilities(cvss_score);

    -- Asset indexes
    CREATE INDEX idx_asset_name ON assets(name);
    CREATE INDEX idx_asset_criticality ON assets(business_criticality);
    CREATE INDEX idx_asset_ip_address ON assets(ip_address);

    -- Association indexes
    CREATE INDEX idx_asset_vuln_asset ON asset_vulnerabilities(asset_id);
    CREATE INDEX idx_asset_vuln_vulnerability ON asset_vulnerabilities(vulnerability_id);
    CREATE INDEX idx_asset_vuln_impact ON asset_vulnerabilities(impact_level);

    -- Metrics indexes
    CREATE INDEX idx_vuln_metric_timestamp ON vulnerability_metrics(timestamp);
    CREATE INDEX idx_vuln_metric_type ON vulnerability_metrics(metric_type);

**Composite Indexes:**

.. code-block:: sql

    -- Multi-column indexes for common query patterns
    CREATE INDEX idx_vuln_severity_status ON vulnerabilities(severity, status);
    CREATE INDEX idx_asset_type_criticality ON assets(asset_type, business_criticality);
    CREATE INDEX idx_risk_level_score ON risk_assessments(risk_level, risk_score);

Data Validation and Constraints
--------------------------------

Field Validation
~~~~~~~~~~~~~~~~

**CVE ID Validation:**

.. code-block:: python

    @validator('cve_id')
    def validate_cve_id(cls, v):
        if v and not v.startswith('CVE-'):
            raise ValueError('CVE ID must start with "CVE-"')
        return v

**CVSS Score Constraints:**

.. code-block:: sql

    ALTER TABLE vulnerabilities 
    ADD CONSTRAINT cvss_score_range 
    CHECK (cvss_score >= 0.0 AND cvss_score <= 10.0);

**Risk Score Validation:**

.. code-block:: sql

    ALTER TABLE risk_assessments 
    ADD CONSTRAINT risk_score_range 
    CHECK (risk_score >= 0.0 AND risk_score <= 10.0);

Business Rules
~~~~~~~~~~~~~~

**Vulnerability Lifecycle:**

* Vulnerabilities must have a discovery date
* Remediation date cannot be before discovery date
* Verification date cannot be before remediation date
* Status transitions must follow defined workflow

**Asset Requirements:**

* Asset names must be unique within the system
* Business criticality must be assigned
* IP addresses must be valid IPv4 or IPv6 format

**Risk Assessment Rules:**

* Risk assessments require both vulnerability and asset
* Business impact and threat likelihood are mandatory
* Assessment date cannot be in the future
* Confidence level must be between 0.0 and 10.0

Migration Strategy
------------------

Database Migrations
~~~~~~~~~~~~~~~~~~~

Phase 3 models are deployed using Alembic migrations with proper dependency management:

.. code-block:: bash

    # Apply Phase 3 migrations
    alembic upgrade head

    # Rollback if needed
    alembic downgrade -1

**Migration Features:**

* **Incremental Updates** - Safe schema evolution
* **Data Preservation** - Existing data protection
* **Rollback Support** - Safe downgrade procedures
* **Index Management** - Performance optimization
* **Constraint Validation** - Data integrity enforcement

Data Import/Export
~~~~~~~~~~~~~~~~~~

**Bulk Import Support:**

.. code-block:: python

    # Example bulk vulnerability import
    from pitas.services.vulnerability import VulnerabilityService
    
    async def bulk_import_vulnerabilities(vulnerability_data):
        service = VulnerabilityService(db)
        results = await service.bulk_create(vulnerability_data)
        return results

**Export Capabilities:**

* **CSV Export** - Spreadsheet-compatible format
* **JSON Export** - API-compatible format
* **XML Export** - Legacy system integration
* **Custom Formats** - Configurable export templates
