Time-Series Vulnerability Metrics
==================================

Phase 3 implements comprehensive time-series analytics using InfluxDB to track vulnerability metrics, trends, and key performance indicators for data-driven security management.

InfluxDB Integration
--------------------

Database Connection
~~~~~~~~~~~~~~~~~~~

.. autoclass:: pitas.db.influxdb.InfluxDBConnection
   :members:
   :undoc-members:
   :show-inheritance:

**Connection Configuration:**

.. code-block:: python

    # InfluxDB connection settings
    INFLUXDB_URL = "http://localhost:8086"
    INFLUXDB_TOKEN = "pitas-dev-token"
    INFLUXDB_ORG = "pitas"
    INFLUXDB_BUCKET = "vulnerability-metrics"
    
    # Client configuration
    client = InfluxDBClient(
        url=INFLUXDB_URL,
        token=INFLUXDB_TOKEN,
        org=INFLUXDB_ORG,
        timeout=30000
    )

**Data Model:**

.. code-block:: python

    # InfluxDB Line Protocol format
    measurement,tag1=value1,tag2=value2 field1=value1,field2=value2 timestamp
    
    # Example vulnerability discovery metric
    vulnerability_discovery,severity=critical,source=nessus count=1,cvss_score=9.8 1640995200000000000

Vulnerability Metrics Service
-----------------------------

Core Metrics Collection
~~~~~~~~~~~~~~~~~~~~~~~

.. autoclass:: pitas.db.influxdb.VulnerabilityMetricsService
   :members:
   :undoc-members:
   :show-inheritance:

**Discovery Metrics:**

.. automethod:: pitas.db.influxdb.VulnerabilityMetricsService.write_vulnerability_discovery_metric

.. automethod:: pitas.db.influxdb.VulnerabilityMetricsService.write_vulnerability_remediation_metric

**Density Metrics:**

.. automethod:: pitas.db.influxdb.VulnerabilityMetricsService.write_vulnerability_density_metric

**Risk Metrics:**

.. automethod:: pitas.db.influxdb.VulnerabilityMetricsService.write_risk_score_metric

Metric Types and Schemas
-------------------------

Discovery Metrics
~~~~~~~~~~~~~~~~~

Track vulnerability discovery patterns and sources:

.. code-block:: python

    # Vulnerability discovery measurement
    {
        "measurement": "vulnerability_discovery",
        "tags": {
            "vulnerability_id": "uuid",
            "asset_id": "uuid",
            "severity": "critical|high|medium|low",
            "source": "scanner_name",
            "asset_type": "server|application|network"
        },
        "fields": {
            "count": 1,
            "cvss_score": 9.8,
            "discovery_time_hours": 2.5
        },
        "timestamp": "2024-01-15T10:30:00Z"
    }

**Query Examples:**

.. code-block:: flux

    // Daily vulnerability discovery rate
    from(bucket: "vulnerability-metrics")
      |> range(start: -30d)
      |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
      |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
      |> yield(name: "daily_discovery_rate")

    // Discovery by severity over time
    from(bucket: "vulnerability-metrics")
      |> range(start: -7d)
      |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
      |> group(columns: ["severity"])
      |> aggregateWindow(every: 1h, fn: sum, createEmpty: false)

Remediation Metrics
~~~~~~~~~~~~~~~~~~~

Track remediation effectiveness and timelines:

.. code-block:: python

    # Vulnerability remediation measurement
    {
        "measurement": "vulnerability_remediation",
        "tags": {
            "vulnerability_id": "uuid",
            "asset_id": "uuid",
            "severity": "critical|high|medium|low",
            "remediation_type": "patch|configuration|mitigation",
            "team": "security|infrastructure|development"
        },
        "fields": {
            "count": 1,
            "remediation_time_hours": 48.5,
            "effort_hours": 8.0,
            "success": 1
        },
        "timestamp": "2024-01-17T14:45:00Z"
    }

**SLA Tracking:**

.. code-block:: flux

    // Average remediation time by severity
    from(bucket: "vulnerability-metrics")
      |> range(start: -90d)
      |> filter(fn: (r) => r._measurement == "vulnerability_remediation")
      |> filter(fn: (r) => r._field == "remediation_time_hours")
      |> group(columns: ["severity"])
      |> mean()

    // SLA compliance rate
    from(bucket: "vulnerability-metrics")
      |> range(start: -30d)
      |> filter(fn: (r) => r._measurement == "vulnerability_remediation")
      |> filter(fn: (r) => r._field == "remediation_time_hours")
      |> map(fn: (r) => ({
          r with
          sla_met: if r.severity == "critical" and r._value <= 24.0 then 1.0
                   else if r.severity == "high" and r._value <= 72.0 then 1.0
                   else if r.severity == "medium" and r._value <= 168.0 then 1.0
                   else 0.0
      }))
      |> group(columns: ["severity"])
      |> mean(column: "sla_met")

Density Metrics
~~~~~~~~~~~~~~~

Track vulnerability density and concentration:

.. code-block:: python

    # Vulnerability density measurement
    {
        "measurement": "vulnerability_density",
        "tags": {
            "asset_id": "uuid",
            "asset_type": "server|application|network",
            "business_criticality": "critical|high|medium|low",
            "network_segment": "dmz|internal|external"
        },
        "fields": {
            "total_vulnerabilities": 25,
            "critical_vulnerabilities": 3,
            "high_vulnerabilities": 8,
            "medium_vulnerabilities": 10,
            "low_vulnerabilities": 4,
            "density_score": 2.5
        },
        "timestamp": "2024-01-15T12:00:00Z"
    }

**Density Analysis:**

.. code-block:: flux

    // Vulnerability density trend by asset criticality
    from(bucket: "vulnerability-metrics")
      |> range(start: -30d)
      |> filter(fn: (r) => r._measurement == "vulnerability_density")
      |> filter(fn: (r) => r._field == "density_score")
      |> group(columns: ["business_criticality"])
      |> aggregateWindow(every: 1d, fn: mean, createEmpty: false)

    // Top 10 most vulnerable assets
    from(bucket: "vulnerability-metrics")
      |> range(start: -1d)
      |> filter(fn: (r) => r._measurement == "vulnerability_density")
      |> filter(fn: (r) => r._field == "total_vulnerabilities")
      |> group(columns: ["asset_id"])
      |> last()
      |> sort(columns: ["_value"], desc: true)
      |> limit(n: 10)

Risk Score Metrics
~~~~~~~~~~~~~~~~~~

Track risk assessment trends and patterns:

.. code-block:: python

    # Risk score measurement
    {
        "measurement": "risk_score",
        "tags": {
            "vulnerability_id": "uuid",
            "asset_id": "uuid",
            "risk_level": "critical|high|medium|low|negligible",
            "methodology": "RBVM|CVSS|Custom",
            "assessor_type": "automated|manual"
        },
        "fields": {
            "risk_score": 8.5,
            "business_impact": 9.0,
            "threat_likelihood": 7.5,
            "confidence_level": 8.0
        },
        "timestamp": "2024-01-15T15:30:00Z"
    }

Analytics and Reporting
-----------------------

Trend Analysis
~~~~~~~~~~~~~~

.. code-block:: python

    class VulnerabilityTrendAnalyzer:
        """Analyze vulnerability trends and patterns."""
        
        async def analyze_discovery_trends(
            self,
            time_range: str = "30d",
            granularity: str = "1d"
        ) -> Dict[str, Any]:
            """Analyze vulnerability discovery trends."""
            
            query = f'''
            from(bucket: "{self.bucket}")
              |> range(start: -{time_range})
              |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
              |> aggregateWindow(every: {granularity}, fn: sum, createEmpty: false)
              |> group(columns: ["severity"])
              |> yield(name: "discovery_trend")
            '''
            
            result = self.connection.query_api.query(query)
            return self._process_trend_data(result)
        
        async def calculate_velocity_metrics(self) -> Dict[str, float]:
            """Calculate vulnerability management velocity metrics."""
            
            # Mean Time to Detection (MTTD)
            mttd_query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -30d)
              |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
              |> filter(fn: (r) => r._field == "discovery_time_hours")
              |> mean()
            '''
            
            # Mean Time to Remediation (MTTR)
            mttr_query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -30d)
              |> filter(fn: (r) => r._measurement == "vulnerability_remediation")
              |> filter(fn: (r) => r._field == "remediation_time_hours")
              |> mean()
            '''
            
            mttd_result = self.connection.query_api.query(mttd_query)
            mttr_result = self.connection.query_api.query(mttr_query)
            
            return {
                "mean_time_to_detection_hours": self._extract_value(mttd_result),
                "mean_time_to_remediation_hours": self._extract_value(mttr_result),
                "velocity_score": self._calculate_velocity_score(mttd_result, mttr_result)
            }

Predictive Analytics
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    class VulnerabilityPredictor:
        """Predict vulnerability trends using time-series analysis."""
        
        async def predict_discovery_rate(
            self,
            forecast_days: int = 30
        ) -> Dict[str, Any]:
            """Predict future vulnerability discovery rate."""
            
            # Get historical data
            historical_query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -90d)
              |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
              |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
              |> group(columns: ["severity"])
            '''
            
            historical_data = self.connection.query_api.query(historical_query)
            
            # Apply time-series forecasting
            predictions = {}
            for severity in ['critical', 'high', 'medium', 'low']:
                severity_data = self._filter_by_severity(historical_data, severity)
                prediction = self._apply_arima_forecast(severity_data, forecast_days)
                predictions[severity] = prediction
            
            return {
                "forecast_period_days": forecast_days,
                "predictions": predictions,
                "confidence_intervals": self._calculate_confidence_intervals(predictions),
                "model_accuracy": self._calculate_model_accuracy()
            }
        
        def _apply_arima_forecast(self, data: List, forecast_days: int):
            """Apply ARIMA forecasting model."""
            # Simplified ARIMA implementation
            # In production, use statsmodels or similar library
            
            values = [point['_value'] for point in data]
            
            # Simple moving average for demonstration
            window_size = min(7, len(values))
            if len(values) >= window_size:
                recent_avg = sum(values[-window_size:]) / window_size
                trend = (values[-1] - values[-window_size]) / window_size
            else:
                recent_avg = sum(values) / len(values) if values else 0
                trend = 0
            
            forecast = []
            for day in range(forecast_days):
                predicted_value = recent_avg + (trend * day)
                forecast.append(max(0, predicted_value))  # Ensure non-negative
            
            return forecast

Dashboard Metrics
~~~~~~~~~~~~~~~~~

.. code-block:: python

    class VulnerabilityDashboard:
        """Generate dashboard metrics and KPIs."""
        
        async def get_executive_summary(self) -> Dict[str, Any]:
            """Get executive-level vulnerability metrics."""
            
            # Current vulnerability count by severity
            current_vulns = await self._get_current_vulnerability_count()
            
            # Trend over last 30 days
            trend_data = await self._get_30_day_trend()
            
            # SLA compliance
            sla_compliance = await self._get_sla_compliance()
            
            # Risk posture
            risk_posture = await self._get_risk_posture()
            
            return {
                "current_vulnerabilities": current_vulns,
                "30_day_trend": trend_data,
                "sla_compliance": sla_compliance,
                "risk_posture": risk_posture,
                "generated_at": datetime.utcnow().isoformat()
            }
        
        async def _get_current_vulnerability_count(self) -> Dict[str, int]:
            """Get current vulnerability count by severity."""
            
            query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -1d)
              |> filter(fn: (r) => r._measurement == "vulnerability_density")
              |> filter(fn: (r) => r._field =~ /.*_vulnerabilities/)
              |> group(columns: ["_field"])
              |> sum()
            '''
            
            result = self.connection.query_api.query(query)
            return self._process_count_data(result)

Real-Time Alerting
------------------

Threshold-Based Alerts
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    class VulnerabilityAlerting:
        """Real-time alerting for vulnerability metrics."""
        
        def __init__(self, influxdb_client, alert_manager):
            self.client = influxdb_client
            self.alert_manager = alert_manager
            self.thresholds = {
                "critical_discovery_rate": 5,  # per day
                "high_discovery_rate": 20,     # per day
                "remediation_sla_breach": 0.8, # 80% compliance minimum
                "vulnerability_density": 10    # per asset
            }
        
        async def check_discovery_rate_alerts(self):
            """Check for unusual vulnerability discovery rates."""
            
            query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -1d)
              |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
              |> filter(fn: (r) => r.severity == "critical")
              |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
            '''
            
            result = self.client.query_api.query(query)
            current_rate = self._extract_latest_value(result)
            
            if current_rate > self.thresholds["critical_discovery_rate"]:
                await self.alert_manager.send_alert({
                    "type": "high_critical_discovery_rate",
                    "severity": "warning",
                    "message": f"Critical vulnerability discovery rate ({current_rate}/day) "
                              f"exceeds threshold ({self.thresholds['critical_discovery_rate']}/day)",
                    "current_value": current_rate,
                    "threshold": self.thresholds["critical_discovery_rate"]
                })

Anomaly Detection
~~~~~~~~~~~~~~~~~

.. code-block:: python

    class AnomalyDetector:
        """Detect anomalies in vulnerability metrics."""
        
        async def detect_discovery_anomalies(self) -> List[Dict[str, Any]]:
            """Detect anomalies in vulnerability discovery patterns."""
            
            # Get baseline statistics
            baseline_query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -30d, stop: -7d)
              |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
              |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
              |> group(columns: ["severity"])
              |> mean()
            '''
            
            # Get recent data
            recent_query = '''
            from(bucket: "vulnerability-metrics")
              |> range(start: -7d)
              |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
              |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
              |> group(columns: ["severity"])
            '''
            
            baseline = self.client.query_api.query(baseline_query)
            recent = self.client.query_api.query(recent_query)
            
            anomalies = []
            for severity in ['critical', 'high', 'medium', 'low']:
                baseline_avg = self._get_severity_average(baseline, severity)
                recent_values = self._get_severity_values(recent, severity)
                
                for value in recent_values:
                    # Simple threshold-based anomaly detection
                    # In production, use more sophisticated algorithms
                    if value > baseline_avg * 2.0:  # 200% increase
                        anomalies.append({
                            "severity": severity,
                            "value": value,
                            "baseline": baseline_avg,
                            "anomaly_score": value / baseline_avg,
                            "type": "spike"
                        })
            
            return anomalies

Performance Optimization
------------------------

Data Retention Policies
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    # InfluxDB retention policies
    retention_policies = {
        "raw_data": "30d",      # Raw metrics for 30 days
        "hourly_aggregates": "1y",   # Hourly aggregates for 1 year
        "daily_aggregates": "5y",    # Daily aggregates for 5 years
        "monthly_aggregates": "10y"  # Monthly aggregates for 10 years
    }

    # Continuous queries for downsampling
    continuous_queries = [
        '''
        CREATE CONTINUOUS QUERY "hourly_vulnerability_discovery" ON "vulnerability_metrics"
        BEGIN
          SELECT sum("count") AS "count", mean("cvss_score") AS "avg_cvss"
          INTO "vulnerability_metrics"."hourly"."vulnerability_discovery"
          FROM "vulnerability_discovery"
          GROUP BY time(1h), "severity", "source"
        END
        ''',
        '''
        CREATE CONTINUOUS QUERY "daily_vulnerability_density" ON "vulnerability_metrics"
        BEGIN
          SELECT last("total_vulnerabilities") AS "total_vulnerabilities",
                 last("critical_vulnerabilities") AS "critical_vulnerabilities"
          INTO "vulnerability_metrics"."daily"."vulnerability_density"
          FROM "vulnerability_density"
          GROUP BY time(1d), "asset_id", "business_criticality"
        END
        '''
    ]

Query Optimization
~~~~~~~~~~~~~~~~~~

.. code-block:: flux

    // Optimized query with proper filtering and grouping
    from(bucket: "vulnerability-metrics")
      |> range(start: -30d)
      |> filter(fn: (r) => r._measurement == "vulnerability_discovery")
      |> filter(fn: (r) => r.severity == "critical")  // Filter early
      |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
      |> group(columns: ["source"])  // Group after aggregation
      |> yield(name: "critical_discovery_by_source")

    // Use pushdown predicates for better performance
    from(bucket: "vulnerability-metrics")
      |> range(start: -7d)
      |> filter(fn: (r) => r._measurement == "vulnerability_remediation" and 
                           r._field == "remediation_time_hours" and
                           r.severity == "critical")
      |> mean()
