Phase 3 API Reference
=====================

Complete API reference for Phase 3 vulnerability assessment and density tracking functionality.

Vulnerability Management APIs
-----------------------------

Core Vulnerability Operations
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.api.v1.endpoints.vulnerabilities
   :members:
   :undoc-members:
   :show-inheritance:

Asset Management APIs
~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.api.v1.endpoints.assets
   :members:
   :undoc-members:
   :show-inheritance:

Risk Assessment APIs
~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.api.v1.endpoints.risk_assessments
   :members:
   :undoc-members:
   :show-inheritance:

Analytics APIs
~~~~~~~~~~~~~~

.. automodule:: pitas.api.v1.endpoints.analytics
   :members:
   :undoc-members:
   :show-inheritance:

Service Layer Reference
-----------------------

Vulnerability Services
~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.services.vulnerability
   :members:
   :undoc-members:
   :show-inheritance:

Asset Services
~~~~~~~~~~~~~~

.. automodule:: pitas.services.asset
   :members:
   :undoc-members:
   :show-inheritance:

Risk Assessment Services
~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.services.risk_assessment
   :members:
   :undoc-members:
   :show-inheritance:

Database Integration Reference
------------------------------

Neo4j Graph Database
~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.db.neo4j
   :members:
   :undoc-members:
   :show-inheritance:

InfluxDB Time-Series Database
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.db.influxdb
   :members:
   :undoc-members:
   :show-inheritance:

Schema Reference
----------------

Vulnerability Schemas
~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.schemas.vulnerability
   :members:
   :undoc-members:
   :show-inheritance:

Risk Assessment Schemas
~~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.schemas.risk_assessment
   :members:
   :undoc-members:
   :show-inheritance:

Model Reference
---------------

Database Models
~~~~~~~~~~~~~~~

.. automodule:: pitas.db.models.vulnerability
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pitas.db.models.risk_assessment
   :members:
   :undoc-members:
   :show-inheritance:

Configuration Reference
-----------------------

Phase 3 Configuration
~~~~~~~~~~~~~~~~~~~~~

.. automodule:: pitas.core.config
   :members: neo4j_url, neo4j_user, neo4j_password, influxdb_url, influxdb_token, influxdb_org, influxdb_bucket
   :show-inheritance:

Error Handling
--------------

Exception Classes
~~~~~~~~~~~~~~~~~

.. automodule:: pitas.core.exceptions
   :members:
   :undoc-members:
   :show-inheritance:

Utility Functions
-----------------

Common Utilities
~~~~~~~~~~~~~~~~

.. automodule:: pitas.utils.vulnerability
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pitas.utils.risk_calculation
   :members:
   :undoc-members:
   :show-inheritance:
