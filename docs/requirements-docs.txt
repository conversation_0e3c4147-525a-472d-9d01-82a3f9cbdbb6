# Documentation requirements for PITAS
# Install with: pip install -r requirements-docs.txt

# Core Sphinx packages
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
sphinx-autodoc-typehints>=1.24.0
sphinx-autobuild>=2021.3.14

# Markdown support
myst-parser>=2.0.0

# API documentation
sphinx-autoapi>=3.0.0

# Additional extensions
sphinx-copybutton>=0.5.2
sphinx-tabs>=3.4.1
sphinx-design>=0.5.0
sphinxcontrib-openapi>=0.8.1
sphinxcontrib-httpdomain>=1.8.1

# Code quality and linting for docs
doc8>=1.1.1
rstcheck>=6.1.2

# Spell checking
sphinxcontrib-spelling>=8.0.0

# PDF generation
rst2pdf>=0.101

# Live reload for development
watchdog>=3.0.0

# Diagram support
sphinxcontrib-mermaid>=0.9.2
sphinxcontrib-plantuml>=0.25

# Performance and optimization
sphinx-notfound-page>=1.0.0
sphinx-sitemap>=2.5.1

# Analytics and tracking
sphinxext-opengraph>=0.8.2

# Version management
sphinx-multiversion>=0.2.4
