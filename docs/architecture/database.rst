Database Architecture
====================

The PITAS Training System uses a sophisticated database schema designed to support comprehensive training and competency management. This document details the database architecture, relationships, and design decisions.

🗄️ Database Schema Overview
----------------------------

The database consists of 20+ core tables organized into logical domains across Phase 5 and Phase 6:

.. mermaid::

   erDiagram
       USERS {
           uuid id PK
           string email UK
           string username UK
           string full_name
           string hashed_password
           boolean is_active
           boolean is_superuser
           datetime last_login
           datetime created_at
           datetime updated_at
       }
       
       COMPETENCY_FRAMEWORKS {
           uuid id PK
           string name UK
           text description
           string version
           string work_role_id
           string specialty_area
           string category
           datetime created_at
           datetime updated_at
       }
       
       COMPETENCIES {
           uuid id PK
           uuid framework_id FK
           string competency_id
           string name
           text description
           json knowledge_statements
           json skill_statements
           json ability_statements
           datetime created_at
           datetime updated_at
       }
       
       SKILL_ASSESSMENTS {
           uuid id PK
           uuid user_id FK
           uuid competency_id FK
           enum current_level
           enum target_level
           datetime assessment_date
           uuid assessor_id FK
           text notes
           json evidence
           datetime created_at
           datetime updated_at
       }
       
       TRAINING_COURSES {
           uuid id PK
           string title
           text description
           string provider
           string course_code
           integer duration_hours
           enum difficulty_level
           json prerequisites
           json learning_objectives
           json competencies_addressed
           boolean is_certification_prep
           uuid certification_id FK
           float cost
           boolean is_active
           datetime created_at
           datetime updated_at
       }
       
       TRAINING_ENROLLMENTS {
           uuid id PK
           uuid user_id FK
           uuid course_id FK
           uuid learning_path_id FK
           datetime enrollment_date
           datetime start_date
           datetime completion_date
           enum status
           float progress_percentage
           json assessment_scores
           json practical_scores
           float time_spent_hours
           datetime created_at
           datetime updated_at
       }
       
       LEARNING_PATHS {
           uuid id PK
           uuid user_id FK
           string name
           text description
           string target_role
           integer estimated_duration_weeks
           json course_sequence
           json competency_goals
           boolean is_active
           float completion_percentage
           datetime created_at
           datetime updated_at
       }
       
       CERTIFICATIONS {
           uuid id PK
           string name
           string abbreviation
           string provider
           text description
           enum level
           json prerequisites
           integer renewal_period_years
           integer cpe_credits_required
           float exam_cost
           boolean is_active
           datetime created_at
           datetime updated_at
       }
       
       CERTIFICATION_ACHIEVEMENTS {
           uuid id PK
           uuid user_id FK
           uuid certification_id FK
           enum status
           datetime achievement_date
           datetime expiration_date
           string credential_id
           integer cpe_credits_earned
           boolean renewal_reminder_sent
           float cost_reimbursed
           datetime created_at
           datetime updated_at
       }
       
       CTF_CHALLENGES {
           uuid id PK
           string title
           text description
           string category
           enum difficulty
           integer points
           string flag
           json hints
           json files
           json competencies_tested
           boolean is_active
           uuid created_by FK
           datetime created_at
           datetime updated_at
       }
       
       CTF_SUBMISSIONS {
           uuid id PK
           uuid user_id FK
           uuid challenge_id FK
           datetime submission_time
           string submitted_flag
           boolean is_correct
           integer points_awarded
           integer time_to_solve_minutes
           datetime created_at
           datetime updated_at
       }
       
       MENTORSHIP_PAIRS {
           uuid id PK
           uuid mentor_id FK
           uuid mentee_id FK
           datetime start_date
           datetime end_date
           json goals
           string meeting_frequency
           boolean is_active
           float satisfaction_rating
           datetime created_at
           datetime updated_at
       }
       
       MENTORSHIP_SESSIONS {
           uuid id PK
           uuid pair_id FK
           datetime session_date
           integer duration_minutes
           json topics_discussed
           json action_items
           text mentor_notes
           text mentee_feedback
           datetime created_at
           datetime updated_at
       }
       
       %% Relationships
       COMPETENCY_FRAMEWORKS ||--o{ COMPETENCIES : contains
       COMPETENCIES ||--o{ SKILL_ASSESSMENTS : assesses
       USERS ||--o{ SKILL_ASSESSMENTS : takes
       USERS ||--o{ SKILL_ASSESSMENTS : assesses
       
       CERTIFICATIONS ||--o{ TRAINING_COURSES : prepares_for
       TRAINING_COURSES ||--o{ TRAINING_ENROLLMENTS : enrolled_in
       USERS ||--o{ TRAINING_ENROLLMENTS : enrolls
       LEARNING_PATHS ||--o{ TRAINING_ENROLLMENTS : includes
       USERS ||--o{ LEARNING_PATHS : owns
       
       CERTIFICATIONS ||--o{ CERTIFICATION_ACHIEVEMENTS : achieves
       USERS ||--o{ CERTIFICATION_ACHIEVEMENTS : earns
       
       USERS ||--o{ CTF_CHALLENGES : creates
       CTF_CHALLENGES ||--o{ CTF_SUBMISSIONS : receives
       USERS ||--o{ CTF_SUBMISSIONS : submits
       
       USERS ||--o{ MENTORSHIP_PAIRS : mentors
       USERS ||--o{ MENTORSHIP_PAIRS : mentored_by
       MENTORSHIP_PAIRS ||--o{ MENTORSHIP_SESSIONS : conducts

       %% Phase 6 Tables
       INDIVIDUAL_DEVELOPMENT_PLANS {
           uuid id PK
           uuid user_id FK
           uuid manager_id FK
           string title
           text description
           date start_date
           date end_date
           enum status
           json focus_areas
           json success_metrics
           float progress_percentage
           datetime created_at
           datetime updated_at
       }

       DEVELOPMENT_GOALS {
           uuid id PK
           uuid idp_id FK
           string title
           text description
           string category
           string priority
           date target_date
           json success_criteria
           json milestones
           float progress_percentage
           enum status
           text notes
           datetime created_at
           datetime updated_at
       }

       WELLNESS_PROGRAMS {
           uuid id PK
           string name
           text description
           string program_type
           integer duration_weeks
           string target_participants
           json wellness_dimensions
           json activities
           json success_metrics
           boolean is_active
           datetime start_date
           datetime end_date
           datetime created_at
           datetime updated_at
       }

       WELLNESS_ASSESSMENTS {
           uuid id PK
           uuid user_id FK
           string assessment_type
           string assessment_period
           json metrics
           json qualitative_feedback
           float overall_score
           json recommendations
           datetime assessment_date
           datetime created_at
           datetime updated_at
       }

       RECOGNITION_NOMINATIONS {
           uuid id PK
           uuid nominator_id FK
           uuid nominee_id FK
           string recognition_type
           string category
           string title
           text description
           text impact_description
           json evidence
           json skills_demonstrated
           string visibility
           enum status
           integer points_awarded
           string badge_earned
           datetime created_at
           datetime updated_at
       }

       %% Phase 6 Relationships
       USERS ||--o{ INDIVIDUAL_DEVELOPMENT_PLANS : has_manager
       USERS ||--o{ INDIVIDUAL_DEVELOPMENT_PLANS : has_employee
       INDIVIDUAL_DEVELOPMENT_PLANS ||--o{ DEVELOPMENT_GOALS : contains

       USERS ||--o{ WELLNESS_ASSESSMENTS : takes

       USERS ||--o{ RECOGNITION_NOMINATIONS : nominates
       USERS ||--o{ RECOGNITION_NOMINATIONS : nominated

🏗️ Design Principles
---------------------

**1. UUID Primary Keys**
   * Globally unique identifiers
   * Better security (no sequential IDs)
   * Distributed system compatibility
   * Merge-friendly for replication

**2. Audit Trail Fields**
   * ``created_at`` and ``updated_at`` on all tables
   * Automatic timestamp management
   * Change tracking capability
   * Compliance and debugging support

**3. JSON Columns for Flexibility**
   * ``knowledge_statements``, ``skill_statements``, ``ability_statements``
   * ``prerequisites``, ``learning_objectives``, ``competencies_addressed``
   * ``assessment_scores``, ``practical_scores``
   * ``goals``, ``topics_discussed``, ``action_items``

**4. Enum Types for Consistency**
   * ``CompetencyLevel``: novice, advanced_beginner, competent, proficient, expert
   * ``TrainingStatus``: not_started, in_progress, completed, failed, expired
   * ``CertificationStatus``: not_started, in_progress, achieved, expired, renewal_required

📊 Table Relationships
----------------------

**Core Entity Relationships:**

1. **User-Centric Design**
   * Users are central to all training activities
   * One-to-many relationships with assessments, enrollments, achievements
   * Many-to-many relationships through junction tables

2. **Competency Framework Hierarchy**
   * Frameworks contain multiple competencies
   * Competencies are assessed through skill assessments
   * Hierarchical structure supports NICE framework organization

3. **Training Course Ecosystem**
   * Courses can prepare for certifications
   * Users enroll in courses through training enrollments
   * Learning paths orchestrate course sequences

4. **Mentorship Network**
   * Pairs connect mentors and mentees
   * Sessions track individual mentorship meetings
   * Flexible goal and feedback tracking

🔍 Indexing Strategy
--------------------

**Primary Indexes:**
   * All primary keys (UUID) are automatically indexed
   * Unique constraints on email and username
   * Foreign key indexes for relationship queries

**Performance Indexes:**
   * ``users.email`` and ``users.username`` for authentication
   * ``training_enrollments.user_id`` for user course queries
   * ``skill_assessments.user_id`` for competency tracking
   * ``ctf_submissions.challenge_id`` for leaderboard queries

**Composite Indexes:**
   * ``(user_id, course_id)`` on training_enrollments for enrollment checks
   * ``(user_id, certification_id)`` on certification_achievements
   * ``(challenge_id, is_correct)`` on ctf_submissions for scoring

📈 Scalability Considerations
-----------------------------

**Partitioning Strategy:**
   * Time-based partitioning for audit tables
   * User-based partitioning for large datasets
   * Archive strategy for historical data

**Query Optimization:**
   * Eager loading for related entities
   * Pagination for large result sets
   * Caching for frequently accessed data

**Connection Management:**
   * Connection pooling with SQLAlchemy
   * Async database operations
   * Read replica support for reporting

🔒 Security Features
--------------------

**Data Protection:**
   * Password hashing with bcrypt
   * Sensitive data encryption at rest
   * Row-level security for multi-tenant scenarios

**Access Control:**
   * Role-based permissions
   * Audit logging for all modifications
   * Soft deletes for data retention

**Compliance:**
   * GDPR-compliant personal data handling
   * Audit trails for compliance reporting
   * Data retention policies

🧪 Testing Strategy
-------------------

**Database Testing:**
   * Unit tests with in-memory SQLite
   * Integration tests with test PostgreSQL
   * Migration testing with schema validation

**Data Integrity:**
   * Foreign key constraint testing
   * Unique constraint validation
   * Enum value validation

**Performance Testing:**
   * Load testing with realistic data volumes
   * Query performance benchmarking
   * Index effectiveness analysis

🔄 Migration Management
-----------------------

**Alembic Integration:**
   * Version-controlled schema changes
   * Automatic migration generation
   * Rollback capability for safe deployments

**Migration Best Practices:**
   * Backward-compatible changes when possible
   * Data migration scripts for complex changes
   * Testing migrations in staging environments

**Schema Evolution:**
   * Additive changes preferred
   * Deprecation strategy for removed columns
   * Version compatibility matrix

This database architecture provides a robust foundation for the PITAS Training System, supporting complex training workflows while maintaining performance, security, and scalability requirements.
