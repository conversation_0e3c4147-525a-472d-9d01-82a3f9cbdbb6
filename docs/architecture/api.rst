API Architecture
================

The PITAS Training System provides a comprehensive RESTful API built with FastAPI, offering high-performance, type-safe endpoints for all training and competency management operations.

🚀 API Design Principles
-------------------------

**1. RESTful Design**
   * Resource-based URLs with clear hierarchies
   * Standard HTTP methods (GET, POST, PUT, DELETE)
   * Consistent response formats and status codes
   * Stateless operations for scalability

**2. Type Safety**
   * Pydantic models for request/response validation
   * Automatic OpenAPI schema generation
   * Runtime type checking and validation
   * Clear error messages for invalid data

**3. Performance Optimization**
   * Async/await pattern for non-blocking operations
   * Database connection pooling
   * Response caching for frequently accessed data
   * Pagination for large datasets

**4. Security First**
   * JWT-based authentication
   * Role-based access control
   * Input validation and sanitization
   * Rate limiting and abuse prevention

📡 API Structure
----------------

The API follows a hierarchical structure organized by functional domains:

.. mermaid::

   graph TD
       API["/api/v1"] --> HEALTH["/health"]
       API --> TRAINING["/training"]
       
       TRAINING --> FRAMEWORKS["/frameworks"]
       TRAINING --> COMPETENCIES["/competencies"] 
       TRAINING --> ASSESSMENTS["/assessments"]
       TRAINING --> COURSES["/courses"]
       TRAINING --> ENROLLMENTS["/enrollments"]
       TRAINING --> PATHS["/learning-paths"]
       TRAINING --> CERTS["/certifications"]
       TRAINING --> CTF["/ctf"]
       TRAINING --> MENTORSHIP["/mentorship"]
       
       FRAMEWORKS --> FRAMEWORKS_CRUD["CRUD Operations"]
       COMPETENCIES --> COMPETENCIES_CRUD["CRUD Operations"]
       ASSESSMENTS --> ASSESSMENTS_CRUD["CRUD + Analytics"]
       COURSES --> COURSES_CRUD["CRUD + Enrollment"]
       ENROLLMENTS --> ENROLLMENTS_CRUD["Progress Tracking"]
       PATHS --> PATHS_CRUD["Path Management"]
       CERTS --> CERTS_CRUD["Lifecycle Management"]
       CTF --> CTF_CRUD["Challenges + Submissions"]
       MENTORSHIP --> MENTORSHIP_CRUD["Pairing + Sessions"]

🔗 Endpoint Categories
----------------------

**1. Competency Management**

.. code-block:: text

   GET    /api/v1/training/frameworks              # List competency frameworks
   POST   /api/v1/training/frameworks              # Create framework
   GET    /api/v1/training/frameworks/{id}         # Get specific framework
   PUT    /api/v1/training/frameworks/{id}         # Update framework
   DELETE /api/v1/training/frameworks/{id}         # Delete framework
   
   GET    /api/v1/training/competencies            # List competencies
   POST   /api/v1/training/competencies            # Create competency
   
   GET    /api/v1/training/assessments/users/{id}  # Get user assessments
   POST   /api/v1/training/assessments             # Create assessment
   GET    /api/v1/training/assessments/users/{id}/gap-analysis  # Skill gap analysis

**2. Training Course Management**

.. code-block:: text

   GET    /api/v1/training/courses                 # List courses
   POST   /api/v1/training/courses                 # Create course
   GET    /api/v1/training/courses/{id}            # Get course details
   PUT    /api/v1/training/courses/{id}            # Update course
   DELETE /api/v1/training/courses/{id}            # Delete course
   
   POST   /api/v1/training/enrollments             # Enroll in course
   PUT    /api/v1/training/enrollments/{id}        # Update progress
   GET    /api/v1/training/enrollments/users/{user_id}/courses/{course_id}/progress

**3. Learning Path Management**

.. code-block:: text

   POST   /api/v1/training/learning-paths          # Create learning path
   GET    /api/v1/training/learning-paths/users/{id}  # Get user paths

**4. Certification Management**

.. code-block:: text

   GET    /api/v1/training/certifications          # List certifications
   POST   /api/v1/training/certifications          # Create certification
   GET    /api/v1/training/certifications/{id}     # Get certification
   
   POST   /api/v1/training/certifications/achievements  # Record achievement
   GET    /api/v1/training/certifications/achievements/users/{id}  # User certs
   GET    /api/v1/training/certifications/expiring     # Expiring certifications
   GET    /api/v1/training/certifications/pathways/users/{id}  # Cert pathways

**5. CTF Platform**

.. code-block:: text

   GET    /api/v1/training/ctf/challenges          # List challenges
   POST   /api/v1/training/ctf/challenges          # Create challenge
   GET    /api/v1/training/ctf/challenges/{id}     # Get challenge
   
   POST   /api/v1/training/ctf/submissions         # Submit flag
   GET    /api/v1/training/ctf/leaderboard         # Get leaderboard
   GET    /api/v1/training/ctf/submissions/users/{id}  # User submissions

**6. Mentorship Program**

.. code-block:: text

   POST   /api/v1/training/mentorship/pairs        # Create mentorship
   GET    /api/v1/training/mentorship/pairs/users/{id}  # User mentorships
   PUT    /api/v1/training/mentorship/pairs/{id}/end    # End mentorship
   
   POST   /api/v1/training/mentorship/sessions     # Create session

🔄 Request/Response Flow
------------------------

.. mermaid::

   sequenceDiagram
       participant Client
       participant API Gateway
       participant FastAPI
       participant Service
       participant Database
       
       Client->>API Gateway: HTTP Request + JWT
       API Gateway->>API Gateway: Validate JWT
       API Gateway->>API Gateway: Check Rate Limits
       API Gateway->>FastAPI: Forward Request
       
       FastAPI->>FastAPI: Validate Request Schema
       FastAPI->>Service: Call Business Logic
       
       Service->>Service: Process Business Rules
       Service->>Database: Execute Queries
       Database-->>Service: Return Data
       
       Service-->>FastAPI: Return Result
       FastAPI->>FastAPI: Validate Response Schema
       FastAPI-->>API Gateway: HTTP Response
       API Gateway-->>Client: Final Response

📝 Request/Response Formats
---------------------------

**Standard Request Format:**

.. code-block:: json

   {
     "data": {
       "title": "Advanced Penetration Testing",
       "description": "Comprehensive pen testing course",
       "difficulty_level": "proficient",
       "duration_hours": 40
     }
   }

**Standard Response Format:**

.. code-block:: json

   {
     "success": true,
     "data": {
       "id": "123e4567-e89b-12d3-a456-426614174000",
       "title": "Advanced Penetration Testing",
       "created_at": "2025-06-16T10:30:00Z",
       "updated_at": "2025-06-16T10:30:00Z"
     },
     "message": "Course created successfully"
   }

**Error Response Format:**

.. code-block:: json

   {
     "success": false,
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid input data",
       "details": [
         {
           "field": "difficulty_level",
           "message": "Invalid competency level"
         }
       ]
     }
   }

🔐 Authentication & Authorization
---------------------------------

**JWT Authentication Flow:**

.. mermaid::

   sequenceDiagram
       participant Client
       participant Auth Service
       participant API
       participant Resource
       
       Client->>Auth Service: Login (username/password)
       Auth Service->>Auth Service: Validate Credentials
       Auth Service-->>Client: JWT Token
       
       Client->>API: Request + JWT Header
       API->>API: Validate JWT
       API->>API: Extract User Claims
       API->>Resource: Authorized Request
       Resource-->>API: Response
       API-->>Client: Final Response

**Authorization Levels:**

* **Public** - Health checks, documentation
* **Authenticated** - Basic user operations
* **Instructor** - Course management, assessment creation
* **Admin** - System configuration, user management
* **Super Admin** - Full system access

📊 API Metrics and Monitoring
------------------------------

**Performance Metrics:**
   * Response time percentiles (P50, P95, P99)
   * Request rate and throughput
   * Error rates by endpoint
   * Database query performance

**Business Metrics:**
   * API usage by feature
   * User engagement patterns
   * Training completion rates
   * Certification achievement rates

**Monitoring Stack:**

.. mermaid::

   graph LR
       API[FastAPI] --> METRICS[Prometheus Metrics]
       API --> LOGS[Structured Logs]
       API --> TRACES[OpenTelemetry]
       
       METRICS --> GRAFANA[Grafana Dashboard]
       LOGS --> ELK[ELK Stack]
       TRACES --> JAEGER[Jaeger UI]
       
       GRAFANA --> ALERTS[Alert Manager]
       ELK --> ALERTS
       JAEGER --> ALERTS

🧪 API Testing Strategy
-----------------------

**Unit Testing:**
   * Endpoint logic testing
   * Schema validation testing
   * Business rule validation

**Integration Testing:**
   * End-to-end API workflows
   * Database integration testing
   * External service integration

**Performance Testing:**
   * Load testing with realistic data
   * Stress testing for peak loads
   * Endurance testing for stability

**Security Testing:**
   * Authentication bypass testing
   * Authorization boundary testing
   * Input validation testing
   * Rate limiting validation

📚 API Documentation
--------------------

**Interactive Documentation:**
   * Swagger UI at ``/api/v1/docs``
   * ReDoc at ``/api/v1/redoc``
   * OpenAPI schema at ``/api/v1/openapi.json``

**Documentation Features:**
   * Interactive request/response testing
   * Schema validation examples
   * Authentication flow documentation
   * Error code reference

🔄 Versioning Strategy
----------------------

**API Versioning:**
   * URL-based versioning (``/api/v1/``)
   * Backward compatibility maintenance
   * Deprecation notices for old versions
   * Migration guides for version updates

**Schema Evolution:**
   * Additive changes preferred
   * Optional fields for new features
   * Graceful degradation for missing fields
   * Version-specific response formats

This API architecture provides a robust, scalable, and secure foundation for the PITAS Training System, enabling seamless integration with various clients while maintaining high performance and reliability.
