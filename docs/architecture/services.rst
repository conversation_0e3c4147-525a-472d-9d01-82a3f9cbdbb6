Services Architecture
====================

The PITAS Training System implements a service-oriented architecture with clearly defined business logic layers. This document details the service architecture, patterns, and interactions that power the training and competency management platform.

🏗️ Service Layer Overview
---------------------------

The service layer acts as the core business logic tier, orchestrating complex operations and maintaining business rules:

.. mermaid::

   graph TB
       subgraph "API Layer"
           ENDPOINTS[API Endpoints]
       end
       
       subgraph "Service Layer"
           COMPETENCY_SVC[CompetencyService]
           TRAINING_SVC[TrainingService]
           CERT_SVC[CertificationService]
           CTF_SVC[CTFService]
           MENTOR_SVC[MentorshipService]
       end
       
       subgraph "Data Access Layer"
           REPOSITORIES[Repository Pattern]
           ORM[SQLAlchemy ORM]
       end
       
       subgraph "External Services"
           SANS_API[SANS Training API]
           CERT_VENDORS[Certification Vendors]
           EMAIL_SVC[Email Service]
           ANALYTICS[Analytics Engine]
       end
       
       ENDPOINTS --> COMPETENCY_SVC
       ENDPOINTS --> TRAINING_SVC
       ENDPOINTS --> CERT_SVC
       ENDPOINTS --> CTF_SVC
       ENDPOINTS --> MENTOR_SVC
       
       COMPETENCY_SVC --> REPOSITORIES
       TRAINING_SVC --> REPOSITORIES
       CERT_SVC --> REPOSITORIES
       CTF_SVC --> REPOSITORIES
       MENTOR_SVC --> REPOSITORIES
       
       REPOSITORIES --> ORM
       
       TRAINING_SVC --> SANS_API
       CERT_SVC --> CERT_VENDORS
       MENTOR_SVC --> EMAIL_SVC
       COMPETENCY_SVC --> ANALYTICS

🎯 Core Services
----------------

**1. CompetencyService**

Manages NICE framework integration and skills assessment:

.. code-block:: python

   class CompetencyService:
       """Service for managing competency frameworks and assessments."""
       
       async def assess_skill_gaps(self, user_id: UUID) -> SkillGapAnalysis:
           """Perform comprehensive skill gap analysis."""
           
       async def create_framework(self, framework_data: CompetencyFrameworkCreate) -> CompetencyFramework:
           """Create new competency framework."""
           
       async def get_recommended_training(self, skill_gaps: Dict) -> List[UUID]:
           """Generate training recommendations based on skill gaps."""

**Key Responsibilities:**
   * NICE framework management
   * Skills assessment coordination
   * Gap analysis and recommendations
   * Competency validation and tracking

**2. TrainingService**

Orchestrates training course management and learning paths:

.. code-block:: python

   class TrainingService:
       """Service for managing training courses and enrollments."""
       
       async def enroll_user(self, enrollment_data: TrainingEnrollmentCreate) -> TrainingEnrollment:
           """Enroll user in training course with validation."""
           
       async def update_progress(self, enrollment_id: UUID, progress_data: TrainingEnrollmentUpdate) -> TrainingEnrollment:
           """Update training progress with business rules."""
           
       async def create_learning_path(self, path_data: LearningPathCreate) -> LearningPath:
           """Create personalized learning path."""

**Key Responsibilities:**
   * Course catalog management
   * Enrollment processing and validation
   * Progress tracking and analytics
   * Learning path orchestration

**3. CertificationService**

Manages certification lifecycle and pathways:

.. code-block:: python

   class CertificationService:
       """Service for managing certifications and achievements."""
       
       async def record_achievement(self, achievement_data: CertificationAchievementCreate) -> CertificationAchievement:
           """Record certification achievement with validation."""
           
       async def get_expiring_certifications(self, days_ahead: int = 90) -> List[CertificationAchievement]:
           """Get certifications requiring renewal."""
           
       async def generate_certification_pathway(self, user_id: UUID, target_level: CompetencyLevel) -> CertificationPathway:
           """Generate optimal certification pathway."""

**Key Responsibilities:**
   * Certification lifecycle management
   * Achievement tracking and validation
   * Renewal reminders and CPE tracking
   * Pathway optimization and recommendations

**4. CTFService**

Powers the CTF platform and competitive learning:

.. code-block:: python

   class CTFService:
       """Service for managing CTF challenges and competitions."""
       
       async def submit_flag(self, submission_data: CTFSubmissionCreate) -> CTFSubmission:
           """Process flag submission with scoring logic."""
           
       async def get_leaderboard(self, limit: int = 10) -> List[CTFLeaderboard]:
           """Generate real-time leaderboard."""
           
       async def create_challenge(self, challenge_data: CTFChallengeCreate) -> CTFChallenge:
           """Create new CTF challenge with validation."""

**Key Responsibilities:**
   * Challenge creation and management
   * Flag submission processing
   * Scoring and leaderboard generation
   * Competition coordination

**5. MentorshipService**

Coordinates mentorship programs and relationships:

.. code-block:: python

   class MentorshipService:
       """Service for managing mentorship programs."""
       
       async def create_mentorship_pair(self, pair_data: MentorshipPairCreate) -> MentorshipPair:
           """Create mentorship pair with validation."""
           
       async def end_mentorship(self, pair_id: UUID, satisfaction_rating: Optional[float] = None) -> MentorshipPair:
           """End mentorship relationship with feedback."""
           
       async def get_mentorship_analytics(self, pair_id: UUID) -> Dict[str, Any]:
           """Generate mentorship effectiveness analytics."""

**Key Responsibilities:**
   * Mentor-mentee pairing and validation
   * Session coordination and tracking
   * Relationship lifecycle management
   * Effectiveness measurement and analytics

🔄 Service Interaction Patterns
-------------------------------

**1. Service Orchestration**

Complex workflows often require multiple services:

.. mermaid::

   sequenceDiagram
       participant API
       participant TrainingService
       participant CompetencyService
       participant CertificationService
       participant EmailService
       
       API->>TrainingService: Complete Course
       TrainingService->>TrainingService: Update Progress
       TrainingService->>CompetencyService: Update Skills
       CompetencyService->>CompetencyService: Reassess Competencies
       TrainingService->>CertificationService: Check Cert Eligibility
       CertificationService->>EmailService: Send Notification
       TrainingService-->>API: Completion Result

**2. Event-Driven Communication**

Services communicate through domain events:

.. code-block:: python

   class TrainingCompletedEvent:
       user_id: UUID
       course_id: UUID
       completion_date: datetime
       final_score: float
   
   class CompetencyUpdatedEvent:
       user_id: UUID
       competency_id: UUID
       previous_level: CompetencyLevel
       new_level: CompetencyLevel

**3. Dependency Injection**

Services are injected with their dependencies:

.. code-block:: python

   class TrainingService:
       def __init__(
           self,
           db: AsyncSession,
           competency_service: CompetencyService,
           notification_service: NotificationService
       ):
           self.db = db
           self.competency_service = competency_service
           self.notification_service = notification_service

🧩 Design Patterns
------------------

**1. Repository Pattern**

Data access abstraction for testability:

.. code-block:: python

   class TrainingRepository:
       async def get_course_by_id(self, course_id: UUID) -> Optional[TrainingCourse]:
           """Get course by ID with caching."""
           
       async def get_user_enrollments(self, user_id: UUID) -> List[TrainingEnrollment]:
           """Get all enrollments for user."""

**2. Factory Pattern**

Dynamic service creation based on configuration:

.. code-block:: python

   class ServiceFactory:
       @staticmethod
       def create_training_service(db: AsyncSession) -> TrainingService:
           """Create training service with dependencies."""
           return TrainingService(
               db=db,
               competency_service=ServiceFactory.create_competency_service(db),
               notification_service=ServiceFactory.create_notification_service()
           )

**3. Strategy Pattern**

Pluggable algorithms for different scenarios:

.. code-block:: python

   class LearningPathStrategy:
       async def generate_path(self, user_profile: UserProfile) -> LearningPath:
           """Generate learning path based on strategy."""
   
   class BeginnerPathStrategy(LearningPathStrategy):
       """Strategy for beginner-level learning paths."""
   
   class ExpertPathStrategy(LearningPathStrategy):
       """Strategy for expert-level learning paths."""

🔒 Service Security
-------------------

**Authentication Integration:**

.. code-block:: python

   class SecureService:
       def __init__(self, current_user: User):
           self.current_user = current_user
       
       async def check_permissions(self, resource: str, action: str) -> bool:
           """Check if current user has permission for action."""

**Authorization Patterns:**

* **Resource-based**: User can only access their own data
* **Role-based**: Admin users have elevated permissions
* **Context-based**: Mentors can access mentee data during active relationships

**Audit Logging:**

.. code-block:: python

   class AuditableService:
       async def log_action(self, action: str, resource_id: UUID, details: Dict):
           """Log user action for audit trail."""
           await self.audit_repository.create_log_entry(
               user_id=self.current_user.id,
               action=action,
               resource_id=resource_id,
               details=details,
               timestamp=datetime.utcnow()
           )

📊 Service Monitoring
---------------------

**Performance Metrics:**

.. code-block:: python

   from prometheus_client import Counter, Histogram
   
   service_requests = Counter('service_requests_total', 'Total service requests', ['service', 'method'])
   service_duration = Histogram('service_duration_seconds', 'Service method duration', ['service', 'method'])
   
   class MonitoredService:
       @service_duration.labels(service='training', method='enroll_user').time()
       async def enroll_user(self, enrollment_data: TrainingEnrollmentCreate):
           service_requests.labels(service='training', method='enroll_user').inc()
           # Service logic here

**Health Checks:**

.. code-block:: python

   class ServiceHealthCheck:
       async def check_database_connection(self) -> bool:
           """Check if database is accessible."""
       
       async def check_external_services(self) -> Dict[str, bool]:
           """Check external service availability."""

🧪 Service Testing
------------------

**Unit Testing with Mocks:**

.. code-block:: python

   @pytest.mark.asyncio
   async def test_enroll_user_success(mock_db):
       # Arrange
       service = TrainingService(mock_db)
       enrollment_data = TrainingEnrollmentCreate(user_id=uuid4(), course_id=uuid4())
       
       # Act
       result = await service.enroll_user(enrollment_data)
       
       # Assert
       assert result.status == TrainingStatus.NOT_STARTED

**Integration Testing:**

.. code-block:: python

   @pytest.mark.asyncio
   async def test_complete_training_workflow(db_session):
       # Test full workflow from enrollment to completion
       service = TrainingService(db_session)
       
       # Create course, enroll user, update progress, complete
       course = await service.create_course(course_data)
       enrollment = await service.enroll_user(enrollment_data)
       completion = await service.update_progress(enrollment.id, completion_data)
       
       assert completion.status == TrainingStatus.COMPLETED

🔄 Service Evolution
--------------------

**Versioning Strategy:**
   * Interface versioning for backward compatibility
   * Feature flags for gradual rollouts
   * Deprecation notices for old methods

**Extensibility:**
   * Plugin architecture for custom business rules
   * Event hooks for external integrations
   * Configuration-driven behavior modification

This service architecture provides a robust, maintainable, and scalable foundation for the PITAS Training System, ensuring clear separation of concerns while enabling complex business workflows and integrations.
