Vulnerability Management User Guide
===================================

This guide provides comprehensive instructions for using PITAS Phase 3 vulnerability management features, including discovery, assessment, and remediation workflows.

Getting Started
---------------

Dashboard Overview
~~~~~~~~~~~~~~~~~~

The vulnerability dashboard provides a centralized view of your security posture:

1. **Navigate to Dashboard**: Access ``/vulnerabilities/dashboard`` in the web interface
2. **Key Metrics**: Review total vulnerabilities, severity distribution, and trends
3. **Quick Actions**: Create new vulnerabilities, run assessments, or generate reports

.. image:: /_static/images/vulnerability-dashboard.png
   :alt: Vulnerability Dashboard
   :width: 800px

**Dashboard Components:**

* **Severity Distribution**: Pie chart showing vulnerability breakdown by severity
* **Trend Analysis**: 30-day trend line showing discovery and remediation rates
* **Critical Assets**: List of high-priority assets with active vulnerabilities
* **SLA Compliance**: Progress bars showing remediation timeline adherence
* **Recent Activity**: Timeline of recent vulnerability discoveries and remediations

Vulnerability Discovery
-----------------------

Manual Vulnerability Entry
~~~~~~~~~~~~~~~~~~~~~~~~~~

For vulnerabilities discovered through manual testing or external sources:

1. **Create New Vulnerability**:
   
   .. code-block:: bash
   
       curl -X POST "https://pitas.example.com/api/v1/vulnerabilities/" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "cve_id": "CVE-2024-1234",
                "title": "SQL Injection in Login Form",
                "description": "User input not properly sanitized",
                "cvss_score": 8.5,
                "severity": "high",
                "discovery_date": "2024-01-15T10:30:00Z",
                "source": "manual_testing",
                "tags": ["sql-injection", "authentication"]
            }'

2. **Required Fields**:
   
   * ``title``: Descriptive vulnerability name
   * ``discovery_date``: When the vulnerability was found
   * ``severity``: One of critical, high, medium, low, informational

3. **Optional Enrichment**:
   
   * ``cve_id``: CVE identifier if available
   * ``cvss_score``: CVSS base score (0.0-10.0)
   * ``cvss_vector``: CVSS vector string
   * ``description``: Detailed vulnerability description
   * ``tags``: Categorization tags
   * ``extra_data``: Additional metadata

Automated Scanner Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

PITAS supports integration with popular vulnerability scanners:

**Nessus Integration:**

.. code-block:: python

    from pitas.integrations.nessus import NessusImporter
    
    # Configure Nessus connection
    importer = NessusImporter(
        host="nessus.example.com",
        access_key="your_access_key",
        secret_key="your_secret_key"
    )
    
    # Import scan results
    scan_id = "12345"
    vulnerabilities = await importer.import_scan_results(scan_id)
    
    print(f"Imported {len(vulnerabilities)} vulnerabilities")

**OpenVAS Integration:**

.. code-block:: python

    from pitas.integrations.openvas import OpenVASImporter
    
    # Configure OpenVAS connection
    importer = OpenVASImporter(
        host="openvas.example.com",
        username="admin",
        password="password"
    )
    
    # Import latest scan
    vulnerabilities = await importer.import_latest_scan()

**Custom Scanner Integration:**

.. code-block:: python

    from pitas.services.vulnerability import VulnerabilityService
    
    async def import_custom_scan(scan_data):
        """Import vulnerabilities from custom scanner format."""
        
        service = VulnerabilityService(db)
        imported_count = 0
        
        for vuln_data in scan_data['vulnerabilities']:
            # Transform scanner data to PITAS format
            vulnerability = VulnerabilityCreate(
                title=vuln_data['name'],
                description=vuln_data['description'],
                severity=map_severity(vuln_data['risk']),
                cvss_score=vuln_data.get('cvss_score'),
                discovery_date=datetime.utcnow(),
                source=scan_data['scanner_name'],
                extra_data=vuln_data
            )
            
            # Create vulnerability
            await service.create_vulnerability(vulnerability)
            imported_count += 1
        
        return imported_count

Asset Association
-----------------

Linking Vulnerabilities to Assets
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Associate vulnerabilities with affected assets for impact analysis:

1. **Create Asset-Vulnerability Association**:

   .. code-block:: bash
   
       curl -X POST "https://pitas.example.com/api/v1/assets/$ASSET_ID/vulnerabilities" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "vulnerability_id": "550e8400-e29b-41d4-a716-446655440000",
                "impact_level": "high",
                "exploitability_likelihood": 8.5,
                "remediation_priority": 1,
                "remediation_effort": "medium"
            }'

2. **Impact Assessment Fields**:

   * ``impact_level``: Qualitative impact (critical, high, medium, low)
   * ``exploitability_likelihood``: Numerical score (0.0-10.0)
   * ``remediation_priority``: Priority ranking (1 = highest)
   * ``remediation_effort``: Effort estimate (low, medium, high)

Bulk Asset Association
~~~~~~~~~~~~~~~~~~~~~

For large-scale operations, use bulk association:

.. code-block:: python

    from pitas.services.asset import AssetService
    
    async def bulk_associate_vulnerabilities(associations):
        """Bulk create asset-vulnerability associations."""
        
        service = AssetService(db)
        results = []
        
        for assoc_data in associations:
            try:
                association = await service.create_asset_vulnerability_association(
                    AssetVulnerabilityCreate(**assoc_data)
                )
                results.append({"status": "success", "id": association.id})
            except Exception as e:
                results.append({"status": "error", "error": str(e)})
        
        return results

Risk Assessment
---------------

RBVM Risk Scoring
~~~~~~~~~~~~~~~~~

Use Risk-Based Vulnerability Management for contextual prioritization:

1. **Automatic Risk Assessment**:

   .. code-block:: bash
   
       curl -X POST "https://pitas.example.com/api/v1/risk-assessments/" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "vulnerability_id": "550e8400-e29b-41d4-a716-446655440000",
                "asset_id": "660e8400-e29b-41d4-a716-446655440001",
                "methodology": "RBVM"
            }'

2. **Manual Risk Override**:

   .. code-block:: bash
   
       curl -X PUT "https://pitas.example.com/api/v1/risk-assessments/$ASSESSMENT_ID" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "risk_score": 9.2,
                "risk_level": "critical",
                "business_impact": 9.0,
                "threat_likelihood": 8.5,
                "confidence_level": 9.5,
                "notes": "Critical business system with external exposure"
            }'

Risk Factors Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

Customize risk calculation weights for your organization:

.. code-block:: python

    # Configure RBVM weights in settings
    RBVM_WEIGHTS = {
        "technical_risk": 0.25,      # CVSS and exploitability
        "business_impact": 0.35,     # Asset criticality and business processes
        "threat_likelihood": 0.25,   # Threat intelligence and exploit availability
        "environmental": 0.15        # Network segmentation and controls
    }
    
    # Asset criticality scoring
    ASSET_CRITICALITY_SCORES = {
        "critical": 10.0,
        "high": 7.5,
        "medium": 5.0,
        "low": 2.5
    }

Threat Intelligence Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Enhance risk assessments with threat intelligence:

.. code-block:: python

    from pitas.services.threat_intelligence import ThreatIntelligenceService
    
    async def enrich_with_threat_intel(vulnerability_id):
        """Enrich vulnerability with threat intelligence."""
        
        service = ThreatIntelligenceService(db)
        
        # Query multiple intelligence sources
        intel_data = await service.gather_intelligence(vulnerability_id)
        
        # Create threat intelligence record
        threat_intel = ThreatIntelligenceCreate(
            vulnerability_id=vulnerability_id,
            source="multiple_sources",
            actor_type=intel_data.get("primary_actor_type"),
            exploit_availability=intel_data.get("public_exploits_available"),
            active_exploitation=intel_data.get("active_campaigns"),
            iocs=intel_data.get("indicators_of_compromise", []),
            ttps=intel_data.get("tactics_techniques_procedures", []),
            mitre_techniques=intel_data.get("mitre_attack_techniques", [])
        )
        
        return await service.create_threat_intelligence(threat_intel)

Remediation Management
----------------------

Remediation Planning
~~~~~~~~~~~~~~~~~~~

Create and track remediation plans:

1. **Create Remediation Plan**:

   .. code-block:: bash
   
       curl -X POST "https://pitas.example.com/api/v1/remediation-plans/" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "vulnerability_id": "550e8400-e29b-41d4-a716-446655440000",
                "priority": 1,
                "remediation_type": "patch",
                "description": "Apply security patch KB123456",
                "planned_start_date": "2024-01-20T09:00:00Z",
                "due_date": "2024-01-22T17:00:00Z",
                "assigned_to": "770e8400-e29b-41d4-a716-446655440002",
                "estimated_effort_hours": 4.0
            }'

2. **Track Progress**:

   .. code-block:: bash
   
       curl -X PUT "https://pitas.example.com/api/v1/remediation-plans/$PLAN_ID" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "status": "in_progress",
                "progress_percentage": 50,
                "actual_start_date": "2024-01-20T09:15:00Z",
                "notes": "Patch testing completed, deploying to production"
            }'

SLA Management
~~~~~~~~~~~~~

Configure and monitor remediation SLAs:

.. code-block:: python

    # SLA configuration by severity
    REMEDIATION_SLAS = {
        "critical": {
            "target_hours": 24,
            "escalation_hours": 12
        },
        "high": {
            "target_hours": 72,
            "escalation_hours": 48
        },
        "medium": {
            "target_hours": 168,  # 1 week
            "escalation_hours": 120
        },
        "low": {
            "target_hours": 720,  # 30 days
            "escalation_hours": 480
        }
    }

Automated Remediation
~~~~~~~~~~~~~~~~~~~~

Set up automated remediation workflows:

.. code-block:: python

    from pitas.automation.remediation import RemediationAutomator
    
    class AutomatedPatchingWorkflow:
        """Automated patching workflow for low-risk vulnerabilities."""
        
        async def process_vulnerability(self, vulnerability):
            """Process vulnerability for automated remediation."""
            
            # Check if eligible for automation
            if not self._is_automation_eligible(vulnerability):
                return False
            
            # Create remediation plan
            plan = await self._create_automated_plan(vulnerability)
            
            # Execute remediation
            result = await self._execute_remediation(plan)
            
            # Update vulnerability status
            if result.success:
                await self._update_vulnerability_status(
                    vulnerability.id, 
                    "remediated"
                )
            
            return result.success
        
        def _is_automation_eligible(self, vulnerability):
            """Check if vulnerability is eligible for automation."""
            return (
                vulnerability.severity in ["low", "medium"] and
                vulnerability.remediation_effort == "low" and
                not vulnerability.requires_manual_testing
            )

Analytics and Reporting
-----------------------

Vulnerability Trends
~~~~~~~~~~~~~~~~~~~

Analyze vulnerability trends over time:

.. code-block:: bash

    # Get 30-day discovery trend
    curl "https://pitas.example.com/api/v1/analytics/discovery-trend?days=30" \
         -H "Authorization: Bearer $TOKEN"

    # Get remediation metrics by severity
    curl "https://pitas.example.com/api/v1/analytics/remediation-metrics?group_by=severity" \
         -H "Authorization: Bearer $TOKEN"

Custom Reports
~~~~~~~~~~~~~

Generate custom vulnerability reports:

.. code-block:: python

    from pitas.reporting.vulnerability import VulnerabilityReporter
    
    async def generate_executive_report():
        """Generate executive vulnerability report."""
        
        reporter = VulnerabilityReporter(db)
        
        # Gather report data
        report_data = await reporter.gather_executive_data(
            time_period="30d",
            include_trends=True,
            include_sla_metrics=True
        )
        
        # Generate PDF report
        pdf_report = await reporter.generate_pdf_report(
            template="executive_summary",
            data=report_data
        )
        
        return pdf_report

Graph Analytics
~~~~~~~~~~~~~~

Leverage graph analytics for advanced insights:

.. code-block:: bash

    # Get attack paths for critical vulnerabilities
    curl "https://pitas.example.com/api/v1/analytics/attack-paths?severity=critical&max_depth=5" \
         -H "Authorization: Bearer $TOKEN"

    # Get vulnerability clusters
    curl "https://pitas.example.com/api/v1/analytics/vulnerability-clusters?similarity_threshold=0.8" \
         -H "Authorization: Bearer $TOKEN"

Best Practices
--------------

Vulnerability Lifecycle
~~~~~~~~~~~~~~~~~~~~~~~

Follow these best practices for vulnerability management:

1. **Discovery Phase**:
   
   * Automate scanner integration where possible
   * Validate findings to reduce false positives
   * Enrich with threat intelligence immediately

2. **Assessment Phase**:
   
   * Use RBVM for contextual risk scoring
   * Consider business impact and asset criticality
   * Document assessment rationale

3. **Remediation Phase**:
   
   * Prioritize based on risk score, not just CVSS
   * Set realistic timelines based on effort estimates
   * Track progress and update stakeholders

4. **Verification Phase**:
   
   * Confirm remediation effectiveness
   * Re-scan affected assets
   * Update vulnerability status

Data Quality
~~~~~~~~~~~

Maintain high data quality:

* **Consistent Tagging**: Use standardized tags for categorization
* **Complete Metadata**: Fill in all relevant fields
* **Regular Reviews**: Periodically review and clean up data
* **Validation Rules**: Implement data validation at entry points

Integration Guidelines
~~~~~~~~~~~~~~~~~~~~~

When integrating with external tools:

* **API Rate Limits**: Respect rate limits and implement backoff
* **Error Handling**: Implement robust error handling and retry logic
* **Data Mapping**: Create clear mappings between external and PITAS data
* **Monitoring**: Monitor integration health and data quality

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~

**Slow Dashboard Loading**:

* Check database indexes on frequently queried fields
* Review query complexity and optimize if needed
* Consider caching for expensive aggregations

**Import Failures**:

* Validate data format and required fields
* Check for duplicate CVE IDs or constraint violations
* Review error logs for specific failure reasons

**Risk Score Inconsistencies**:

* Verify RBVM weight configuration
* Check threat intelligence data quality
* Review asset criticality assignments

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~

For large-scale deployments:

* **Database Tuning**: Optimize PostgreSQL configuration
* **Index Management**: Create appropriate indexes for query patterns
* **Caching Strategy**: Implement Redis caching for frequent queries
* **Batch Processing**: Use bulk operations for large data sets
