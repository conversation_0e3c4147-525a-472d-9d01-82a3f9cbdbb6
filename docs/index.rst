PITAS Documentation
===================

**PITAS** (Pentesting Team Management System) is a comprehensive platform for managing global pentesting teams with advanced vulnerability assessment, training delivery, and career development capabilities.

.. image:: https://img.shields.io/badge/version-0.5.0-blue.svg
   :target: https://github.com/forkrul/pitas
   :alt: Version

.. image:: https://img.shields.io/badge/python-3.11+-blue.svg
   :target: https://www.python.org/downloads/
   :alt: Python Version

.. image:: https://img.shields.io/badge/framework-FastAPI-green.svg
   :target: https://fastapi.tiangolo.com/
   :alt: FastAPI

.. image:: https://img.shields.io/badge/license-MIT-green.svg
   :target: https://opensource.org/licenses/MIT
   :alt: License

Overview
--------

PITAS provides enterprise-grade pentesting team management with comprehensive capabilities across multiple phases:

**Phase 2: Team Resource Management**
* Resource allocation and capacity planning
* Skill matrix and competency tracking
* Project management and team optimization

**Phase 3: Vulnerability Assessment and Density Tracking**
* **Risk-Based Vulnerability Management (RBVM)** with TruRisk methodology
* **Graph-based vulnerability correlation** using Neo4j
* **Time-series analytics** with InfluxDB for trend analysis
* **25+ threat intelligence source integration**
* **Multi-framework support** (MITRE ATT&CK, CVSS 4.0, NIST CSF 2.0)

**Phase 5: Training Delivery and Competency Management**
* **NICE Framework Integration** - 52 work roles and competency tracking
* **Training Course Management** - Complete course catalog and enrollment system
* **Certification Pathways** - From CEH to OSEE progression tracking
* **CTF Platform** - Challenge creation and competitive leaderboards
* **Mentorship Program** - Structured mentor-mentee relationship management

**Phase 6: Employee Retention and Career Development**
* **Career Development Planning** - Structured progression routes and goal setting
* **Employee Wellness Programs** - Comprehensive health and wellbeing initiatives
* **Recognition and Rewards** - Peer and management recognition systems
* **Enhanced Mentorship** - AI-powered matching and community building

Quick Start
-----------

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/pitas.git
   cd pitas

   # Enter development environment
   nix-shell

   # Start all databases
   docker-compose -f docker-compose.phase3.yml up -d

   # Run database migrations
   alembic upgrade head

   # Start the application
   uvicorn pitas.main:app --reload

Architecture
------------

PITAS follows a modern microservices architecture with:

* **FastAPI** for high-performance REST APIs
* **PostgreSQL** for relational data storage
* **Neo4j** for graph-based vulnerability correlation
* **InfluxDB** for time-series vulnerability metrics
* **Redis** for caching and session management
* **Celery** for background task processing

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   overview
   installation
   quickstart

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   user-guide/installation
   user-guide/configuration
   user-guide/quick-start
   user-guide/vulnerability-management
   user-guide/risk-assessment
   user-guide/analytics

.. toctree::
   :maxdepth: 2
   :caption: Phase 3: Vulnerability Assessment

   phase3/overview
   phase3/vulnerability-models
   phase3/risk-assessment
   phase3/graph-analytics
   phase3/time-series-metrics
   phase3/api-reference

.. toctree::
   :maxdepth: 2
   :caption: User Guides

   guides/competency-management
   guides/training-courses
   guides/certification-tracking
   guides/ctf-platform
   guides/mentorship-program
   guides/career-development
   guides/employee-wellness

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/vulnerabilities
   api/assets
   api/risk-assessments
   api/analytics
   api/schemas
   api/endpoints
   api/models

.. toctree::
   :maxdepth: 2
   :caption: Architecture

   architecture/overview
   architecture/database
   architecture/api
   architecture/services

.. toctree::
   :maxdepth: 2
   :caption: Database

   database/models
   database/migrations
   database/neo4j
   database/influxdb

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/setup
   development/testing
   development/contributing

.. toctree::
   :maxdepth: 2
   :caption: Deployment

   deployment/configuration
   deployment/database
   deployment/monitoring

.. toctree::
   :maxdepth: 2
   :caption: Reference

   reference/configuration
   reference/cli
   reference/troubleshooting
   reference/changelog

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
