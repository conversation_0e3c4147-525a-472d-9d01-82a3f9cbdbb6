API Endpoints Reference
========================

This document provides a comprehensive reference for all PITAS Training System API endpoints, including request/response formats, authentication requirements, and usage examples.

🔗 Base URL and Versioning
---------------------------

**Base URL:** ``https://api.pitas.com/api/v1``

**API Versioning:** The API uses URL-based versioning with the current version being ``v1``.

**Content Type:** All requests and responses use ``application/json`` unless otherwise specified.

🔐 Authentication
-----------------

All API endpoints require JWT authentication unless marked as public.

**Authentication Header:**

.. code-block:: http

   Authorization: Bearer <jwt_token>

**Getting a JWT Token:**

.. code-block:: http

   POST /api/v1/auth/login
   Content-Type: application/json

   {
     "username": "<EMAIL>",
     "password": "secure_password"
   }

📊 Health Check Endpoints
--------------------------

**System Health Check**

.. http:get:: /api/v1/health

   Check system health and availability.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/health HTTP/1.1
      Host: api.pitas.com

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "status": "healthy",
        "timestamp": "2025-06-16T10:30:00Z",
        "version": "0.5.0",
        "services": {
          "database": "healthy",
          "redis": "healthy",
          "external_apis": "healthy"
        }
      }

🎯 Competency Framework Endpoints
----------------------------------

**List Competency Frameworks**

.. http:get:: /api/v1/training/frameworks

   Retrieve a list of all competency frameworks.

   :query string category: Filter by framework category (optional)
   :query int limit: Maximum number of results (default: 50)
   :query int offset: Number of results to skip (default: 0)

   **Example Request:**

   .. code-block:: http

      GET /api/v1/training/frameworks?category=Securely%20Provision&limit=10 HTTP/1.1
      Authorization: Bearer <jwt_token>

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "success": true,
        "data": [
          {
            "id": "123e4567-e89b-12d3-a456-************",
            "name": "NICE Cybersecurity Framework - Penetration Testing",
            "version": "1.0",
            "work_role_id": "SP-TES-001",
            "specialty_area": "Vulnerability Assessment and Management",
            "category": "Securely Provision",
            "created_at": "2025-06-16T10:30:00Z"
          }
        ],
        "total": 1,
        "limit": 10,
        "offset": 0
      }

**Create Competency Framework**

.. http:post:: /api/v1/training/frameworks

   Create a new competency framework.

   :<json string name: Framework name (required)
   :<json string description: Framework description (optional)
   :<json string version: Framework version (required)
   :<json string work_role_id: NICE work role ID (required)
   :<json string specialty_area: Specialty area (required)
   :<json string category: Framework category (required)

   **Example Request:**

   .. code-block:: http

      POST /api/v1/training/frameworks HTTP/1.1
      Authorization: Bearer <jwt_token>
      Content-Type: application/json

      {
        "name": "NICE Cybersecurity Framework - Incident Response",
        "description": "Competency framework for incident response specialists",
        "version": "1.0",
        "work_role_id": "PR-INF-001",
        "specialty_area": "Incident Response",
        "category": "Protect and Defend"
      }

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 201 Created
      Content-Type: application/json

      {
        "success": true,
        "data": {
          "id": "456e7890-e89b-12d3-a456-************",
          "name": "NICE Cybersecurity Framework - Incident Response",
          "version": "1.0",
          "work_role_id": "PR-INF-001",
          "created_at": "2025-06-16T10:35:00Z"
        },
        "message": "Competency framework created successfully"
      }

**Get Competency Framework**

.. http:get:: /api/v1/training/frameworks/(uuid:framework_id)

   Retrieve a specific competency framework by ID.

   :param framework_id: The framework UUID

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "success": true,
        "data": {
          "id": "123e4567-e89b-12d3-a456-************",
          "name": "NICE Cybersecurity Framework - Penetration Testing",
          "description": "Competency framework for penetration testing specialists",
          "version": "1.0",
          "work_role_id": "SP-TES-001",
          "specialty_area": "Vulnerability Assessment and Management",
          "category": "Securely Provision",
          "competencies": [
            {
              "id": "comp-uuid-1",
              "competency_id": "K0001",
              "name": "Knowledge of computer networking concepts"
            }
          ],
          "created_at": "2025-06-16T10:30:00Z",
          "updated_at": "2025-06-16T10:30:00Z"
        }
      }

📚 Training Course Endpoints
-----------------------------

**List Training Courses**

.. http:get:: /api/v1/training/courses

   Retrieve a list of training courses.

   :query string provider: Filter by training provider (optional)
   :query string difficulty: Filter by difficulty level (optional)
   :query boolean is_active: Filter by active status (default: true)
   :query int limit: Maximum number of results (default: 50)
   :query int offset: Number of results to skip (default: 0)

   **Example Request:**

   .. code-block:: http

      GET /api/v1/training/courses?provider=SANS&difficulty=proficient HTTP/1.1
      Authorization: Bearer <jwt_token>

**Create Training Course**

.. http:post:: /api/v1/training/courses

   Create a new training course.

   :<json string title: Course title (required)
   :<json string description: Course description (optional)
   :<json string provider: Training provider (optional)
   :<json string course_code: Provider course code (optional)
   :<json integer duration_hours: Course duration in hours (optional)
   :<json string difficulty_level: Difficulty level (required)
   :<json array prerequisites: List of prerequisite course UUIDs (optional)
   :<json array learning_objectives: List of learning objectives (optional)
   :<json array competencies_addressed: List of competency UUIDs (optional)
   :<json boolean is_certification_prep: Is certification preparation (default: false)
   :<json uuid certification_id: Related certification UUID (optional)
   :<json number cost: Course cost (optional)
   :<json boolean is_active: Is course active (default: true)

🎓 Training Enrollment Endpoints
---------------------------------

**Enroll in Course**

.. http:post:: /api/v1/training/enrollments

   Enroll a user in a training course.

   :<json uuid user_id: User UUID (required)
   :<json uuid course_id: Course UUID (required)
   :<json uuid learning_path_id: Learning path UUID (optional)
   :<json string status: Initial status (default: "not_started")

**Update Training Progress**

.. http:put:: /api/v1/training/enrollments/(uuid:enrollment_id)

   Update training progress for an enrollment.

   :param enrollment_id: The enrollment UUID

   :<json datetime start_date: Course start date (optional)
   :<json datetime completion_date: Course completion date (optional)
   :<json string status: Training status (optional)
   :<json number progress_percentage: Progress percentage 0-100 (optional)
   :<json object assessment_scores: Assessment scores object (optional)
   :<json object practical_scores: Practical exercise scores (optional)
   :<json number time_spent_hours: Time spent in hours (optional)

🏆 Certification Endpoints
---------------------------

**List Certifications**

.. http:get:: /api/v1/training/certifications

   Retrieve a list of available certifications.

   :query string level: Filter by competency level (optional)
   :query string provider: Filter by certification provider (optional)
   :query boolean is_active: Filter by active status (default: true)

**Record Certification Achievement**

.. http:post:: /api/v1/training/certifications/achievements

   Record a certification achievement for a user.

   :<json uuid user_id: User UUID (required)
   :<json uuid certification_id: Certification UUID (required)
   :<json string status: Achievement status (default: "not_started")
   :<json datetime achievement_date: Achievement date (optional)
   :<json datetime expiration_date: Expiration date (optional)
   :<json string credential_id: External credential ID (optional)
   :<json integer cpe_credits_earned: CPE credits earned (default: 0)
   :<json number cost_reimbursed: Cost reimbursed (optional)

🚩 CTF Platform Endpoints
--------------------------

**List CTF Challenges**

.. http:get:: /api/v1/training/ctf/challenges

   Retrieve a list of CTF challenges.

   :query string category: Filter by challenge category (optional)
   :query string difficulty: Filter by difficulty level (optional)
   :query boolean is_active: Filter by active status (default: true)

**Submit CTF Flag**

.. http:post:: /api/v1/training/ctf/submissions

   Submit a flag for a CTF challenge.

   :<json uuid user_id: User UUID (required)
   :<json uuid challenge_id: Challenge UUID (required)
   :<json string submitted_flag: Submitted flag (required)
   :<json boolean is_correct: Is submission correct (required)

**Get CTF Leaderboard**

.. http:get:: /api/v1/training/ctf/leaderboard

   Retrieve the CTF leaderboard.

   :query int limit: Number of top entries to return (default: 10)

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "success": true,
        "data": [
          {
            "user_id": "user-uuid-1",
            "username": "hacker_pro",
            "total_points": 1250,
            "challenges_solved": 15,
            "rank": 1,
            "last_submission": "2025-06-16T09:45:00Z"
          }
        ]
      }

🤝 Mentorship Endpoints
------------------------

**Create Mentorship Pair**

.. http:post:: /api/v1/training/mentorship/pairs

   Create a new mentorship relationship.

   :<json uuid mentor_id: Mentor user UUID (required)
   :<json uuid mentee_id: Mentee user UUID (required)
   :<json array goals: List of mentorship goals (optional)
   :<json string meeting_frequency: Meeting frequency (optional)

**End Mentorship**

.. http:put:: /api/v1/training/mentorship/pairs/(uuid:pair_id)/end

   End a mentorship relationship.

   :param pair_id: The mentorship pair UUID
   :query number satisfaction_rating: Satisfaction rating 1-5 (optional)

📝 Error Responses
------------------

**Standard Error Format:**

.. code-block:: http

   HTTP/1.1 400 Bad Request
   Content-Type: application/json

   {
     "success": false,
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid input data",
       "details": [
         {
           "field": "difficulty_level",
           "message": "Invalid competency level. Must be one of: novice, advanced_beginner, competent, proficient, expert"
         }
       ]
     }
   }

**Common Error Codes:**

* ``VALIDATION_ERROR`` - Invalid input data
* ``AUTHENTICATION_ERROR`` - Invalid or missing authentication
* ``AUTHORIZATION_ERROR`` - Insufficient permissions
* ``NOT_FOUND`` - Resource not found
* ``CONFLICT`` - Resource conflict (e.g., duplicate enrollment)
* ``RATE_LIMIT_EXCEEDED`` - Too many requests
* ``INTERNAL_ERROR`` - Server error

📊 Rate Limiting
----------------

API endpoints are rate-limited to ensure fair usage:

* **Authenticated Users**: 1000 requests per hour
* **Premium Users**: 5000 requests per hour
* **Admin Users**: 10000 requests per hour

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 1000
   X-RateLimit-Remaining: 999
   X-RateLimit-Reset: 1640995200

🚀 Career Development Endpoints
--------------------------------

**Individual Development Plans (IDPs)**

.. http:post:: /api/v1/career/idps

   Create a new Individual Development Plan.

   :<json uuid user_id: User UUID (required)
   :<json uuid manager_id: Manager UUID (required)
   :<json string title: IDP title (required)
   :<json string description: IDP description (optional)
   :<json date start_date: Plan start date (required)
   :<json date end_date: Plan end date (required)
   :<json string status: Plan status (default: "draft")
   :<json array focus_areas: Development focus areas (optional)
   :<json object success_metrics: Success measurement criteria (optional)

   **Example Request:**

   .. code-block:: http

      POST /api/v1/career/idps HTTP/1.1
      Authorization: Bearer <jwt_token>
      Content-Type: application/json

      {
        "user_id": "user-uuid-here",
        "manager_id": "manager-uuid-here",
        "title": "Senior Penetration Tester Development Plan",
        "description": "12-month plan to advance to senior role",
        "start_date": "2025-07-01",
        "end_date": "2026-06-30",
        "focus_areas": [
          "Advanced exploitation techniques",
          "Leadership skills",
          "Client communication"
        ],
        "success_metrics": {
          "promotion_achieved": true,
          "certification_completed": "OSCP",
          "team_leadership_experience": true
        }
      }

.. http:get:: /api/v1/career/idps/active

   Get the active IDP for the current user.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "success": true,
        "data": {
          "id": "idp-uuid-here",
          "title": "Senior Penetration Tester Development Plan",
          "status": "active",
          "progress_percentage": 45.0,
          "goals": [
            {
              "id": "goal-uuid-1",
              "title": "Complete OSCP Certification",
              "progress": 60.0,
              "target_date": "2025-12-31"
            }
          ],
          "start_date": "2025-07-01",
          "end_date": "2026-06-30"
        }
      }

**Development Goals**

.. http:post:: /api/v1/career/goals

   Create a new development goal.

   :<json uuid idp_id: IDP UUID (required)
   :<json string title: Goal title (required)
   :<json string description: Goal description (optional)
   :<json string category: Goal category (required)
   :<json string priority: Goal priority (required)
   :<json date target_date: Target completion date (required)
   :<json array success_criteria: Success measurement criteria (required)
   :<json array milestones: Goal milestones (optional)

.. http:put:: /api/v1/career/goals/(uuid:goal_id)/progress

   Update goal progress.

   :param goal_id: The goal UUID
   :<json float progress: Progress percentage 0-100 (required)
   :<json string notes: Progress notes (optional)

**Career Analytics**

.. http:get:: /api/v1/career/progress-summary

   Get career progress summary for the current user.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "success": true,
        "data": {
          "overall_progress": 67.5,
          "active_goals": 5,
          "completed_goals": 12,
          "certifications_earned": 3,
          "skills_developed": 8,
          "career_velocity": 2.3,
          "next_milestones": [
            {
              "title": "Complete OSCP Exam",
              "due_date": "2025-12-31",
              "progress": 75.0
            }
          ]
        }
      }

💪 Employee Wellness Endpoints
-------------------------------

**Wellness Programs**

.. http:post:: /api/v1/wellness/programs

   Create a new wellness program.

   :<json string name: Program name (required)
   :<json string description: Program description (optional)
   :<json string program_type: Program type (required)
   :<json integer duration_weeks: Program duration (required)
   :<json string target_participants: Target audience (required)
   :<json array wellness_dimensions: Wellness focus areas (required)
   :<json array activities: Program activities (required)
   :<json array success_metrics: Success measurement criteria (optional)

**Wellness Assessments**

.. http:post:: /api/v1/wellness/assessments

   Create a wellness assessment.

   :<json uuid user_id: User UUID (required)
   :<json string assessment_type: Assessment type (required)
   :<json string assessment_period: Assessment period (required)
   :<json object metrics: Wellness metrics (required)
   :<json object qualitative_feedback: Qualitative feedback (optional)

**Mental Health Resources**

.. http:post:: /api/v1/wellness/mental-health/resources

   Request mental health resources.

   :<json uuid user_id: User UUID (required)
   :<json string resource_type: Resource type (required)
   :<json string urgency: Urgency level (required)
   :<json string preferred_format: Preferred format (optional)
   :<json array topics_of_interest: Topics of interest (optional)
   :<json object availability: Availability preferences (optional)

🏆 Recognition and Rewards Endpoints
------------------------------------

**Recognition Nominations**

.. http:post:: /api/v1/recognition/nominations

   Nominate someone for recognition.

   :<json uuid nominator_id: Nominator UUID (required)
   :<json uuid nominee_id: Nominee UUID (required)
   :<json string recognition_type: Recognition type (required)
   :<json string category: Recognition category (required)
   :<json string title: Recognition title (required)
   :<json string description: Recognition description (required)
   :<json string impact_description: Impact description (required)
   :<json array evidence: Supporting evidence (optional)
   :<json array skills_demonstrated: Skills demonstrated (optional)

**Rewards Catalog**

.. http:get:: /api/v1/recognition/rewards/catalog

   Get available rewards catalog.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/json

      {
        "success": true,
        "data": {
          "point_based_rewards": [
            {
              "id": "reward-1",
              "name": "Extra Vacation Day",
              "points_required": 1000,
              "category": "time_off"
            }
          ],
          "experience_rewards": [
            {
              "name": "Lunch with Leadership",
              "points_required": 1500,
              "description": "Private lunch with senior leadership"
            }
          ]
        }
      }

🤝 Enhanced Mentorship Endpoints
--------------------------------

**Mentorship Programs**

.. http:post:: /api/v1/mentorship/programs

   Create a new mentorship program.

   :<json string name: Program name (required)
   :<json string description: Program description (optional)
   :<json string program_type: Program type (required)
   :<json integer duration_months: Program duration (required)
   :<json integer max_participants: Maximum participants (required)
   :<json string mentorship_model: Mentorship model (required)
   :<json array focus_areas: Focus areas (required)
   :<json object eligibility_criteria: Eligibility criteria (required)

**Mentor-Mentee Matching**

.. http:post:: /api/v1/mentorship/matching/request

   Request mentor-mentee matching.

   :<json uuid mentee_id: Mentee UUID (required)
   :<json uuid program_id: Program UUID (required)
   :<json array mentorship_goals: Mentorship goals (required)
   :<json object preferred_mentor_characteristics: Mentor preferences (optional)
   :<json array development_priorities: Development priorities (required)
   :<json string learning_style: Learning style preference (optional)

**Mentorship Communities**

.. http:post:: /api/v1/mentorship/communities

   Create a mentorship community.

   :<json string name: Community name (required)
   :<json string description: Community description (optional)
   :<json string community_type: Community type (required)
   :<json array focus_areas: Focus areas (required)
   :<json object membership_criteria: Membership criteria (required)
   :<json array activities: Community activities (optional)

This API reference provides comprehensive documentation for integrating with the PITAS Training System, enabling developers to build powerful training, career development, and employee wellness applications.
