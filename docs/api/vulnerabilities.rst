Vulnerability Management API
=============================

The Vulnerability Management API provides comprehensive CRUD operations for vulnerability tracking, risk assessment, and lifecycle management.

Endpoints Overview
------------------

.. automodule:: pitas.api.v1.endpoints.vulnerabilities
   :members:
   :undoc-members:
   :show-inheritance:

Base URL: ``/api/v1/vulnerabilities``

Authentication
--------------

All vulnerability API endpoints require authentication using JWT tokens:

.. code-block:: http

    Authorization: Bearer <jwt_token>

Create Vulnerability
--------------------

Create a new vulnerability record with comprehensive metadata.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.create_vulnerability

**Request:**

.. code-block:: http

    POST /api/v1/vulnerabilities/
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "cve_id": "CVE-2024-1234",
        "title": "Critical SQL Injection in Web Application",
        "description": "A critical SQL injection vulnerability allows remote code execution",
        "cvss_score": 9.8,
        "cvss_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
        "severity": "critical",
        "status": "discovered",
        "discovery_date": "2024-01-15T10:30:00Z",
        "business_impact_score": 8.5,
        "exploitability_score": 9.0,
        "source": "automated_scanner",
        "tags": ["sql-injection", "web-application", "rce"],
        "extra_data": {
            "scanner": "nessus",
            "plugin_id": "12345",
            "affected_urls": ["/login", "/search"]
        }
    }

**Response:**

.. code-block:: http

    HTTP/1.1 201 Created
    Content-Type: application/json

    {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "cve_id": "CVE-2024-1234",
        "title": "Critical SQL Injection in Web Application",
        "description": "A critical SQL injection vulnerability allows remote code execution",
        "cvss_score": 9.8,
        "cvss_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
        "severity": "critical",
        "status": "discovered",
        "discovery_date": "2024-01-15T10:30:00Z",
        "remediation_date": null,
        "verification_date": null,
        "business_impact_score": 8.5,
        "exploitability_score": 9.0,
        "true_risk_score": null,
        "source": "automated_scanner",
        "tags": ["sql-injection", "web-application", "rce"],
        "extra_data": {
            "scanner": "nessus",
            "plugin_id": "12345",
            "affected_urls": ["/login", "/search"]
        },
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }

List Vulnerabilities
--------------------

Retrieve vulnerabilities with advanced filtering and pagination.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.list_vulnerabilities

**Request:**

.. code-block:: http

    GET /api/v1/vulnerabilities/?severity=critical&severity=high&page=1&page_size=20&cvss_score_min=7.0
    Authorization: Bearer <token>

**Query Parameters:**

* ``page`` (int): Page number (default: 1)
* ``page_size`` (int): Items per page (default: 50, max: 100)
* ``severity`` (list): Filter by severity levels (critical, high, medium, low, informational)
* ``status`` (list): Filter by status values (discovered, confirmed, triaged, in_progress, remediated, verified, closed, false_positive, accepted_risk)
* ``cvss_score_min`` (float): Minimum CVSS score (0.0-10.0)
* ``cvss_score_max`` (float): Maximum CVSS score (0.0-10.0)
* ``discovery_date_from`` (datetime): Filter from discovery date
* ``discovery_date_to`` (datetime): Filter to discovery date
* ``tags`` (list): Filter by tags
* ``source`` (string): Filter by vulnerability source

**Response:**

.. code-block:: http

    HTTP/1.1 200 OK
    Content-Type: application/json

    {
        "vulnerabilities": [
            {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "cve_id": "CVE-2024-1234",
                "title": "Critical SQL Injection in Web Application",
                "severity": "critical",
                "cvss_score": 9.8,
                "status": "discovered",
                "discovery_date": "2024-01-15T10:30:00Z",
                "created_at": "2024-01-15T10:30:00Z"
            }
        ],
        "total_count": 1,
        "page": 1,
        "page_size": 20,
        "filters_applied": {
            "severity": ["critical", "high"],
            "cvss_score_min": 7.0
        }
    }

Get Vulnerability Details
-------------------------

Retrieve detailed vulnerability information including associated assets.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.get_vulnerability

**Request:**

.. code-block:: http

    GET /api/v1/vulnerabilities/550e8400-e29b-41d4-a716-446655440000
    Authorization: Bearer <token>

**Response:**

.. code-block:: http

    HTTP/1.1 200 OK
    Content-Type: application/json

    {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "cve_id": "CVE-2024-1234",
        "title": "Critical SQL Injection in Web Application",
        "description": "A critical SQL injection vulnerability allows remote code execution",
        "cvss_score": 9.8,
        "severity": "critical",
        "status": "discovered",
        "discovery_date": "2024-01-15T10:30:00Z",
        "assets": [
            {
                "id": "660e8400-e29b-41d4-a716-446655440001",
                "name": "web-server-01",
                "asset_type": "server",
                "business_criticality": "high",
                "ip_address": "*************"
            }
        ],
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }

Update Vulnerability
--------------------

Update vulnerability information and lifecycle status.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.update_vulnerability

**Request:**

.. code-block:: http

    PUT /api/v1/vulnerabilities/550e8400-e29b-41d4-a716-446655440000
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "status": "remediated",
        "remediation_date": "2024-01-20T14:30:00Z",
        "notes": "Patched with latest security update"
    }

**Response:**

.. code-block:: http

    HTTP/1.1 200 OK
    Content-Type: application/json

    {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "cve_id": "CVE-2024-1234",
        "title": "Critical SQL Injection in Web Application",
        "status": "remediated",
        "remediation_date": "2024-01-20T14:30:00Z",
        "updated_at": "2024-01-20T14:30:00Z"
    }

Delete Vulnerability
--------------------

Remove a vulnerability record from the system.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.delete_vulnerability

**Request:**

.. code-block:: http

    DELETE /api/v1/vulnerabilities/550e8400-e29b-41d4-a716-446655440000
    Authorization: Bearer <token>

**Response:**

.. code-block:: http

    HTTP/1.1 204 No Content

Dashboard Summary
-----------------

Get comprehensive vulnerability dashboard metrics.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.get_vulnerability_dashboard

**Request:**

.. code-block:: http

    GET /api/v1/vulnerabilities/dashboard/summary
    Authorization: Bearer <token>

**Response:**

.. code-block:: http

    HTTP/1.1 200 OK
    Content-Type: application/json

    {
        "total_vulnerabilities": 1250,
        "by_severity": {
            "critical": 45,
            "high": 180,
            "medium": 520,
            "low": 405,
            "informational": 100
        },
        "by_status": {
            "discovered": 320,
            "confirmed": 180,
            "triaged": 150,
            "in_progress": 200,
            "remediated": 350,
            "verified": 50
        },
        "critical_assets_affected": 23,
        "average_remediation_time_days": 12.5,
        "vulnerability_density": 2.3,
        "trend_data": [
            {
                "date": "2024-01-01",
                "count": 15
            },
            {
                "date": "2024-01-02",
                "count": 23
            }
        ]
    }

Attack Path Analysis
--------------------

Analyze potential attack paths for a vulnerability using graph database.

.. autofunction:: pitas.api.v1.endpoints.vulnerabilities.get_vulnerability_attack_paths

**Request:**

.. code-block:: http

    GET /api/v1/vulnerabilities/550e8400-e29b-41d4-a716-446655440000/attack-paths?max_depth=5
    Authorization: Bearer <token>

**Response:**

.. code-block:: http

    HTTP/1.1 200 OK
    Content-Type: application/json

    {
        "vulnerability_id": "550e8400-e29b-41d4-a716-446655440000",
        "attack_paths": [
            {
                "path": [
                    {
                        "node_type": "Asset",
                        "id": "web-server-01",
                        "properties": {
                            "name": "web-server-01",
                            "business_criticality": "high"
                        }
                    },
                    {
                        "node_type": "Vulnerability",
                        "id": "CVE-2024-1234",
                        "properties": {
                            "cve_id": "CVE-2024-1234",
                            "severity": "critical"
                        }
                    }
                ],
                "depth": 2,
                "risk_score": 9.2
            }
        ]
    }

Error Responses
---------------

The API returns standard HTTP status codes and error messages:

**400 Bad Request:**

.. code-block:: http

    HTTP/1.1 400 Bad Request
    Content-Type: application/json

    {
        "detail": "Invalid CVE ID format. Must start with 'CVE-'"
    }

**401 Unauthorized:**

.. code-block:: http

    HTTP/1.1 401 Unauthorized
    Content-Type: application/json

    {
        "detail": "Invalid authentication credentials"
    }

**404 Not Found:**

.. code-block:: http

    HTTP/1.1 404 Not Found
    Content-Type: application/json

    {
        "detail": "Vulnerability not found"
    }

**422 Validation Error:**

.. code-block:: http

    HTTP/1.1 422 Unprocessable Entity
    Content-Type: application/json

    {
        "detail": [
            {
                "loc": ["body", "cvss_score"],
                "msg": "ensure this value is less than or equal to 10.0",
                "type": "value_error.number.not_le",
                "ctx": {"limit_value": 10.0}
            }
        ]
    }

Rate Limiting
-------------

API endpoints are rate-limited to prevent abuse:

* **Standard endpoints**: 100 requests per minute per user
* **Dashboard endpoints**: 10 requests per minute per user
* **Bulk operations**: 5 requests per minute per user

Rate limit headers are included in responses:

.. code-block:: http

    X-RateLimit-Limit: 100
    X-RateLimit-Remaining: 95
    X-RateLimit-Reset: 1640995200

Bulk Operations
---------------

For high-volume operations, use the bulk endpoints:

**Bulk Create:**

.. code-block:: http

    POST /api/v1/vulnerabilities/bulk
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "vulnerabilities": [
            {
                "cve_id": "CVE-2024-1234",
                "title": "Vulnerability 1",
                "severity": "high"
            },
            {
                "cve_id": "CVE-2024-1235",
                "title": "Vulnerability 2",
                "severity": "medium"
            }
        ]
    }

**Bulk Update:**

.. code-block:: http

    PUT /api/v1/vulnerabilities/bulk
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "updates": [
            {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "remediated"
            },
            {
                "id": "550e8400-e29b-41d4-a716-446655440001",
                "status": "in_progress"
            }
        ]
    }
