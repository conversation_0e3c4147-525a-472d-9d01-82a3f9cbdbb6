Quick Start Guide
==================

Get up and running with the PITAS Training System in minutes! This guide will walk you through the essential steps to start managing training and competencies.

🚀 Prerequisites
-----------------

Before you begin, ensure you have:

* Python 3.11 or higher installed
* PostgreSQL 13+ running locally or accessible remotely
* Git for cloning the repository
* Basic familiarity with REST APIs

⚡ 5-Minute Setup
-----------------

**1. <PERSON><PERSON> and Setup**

.. code-block:: bash

   # Clone the repository
   git clone https://github.com/forkrul/pitas.git
   cd pitas

   # Quick setup with <PERSON> (recommended)
   nix-shell
   make setup

   # Or manual setup
   python -m venv .venv
   source .venv/bin/activate
   pip install -e ".[dev]"

**2. Configure Environment**

.. code-block:: bash

   # Copy environment template
   cp .env.example .env
   
   # Edit .env with your database settings
   DATABASE_URL=postgresql://user:pass@localhost:5432/pitas_db
   SECRET_KEY=your-secret-key-here

**3. Initialize Database**

.. code-block:: bash

   # Create database and run migrations
   createdb pitas_db
   alembic upgrade head

**4. Start the Application**

.. code-block:: bash

   # Start the development server
   make run
   
   # Or directly with uvicorn
   uvicorn pitas.main:app --reload

**5. Verify Installation**

Open your browser to http://localhost:8000/api/v1/docs to see the interactive API documentation.

🎯 First Steps Tutorial
-----------------------

Let's create a complete training workflow from scratch:

**Step 1: Create a Competency Framework**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/training/frameworks" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Penetration Testing Competencies",
          "description": "Core competencies for penetration testers",
          "version": "1.0",
          "work_role_id": "SP-TES-001",
          "specialty_area": "Vulnerability Assessment",
          "category": "Securely Provision"
        }'

**Step 2: Add a Competency**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/training/competencies" \
        -H "Content-Type: application/json" \
        -d '{
          "framework_id": "YOUR_FRAMEWORK_ID",
          "competency_id": "K0001",
          "name": "Network Security Knowledge",
          "description": "Understanding of network security concepts",
          "knowledge_statements": ["TCP/IP", "Firewalls", "VPNs"],
          "skill_statements": ["Network scanning", "Vulnerability assessment"],
          "ability_statements": ["Analyze network traffic", "Identify vulnerabilities"]
        }'

**Step 3: Create a Training Course**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/training/courses" \
        -H "Content-Type: application/json" \
        -d '{
          "title": "Introduction to Penetration Testing",
          "description": "Beginner-friendly penetration testing course",
          "provider": "Internal",
          "duration_hours": 20,
          "difficulty_level": "advanced_beginner",
          "learning_objectives": [
            "Understand penetration testing methodology",
            "Learn basic scanning techniques",
            "Practice vulnerability identification"
          ],
          "cost": 0.0
        }'

**Step 4: Enroll a User**

.. code-block:: bash

   curl -X POST "http://localhost:8000/api/v1/training/enrollments" \
        -H "Content-Type: application/json" \
        -d '{
          "user_id": "YOUR_USER_ID",
          "course_id": "YOUR_COURSE_ID",
          "status": "not_started"
        }'

**Step 5: Track Progress**

.. code-block:: bash

   curl -X PUT "http://localhost:8000/api/v1/training/enrollments/YOUR_ENROLLMENT_ID" \
        -H "Content-Type: application/json" \
        -d '{
          "status": "in_progress",
          "progress_percentage": 50.0,
          "assessment_scores": {
            "module_1_quiz": 85.0,
            "module_2_quiz": 92.0
          },
          "time_spent_hours": 10.5
        }'

🎓 Common Workflows
-------------------

**Workflow 1: Skills Assessment and Gap Analysis**

.. mermaid::

   flowchart LR
       A[Create Framework] --> B[Define Competencies]
       B --> C[Assess Current Skills]
       C --> D[Identify Gaps]
       D --> E[Recommend Training]
       E --> F[Create Learning Path]

.. code-block:: bash

   # 1. Assess user skills
   curl -X POST "http://localhost:8000/api/v1/training/assessments" \
        -H "Content-Type: application/json" \
        -d '{
          "user_id": "user-uuid",
          "competency_id": "competency-uuid",
          "current_level": "novice",
          "target_level": "competent"
        }'

   # 2. Get gap analysis
   curl "http://localhost:8000/api/v1/training/assessments/users/USER_ID/gap-analysis"

**Workflow 2: Certification Pathway Management**

.. code-block:: bash

   # 1. Create certification
   curl -X POST "http://localhost:8000/api/v1/training/certifications" \
        -H "Content-Type: application/json" \
        -d '{
          "name": "Certified Ethical Hacker",
          "abbreviation": "CEH",
          "provider": "EC-Council",
          "level": "advanced_beginner",
          "renewal_period_years": 3
        }'

   # 2. Record achievement
   curl -X POST "http://localhost:8000/api/v1/training/certifications/achievements" \
        -H "Content-Type: application/json" \
        -d '{
          "user_id": "user-uuid",
          "certification_id": "cert-uuid",
          "status": "achieved",
          "achievement_date": "2025-06-16T10:30:00Z"
        }'

**Workflow 3: CTF Challenge Platform**

.. code-block:: bash

   # 1. Create CTF challenge
   curl -X POST "http://localhost:8000/api/v1/training/ctf/challenges" \
        -H "Content-Type: application/json" \
        -d '{
          "title": "Basic Buffer Overflow",
          "description": "Exploit a simple buffer overflow",
          "category": "pwn",
          "difficulty": "advanced_beginner",
          "points": 100,
          "flag": "flag{buffer_overflow_basics}",
          "created_by": "admin-uuid"
        }'

   # 2. Submit flag
   curl -X POST "http://localhost:8000/api/v1/training/ctf/submissions" \
        -H "Content-Type: application/json" \
        -d '{
          "user_id": "user-uuid",
          "challenge_id": "challenge-uuid",
          "submitted_flag": "flag{buffer_overflow_basics}",
          "is_correct": true
        }'

   # 3. Check leaderboard
   curl "http://localhost:8000/api/v1/training/ctf/leaderboard?limit=10"

📊 Monitoring and Analytics
---------------------------

**Check System Health**

.. code-block:: bash

   curl "http://localhost:8000/api/v1/health"

**View Training Analytics**

.. code-block:: bash

   # Get user progress
   curl "http://localhost:8000/api/v1/training/enrollments/users/USER_ID/courses/COURSE_ID/progress"

   # Get certification pathway
   curl "http://localhost:8000/api/v1/training/certifications/pathways/users/USER_ID?target_level=proficient"

🔧 Development Tools
--------------------

**Interactive API Documentation**

* **Swagger UI**: http://localhost:8000/api/v1/docs
* **ReDoc**: http://localhost:8000/api/v1/redoc
* **OpenAPI Schema**: http://localhost:8000/api/v1/openapi.json

**Testing**

.. code-block:: bash

   # Run all tests
   make test

   # Run specific test file
   pytest tests/api/test_training.py

   # Run with coverage
   make test-cov

**Code Quality**

.. code-block:: bash

   # Format code
   make format

   # Lint code
   make lint

   # Type checking
   make type-check

🎯 Next Steps
-------------

Now that you have the basics working, explore these advanced features:

1. **Competency Management** - :doc:`guides/competency-management`
2. **Training Courses** - :doc:`guides/training-courses`
3. **Certification Tracking** - :doc:`guides/certification-tracking`
4. **CTF Platform** - :doc:`guides/ctf-platform`
5. **Mentorship Program** - :doc:`guides/mentorship-program`

**Production Deployment**

When ready for production:

1. **Configuration** - :doc:`deployment/configuration`
2. **Database Setup** - :doc:`deployment/database`
3. **Monitoring** - :doc:`deployment/monitoring`

🆘 Getting Help
---------------

If you encounter issues:

1. **Check the logs** - Look for error messages in the console
2. **Verify database connection** - Ensure PostgreSQL is running
3. **Review configuration** - Check your .env file settings
4. **Consult documentation** - Browse the full documentation
5. **GitHub Issues** - Report bugs or request features

**Common Issues:**

* **Database connection errors** - Check PostgreSQL service and credentials
* **Import errors** - Ensure virtual environment is activated
* **Port conflicts** - Change the port if 8000 is in use
* **Permission errors** - Check file permissions and user access

Congratulations! You now have a fully functional PITAS Training System. Start exploring the features and building your training and competency management workflows.
