#!/usr/bin/env python3
"""
Simple test to verify shell.nix environment is working correctly.
This test can be run both inside and outside the Nix shell.
"""

import os
import sys
import subprocess
from pathlib import Path

def test_shell_nix():
    """Test shell.nix environment functionality."""
    print("🔍 Testing Shell.nix Environment")
    print("=" * 50)
    
    # Check if we're in a Nix shell
    in_nix_shell = os.environ.get('IN_NIX_SHELL', '0') == '1'
    print(f"In Nix Shell: {'Yes' if in_nix_shell else 'No'}")
    
    # Test Python availability
    try:
        python_version = sys.version
        print(f"✅ Python: {python_version.split()[0]}")
    except Exception as e:
        print(f"❌ Python: {e}")
        return False
    
    # Test basic tools availability
    tools_to_test = [
        ('python', '--version'),
        ('pip', '--version'),
        ('git', '--version'),
    ]
    
    success_count = 0
    for tool, version_flag in tools_to_test:
        try:
            result = subprocess.run([tool, version_flag], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip().split('\n')[0]
                print(f"✅ {tool}: {version}")
                success_count += 1
            else:
                print(f"❌ {tool}: Command failed")
        except subprocess.TimeoutExpired:
            print(f"❌ {tool}: Timeout")
        except FileNotFoundError:
            print(f"❌ {tool}: Not found")
        except Exception as e:
            print(f"❌ {tool}: {e}")
    
    # Test environment variables
    env_vars = [
        'PYTHONPATH',
        'PITAS_ENV',
        'PATH'
    ]
    
    print("\n🔧 Environment Variables:")
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        if var == 'PATH':
            # Show only relevant PATH entries
            path_entries = value.split(':') if value != 'Not set' else []
            relevant_paths = [p for p in path_entries if 'nix' in p.lower() or 'python' in p.lower()][:3]
            if relevant_paths:
                print(f"  {var}: {':'.join(relevant_paths)}... (truncated)")
            else:
                print(f"  {var}: (no relevant entries found)")
        else:
            print(f"  {var}: {value}")
    
    # Test PITAS imports
    print("\n🐍 Testing PITAS Imports:")
    sys.path.insert(0, str(Path('.') / 'src'))
    
    test_imports = [
        ('pitas.core.config', 'settings'),
        ('pitas.db.models', 'User'),
        ('pitas.services.user', 'UserService'),
    ]
    
    import_success = 0
    for module, item in test_imports:
        try:
            __import__(module, fromlist=[item])
            print(f"✅ {module}.{item}")
            import_success += 1
        except Exception as e:
            print(f"❌ {module}.{item}: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results:")
    print(f"  Tools: {success_count}/{len(tools_to_test)} working")
    print(f"  Imports: {import_success}/{len(test_imports)} working")
    print(f"  Overall: {'✅ PASS' if success_count >= 2 and import_success >= 2 else '❌ FAIL'}")
    
    return success_count >= 2 and import_success >= 2

if __name__ == "__main__":
    success = test_shell_nix()
    sys.exit(0 if success else 1)
