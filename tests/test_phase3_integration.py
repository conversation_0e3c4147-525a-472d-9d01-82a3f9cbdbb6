"""Integration tests for Phase 3 vulnerability management."""

import pytest
from datetime import datetime
from decimal import Decimal
from uuid import uuid4

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from pitas.main import app
from pitas.db.models.vulnerability import (
    Vulnerability, Asset, AssetVulnerability,
    VulnerabilitySeverity, VulnerabilityStatus, AssetCriticality
)
from pitas.schemas.vulnerability import (
    VulnerabilityCreate, AssetCreate, AssetVulnerabilityCreate
)


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


@pytest.fixture
async def sample_vulnerability_data():
    """Sample vulnerability data for testing."""
    return VulnerabilityCreate(
        cve_id="CVE-2023-1234",
        title="Test SQL Injection Vulnerability",
        description="A critical SQL injection vulnerability in web application",
        cvss_score=Decimal("9.8"),
        cvss_vector="CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
        severity=VulnerabilitySeverity.CRITICAL,
        status=VulnerabilityStatus.DISCOVERED,
        discovery_date=datetime.utcnow(),
        business_impact_score=Decimal("8.5"),
        exploitability_score=Decimal("9.0"),
        source="automated_scanner",
        tags=["sql-injection", "web-application", "critical"],
        extra_data={"scanner": "nessus", "plugin_id": "12345"}
    )


@pytest.fixture
async def sample_asset_data():
    """Sample asset data for testing."""
    return AssetCreate(
        name="web-server-01",
        asset_type="server",
        ip_address="*************",
        hostname="web01.example.com",
        business_criticality=AssetCriticality.HIGH,
        business_processes=["customer_portal", "e_commerce"],
        compliance_requirements=["PCI_DSS", "SOX"],
        extra_data={"os": "Ubuntu 20.04", "services": ["nginx", "mysql"]}
    )


class TestVulnerabilityAPI:
    """Test vulnerability management API endpoints."""
    
    def test_create_vulnerability(self, client, sample_vulnerability_data):
        """Test creating a new vulnerability."""
        response = client.post(
            "/api/v1/vulnerabilities/",
            json=sample_vulnerability_data.model_dump(mode='json'),
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Note: This will fail without proper auth setup, but tests the endpoint structure
        assert response.status_code in [201, 401, 422]  # Created, Unauthorized, or Validation Error
    
    def test_list_vulnerabilities(self, client):
        """Test listing vulnerabilities with filters."""
        response = client.get(
            "/api/v1/vulnerabilities/?severity=critical&page=1&page_size=10",
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code in [200, 401]  # OK or Unauthorized
    
    def test_vulnerability_dashboard(self, client):
        """Test vulnerability dashboard endpoint."""
        response = client.get(
            "/api/v1/vulnerabilities/dashboard/summary",
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code in [200, 401]  # OK or Unauthorized


class TestAssetAPI:
    """Test asset management API endpoints."""
    
    def test_create_asset(self, client, sample_asset_data):
        """Test creating a new asset."""
        response = client.post(
            "/api/v1/assets/",
            json=sample_asset_data.model_dump(mode='json'),
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code in [201, 401, 422]  # Created, Unauthorized, or Validation Error
    
    def test_list_assets(self, client):
        """Test listing assets with filters."""
        response = client.get(
            "/api/v1/assets/?asset_type=server&business_criticality=high",
            headers={"Authorization": "Bearer test-token"}
        )
        
        assert response.status_code in [200, 401]  # OK or Unauthorized


class TestVulnerabilitySchemas:
    """Test Pydantic schemas for vulnerability management."""
    
    def test_vulnerability_create_schema(self, sample_vulnerability_data):
        """Test vulnerability creation schema validation."""
        # Test valid data
        assert sample_vulnerability_data.cve_id == "CVE-2023-1234"
        assert sample_vulnerability_data.severity == VulnerabilitySeverity.CRITICAL
        assert sample_vulnerability_data.cvss_score == Decimal("9.8")
    
    def test_vulnerability_cve_validation(self):
        """Test CVE ID validation."""
        # Valid CVE ID
        valid_vuln = VulnerabilityCreate(
            cve_id="CVE-2023-5678",
            title="Test Vulnerability",
            discovery_date=datetime.utcnow()
        )
        assert valid_vuln.cve_id == "CVE-2023-5678"
        
        # Invalid CVE ID should raise validation error
        with pytest.raises(ValueError):
            VulnerabilityCreate(
                cve_id="INVALID-2023-5678",
                title="Test Vulnerability",
                discovery_date=datetime.utcnow()
            )
    
    def test_asset_create_schema(self, sample_asset_data):
        """Test asset creation schema validation."""
        assert sample_asset_data.name == "web-server-01"
        assert sample_asset_data.business_criticality == AssetCriticality.HIGH
        assert "customer_portal" in sample_asset_data.business_processes


class TestDatabaseModels:
    """Test database models for Phase 3."""
    
    def test_vulnerability_model_creation(self):
        """Test vulnerability model instantiation."""
        vuln = Vulnerability(
            cve_id="CVE-2023-9999",
            title="Test Vulnerability",
            severity=VulnerabilitySeverity.HIGH,
            status=VulnerabilityStatus.DISCOVERED,
            discovery_date=datetime.utcnow(),
            cvss_score=Decimal("7.5")
        )
        
        assert vuln.cve_id == "CVE-2023-9999"
        assert vuln.severity == VulnerabilitySeverity.HIGH
        assert vuln.cvss_score == Decimal("7.5")
    
    def test_asset_model_creation(self):
        """Test asset model instantiation."""
        asset = Asset(
            name="test-server",
            asset_type="server",
            business_criticality=AssetCriticality.MEDIUM,
            ip_address="********"
        )
        
        assert asset.name == "test-server"
        assert asset.asset_type == "server"
        assert asset.business_criticality == AssetCriticality.MEDIUM
    
    def test_asset_vulnerability_association(self):
        """Test asset-vulnerability association model."""
        association = AssetVulnerability(
            asset_id=uuid4(),
            vulnerability_id=uuid4(),
            impact_level="high",
            exploitability_likelihood=Decimal("8.0"),
            remediation_priority=1
        )
        
        assert association.impact_level == "high"
        assert association.exploitability_likelihood == Decimal("8.0")
        assert association.remediation_priority == 1


class TestPhase3Configuration:
    """Test Phase 3 configuration and setup."""
    
    def test_neo4j_configuration(self):
        """Test Neo4j configuration settings."""
        from pitas.core.config import settings
        
        assert hasattr(settings, 'neo4j_url')
        assert hasattr(settings, 'neo4j_user')
        assert hasattr(settings, 'neo4j_password')
    
    def test_influxdb_configuration(self):
        """Test InfluxDB configuration settings."""
        from pitas.core.config import settings
        
        assert hasattr(settings, 'influxdb_url')
        assert hasattr(settings, 'influxdb_token')
        assert hasattr(settings, 'influxdb_org')
        assert hasattr(settings, 'influxdb_bucket')
    
    def test_database_models_import(self):
        """Test that all Phase 3 models can be imported."""
        from pitas.db.models.vulnerability import (
            Vulnerability, Asset, AssetVulnerability, VulnerabilityMetric
        )
        from pitas.db.models.risk_assessment import (
            RiskAssessment, ThreatIntelligence, RemediationPlan
        )
        
        # If we can import them, the models are properly defined
        assert Vulnerability is not None
        assert Asset is not None
        assert AssetVulnerability is not None
        assert VulnerabilityMetric is not None
        assert RiskAssessment is not None
        assert ThreatIntelligence is not None
        assert RemediationPlan is not None


class TestAPIDocumentation:
    """Test API documentation and OpenAPI schema."""
    
    def test_openapi_schema_generation(self, client):
        """Test that OpenAPI schema can be generated."""
        response = client.get("/api/v1/openapi.json")
        assert response.status_code == 200
        
        schema = response.json()
        assert "openapi" in schema
        assert "paths" in schema
        
        # Check that Phase 3 endpoints are documented
        paths = schema["paths"]
        assert any("/vulnerabilities" in path for path in paths.keys())
        assert any("/assets" in path for path in paths.keys())
    
    def test_docs_endpoint_accessible(self, client):
        """Test that documentation endpoint is accessible."""
        response = client.get("/api/v1/docs")
        assert response.status_code == 200


# Integration test markers
pytestmark = [
    pytest.mark.integration,
    pytest.mark.phase3
]
