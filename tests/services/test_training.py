"""Tests for training services."""

import pytest
from datetime import datetime
from uuid import uuid4

from pitas.db.models.training import (
    CompetencyFramework,
    Competency,
    TrainingCourse,
    Certification,
    CTFChallenge,
    MentorshipPair,
    CompetencyLevel,
    TrainingStatus,
    CertificationStatus,
)
from pitas.schemas.training import (
    CompetencyFrameworkCreate,
    CompetencyCreate,
    TrainingCourseCreate,
    CertificationCreate,
    CTFChallengeCreate,
    MentorshipPairCreate,
    SkillAssessmentCreate,
    TrainingEnrollmentCreate,
    CTFSubmissionCreate,
)
from pitas.services.training import (
    CompetencyService,
    TrainingService,
    CertificationService,
    CTFService,
    MentorshipService,
)


class TestCompetencyService:
    """Test competency service functionality."""

    @pytest.mark.asyncio
    async def test_create_framework(self, db_session):
        """Test creating a competency framework."""
        service = CompetencyService(db_session)
        
        framework_data = CompetencyFrameworkCreate(
            name="NICE Cybersecurity Framework",
            description="NICE Cybersecurity Workforce Framework",
            version="1.0",
            work_role_id="SP-RSK-001",
            specialty_area="Risk Management",
            category="Securely Provision"
        )
        
        framework = await service.create_framework(framework_data)
        
        assert framework.name == "NICE Cybersecurity Framework"
        assert framework.work_role_id == "SP-RSK-001"
        assert framework.id is not None

    @pytest.mark.asyncio
    async def test_create_competency(self, db_session):
        """Test creating a competency."""
        service = CompetencyService(db_session)
        
        # First create a framework
        framework_data = CompetencyFrameworkCreate(
            name="Test Framework",
            version="1.0",
            work_role_id="TEST-001",
            specialty_area="Testing",
            category="Test"
        )
        framework = await service.create_framework(framework_data)
        
        # Then create a competency
        competency_data = CompetencyCreate(
            framework_id=framework.id,
            competency_id="K0001",
            name="Knowledge of computer networking concepts",
            description="Understanding of networking protocols and concepts",
            knowledge_statements=["TCP/IP", "OSI Model", "Routing"],
            skill_statements=["Network troubleshooting", "Protocol analysis"],
            ability_statements=["Analyze network traffic", "Design network architecture"]
        )
        
        competency = await service.create_competency(competency_data)
        
        assert competency.name == "Knowledge of computer networking concepts"
        assert competency.competency_id == "K0001"
        assert competency.framework_id == framework.id

    @pytest.mark.asyncio
    async def test_skill_assessment(self, db_session):
        """Test skill assessment functionality."""
        service = CompetencyService(db_session)
        
        # Create framework and competency
        framework_data = CompetencyFrameworkCreate(
            name="Test Framework",
            version="1.0",
            work_role_id="TEST-001",
            specialty_area="Testing",
            category="Test"
        )
        framework = await service.create_framework(framework_data)
        
        competency_data = CompetencyCreate(
            framework_id=framework.id,
            competency_id="K0001",
            name="Test Competency",
            description="Test competency description"
        )
        competency = await service.create_competency(competency_data)
        
        # Create skill assessment
        user_id = uuid4()
        assessment_data = SkillAssessmentCreate(
            user_id=user_id,
            competency_id=competency.id,
            current_level=CompetencyLevel.NOVICE,
            target_level=CompetencyLevel.COMPETENT,
            notes="Initial assessment"
        )
        
        assessment = await service.create_skill_assessment(assessment_data)
        
        assert assessment.current_level == CompetencyLevel.NOVICE
        assert assessment.target_level == CompetencyLevel.COMPETENT
        assert assessment.user_id == user_id


class TestTrainingService:
    """Test training service functionality."""

    @pytest.mark.asyncio
    async def test_create_course(self, db_session):
        """Test creating a training course."""
        service = TrainingService(db_session)
        
        course_data = TrainingCourseCreate(
            title="Introduction to Penetration Testing",
            description="Basic penetration testing concepts and techniques",
            provider="SANS",
            course_code="SEC560",
            duration_hours=40,
            difficulty_level=CompetencyLevel.ADVANCED_BEGINNER,
            learning_objectives=["Understand pen testing methodology", "Learn basic tools"],
            is_certification_prep=True,
            cost=5000.0
        )
        
        course = await service.create_course(course_data)
        
        assert course.title == "Introduction to Penetration Testing"
        assert course.provider == "SANS"
        assert course.difficulty_level == CompetencyLevel.ADVANCED_BEGINNER
        assert course.cost == 5000.0

    @pytest.mark.asyncio
    async def test_enroll_user(self, db_session):
        """Test enrolling a user in a course."""
        service = TrainingService(db_session)
        
        # Create a course first
        course_data = TrainingCourseCreate(
            title="Test Course",
            difficulty_level=CompetencyLevel.NOVICE
        )
        course = await service.create_course(course_data)
        
        # Enroll a user
        user_id = uuid4()
        enrollment_data = TrainingEnrollmentCreate(
            user_id=user_id,
            course_id=course.id,
            status=TrainingStatus.NOT_STARTED
        )
        
        enrollment = await service.enroll_user(enrollment_data)
        
        assert enrollment.user_id == user_id
        assert enrollment.course_id == course.id
        assert enrollment.status == TrainingStatus.NOT_STARTED


class TestCertificationService:
    """Test certification service functionality."""

    @pytest.mark.asyncio
    async def test_create_certification(self, db_session):
        """Test creating a certification."""
        service = CertificationService(db_session)
        
        cert_data = CertificationCreate(
            name="Certified Ethical Hacker",
            abbreviation="CEH",
            provider="EC-Council",
            description="Entry-level ethical hacking certification",
            level=CompetencyLevel.ADVANCED_BEGINNER,
            renewal_period_years=3,
            cpe_credits_required=120,
            exam_cost=1199.0
        )
        
        certification = await service.create_certification(cert_data)
        
        assert certification.name == "Certified Ethical Hacker"
        assert certification.abbreviation == "CEH"
        assert certification.provider == "EC-Council"
        assert certification.level == CompetencyLevel.ADVANCED_BEGINNER


class TestCTFService:
    """Test CTF service functionality."""

    @pytest.mark.asyncio
    async def test_create_challenge(self, db_session):
        """Test creating a CTF challenge."""
        service = CTFService(db_session)
        
        creator_id = uuid4()
        challenge_data = CTFChallengeCreate(
            title="Basic Buffer Overflow",
            description="Exploit a simple buffer overflow vulnerability",
            category="pwn",
            difficulty=CompetencyLevel.ADVANCED_BEGINNER,
            points=100,
            flag="flag{buffer_overflow_basics}",
            hints=["Check the input validation", "Look for stack overflow"],
            created_by=creator_id
        )
        
        challenge = await service.create_challenge(challenge_data)
        
        assert challenge.title == "Basic Buffer Overflow"
        assert challenge.category == "pwn"
        assert challenge.points == 100
        assert challenge.created_by == creator_id

    @pytest.mark.asyncio
    async def test_submit_flag(self, db_session):
        """Test submitting a flag for a CTF challenge."""
        service = CTFService(db_session)
        
        # Create a challenge first
        creator_id = uuid4()
        challenge_data = CTFChallengeCreate(
            title="Test Challenge",
            category="test",
            difficulty=CompetencyLevel.NOVICE,
            points=50,
            flag="flag{test_flag}",
            created_by=creator_id
        )
        challenge = await service.create_challenge(challenge_data)
        
        # Submit correct flag
        user_id = uuid4()
        submission_data = CTFSubmissionCreate(
            user_id=user_id,
            challenge_id=challenge.id,
            submitted_flag="flag{test_flag}",
            is_correct=True
        )
        
        submission = await service.submit_flag(submission_data)
        
        assert submission.is_correct is True
        assert submission.points_awarded == 50
        assert submission.user_id == user_id


class TestMentorshipService:
    """Test mentorship service functionality."""

    @pytest.mark.asyncio
    async def test_create_mentorship_pair(self, db_session):
        """Test creating a mentorship pair."""
        service = MentorshipService(db_session)
        
        mentor_id = uuid4()
        mentee_id = uuid4()
        
        pair_data = MentorshipPairCreate(
            mentor_id=mentor_id,
            mentee_id=mentee_id,
            goals=["Improve penetration testing skills", "Career development"],
            meeting_frequency="weekly"
        )
        
        pair = await service.create_mentorship_pair(pair_data)
        
        assert pair.mentor_id == mentor_id
        assert pair.mentee_id == mentee_id
        assert pair.meeting_frequency == "weekly"
        assert pair.is_active is True

    @pytest.mark.asyncio
    async def test_end_mentorship(self, db_session):
        """Test ending a mentorship relationship."""
        service = MentorshipService(db_session)
        
        # Create a mentorship pair first
        mentor_id = uuid4()
        mentee_id = uuid4()
        
        pair_data = MentorshipPairCreate(
            mentor_id=mentor_id,
            mentee_id=mentee_id
        )
        pair = await service.create_mentorship_pair(pair_data)
        
        # End the mentorship
        ended_pair = await service.end_mentorship(pair.id, satisfaction_rating=4.5)
        
        assert ended_pair.is_active is False
        assert ended_pair.satisfaction_rating == 4.5
        assert ended_pair.end_date is not None
