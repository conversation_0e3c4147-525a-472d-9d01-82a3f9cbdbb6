"""Tests for security frameworks API endpoints."""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient

from pitas.main import app
from pitas.integrations.orchestrator import EnrichedVulnerability
from pitas.integrations.mitre import MITReTechnique
from pitas.integrations.cvss import CVSSScore
from pitas.integrations.nist import NISTAssessmentResult, NISTFunction, NISTImplementationTier


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    return MagicMock(id="test-user-123", email="<EMAIL>")


@pytest.fixture
def sample_vulnerability_input():
    """Sample vulnerability input for API testing."""
    return {
        "id": "CVE-2023-1234",
        "title": "Test Vulnerability",
        "description": "A test vulnerability for API testing",
        "severity": "High",
        "cve_id": "CVE-2023-1234",
        "affected_systems": ["web-server"],
        "discovery_date": "2023-12-01"
    }


@pytest.fixture
def mock_enriched_vulnerability():
    """Mock enriched vulnerability for testing."""
    return EnrichedVulnerability(
        vulnerability_id="CVE-2023-1234",
        title="Test Vulnerability",
        description="A test vulnerability",
        severity="High",
        mitre_techniques=[
            MITReTechnique(
                technique_id="T1055",
                name="Process Injection",
                description="Test technique",
                tactic="defense-evasion",
                platform=["Windows"],
                data_sources=["Process"],
                url="https://attack.mitre.org/techniques/T1055"
            )
        ],
        mitre_confidence=0.8,
        cvss_scores=CVSSScore(
            base_score=8.5,
            base_severity="High",
            vector_string="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N"
        ),
        nist_controls=["PR.AC-01", "DE.AE-01"],
        risk_score=8.5,
        business_impact="High",
        remediation_priority="High"
    )


@pytest.fixture
def mock_mitre_techniques():
    """Mock MITRE techniques for testing."""
    return [
        MITReTechnique(
            technique_id="T1055",
            name="Process Injection",
            description="Adversaries may inject code into processes",
            tactic="defense-evasion",
            platform=["Windows", "Linux"],
            data_sources=["Process Creation"],
            url="https://attack.mitre.org/techniques/T1055"
        ),
        MITReTechnique(
            technique_id="T1059",
            name="Command and Scripting Interpreter",
            description="Adversaries may abuse command interpreters",
            tactic="execution",
            platform=["Windows", "Linux"],
            data_sources=["Command Execution"],
            url="https://attack.mitre.org/techniques/T1059"
        )
    ]


class TestVulnerabilityAnalysisEndpoint:
    """Test vulnerability analysis endpoint."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_analyze_vulnerability_success(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        sample_vulnerability_input,
        mock_enriched_vulnerability
    ):
        """Test successful vulnerability analysis."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.correlate_security_data.return_value = mock_enriched_vulnerability
        
        response = client.post(
            "/api/v1/security-frameworks/vulnerabilities/analyze",
            json=sample_vulnerability_input
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["vulnerability_id"] == "CVE-2023-1234"
        assert data["title"] == "Test Vulnerability"
        assert data["risk_score"] == 8.5
        assert data["business_impact"] == "High"
        assert data["remediation_priority"] == "High"
        assert len(data["mitre_techniques"]) == 1
        assert len(data["nist_controls"]) == 2
        assert data["cvss_scores"]["base_score"] == 8.5
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_analyze_vulnerability_error(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        sample_vulnerability_input
    ):
        """Test vulnerability analysis with error."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.correlate_security_data.side_effect = Exception("Analysis failed")
        
        response = client.post(
            "/api/v1/security-frameworks/vulnerabilities/analyze",
            json=sample_vulnerability_input
        )
        
        assert response.status_code == 500
        assert "Vulnerability analysis failed" in response.json()["detail"]
    
    def test_analyze_vulnerability_invalid_input(self, client):
        """Test vulnerability analysis with invalid input."""
        invalid_input = {
            "id": "",  # Empty ID
            "title": "Test",
            # Missing required fields
        }
        
        response = client.post(
            "/api/v1/security-frameworks/vulnerabilities/analyze",
            json=invalid_input
        )
        
        assert response.status_code == 422  # Validation error


class TestMITRETechniquesEndpoints:
    """Test MITRE techniques endpoints."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_get_mitre_techniques(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        mock_mitre_techniques
    ):
        """Test getting MITRE techniques."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.get_techniques.return_value = {
            "T1055": mock_mitre_techniques[0],
            "T1059": mock_mitre_techniques[1]
        }
        
        response = client.get("/api/v1/security-frameworks/mitre/techniques?limit=2")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data) == 2
        assert data[0]["technique_id"] == "T1055"
        assert data[1]["technique_id"] == "T1059"
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_search_mitre_techniques(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        mock_mitre_techniques
    ):
        """Test searching MITRE techniques."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.search_techniques.return_value = [mock_mitre_techniques[0]]
        
        response = client.get("/api/v1/security-frameworks/mitre/techniques?search=process&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data) == 1
        assert data[0]["technique_id"] == "T1055"
        assert "Process Injection" in data[0]["name"]
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_get_mitre_technique_by_id(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        mock_mitre_techniques
    ):
        """Test getting specific MITRE technique."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.get_technique_by_id.return_value = mock_mitre_techniques[0]
        
        response = client.get("/api/v1/security-frameworks/mitre/techniques/T1055")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["technique_id"] == "T1055"
        assert data["name"] == "Process Injection"
        assert data["tactic"] == "defense-evasion"
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_get_mitre_technique_not_found(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test getting non-existent MITRE technique."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.get_technique_by_id.return_value = None
        
        response = client.get("/api/v1/security-frameworks/mitre/techniques/T9999")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]


class TestCVSSEndpoints:
    """Test CVSS calculation endpoints."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_calculate_cvss_score(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        sample_vulnerability_input
    ):
        """Test CVSS score calculation."""
        mock_get_user.return_value = mock_user
        mock_score = CVSSScore(
            base_score=7.5,
            base_severity="High",
            vector_string="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N"
        )
        mock_orchestrator.cvss_calculator.calculate_score_with_context.return_value = mock_score
        
        response = client.post(
            "/api/v1/security-frameworks/cvss/calculate",
            json=sample_vulnerability_input
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["base_score"] == 7.5
        assert data["base_severity"] == "High"
        assert data["vector_string"].startswith("CVSS:4.0")
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_calculate_cvss_score_error(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user,
        sample_vulnerability_input
    ):
        """Test CVSS calculation with error."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.cvss_calculator.calculate_score_with_context.side_effect = Exception("CVSS Error")
        
        response = client.post(
            "/api/v1/security-frameworks/cvss/calculate",
            json=sample_vulnerability_input
        )
        
        assert response.status_code == 500
        assert "CVSS calculation failed" in response.json()["detail"]


class TestNISTEndpoints:
    """Test NIST CSF endpoints."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_assess_nist_function(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test NIST function assessment."""
        mock_get_user.return_value = mock_user
        mock_assessment = NISTAssessmentResult(
            function=NISTFunction.IDENTIFY,
            current_tier=NISTImplementationTier.PARTIAL,
            target_tier=NISTImplementationTier.REPEATABLE,
            maturity_score=65.0,
            gaps=["Incomplete asset inventory"],
            recommendations=["Implement comprehensive asset management"]
        )
        mock_orchestrator.nist_assessor.assess_function_maturity.return_value = mock_assessment
        
        assessment_data = {"organization": "Test Org", "scope": "IT Infrastructure"}
        
        response = client.post(
            "/api/v1/security-frameworks/nist/assess/ID",
            json=assessment_data
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["function"] == "ID"
        assert data["current_tier"] == "Partial"
        assert data["target_tier"] == "Repeatable"
        assert data["maturity_score"] == 65.0
        assert len(data["gaps"]) > 0
        assert len(data["recommendations"]) > 0
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_generate_compliance_report(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test NIST compliance report generation."""
        mock_get_user.return_value = mock_user
        mock_report = {
            "assessment_date": "2023-12-01T10:00:00",
            "organization": "Test Organization",
            "framework_version": "NIST CSF 2.0",
            "functions": {
                "ID": {
                    "name": "ID",
                    "current_tier": "Partial",
                    "target_tier": "Repeatable",
                    "maturity_score": 65.0,
                    "gaps": ["Asset inventory incomplete"],
                    "recommendations": ["Implement asset management"]
                }
            },
            "overall_maturity": 65.0,
            "priority_gaps": ["Asset inventory incomplete"],
            "recommendations": ["Implement asset management"]
        }
        mock_orchestrator.nist_assessor.generate_compliance_report.return_value = mock_report
        
        response = client.get(
            "/api/v1/security-frameworks/nist/compliance-report?organization_name=Test Organization"
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["organization"] == "Test Organization"
        assert data["framework_version"] == "NIST CSF 2.0"
        assert data["overall_maturity"] == 65.0
        assert "functions" in data
        assert len(data["priority_gaps"]) > 0


class TestFrameworkStatisticsEndpoint:
    """Test framework statistics endpoint."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_get_framework_statistics(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test getting framework statistics."""
        mock_get_user.return_value = mock_user
        mock_stats = {
            "mitre_techniques_count": 500,
            "cvss_version": "4.0",
            "nist_categories_count": 23,
            "nist_subcategories_count": 108,
            "correlation_cache_size": 150,
            "last_updated": "2023-12-01T10:00:00"
        }
        mock_orchestrator.get_framework_statistics.return_value = mock_stats
        
        response = client.get("/api/v1/security-frameworks/statistics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["mitre_techniques_count"] == 500
        assert data["cvss_version"] == "4.0"
        assert data["nist_categories_count"] == 23
        assert data["nist_subcategories_count"] == 108
        assert data["correlation_cache_size"] == 150
        assert "last_updated" in data


class TestFrameworkRefreshEndpoint:
    """Test framework data refresh endpoint."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_refresh_framework_data(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test framework data refresh."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.get_techniques.return_value = {}
        
        response = client.post("/api/v1/security-frameworks/frameworks/refresh")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "refreshed successfully" in data["message"]
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_refresh_framework_data_force(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test forced framework data refresh."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.get_techniques.return_value = {}
        
        response = client.post("/api/v1/security-frameworks/frameworks/refresh?force=true")
        
        assert response.status_code == 200
        
        # Verify force refresh was called
        mock_orchestrator.mitre_client.get_techniques.assert_called_with(force_refresh=True)
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    @patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator')
    def test_refresh_framework_data_error(
        self,
        mock_orchestrator,
        mock_get_user,
        client,
        mock_user
    ):
        """Test framework refresh with error."""
        mock_get_user.return_value = mock_user
        mock_orchestrator.mitre_client.get_techniques.side_effect = Exception("Refresh failed")
        
        response = client.post("/api/v1/security-frameworks/frameworks/refresh")
        
        assert response.status_code == 500
        assert "Framework data refresh failed" in response.json()["detail"]


@pytest.mark.integration
class TestSecurityFrameworksIntegration:
    """Integration tests for security frameworks API."""
    
    @patch('pitas.api.v1.endpoints.security_frameworks.get_current_user')
    def test_complete_vulnerability_workflow(self, mock_get_user, client, mock_user):
        """Test complete vulnerability analysis workflow."""
        mock_get_user.return_value = mock_user
        
        # Test vulnerability input
        vulnerability_input = {
            "id": "CVE-2023-WORKFLOW",
            "title": "Workflow Test Vulnerability",
            "description": "A comprehensive vulnerability for testing the complete workflow including authentication bypass, data exposure, and privilege escalation",
            "severity": "Critical",
            "affected_systems": ["auth-service", "data-api", "admin-panel"],
            "discovery_date": "2023-12-01"
        }
        
        # Mock all framework responses
        with patch('pitas.api.v1.endpoints.security_frameworks.security_orchestrator') as mock_orchestrator:
            # Mock enriched vulnerability response
            mock_enriched = EnrichedVulnerability(
                vulnerability_id="CVE-2023-WORKFLOW",
                title="Workflow Test Vulnerability",
                description="A comprehensive vulnerability",
                severity="Critical",
                mitre_techniques=[
                    MITReTechnique(
                        technique_id="T1078",
                        name="Valid Accounts",
                        description="Adversaries may obtain and abuse credentials",
                        tactic="initial-access",
                        platform=["Windows", "Linux"],
                        data_sources=["Authentication"],
                        url="https://attack.mitre.org/techniques/T1078"
                    )
                ],
                mitre_confidence=0.9,
                cvss_scores=CVSSScore(
                    base_score=9.8,
                    base_severity="Critical",
                    vector_string="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N"
                ),
                nist_controls=["PR.AC-01", "PR.AC-02", "PR.DS-01", "DE.AE-01", "RS.RP-01"],
                risk_score=9.5,
                business_impact="Critical",
                remediation_priority="Critical"
            )
            
            mock_orchestrator.correlate_security_data.return_value = mock_enriched
            
            # Test vulnerability analysis
            response = client.post(
                "/api/v1/security-frameworks/vulnerabilities/analyze",
                json=vulnerability_input
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify comprehensive analysis results
            assert data["vulnerability_id"] == "CVE-2023-WORKFLOW"
            assert data["risk_score"] == 9.5
            assert data["business_impact"] == "Critical"
            assert data["remediation_priority"] == "Critical"
            assert len(data["mitre_techniques"]) == 1
            assert data["mitre_techniques"][0]["tactic"] == "initial-access"
            assert data["cvss_scores"]["base_severity"] == "Critical"
            assert len(data["nist_controls"]) == 5
            assert data["mitre_confidence"] == 0.9
