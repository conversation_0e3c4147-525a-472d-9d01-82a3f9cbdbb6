"""Tests for training API endpoints."""

import pytest
from uuid import uuid4
from fastapi.testclient import TestClient

from pitas.db.models.training import CompetencyLevel, TrainingStatus


class TestCompetencyFrameworkAPI:
    """Test competency framework API endpoints."""

    def test_create_competency_framework(self, client: TestClient):
        """Test creating a competency framework via API."""
        framework_data = {
            "name": "NICE Cybersecurity Framework",
            "description": "NICE Cybersecurity Workforce Framework",
            "version": "1.0",
            "work_role_id": "SP-RSK-001",
            "specialty_area": "Risk Management",
            "category": "Securely Provision"
        }
        
        response = client.post("/api/v1/training/frameworks", json=framework_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "NICE Cybersecurity Framework"
        assert data["work_role_id"] == "SP-RSK-001"
        assert "id" in data

    def test_list_competency_frameworks(self, client: TestClient):
        """Test listing competency frameworks."""
        # Create a framework first
        framework_data = {
            "name": "Test Framework",
            "version": "1.0",
            "work_role_id": "TEST-001",
            "specialty_area": "Testing",
            "category": "Test"
        }
        client.post("/api/v1/training/frameworks", json=framework_data)
        
        # List frameworks
        response = client.get("/api/v1/training/frameworks")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_get_competency_framework(self, client: TestClient):
        """Test getting a specific competency framework."""
        # Create a framework first
        framework_data = {
            "name": "Test Framework",
            "version": "1.0",
            "work_role_id": "TEST-001",
            "specialty_area": "Testing",
            "category": "Test"
        }
        create_response = client.post("/api/v1/training/frameworks", json=framework_data)
        framework_id = create_response.json()["id"]
        
        # Get the framework
        response = client.get(f"/api/v1/training/frameworks/{framework_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == framework_id
        assert data["name"] == "Test Framework"


class TestTrainingCourseAPI:
    """Test training course API endpoints."""

    def test_create_training_course(self, client: TestClient):
        """Test creating a training course via API."""
        course_data = {
            "title": "Introduction to Penetration Testing",
            "description": "Basic penetration testing concepts",
            "provider": "SANS",
            "course_code": "SEC560",
            "duration_hours": 40,
            "difficulty_level": "advanced_beginner",
            "learning_objectives": ["Understand pen testing methodology"],
            "is_certification_prep": True,
            "cost": 5000.0
        }
        
        response = client.post("/api/v1/training/courses", json=course_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Introduction to Penetration Testing"
        assert data["provider"] == "SANS"
        assert data["difficulty_level"] == "advanced_beginner"

    def test_list_training_courses(self, client: TestClient):
        """Test listing training courses."""
        # Create a course first
        course_data = {
            "title": "Test Course",
            "difficulty_level": "novice"
        }
        client.post("/api/v1/training/courses", json=course_data)
        
        # List courses
        response = client.get("/api/v1/training/courses")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_enroll_in_course(self, client: TestClient):
        """Test enrolling in a training course."""
        # Create a course first
        course_data = {
            "title": "Test Course",
            "difficulty_level": "novice"
        }
        course_response = client.post("/api/v1/training/courses", json=course_data)
        course_id = course_response.json()["id"]
        
        # Enroll in the course
        enrollment_data = {
            "user_id": str(uuid4()),
            "course_id": course_id,
            "status": "not_started"
        }
        
        response = client.post("/api/v1/training/enrollments", json=enrollment_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["course_id"] == course_id
        assert data["status"] == "not_started"


class TestCertificationAPI:
    """Test certification API endpoints."""

    def test_create_certification(self, client: TestClient):
        """Test creating a certification via API."""
        cert_data = {
            "name": "Certified Ethical Hacker",
            "abbreviation": "CEH",
            "provider": "EC-Council",
            "description": "Entry-level ethical hacking certification",
            "level": "advanced_beginner",
            "renewal_period_years": 3,
            "cpe_credits_required": 120,
            "exam_cost": 1199.0
        }
        
        response = client.post("/api/v1/training/certifications", json=cert_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Certified Ethical Hacker"
        assert data["abbreviation"] == "CEH"
        assert data["level"] == "advanced_beginner"

    def test_list_certifications(self, client: TestClient):
        """Test listing certifications."""
        # Create a certification first
        cert_data = {
            "name": "Test Certification",
            "abbreviation": "TC",
            "provider": "Test Provider",
            "level": "novice"
        }
        client.post("/api/v1/training/certifications", json=cert_data)
        
        # List certifications
        response = client.get("/api/v1/training/certifications")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1


class TestCTFAPI:
    """Test CTF API endpoints."""

    def test_create_ctf_challenge(self, client: TestClient):
        """Test creating a CTF challenge via API."""
        challenge_data = {
            "title": "Basic Buffer Overflow",
            "description": "Exploit a simple buffer overflow vulnerability",
            "category": "pwn",
            "difficulty": "advanced_beginner",
            "points": 100,
            "flag": "flag{buffer_overflow_basics}",
            "hints": ["Check the input validation"],
            "created_by": str(uuid4())
        }
        
        response = client.post("/api/v1/training/ctf/challenges", json=challenge_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Basic Buffer Overflow"
        assert data["category"] == "pwn"
        assert data["points"] == 100

    def test_list_ctf_challenges(self, client: TestClient):
        """Test listing CTF challenges."""
        # Create a challenge first
        challenge_data = {
            "title": "Test Challenge",
            "category": "test",
            "difficulty": "novice",
            "points": 50,
            "flag": "flag{test}",
            "created_by": str(uuid4())
        }
        client.post("/api/v1/training/ctf/challenges", json=challenge_data)
        
        # List challenges
        response = client.get("/api/v1/training/ctf/challenges")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_submit_ctf_flag(self, client: TestClient):
        """Test submitting a CTF flag."""
        # Create a challenge first
        challenge_data = {
            "title": "Test Challenge",
            "category": "test",
            "difficulty": "novice",
            "points": 50,
            "flag": "flag{test_flag}",
            "created_by": str(uuid4())
        }
        challenge_response = client.post("/api/v1/training/ctf/challenges", json=challenge_data)
        challenge_id = challenge_response.json()["id"]
        
        # Submit flag
        submission_data = {
            "user_id": str(uuid4()),
            "challenge_id": challenge_id,
            "submitted_flag": "flag{test_flag}",
            "is_correct": True
        }
        
        response = client.post("/api/v1/training/ctf/submissions", json=submission_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["challenge_id"] == challenge_id
        assert data["is_correct"] is True

    def test_get_ctf_leaderboard(self, client: TestClient):
        """Test getting CTF leaderboard."""
        response = client.get("/api/v1/training/ctf/leaderboard")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)


class TestMentorshipAPI:
    """Test mentorship API endpoints."""

    def test_create_mentorship_pair(self, client: TestClient):
        """Test creating a mentorship pair via API."""
        pair_data = {
            "mentor_id": str(uuid4()),
            "mentee_id": str(uuid4()),
            "goals": ["Improve penetration testing skills"],
            "meeting_frequency": "weekly"
        }
        
        response = client.post("/api/v1/training/mentorship/pairs", json=pair_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["mentor_id"] == pair_data["mentor_id"]
        assert data["mentee_id"] == pair_data["mentee_id"]
        assert data["meeting_frequency"] == "weekly"

    def test_get_user_mentorships(self, client: TestClient):
        """Test getting mentorships for a user."""
        # Create a mentorship pair first
        mentor_id = str(uuid4())
        pair_data = {
            "mentor_id": mentor_id,
            "mentee_id": str(uuid4())
        }
        client.post("/api/v1/training/mentorship/pairs", json=pair_data)
        
        # Get mentorships for the mentor
        response = client.get(f"/api/v1/training/mentorship/pairs/users/{mentor_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_end_mentorship(self, client: TestClient):
        """Test ending a mentorship relationship."""
        # Create a mentorship pair first
        pair_data = {
            "mentor_id": str(uuid4()),
            "mentee_id": str(uuid4())
        }
        pair_response = client.post("/api/v1/training/mentorship/pairs", json=pair_data)
        pair_id = pair_response.json()["id"]
        
        # End the mentorship
        response = client.put(
            f"/api/v1/training/mentorship/pairs/{pair_id}/end",
            params={"satisfaction_rating": 4.5}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_active"] is False
        assert data["satisfaction_rating"] == 4.5
