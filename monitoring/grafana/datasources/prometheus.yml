apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
    
  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: performance-metrics
    user: pitas_admin
    secureJsonData:
      password: pitas_influxdb
    jsonData:
      version: "Flux"
      organization: pitas
      defaultBucket: performance-metrics
      tlsSkipVerify: true
