# Prometheus configuration for PITAS performance monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # PITAS Application Metrics
  - job_name: 'pitas-app'
    static_configs:
      - targets: ['pitas-app:9090']
    metrics_path: '/api/v1/performance/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # System Metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Redis Cluster Metrics
  - job_name: 'redis-cluster'
    static_configs:
      - targets: 
        - 'redis-node-1:6379'
        - 'redis-node-2:6379'
        - 'redis-node-3:6379'
    scrape_interval: 30s

  # PostgreSQL Metrics (if postgres_exporter is available)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  # Prometheus Self-Monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 30s

  # HAProxy Load Balancer Metrics
  - job_name: 'haproxy'
    static_configs:
      - targets: ['load-balancer:8404']
    scrape_interval: 30s

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Performance-specific recording rules
rule_files:
  - "performance_rules.yml"
