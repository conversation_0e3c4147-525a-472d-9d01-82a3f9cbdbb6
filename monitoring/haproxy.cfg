# HAProxy configuration for PITAS load balancing and performance optimization
global
    daemon
    maxconn 4096
    log stdout local0
    stats socket /var/run/haproxy.sock mode 600 level admin
    stats timeout 2m

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    option httplog
    option dontlognull
    option redispatch
    retries 3
    maxconn 2000

# Statistics page
stats enable
stats uri /stats
stats refresh 30s
stats admin if TRUE

# Frontend for PITAS application
frontend pitas_frontend
    bind *:80
    bind *:443 ssl crt /etc/ssl/certs/pitas.pem
    redirect scheme https if !{ ssl_fc }
    
    # Rate limiting
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request reject if { sc_http_req_rate(0) gt 20 }
    
    # Health check endpoint
    acl health_check path_beg /health
    use_backend pitas_health if health_check
    
    # Performance monitoring endpoint
    acl metrics_check path_beg /api/v1/performance/metrics
    use_backend pitas_metrics if metrics_check
    
    # Default backend
    default_backend pitas_backend

# Backend for PITAS application instances
backend pitas_backend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Connection pooling and keep-alive
    option http-keep-alive
    timeout http-keep-alive 1s
    
    # Compression
    compression algo gzip
    compression type text/html text/plain text/css text/javascript application/javascript application/json
    
    # Server instances (in production, these would be multiple instances)
    server pitas1 pitas-app:8000 check inter 5s rise 2 fall 3 maxconn 100
    # server pitas2 pitas-app-2:8000 check inter 5s rise 2 fall 3 maxconn 100
    # server pitas3 pitas-app-3:8000 check inter 5s rise 2 fall 3 maxconn 100

# Health check backend
backend pitas_health
    server health pitas-app:8000 check

# Metrics backend
backend pitas_metrics
    server metrics pitas-app:9090 check

# Statistics backend
listen stats
    bind *:8404
    stats enable
    stats uri /
    stats refresh 10s
    stats admin if TRUE
    stats show-legends
