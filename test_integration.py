#!/usr/bin/env python3
"""Integration test script to verify all phases work together."""

import os
import sys
import traceback
from pathlib import Path

# Set test environment
os.environ["PITAS_ENV"] = "test"

# Load test environment variables
test_env_file = Path(__file__).parent / ".env.test"
if test_env_file.exists():
    with open(test_env_file) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                os.environ[key] = value

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_imports():
    """Test basic application imports."""
    print("🔍 Testing basic imports...")
    
    try:
        # Test core imports
        from pitas.core.config import settings
        print("✅ Core config imported successfully")
        
        from pitas.core.logging import setup_logging
        print("✅ Logging setup imported successfully")
        
        # Test database models
        from pitas.db.models import Base, User
        print("✅ Database models imported successfully")
        
        # Test API router
        from pitas.api.v1.router import api_router
        print("✅ API router imported successfully")
        
        # Test main application
        from pitas.main import create_app
        print("✅ Main application imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False

def test_endpoint_imports():
    """Test endpoint imports."""
    print("\n🔍 Testing endpoint imports...")
    
    endpoints = [
        "health", "pentesters", "projects", "resource_allocation",
        "vulnerabilities", "assets", "career", "training",
        "workflow", "remediation", "integrations", "knowledge"
    ]
    
    success_count = 0
    for endpoint in endpoints:
        try:
            module = __import__(f"pitas.api.v1.endpoints.{endpoint}", fromlist=[endpoint])
            print(f"✅ {endpoint} endpoint imported successfully")
            success_count += 1
        except Exception as e:
            print(f"❌ {endpoint} endpoint failed: {e}")
    
    print(f"\n📊 Endpoint Import Results: {success_count}/{len(endpoints)} successful")
    return success_count == len(endpoints)

def test_service_imports():
    """Test service imports."""
    print("\n🔍 Testing service imports...")
    
    services = [
        "user", "pentester", "project", "vulnerability", "asset",
        "career", "training", "workflow", "remediation"
    ]
    
    success_count = 0
    for service in services:
        try:
            module = __import__(f"pitas.services.{service}", fromlist=[service])
            print(f"✅ {service} service imported successfully")
            success_count += 1
        except Exception as e:
            print(f"❌ {service} service failed: {e}")
    
    print(f"\n📊 Service Import Results: {success_count}/{len(services)} successful")
    return success_count == len(services)

def test_app_creation():
    """Test FastAPI app creation."""
    print("\n🔍 Testing FastAPI app creation...")
    
    try:
        from pitas.main import create_app
        app = create_app()
        print("✅ FastAPI app created successfully")
        print(f"✅ App title: {app.title}")
        print(f"✅ App version: {app.version}")
        return True
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run integration tests."""
    print("🚀 PITAS Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Endpoint Imports", test_endpoint_imports),
        ("Service Imports", test_service_imports),
        ("App Creation", test_app_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📋 INTEGRATION TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Overall Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All integration tests passed! System is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
